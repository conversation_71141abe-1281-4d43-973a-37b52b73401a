"""
Kaggle submission script for NEURIPs Polymer Competition
"""

import pandas as pd
import numpy as np
import os
import sys
from pathlib import Path

# Set working directory
WORK_DIR = Path("/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/Kaggle/NEURIPs_polymer")
os.chdir(WORK_DIR)
sys.path.insert(0, str(WORK_DIR))

from polymer_competition_solution import PolymerPropertyPredictor

def predict(test_df):
    """
    Main prediction function for Kaggle evaluation
    
    Args:
        test_df: DataFrame with 'id' and 'SMILES' columns
    
    Returns:
        DataFrame: Predictions with id and all target properties
    """
    try:
        print(f"Working directory: {WORK_DIR}")
        print(f"Test data shape: {test_df.shape}")
        
        # Initialize predictor
        predictor = PolymerPropertyPredictor(work_dir=WORK_DIR)
        
        # Try to load pre-trained model
        model_path = WORK_DIR / 'polymer_model.pkl'
        if model_path.exists():
            predictor.load_model('polymer_model.pkl')
            print("Loaded pre-trained model")
        else:
            print("No pre-trained model found, training on available data...")
            
            # Train on available data
            train_df, datasets = predictor.load_and_integrate_data()
            if train_df.empty:
                print("No training data available for on-the-fly training")
                # Return safe fallback predictions
                submission = pd.DataFrame()
                submission['id'] = test_df['id']
                for prop in ['Tg', 'FFV', 'Tc', 'Density', 'Rg']:
                    submission[prop] = 0.0
                return submission
            
            integrated_data = predictor.integrate_supplementary_data(train_df, datasets)
            X, y = predictor.prepare_multitask_data(integrated_data)
            predictor.create_multitask_models()
            # Use fewer trials for faster on-the-fly training
            predictor.train_models(X, y, cv_folds=3, n_trials=20)
        
        # Make predictions
        predictions_df = predictor.predict(test_df['SMILES'])
        
        # Create submission format
        submission = predictor.create_submission(test_df, predictions_df)
        
        print("Prediction completed successfully")
        print(f"Submission shape: {submission.shape}")
        return submission
        
    except Exception as e:
        print(f"Error in prediction: {e}")
        import traceback
        traceback.print_exc()
        
        # Return safe fallback predictions
        submission = pd.DataFrame()
        submission['id'] = test_df['id']
        for prop in ['Tg', 'FFV', 'Tc', 'Density', 'Rg']:
            submission[prop] = 0.0
        return submission

def main():
    """Test the prediction pipeline locally"""
    print("Testing polymer prediction pipeline...")
    print(f"Working directory: {WORK_DIR}")
    
    # Load test data
    test_path = WORK_DIR / 'test.csv'
    if test_path.exists():
        test_df = pd.read_csv(test_path)
        print(f"Test data shape: {test_df.shape}")
        
        # Make predictions
        submission = predict(test_df)
        
        # Save submission
        submission_path = WORK_DIR / 'submission.csv'
        submission.to_csv(submission_path, index=False)
        print(f"Submission saved to {submission_path}")
        print(submission.head())
    else:
        print(f"test.csv not found at {test_path}")
        
        # Create sample test data for testing
        print("Creating sample test data for testing...")
        sample_test = pd.DataFrame({
            'id': [1, 2, 3],
            'SMILES': ['CCO', 'CC(C)O', 'CCCC']
        })
        
        submission = predict(sample_test)
        print("Sample prediction:")
        print(submission)

if __name__ == "__main__":
    main()
What is PolyBERT ?:
- https://www.nature.com/articles/s41467-023-39868-6
- https://github.com/Ramprasad-Group/canonicalize_psmiles.

# Import Dependencies 


```python
!pip install /kaggle/input/rdkit-2025-3-3-cp311/rdkit-2025.3.3-cp311-cp311-manylinux_2_28_x86_64.whl
```

    Processing /kaggle/input/rdkit-2025-3-3-cp311/rdkit-2025.3.3-cp311-cp311-manylinux_2_28_x86_64.whl
    Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from rdkit==2025.3.3) (1.26.4)
    Requirement already satisfied: Pillow in /usr/local/lib/python3.11/dist-packages (from rdkit==2025.3.3) (11.1.0)
    Requirement already satisfied: mkl_fft in /usr/local/lib/python3.11/dist-packages (from numpy->rdkit==2025.3.3) (1.3.8)
    Requirement already satisfied: mkl_random in /usr/local/lib/python3.11/dist-packages (from numpy->rdkit==2025.3.3) (1.2.4)
    Requirement already satisfied: mkl_umath in /usr/local/lib/python3.11/dist-packages (from numpy->rdkit==2025.3.3) (0.1.1)
    Requirement already satisfied: mkl in /usr/local/lib/python3.11/dist-packages (from numpy->rdkit==2025.3.3) (2025.1.0)
    Requirement already satisfied: tbb4py in /usr/local/lib/python3.11/dist-packages (from numpy->rdkit==2025.3.3) (2022.1.0)
    Requirement already satisfied: mkl-service in /usr/local/lib/python3.11/dist-packages (from numpy->rdkit==2025.3.3) (2.4.1)
    Requirement already satisfied: intel-openmp<2026,>=2024 in /usr/local/lib/python3.11/dist-packages (from mkl->numpy->rdkit==2025.3.3) (2024.2.0)
    Requirement already satisfied: tbb==2022.* in /usr/local/lib/python3.11/dist-packages (from mkl->numpy->rdkit==2025.3.3) (2022.1.0)
    Requirement already satisfied: tcmlib==1.* in /usr/local/lib/python3.11/dist-packages (from tbb==2022.*->mkl->numpy->rdkit==2025.3.3) (1.3.0)
    Requirement already satisfied: intel-cmplr-lib-rt in /usr/local/lib/python3.11/dist-packages (from mkl_umath->numpy->rdkit==2025.3.3) (2024.2.0)
    Requirement already satisfied: intel-cmplr-lib-ur==2024.2.0 in /usr/local/lib/python3.11/dist-packages (from intel-openmp<2026,>=2024->mkl->numpy->rdkit==2025.3.3) (2024.2.0)
    Installing collected packages: rdkit
    Successfully installed rdkit-2025.3.3



```python
from sentence_transformers import SentenceTransformer

# zip解凍後のパスに合わせて読み込み
model = SentenceTransformer('/kaggle/input/polybert/polyBERT-local')

# SMILESのベクトル化
embeddings = model.encode(["[*]CC[*]", "[*]COC[*]"])
print(embeddings)
```

    2025-07-20 12:39:23.994405: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:477] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered
    WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
    E0000 00:00:1753015164.279031      13 cuda_dnn.cc:8310] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered
    E0000 00:00:1753015164.364938      13 cuda_blas.cc:1418] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered



    Batches:   0%|          | 0/1 [00:00<?, ?it/s]


    [[-0.11860413  0.18905912 -0.5370367  ... -0.87153924  0.2978902
       0.67119443]
     [-0.39811426  0.4832777  -0.27974743 ... -0.54284924  0.20273334
       0.9449012 ]]



```python

```


```python
 # Importing Required Libraries\nLet's begin by importing the essential Python libraries needed for data processing, visualization, and modeling.

import pandas as pd
import numpy as np


from sklearn.ensemble import HistGradientBoostingRegressor,ExtraTreesRegressor
from catboost import CatBoostRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error


import networkx as nx
from rdkit.Chem import AllChem
from rdkit.Chem import Descriptors
from rdkit.Chem import rdmolops
from rdkit import Chem

import warnings
warnings.filterwarnings("ignore")
pd.set_option('display.max_columns', None)
```


```python
class CFG:
    TARGETS = ['Tg', 'FFV', 'Tc', 'Density', 'Rg']
    SEED = 42
    FOLDS = 5
```


```python
useless_cols = [

    # === NaNが80%以上 ===
    'Tg', 'Tc', 'Density', 'Rg',
    'MaxPartialCharge', 'MinPartialCharge', 'MaxAbsPartialCharge', 'MinAbsPartialCharge',
    'BCUT2D_MWHI', 'BCUT2D_MWLOW',
    'BCUT2D_CHGHI', 'BCUT2D_CHGLO',
    'BCUT2D_LOGPHI', 'BCUT2D_LOGPLOW',
    'BCUT2D_MRHI', 'BCUT2D_MRLOW',

    # === 定数列 ===
    'SMR_VSA8', 'SlogP_VSA9', 'fr_isothiocyan', 'fr_prisulfonamd',

    # === 高相関 (|r| > 0.95) ===
    'MaxEStateIndex', 'HeavyAtomMolWt', 'ExactMolWt', 'NumValenceElectrons',
    'FpDensityMorgan2', 'FpDensityMorgan3',
    'Chi0', 'Chi0n', 'Chi0v', 'Chi1', 'Chi1n', 'Chi1v',
    'Chi2n', 'Chi2v', 'Chi3n', 'Chi3v', 'Chi4n', 'Chi4v',
    'HallKierAlpha', 'Kappa1', 'LabuteASA', 'SlogP_VSA6', 'VSA_EState6',
    'HeavyAtomCount', 'NOCount', 'NumAromaticCarbocycles', 'NumAromaticRings',
    'NumHDonors', 'Phi', 'MolMR',
    'fr_Al_OH_noTert', 'fr_COO2', 'fr_C_O', 'fr_C_O_noCOO',
    'fr_Nhpyrrole', 'fr_amide', 'fr_benzene', 'fr_diazo',
    'fr_halogen', 'fr_nitrile', 'fr_nitro_arom',
    'fr_phenol', 'fr_phenol_noOrthoHbond', 'fr_phos_ester'
    #'avg_shortest_path', 'num_cycles' #この二つは残す
]
```

# Read Files

### Main Files


```python
 #We will load both the training and test datasets using pandas, and store test IDs 
train=pd.read_csv('/kaggle/input/neurips-open-polymer-prediction-2025/train.csv')
test=pd.read_csv('/kaggle/input/neurips-open-polymer-prediction-2025/test.csv')
ss=pd.read_csv('/kaggle/input/neurips-open-polymer-prediction-2025/sample_submission.csv')
ID=test['id'].copy()
```


```python

```


```python

```


```python

```


```python
train
```




<div>
<style scoped>
    .dataframe tbody tr th:only-of-type {
        vertical-align: middle;
    }

    .dataframe tbody tr th {
        vertical-align: top;
    }

    .dataframe thead th {
        text-align: right;
    }
</style>
<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>id</th>
      <th>SMILES</th>
      <th>Tg</th>
      <th>FFV</th>
      <th>Tc</th>
      <th>Density</th>
      <th>Rg</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>0</th>
      <td>87817</td>
      <td>*CC(*)c1ccccc1C(=O)OCCCCCC</td>
      <td>NaN</td>
      <td>0.374645</td>
      <td>0.205667</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>1</th>
      <td>106919</td>
      <td>*Nc1ccc([C@H](CCC)c2ccc(C3(c4ccc([C@@H](CCC)c5...</td>
      <td>NaN</td>
      <td>0.370410</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>2</th>
      <td>388772</td>
      <td>*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(Oc6ccc(...</td>
      <td>NaN</td>
      <td>0.378860</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>3</th>
      <td>519416</td>
      <td>*Nc1ccc(-c2c(-c3ccc(C)cc3)c(-c3ccc(C)cc3)c(N*)...</td>
      <td>NaN</td>
      <td>0.387324</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>4</th>
      <td>539187</td>
      <td>*Oc1ccc(OC(=O)c2cc(OCCCCCCCCCOCC3CCCN3c3ccc([N...</td>
      <td>NaN</td>
      <td>0.355470</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>...</th>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
    </tr>
    <tr>
      <th>7968</th>
      <td>2146592435</td>
      <td>*Oc1cc(CCCCCCCC)cc(OC(=O)c2cccc(C(*)=O)c2)c1</td>
      <td>NaN</td>
      <td>0.367498</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>7969</th>
      <td>2146810552</td>
      <td>*C(=O)OCCN(CCOC(=O)c1ccc2c(c1)C(=O)N(c1cccc(N3...</td>
      <td>NaN</td>
      <td>0.353280</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>7970</th>
      <td>2147191531</td>
      <td>*c1cc(C(=O)NCCCCCCCC)cc(N2C(=O)c3ccc(-c4ccc5c(...</td>
      <td>NaN</td>
      <td>0.369411</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>7971</th>
      <td>2147435020</td>
      <td>*C=C(*)c1ccccc1C</td>
      <td>261.662355</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>7972</th>
      <td>2147438299</td>
      <td>*c1ccc(OCCCCCCCCCCCOC(=O)CCCCC(=O)OCCCCCCCCCCC...</td>
      <td>NaN</td>
      <td>0.374049</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
  </tbody>
</table>
<p>7973 rows × 7 columns</p>
</div>




```python
test
```




<div>
<style scoped>
    .dataframe tbody tr th:only-of-type {
        vertical-align: middle;
    }

    .dataframe tbody tr th {
        vertical-align: top;
    }

    .dataframe thead th {
        text-align: right;
    }
</style>
<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>id</th>
      <th>SMILES</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>0</th>
      <td>1109053969</td>
      <td>*Oc1ccc(C=NN=Cc2ccc(Oc3ccc(C(c4ccc(*)cc4)(C(F)...</td>
    </tr>
    <tr>
      <th>1</th>
      <td>1422188626</td>
      <td>*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4cccc(C(=O)c...</td>
    </tr>
    <tr>
      <th>2</th>
      <td>2032016830</td>
      <td>*c1cccc(OCCCCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6...</td>
    </tr>
  </tbody>
</table>
</div>




```python

```

### Extra Files


```python
tc_smiles =pd.read_csv('/kaggle/input/tc-smiles/Tc_SMILES.csv')
tg_smiles =pd.read_csv('/kaggle/input/smiles-extra-data/JCIM_sup_bigsmiles.csv')
ktg_smiles =pd.read_excel('/kaggle/input/smiles-extra-data/data_tg3.xlsx')
de_smiles =pd.read_excel('/kaggle/input/smiles-extra-data/data_dnst1.xlsx')
ffv_smiles =pd.read_csv('/kaggle/input/neurips-open-polymer-prediction-2025/train_supplement/dataset4.csv')
```

# Preprocessing 


```python
def make_smile_canonical(smile): # To avoid duplicates, for example: canonical '*C=C(*)C' == '*C(=C*)C'
    try:
        mol = Chem.MolFromSmiles(smile)
        canon_smile = Chem.MolToSmiles(mol, canonical=True)
        return canon_smile
    except:
        return np.nan

train['SMILES'] = train['SMILES'].apply(lambda s: make_smile_canonical(s))
test['SMILES'] = test['SMILES'].apply(lambda s: make_smile_canonical(s))
```


```python
ktg_smiles.rename(columns={'Tg [K]': 'Tg'}, inplace=True)
tg_smiles.rename(columns={'Tg (C)': 'Tg'}, inplace=True)
tc_smiles.rename(columns={'TC_mean': 'Tc'}, inplace=True)
de_smiles.rename(columns={'density(g/cm3)': 'Density'}, inplace=True)
ffv_smiles.rename(columns={'FFV': 'FFV'}, inplace=True)
```


```python
de_smiles['SMILES'] = de_smiles['SMILES'].apply(lambda s: make_smile_canonical(s))
de_smiles = de_smiles[(de_smiles['SMILES'].notnull())&(de_smiles['Density'].notnull())&(de_smiles['Density'] != 'nylon')]
de_smiles['Density'] = de_smiles['Density'].astype('float64')
de_smiles['Density'] -= 0.118

ktg_smiles['Tg'] = ktg_smiles['Tg'] - 273.15
```

    [12:40:02] SMILES Parse Error: syntax error while parsing: *O[Si](*)([R])[R]
    [12:40:02] SMILES Parse Error: check for mistakes around position 12:
    [12:40:02] *O[Si](*)([R])[R]
    [12:40:02] ~~~~~~~~~~~^
    [12:40:02] SMILES Parse Error: Failed parsing SMILES '*O[Si](*)([R])[R]' for input: '*O[Si](*)([R])[R]'
    [12:40:02] SMILES Parse Error: syntax error while parsing: *NC(=O)c4ccc3c(=O)n(c2ccc([R]c1ccc(*)cc1)cc2)c(=O)c3c4
    [12:40:02] SMILES Parse Error: check for mistakes around position 28:
    [12:40:02] c4ccc3c(=O)n(c2ccc([R]c1ccc(*)cc1)cc2)c(=
    [12:40:02] ~~~~~~~~~~~~~~~~~~~~^
    [12:40:02] SMILES Parse Error: Failed parsing SMILES '*NC(=O)c4ccc3c(=O)n(c2ccc([R]c1ccc(*)cc1)cc2)c(=O)c3c4' for input: '*NC(=O)c4ccc3c(=O)n(c2ccc([R]c1ccc(*)cc1)cc2)c(=O)c3c4'
    [12:40:02] SMILES Parse Error: syntax error while parsing: O=C=N[R1]N=C=O.O[R2]O.O[R3]O
    [12:40:02] SMILES Parse Error: check for mistakes around position 7:
    [12:40:02] O=C=N[R1]N=C=O.O[R2]O.O[R3]O
    [12:40:02] ~~~~~~^
    [12:40:02] SMILES Parse Error: Failed parsing SMILES 'O=C=N[R1]N=C=O.O[R2]O.O[R3]O' for input: 'O=C=N[R1]N=C=O.O[R2]O.O[R3]O'
    [12:40:02] SMILES Parse Error: syntax error while parsing: *CN([R'])Cc2cc([R]c1cc(*)c(O)c(CN([R'])C*)c1)cc(*)c2O
    [12:40:02] SMILES Parse Error: check for mistakes around position 6:
    [12:40:02] *CN([R'])Cc2cc([R]c1cc(*)c(O)c(CN([R'])C*
    [12:40:02] ~~~~~^
    [12:40:02] SMILES Parse Error: Failed parsing SMILES '*CN([R'])Cc2cc([R]c1cc(*)c(O)c(CN([R'])C*)c1)cc(*)c2O' for input: '*CN([R'])Cc2cc([R]c1cc(*)c(O)c(CN([R'])C*)c1)cc(*)c2O'
    [12:40:02] SMILES Parse Error: syntax error while parsing: *C(F)(F)CC(F)([R])C(*)(F)F
    [12:40:02] SMILES Parse Error: check for mistakes around position 16:
    [12:40:02] *C(F)(F)CC(F)([R])C(*)(F)F
    [12:40:02] ~~~~~~~~~~~~~~~^
    [12:40:02] SMILES Parse Error: Failed parsing SMILES '*C(F)(F)CC(F)([R])C(*)(F)F' for input: '*C(F)(F)CC(F)([R])C(*)(F)F'
    [12:40:02] SMILES Parse Error: syntax error while parsing: *OC2OC(CO[R])C(OC1OC(CO[R])C(*)C(O[R])C1O[R])C(O[R])C2O[R]
    [12:40:02] SMILES Parse Error: check for mistakes around position 11:
    [12:40:02] *OC2OC(CO[R])C(OC1OC(CO[R])C(*)C(O[R])C1O
    [12:40:02] ~~~~~~~~~~^
    [12:40:02] SMILES Parse Error: Failed parsing SMILES '*OC2OC(CO[R])C(OC1OC(CO[R])C(*)C(O[R])C1O[R])C(O[R])C2O[R]' for input: '*OC2OC(CO[R])C(OC1OC(CO[R])C(*)C(O[R])C1O[R])C(O[R])C2O[R]'



```python
def preprocessing(df):
    desc_names = [desc[0] for desc in Descriptors.descList if desc[0] not in useless_cols]
    descriptors = [compute_all_descriptors(smi) for smi in df['SMILES'].to_list()]

    graph_feats = {'graph_diameter': [], 'avg_shortest_path': [], 'num_cycles': []}
    for smile in df['SMILES']:
         compute_graph_features(smile, graph_feats)
        
    result = pd.concat(
        [
            pd.DataFrame(descriptors, columns=desc_names),
            pd.DataFrame(graph_feats)
        ],
        axis=1
    )

    result = result.replace([-np.inf, np.inf], np.nan)
    return result
```


```python

```

# Feature Extraction 


```python

def add_extra_data(df_train, df_extra, target):
    n_samples_before = len(df_train[df_train[target].notnull()])
    
    df_extra['SMILES'] = df_extra['SMILES'].apply(lambda s: make_smile_canonical(s))
    df_extra = df_extra.groupby('SMILES', as_index=False)[target].mean()
    cross_smiles = set(df_extra['SMILES']) & set(df_train['SMILES'])
    unique_smiles_extra = set(df_extra['SMILES']) - set(df_train['SMILES'])

    # Make priority target value from competition's df
    for smile in df_train[df_train[target].notnull()]['SMILES'].tolist():
        if smile in cross_smiles:
            cross_smiles.remove(smile)

    # Imput missing values for competition's SMILES
    for smile in cross_smiles:
        df_train.loc[df_train['SMILES']==smile, target] = df_extra[df_extra['SMILES']==smile][target].values[0]
    
    df_train = pd.concat([df_train, df_extra[df_extra['SMILES'].isin(unique_smiles_extra)]], axis=0).reset_index(drop=True)

    n_samples_after = len(df_train[df_train[target].notnull()])
    print(f'\nFor target "{target}" added {n_samples_after-n_samples_before} new samples!')
    print(f'New unique SMILES: {len(unique_smiles_extra)}')
    return df_train

train = add_extra_data(train, tc_smiles, 'Tc')
train = add_extra_data(train, tg_smiles, 'Tg')
train = add_extra_data(train, ktg_smiles, 'Tg')
train = add_extra_data(train, de_smiles, 'Density')
train = add_extra_data(train, ffv_smiles, 'FFV')
```

    
    For target "Tc" added 129 new samples!
    New unique SMILES: 129
    
    For target "Tg" added 151 new samples!
    New unique SMILES: 136
    
    For target "Tg" added 499 new samples!
    New unique SMILES: 499
    
    For target "Density" added 634 new samples!
    New unique SMILES: 524
    
    For target "FFV" added 862 new samples!
    New unique SMILES: 819


# Feature analysis


```python
train
```




<div>
<style scoped>
    .dataframe tbody tr th:only-of-type {
        vertical-align: middle;
    }

    .dataframe tbody tr th {
        vertical-align: top;
    }

    .dataframe thead th {
        text-align: right;
    }
</style>
<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>id</th>
      <th>SMILES</th>
      <th>Tg</th>
      <th>FFV</th>
      <th>Tc</th>
      <th>Density</th>
      <th>Rg</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>0</th>
      <td>87817.0</td>
      <td>*CC(*)c1ccccc1C(=O)OCCCCCC</td>
      <td>NaN</td>
      <td>0.374645</td>
      <td>0.205667</td>
      <td>0.932</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>1</th>
      <td>106919.0</td>
      <td>*Nc1ccc([C@H](CCC)c2ccc(C3(c4ccc([C@@H](CCC)c5...</td>
      <td>NaN</td>
      <td>0.370410</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>2</th>
      <td>388772.0</td>
      <td>*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(Oc6ccc(...</td>
      <td>NaN</td>
      <td>0.378860</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>3</th>
      <td>519416.0</td>
      <td>*Nc1ccc(-c2c(-c3ccc(C)cc3)c(-c3ccc(C)cc3)c(N*)...</td>
      <td>NaN</td>
      <td>0.387324</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>4</th>
      <td>539187.0</td>
      <td>*Oc1ccc(OC(=O)c2cc(OCCCCCCCCCOCC3CCCN3c3ccc([N...</td>
      <td>NaN</td>
      <td>0.355470</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>...</th>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
    </tr>
    <tr>
      <th>10075</th>
      <td>NaN</td>
      <td>*c1cccc(OCCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5...</td>
      <td>NaN</td>
      <td>0.349095</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>10076</th>
      <td>NaN</td>
      <td>*c1cccc(OCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C...</td>
      <td>NaN</td>
      <td>0.350892</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>10077</th>
      <td>NaN</td>
      <td>*c1cccc(OCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C(...</td>
      <td>NaN</td>
      <td>0.345386</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>10078</th>
      <td>NaN</td>
      <td>*c1cccc(Oc2cccc(Oc3cccc(N4C(=O)c5ccc(Oc6ccc(Sc...</td>
      <td>NaN</td>
      <td>0.362224</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>10079</th>
      <td>NaN</td>
      <td>*c1cccc(P(C)(=O)c2cccc(N3C(=O)c4ccc(Oc5ccc(C(C...</td>
      <td>NaN</td>
      <td>0.369574</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
  </tbody>
</table>
<p>10080 rows × 7 columns</p>
</div>



Canonical SMILES:RDKitで canonical SMILES に変換


```python
from rdkit import Chem
from rdkit.Chem import MolToSmiles

def safe_canonicalize(smi):
    try:
        mol = Chem.MolFromSmiles(smi)
        if mol is None:
            return None
        return Chem.MolToSmiles(mol, canonical=True)
    except:
        return None

train['SMILES'] = train['SMILES'].apply(safe_canonicalize)
duplicates = train.duplicated('SMILES')

test['SMILES'] = test['SMILES'].apply(safe_canonicalize)
duplicates = test.duplicated('SMILES')
```


```python
train
```




<div>
<style scoped>
    .dataframe tbody tr th:only-of-type {
        vertical-align: middle;
    }

    .dataframe tbody tr th {
        vertical-align: top;
    }

    .dataframe thead th {
        text-align: right;
    }
</style>
<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>id</th>
      <th>SMILES</th>
      <th>Tg</th>
      <th>FFV</th>
      <th>Tc</th>
      <th>Density</th>
      <th>Rg</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>0</th>
      <td>87817.0</td>
      <td>*CC(*)c1ccccc1C(=O)OCCCCCC</td>
      <td>NaN</td>
      <td>0.374645</td>
      <td>0.205667</td>
      <td>0.932</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>1</th>
      <td>106919.0</td>
      <td>*Nc1ccc([C@H](CCC)c2ccc(C3(c4ccc([C@@H](CCC)c5...</td>
      <td>NaN</td>
      <td>0.370410</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>2</th>
      <td>388772.0</td>
      <td>*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(Oc6ccc(...</td>
      <td>NaN</td>
      <td>0.378860</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>3</th>
      <td>519416.0</td>
      <td>*Nc1ccc(-c2c(-c3ccc(C)cc3)c(-c3ccc(C)cc3)c(N*)...</td>
      <td>NaN</td>
      <td>0.387324</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>4</th>
      <td>539187.0</td>
      <td>*Oc1ccc(OC(=O)c2cc(OCCCCCCCCCOCC3CCCN3c3ccc([N...</td>
      <td>NaN</td>
      <td>0.355470</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>...</th>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
    </tr>
    <tr>
      <th>10075</th>
      <td>NaN</td>
      <td>*c1cccc(OCCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5...</td>
      <td>NaN</td>
      <td>0.349095</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>10076</th>
      <td>NaN</td>
      <td>*c1cccc(OCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C...</td>
      <td>NaN</td>
      <td>0.350892</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>10077</th>
      <td>NaN</td>
      <td>*c1cccc(OCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C(...</td>
      <td>NaN</td>
      <td>0.345386</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>10078</th>
      <td>NaN</td>
      <td>*c1cccc(Oc2cccc(Oc3cccc(N4C(=O)c5ccc(Oc6ccc(Sc...</td>
      <td>NaN</td>
      <td>0.362224</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
    <tr>
      <th>10079</th>
      <td>NaN</td>
      <td>*c1cccc(P(C)(=O)c2cccc(N3C(=O)c4ccc(Oc5ccc(C(C...</td>
      <td>NaN</td>
      <td>0.369574</td>
      <td>NaN</td>
      <td>NaN</td>
      <td>NaN</td>
    </tr>
  </tbody>
</table>
<p>10080 rows × 7 columns</p>
</div>




```python

```

# PolyBERT


```python
from sentence_transformers import SentenceTransformer

# ✅ ローカルに保存されたpolyBERTモデルを読み込む（インターネット不要）
model = SentenceTransformer('/kaggle/input/polybert/polyBERT-local')

# 例：train_df の SMILES から埋め込みベクトルを生成
smiles_list = train['SMILES'].tolist()

# ベクトル化
embeddings = model.encode(smiles_list, output_value='sentence_embedding')  # または省略（デフォルト）

# 埋め込みを追加
train['embedding'] = embeddings.tolist()

# PolyBERT埋め込み部分
embedding_df = pd.DataFrame(embeddings, columns=[f'emb_{i}' for i in range(embeddings.shape[1])])

# PolyBERT埋め込みを train に連結
train = pd.concat([train.reset_index(drop=True), embedding_df], axis=1)

# 埋め込み列名を取得
embedding_cols = [col for col in train.columns if col.startswith('emb_')]
```


    Batches:   0%|          | 0/315 [00:00<?, ?it/s]



```python
from sentence_transformers import SentenceTransformer
import pandas as pd

# SMILES 列からリスト化
smiles_list_test = test['SMILES'].tolist()

# 埋め込み生成
embeddings_test = model.encode(smiles_list_test)

# DataFrame 化（768次元想定）
embedding_test_df = pd.DataFrame(embeddings_test, columns=[f'emb_{i}' for i in range(embeddings_test.shape[1])])

# 元の test に結合（index ずれ防止）
test = pd.concat([test.reset_index(drop=True), embedding_test_df], axis=1)

# 埋め込み列名一覧を取得（再利用可能）
embedding_cols = [col for col in test.columns if col.startswith('emb_')]
```


    Batches:   0%|          | 0/1 [00:00<?, ?it/s]



```python
#embedding列を削除

train = train.drop(columns="embedding", errors='ignore')
test = test.drop(columns="embedding", errors='ignore')
```


```python

```

# Analysis of features


```python
from rdkit import Chem
from rdkit.ML.Descriptors import MoleculeDescriptors
from rdkit.Chem import Descriptors, rdmolops
import networkx as nx
import pandas as pd
from tqdm import tqdm
import numpy as np

# === 1. 208個のdescriptor名を取得 ===
descriptor_names = [desc[0] for desc in Descriptors._descList]

# === 2. descriptor計算器を準備 ===
calc = MoleculeDescriptors.MolecularDescriptorCalculator(descriptor_names)

# === 3. 記述子計算関数（例外をnp.nanで吸収） ===
def compute_descriptors(smiles):
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return [np.nan] * len(descriptor_names)
        return calc.CalcDescriptors(mol)
    except Exception as e:
        print(f"⚠️ SMILES: {smiles} -> 記述子計算エラー: {e}")
        return [np.nan] * len(descriptor_names)

# === 4. グラフ特徴量計算関数（安全処理あり） ===
def compute_graph_features(smiles):
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return [np.nan, np.nan, np.nan]
        adj = rdmolops.GetAdjacencyMatrix(mol)
        G = nx.from_numpy_array(adj)
        diameter = nx.diameter(G) if nx.is_connected(G) else 0
        avg_shortest = nx.average_shortest_path_length(G) if nx.is_connected(G) else 0
        num_cycles = len(list(nx.cycle_basis(G)))
        return [diameter, avg_shortest, num_cycles]
    except Exception as e:
        print(f"⚠️ SMILES: {smiles} -> グラフ特徴量計算エラー: {e}")
        return [np.nan, np.nan, np.nan]

# === 5. SMILES列から 208記述子 + グラフ特徴量 を計算 ===
descriptors_list = []
graph_feats_list = []

for smi in tqdm(train['SMILES'], desc="記述子＋グラフ特徴量計算"):
    descriptors_list.append(compute_descriptors(smi))
    graph_feats_list.append(compute_graph_features(smi))

# === 6. DataFrame化 ===
descriptor_df = pd.DataFrame(descriptors_list, columns=descriptor_names)
graph_feats_df = pd.DataFrame(graph_feats_list, columns=['graph_diameter', 'avg_shortest_path', 'num_cycles'])

# === 7. 結合（元のtrain_dfと） ===
train_df_with_desc = pd.concat([train.reset_index(drop=True), descriptor_df, graph_feats_df], axis=1)

# === 8. 欠損確認 ===
print("✅ 欠損確認 (train_df_with_desc)")
print(train_df_with_desc.isnull().sum().sort_values(ascending=False).head())
```

    記述子＋グラフ特徴量計算: 100%|██████████| 10080/10080 [04:15<00:00, 39.49it/s]


    ✅ 欠損確認 (train_df_with_desc)
    Rg              9466
    BCUT2D_MRHI     9273
    BCUT2D_CHGLO    9273
    BCUT2D_MWLOW    9273
    BCUT2D_MWHI     9273
    dtype: int64



```python
# NaNが80%以上の列を抽出
nan_thresh = 0.8  # 80%
nan_ratio = train_df_with_desc.isna().mean()  # 各列のNaN率
high_nan_cols = nan_ratio[nan_ratio >= nan_thresh].index.tolist()

print("NaNが80%以上の列:")
print(high_nan_cols)

# 定数列を抽出
const_cols = [col for col in train_df_with_desc.columns if train_df_with_desc[col].nunique(dropna=False) == 1]
print("定数列:")
print(const_cols)
```

    NaNが80%以上の列:
    ['Tg', 'Tc', 'Density', 'Rg', 'MaxPartialCharge', 'MinPartialCharge', 'MaxAbsPartialCharge', 'MinAbsPartialCharge', 'BCUT2D_MWHI', 'BCUT2D_MWLOW', 'BCUT2D_CHGHI', 'BCUT2D_CHGLO', 'BCUT2D_LOGPHI', 'BCUT2D_LOGPLOW', 'BCUT2D_MRHI', 'BCUT2D_MRLOW']
    定数列:
    ['SMR_VSA8', 'SlogP_VSA9', 'fr_isothiocyan', 'fr_prisulfonamd']



```python
# 数値列だけ抽出
numeric_df = train_df_with_desc.select_dtypes(include=[np.number])

# 相関行列を計算（絶対値）
corr_matrix = numeric_df.corr().abs()

# 上三角を使って高相関列を見つける
upper = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))

# 閾値以上の相関のある列（片方だけ）
correlation_threshold = 0.95
high_corr_cols = [col for col in upper.columns if any(upper[col] > correlation_threshold)]

print("相関が0.95を超えるため削除候補の列:")
print(high_corr_cols)
```

    相関が0.95を超えるため削除候補の列:
    ['MaxEStateIndex', 'HeavyAtomMolWt', 'ExactMolWt', 'NumValenceElectrons', 'MaxAbsPartialCharge', 'MinAbsPartialCharge', 'FpDensityMorgan2', 'FpDensityMorgan3', 'Chi0', 'Chi0n', 'Chi0v', 'Chi1', 'Chi1n', 'Chi1v', 'Chi2n', 'Chi2v', 'Chi3n', 'Chi3v', 'Chi4n', 'Chi4v', 'HallKierAlpha', 'Kappa1', 'LabuteASA', 'SlogP_VSA6', 'VSA_EState6', 'HeavyAtomCount', 'NOCount', 'NumAromaticCarbocycles', 'NumAromaticRings', 'NumHDonors', 'Phi', 'MolMR', 'fr_Al_OH_noTert', 'fr_COO2', 'fr_C_O', 'fr_C_O_noCOO', 'fr_Nhpyrrole', 'fr_amide', 'fr_benzene', 'fr_diazo', 'fr_halogen', 'fr_nitrile', 'fr_nitro_arom', 'fr_phenol', 'fr_phenol_noOrthoHbond', 'fr_phos_ester', 'avg_shortest_path', 'num_cycles']



```python

```


```python

```


```python

```


```python

```


```python
def compute_all_descriptors(smiles):
    mol = Chem.MolFromSmiles(smiles)
    if mol is None:
        return [None] * len(desc_names)
    return [desc[1](mol) for desc in Descriptors.descList if desc[0] not in useless_cols]

def compute_graph_features(smiles, graph_feats):
    mol = Chem.MolFromSmiles(smiles)
    adj = rdmolops.GetAdjacencyMatrix(mol)
    G = nx.from_numpy_array(adj)

    graph_feats['graph_diameter'].append(nx.diameter(G) if nx.is_connected(G) else 0)
    graph_feats['avg_shortest_path'].append(nx.average_shortest_path_length(G) if nx.is_connected(G) else 0)
    graph_feats['num_cycles'].append(len(list(nx.cycle_basis(G))))

train = pd.concat([train, preprocessing(train)], axis=1)
test = pd.concat([test, preprocessing(test)], axis=1)

# Find constant columns for each target
all_features = train.columns[7:].tolist()
features = {}
for target in CFG.TARGETS:
    const_descs = []
    for col in train.columns.drop(CFG.TARGETS):
        if train[train[target].notnull()][col].nunique() == 1:
            const_descs.append(col)
    features[target] = [f for f in all_features if f not in const_descs]

print(train.shape)
train['Ipc']=np.log10(train['Ipc'])  
for n in train.columns[7:]:
    train[n]=train[n].replace(-np.inf,np.nan)
    train[n]=train[n].replace(np.inf,np.nan)    
    train[n].fillna(train[n].mean())
  
print(train.shape)
test['Ipc']=np.log10(test['Ipc'])
for n in test.columns[7:]:
    train[n]=train[n].replace(-np.inf,np.nan)
    train[n]=train[n].replace(np.inf,np.nan)      
    test[n].fillna(train[n].mean())
```

    (10080, 767)
    (10080, 767)


# Data Preparation For Model Training 


```python
# We'll separate train to be one model for each target variable.
t_1=train[['SMILES','Tg']].copy()
t_2=train[['SMILES','FFV']].copy()
t_3=train[['SMILES','Tc']].copy()
t_4=train[['SMILES','Density']].copy()
t_5=train[['SMILES','Rg']].copy()

# We will drop the rows with missing values related to that target after separation.
#This is important , dropping them beforehand would result Null for all data.
t_1.dropna(inplace=True)
t_2.dropna(inplace=True)
t_3.dropna(inplace=True)
t_4.dropna(inplace=True)
t_5.dropna(inplace=True)
```


```python
train=train.drop(['id','Tg','FFV','Tc','Density','Rg'],axis=1)
test=test.drop(['id','SMILES'],axis=1)
```


```python
tg=t_1.merge(train,on='SMILES',how='left')
ffv=t_2.merge(train,on='SMILES',how='left')
tc=t_3.merge(train,on='SMILES',how='left')
density=t_4.merge(train,on='SMILES',how='left')
rg=t_5.merge(train,on='SMILES',how='left')
```


```python
for i in (tg,tc,density,ffv,rg):
    i.drop('SMILES',axis=1,inplace=True)
    i.dropna(inplace=True)
```

# Model 


```python
train
```




<div>
<style scoped>
    .dataframe tbody tr th:only-of-type {
        vertical-align: middle;
    }

    .dataframe tbody tr th {
        vertical-align: top;
    }

    .dataframe thead th {
        text-align: right;
    }
</style>
<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>SMILES</th>
      <th>emb_0</th>
      <th>emb_1</th>
      <th>emb_2</th>
      <th>emb_3</th>
      <th>emb_4</th>
      <th>emb_5</th>
      <th>emb_6</th>
      <th>emb_7</th>
      <th>emb_8</th>
      <th>emb_9</th>
      <th>emb_10</th>
      <th>emb_11</th>
      <th>emb_12</th>
      <th>emb_13</th>
      <th>emb_14</th>
      <th>emb_15</th>
      <th>emb_16</th>
      <th>emb_17</th>
      <th>emb_18</th>
      <th>emb_19</th>
      <th>emb_20</th>
      <th>emb_21</th>
      <th>emb_22</th>
      <th>emb_23</th>
      <th>emb_24</th>
      <th>emb_25</th>
      <th>emb_26</th>
      <th>emb_27</th>
      <th>emb_28</th>
      <th>emb_29</th>
      <th>emb_30</th>
      <th>emb_31</th>
      <th>emb_32</th>
      <th>emb_33</th>
      <th>emb_34</th>
      <th>emb_35</th>
      <th>emb_36</th>
      <th>emb_37</th>
      <th>emb_38</th>
      <th>emb_39</th>
      <th>emb_40</th>
      <th>emb_41</th>
      <th>emb_42</th>
      <th>emb_43</th>
      <th>emb_44</th>
      <th>emb_45</th>
      <th>emb_46</th>
      <th>emb_47</th>
      <th>emb_48</th>
      <th>emb_49</th>
      <th>emb_50</th>
      <th>emb_51</th>
      <th>emb_52</th>
      <th>emb_53</th>
      <th>emb_54</th>
      <th>emb_55</th>
      <th>emb_56</th>
      <th>emb_57</th>
      <th>emb_58</th>
      <th>emb_59</th>
      <th>emb_60</th>
      <th>emb_61</th>
      <th>emb_62</th>
      <th>emb_63</th>
      <th>emb_64</th>
      <th>emb_65</th>
      <th>emb_66</th>
      <th>emb_67</th>
      <th>emb_68</th>
      <th>emb_69</th>
      <th>emb_70</th>
      <th>emb_71</th>
      <th>emb_72</th>
      <th>emb_73</th>
      <th>emb_74</th>
      <th>emb_75</th>
      <th>emb_76</th>
      <th>emb_77</th>
      <th>emb_78</th>
      <th>emb_79</th>
      <th>emb_80</th>
      <th>emb_81</th>
      <th>emb_82</th>
      <th>emb_83</th>
      <th>emb_84</th>
      <th>emb_85</th>
      <th>emb_86</th>
      <th>emb_87</th>
      <th>emb_88</th>
      <th>emb_89</th>
      <th>emb_90</th>
      <th>emb_91</th>
      <th>emb_92</th>
      <th>emb_93</th>
      <th>emb_94</th>
      <th>emb_95</th>
      <th>emb_96</th>
      <th>emb_97</th>
      <th>emb_98</th>
      <th>emb_99</th>
      <th>emb_100</th>
      <th>emb_101</th>
      <th>emb_102</th>
      <th>emb_103</th>
      <th>emb_104</th>
      <th>emb_105</th>
      <th>emb_106</th>
      <th>emb_107</th>
      <th>emb_108</th>
      <th>emb_109</th>
      <th>emb_110</th>
      <th>emb_111</th>
      <th>emb_112</th>
      <th>emb_113</th>
      <th>emb_114</th>
      <th>emb_115</th>
      <th>emb_116</th>
      <th>emb_117</th>
      <th>emb_118</th>
      <th>emb_119</th>
      <th>emb_120</th>
      <th>emb_121</th>
      <th>emb_122</th>
      <th>emb_123</th>
      <th>emb_124</th>
      <th>emb_125</th>
      <th>emb_126</th>
      <th>emb_127</th>
      <th>emb_128</th>
      <th>emb_129</th>
      <th>emb_130</th>
      <th>emb_131</th>
      <th>emb_132</th>
      <th>emb_133</th>
      <th>emb_134</th>
      <th>emb_135</th>
      <th>emb_136</th>
      <th>emb_137</th>
      <th>emb_138</th>
      <th>emb_139</th>
      <th>emb_140</th>
      <th>emb_141</th>
      <th>emb_142</th>
      <th>emb_143</th>
      <th>emb_144</th>
      <th>emb_145</th>
      <th>emb_146</th>
      <th>emb_147</th>
      <th>emb_148</th>
      <th>emb_149</th>
      <th>emb_150</th>
      <th>emb_151</th>
      <th>emb_152</th>
      <th>emb_153</th>
      <th>emb_154</th>
      <th>emb_155</th>
      <th>emb_156</th>
      <th>emb_157</th>
      <th>emb_158</th>
      <th>emb_159</th>
      <th>emb_160</th>
      <th>emb_161</th>
      <th>emb_162</th>
      <th>emb_163</th>
      <th>emb_164</th>
      <th>emb_165</th>
      <th>emb_166</th>
      <th>emb_167</th>
      <th>emb_168</th>
      <th>emb_169</th>
      <th>emb_170</th>
      <th>emb_171</th>
      <th>emb_172</th>
      <th>emb_173</th>
      <th>emb_174</th>
      <th>emb_175</th>
      <th>emb_176</th>
      <th>emb_177</th>
      <th>emb_178</th>
      <th>emb_179</th>
      <th>emb_180</th>
      <th>emb_181</th>
      <th>emb_182</th>
      <th>emb_183</th>
      <th>emb_184</th>
      <th>emb_185</th>
      <th>emb_186</th>
      <th>emb_187</th>
      <th>emb_188</th>
      <th>emb_189</th>
      <th>emb_190</th>
      <th>emb_191</th>
      <th>emb_192</th>
      <th>emb_193</th>
      <th>emb_194</th>
      <th>emb_195</th>
      <th>emb_196</th>
      <th>emb_197</th>
      <th>emb_198</th>
      <th>emb_199</th>
      <th>emb_200</th>
      <th>emb_201</th>
      <th>emb_202</th>
      <th>emb_203</th>
      <th>emb_204</th>
      <th>emb_205</th>
      <th>emb_206</th>
      <th>emb_207</th>
      <th>emb_208</th>
      <th>emb_209</th>
      <th>emb_210</th>
      <th>emb_211</th>
      <th>emb_212</th>
      <th>emb_213</th>
      <th>emb_214</th>
      <th>emb_215</th>
      <th>emb_216</th>
      <th>emb_217</th>
      <th>emb_218</th>
      <th>emb_219</th>
      <th>emb_220</th>
      <th>emb_221</th>
      <th>emb_222</th>
      <th>emb_223</th>
      <th>emb_224</th>
      <th>emb_225</th>
      <th>emb_226</th>
      <th>emb_227</th>
      <th>emb_228</th>
      <th>emb_229</th>
      <th>emb_230</th>
      <th>emb_231</th>
      <th>emb_232</th>
      <th>emb_233</th>
      <th>emb_234</th>
      <th>emb_235</th>
      <th>emb_236</th>
      <th>emb_237</th>
      <th>emb_238</th>
      <th>emb_239</th>
      <th>emb_240</th>
      <th>emb_241</th>
      <th>emb_242</th>
      <th>emb_243</th>
      <th>emb_244</th>
      <th>emb_245</th>
      <th>emb_246</th>
      <th>emb_247</th>
      <th>emb_248</th>
      <th>emb_249</th>
      <th>emb_250</th>
      <th>emb_251</th>
      <th>emb_252</th>
      <th>emb_253</th>
      <th>emb_254</th>
      <th>emb_255</th>
      <th>emb_256</th>
      <th>emb_257</th>
      <th>emb_258</th>
      <th>emb_259</th>
      <th>emb_260</th>
      <th>emb_261</th>
      <th>emb_262</th>
      <th>emb_263</th>
      <th>emb_264</th>
      <th>emb_265</th>
      <th>emb_266</th>
      <th>emb_267</th>
      <th>emb_268</th>
      <th>emb_269</th>
      <th>emb_270</th>
      <th>emb_271</th>
      <th>emb_272</th>
      <th>emb_273</th>
      <th>emb_274</th>
      <th>emb_275</th>
      <th>emb_276</th>
      <th>emb_277</th>
      <th>emb_278</th>
      <th>emb_279</th>
      <th>emb_280</th>
      <th>emb_281</th>
      <th>emb_282</th>
      <th>emb_283</th>
      <th>emb_284</th>
      <th>emb_285</th>
      <th>emb_286</th>
      <th>emb_287</th>
      <th>emb_288</th>
      <th>emb_289</th>
      <th>emb_290</th>
      <th>emb_291</th>
      <th>emb_292</th>
      <th>emb_293</th>
      <th>emb_294</th>
      <th>emb_295</th>
      <th>emb_296</th>
      <th>emb_297</th>
      <th>emb_298</th>
      <th>emb_299</th>
      <th>emb_300</th>
      <th>emb_301</th>
      <th>emb_302</th>
      <th>emb_303</th>
      <th>emb_304</th>
      <th>emb_305</th>
      <th>emb_306</th>
      <th>emb_307</th>
      <th>emb_308</th>
      <th>emb_309</th>
      <th>emb_310</th>
      <th>emb_311</th>
      <th>emb_312</th>
      <th>emb_313</th>
      <th>emb_314</th>
      <th>emb_315</th>
      <th>emb_316</th>
      <th>emb_317</th>
      <th>emb_318</th>
      <th>emb_319</th>
      <th>emb_320</th>
      <th>emb_321</th>
      <th>emb_322</th>
      <th>emb_323</th>
      <th>emb_324</th>
      <th>emb_325</th>
      <th>emb_326</th>
      <th>emb_327</th>
      <th>emb_328</th>
      <th>emb_329</th>
      <th>emb_330</th>
      <th>emb_331</th>
      <th>emb_332</th>
      <th>emb_333</th>
      <th>emb_334</th>
      <th>emb_335</th>
      <th>emb_336</th>
      <th>emb_337</th>
      <th>emb_338</th>
      <th>emb_339</th>
      <th>emb_340</th>
      <th>emb_341</th>
      <th>emb_342</th>
      <th>emb_343</th>
      <th>emb_344</th>
      <th>emb_345</th>
      <th>emb_346</th>
      <th>emb_347</th>
      <th>emb_348</th>
      <th>emb_349</th>
      <th>emb_350</th>
      <th>emb_351</th>
      <th>emb_352</th>
      <th>emb_353</th>
      <th>emb_354</th>
      <th>emb_355</th>
      <th>emb_356</th>
      <th>emb_357</th>
      <th>emb_358</th>
      <th>emb_359</th>
      <th>emb_360</th>
      <th>emb_361</th>
      <th>emb_362</th>
      <th>emb_363</th>
      <th>emb_364</th>
      <th>emb_365</th>
      <th>emb_366</th>
      <th>emb_367</th>
      <th>emb_368</th>
      <th>emb_369</th>
      <th>emb_370</th>
      <th>emb_371</th>
      <th>emb_372</th>
      <th>emb_373</th>
      <th>emb_374</th>
      <th>emb_375</th>
      <th>emb_376</th>
      <th>emb_377</th>
      <th>emb_378</th>
      <th>emb_379</th>
      <th>emb_380</th>
      <th>emb_381</th>
      <th>emb_382</th>
      <th>emb_383</th>
      <th>emb_384</th>
      <th>emb_385</th>
      <th>emb_386</th>
      <th>emb_387</th>
      <th>emb_388</th>
      <th>emb_389</th>
      <th>emb_390</th>
      <th>emb_391</th>
      <th>emb_392</th>
      <th>emb_393</th>
      <th>emb_394</th>
      <th>emb_395</th>
      <th>emb_396</th>
      <th>emb_397</th>
      <th>emb_398</th>
      <th>emb_399</th>
      <th>emb_400</th>
      <th>emb_401</th>
      <th>emb_402</th>
      <th>emb_403</th>
      <th>emb_404</th>
      <th>emb_405</th>
      <th>emb_406</th>
      <th>emb_407</th>
      <th>emb_408</th>
      <th>emb_409</th>
      <th>emb_410</th>
      <th>emb_411</th>
      <th>emb_412</th>
      <th>emb_413</th>
      <th>emb_414</th>
      <th>emb_415</th>
      <th>emb_416</th>
      <th>emb_417</th>
      <th>emb_418</th>
      <th>emb_419</th>
      <th>emb_420</th>
      <th>emb_421</th>
      <th>emb_422</th>
      <th>emb_423</th>
      <th>emb_424</th>
      <th>emb_425</th>
      <th>emb_426</th>
      <th>emb_427</th>
      <th>emb_428</th>
      <th>emb_429</th>
      <th>emb_430</th>
      <th>emb_431</th>
      <th>emb_432</th>
      <th>emb_433</th>
      <th>emb_434</th>
      <th>emb_435</th>
      <th>emb_436</th>
      <th>emb_437</th>
      <th>emb_438</th>
      <th>emb_439</th>
      <th>emb_440</th>
      <th>emb_441</th>
      <th>emb_442</th>
      <th>emb_443</th>
      <th>emb_444</th>
      <th>emb_445</th>
      <th>emb_446</th>
      <th>emb_447</th>
      <th>emb_448</th>
      <th>emb_449</th>
      <th>emb_450</th>
      <th>emb_451</th>
      <th>emb_452</th>
      <th>emb_453</th>
      <th>emb_454</th>
      <th>emb_455</th>
      <th>emb_456</th>
      <th>emb_457</th>
      <th>emb_458</th>
      <th>emb_459</th>
      <th>emb_460</th>
      <th>emb_461</th>
      <th>emb_462</th>
      <th>emb_463</th>
      <th>emb_464</th>
      <th>emb_465</th>
      <th>emb_466</th>
      <th>emb_467</th>
      <th>emb_468</th>
      <th>emb_469</th>
      <th>emb_470</th>
      <th>emb_471</th>
      <th>emb_472</th>
      <th>emb_473</th>
      <th>emb_474</th>
      <th>emb_475</th>
      <th>emb_476</th>
      <th>emb_477</th>
      <th>emb_478</th>
      <th>emb_479</th>
      <th>emb_480</th>
      <th>emb_481</th>
      <th>emb_482</th>
      <th>emb_483</th>
      <th>emb_484</th>
      <th>emb_485</th>
      <th>emb_486</th>
      <th>emb_487</th>
      <th>emb_488</th>
      <th>emb_489</th>
      <th>emb_490</th>
      <th>emb_491</th>
      <th>emb_492</th>
      <th>emb_493</th>
      <th>emb_494</th>
      <th>emb_495</th>
      <th>emb_496</th>
      <th>emb_497</th>
      <th>emb_498</th>
      <th>emb_499</th>
      <th>emb_500</th>
      <th>emb_501</th>
      <th>emb_502</th>
      <th>emb_503</th>
      <th>emb_504</th>
      <th>emb_505</th>
      <th>emb_506</th>
      <th>emb_507</th>
      <th>emb_508</th>
      <th>emb_509</th>
      <th>emb_510</th>
      <th>emb_511</th>
      <th>emb_512</th>
      <th>emb_513</th>
      <th>emb_514</th>
      <th>emb_515</th>
      <th>emb_516</th>
      <th>emb_517</th>
      <th>emb_518</th>
      <th>emb_519</th>
      <th>emb_520</th>
      <th>emb_521</th>
      <th>emb_522</th>
      <th>emb_523</th>
      <th>emb_524</th>
      <th>emb_525</th>
      <th>emb_526</th>
      <th>emb_527</th>
      <th>emb_528</th>
      <th>emb_529</th>
      <th>emb_530</th>
      <th>emb_531</th>
      <th>emb_532</th>
      <th>emb_533</th>
      <th>emb_534</th>
      <th>emb_535</th>
      <th>emb_536</th>
      <th>emb_537</th>
      <th>emb_538</th>
      <th>emb_539</th>
      <th>emb_540</th>
      <th>emb_541</th>
      <th>emb_542</th>
      <th>emb_543</th>
      <th>emb_544</th>
      <th>emb_545</th>
      <th>emb_546</th>
      <th>emb_547</th>
      <th>emb_548</th>
      <th>emb_549</th>
      <th>emb_550</th>
      <th>emb_551</th>
      <th>emb_552</th>
      <th>emb_553</th>
      <th>emb_554</th>
      <th>emb_555</th>
      <th>emb_556</th>
      <th>emb_557</th>
      <th>emb_558</th>
      <th>emb_559</th>
      <th>emb_560</th>
      <th>emb_561</th>
      <th>emb_562</th>
      <th>emb_563</th>
      <th>emb_564</th>
      <th>emb_565</th>
      <th>emb_566</th>
      <th>emb_567</th>
      <th>emb_568</th>
      <th>emb_569</th>
      <th>emb_570</th>
      <th>emb_571</th>
      <th>emb_572</th>
      <th>emb_573</th>
      <th>emb_574</th>
      <th>emb_575</th>
      <th>emb_576</th>
      <th>emb_577</th>
      <th>emb_578</th>
      <th>emb_579</th>
      <th>emb_580</th>
      <th>emb_581</th>
      <th>emb_582</th>
      <th>emb_583</th>
      <th>emb_584</th>
      <th>emb_585</th>
      <th>emb_586</th>
      <th>emb_587</th>
      <th>emb_588</th>
      <th>emb_589</th>
      <th>emb_590</th>
      <th>emb_591</th>
      <th>emb_592</th>
      <th>emb_593</th>
      <th>emb_594</th>
      <th>emb_595</th>
      <th>emb_596</th>
      <th>emb_597</th>
      <th>emb_598</th>
      <th>emb_599</th>
      <th>MaxAbsEStateIndex</th>
      <th>MinAbsEStateIndex</th>
      <th>MinEStateIndex</th>
      <th>qed</th>
      <th>SPS</th>
      <th>MolWt</th>
      <th>NumRadicalElectrons</th>
      <th>FpDensityMorgan1</th>
      <th>AvgIpc</th>
      <th>BalabanJ</th>
      <th>BertzCT</th>
      <th>Ipc</th>
      <th>Kappa2</th>
      <th>Kappa3</th>
      <th>PEOE_VSA1</th>
      <th>PEOE_VSA10</th>
      <th>PEOE_VSA11</th>
      <th>PEOE_VSA12</th>
      <th>PEOE_VSA13</th>
      <th>PEOE_VSA14</th>
      <th>PEOE_VSA2</th>
      <th>PEOE_VSA3</th>
      <th>PEOE_VSA4</th>
      <th>PEOE_VSA5</th>
      <th>PEOE_VSA6</th>
      <th>PEOE_VSA7</th>
      <th>PEOE_VSA8</th>
      <th>PEOE_VSA9</th>
      <th>SMR_VSA1</th>
      <th>SMR_VSA10</th>
      <th>SMR_VSA2</th>
      <th>SMR_VSA3</th>
      <th>SMR_VSA4</th>
      <th>SMR_VSA5</th>
      <th>SMR_VSA6</th>
      <th>SMR_VSA7</th>
      <th>SMR_VSA9</th>
      <th>SlogP_VSA1</th>
      <th>SlogP_VSA10</th>
      <th>SlogP_VSA11</th>
      <th>SlogP_VSA12</th>
      <th>SlogP_VSA2</th>
      <th>SlogP_VSA3</th>
      <th>SlogP_VSA4</th>
      <th>SlogP_VSA5</th>
      <th>SlogP_VSA7</th>
      <th>SlogP_VSA8</th>
      <th>TPSA</th>
      <th>EState_VSA1</th>
      <th>EState_VSA10</th>
      <th>EState_VSA11</th>
      <th>EState_VSA2</th>
      <th>EState_VSA3</th>
      <th>EState_VSA4</th>
      <th>EState_VSA5</th>
      <th>EState_VSA6</th>
      <th>EState_VSA7</th>
      <th>EState_VSA8</th>
      <th>EState_VSA9</th>
      <th>VSA_EState1</th>
      <th>VSA_EState10</th>
      <th>VSA_EState2</th>
      <th>VSA_EState3</th>
      <th>VSA_EState4</th>
      <th>VSA_EState5</th>
      <th>VSA_EState7</th>
      <th>VSA_EState8</th>
      <th>VSA_EState9</th>
      <th>FractionCSP3</th>
      <th>NHOHCount</th>
      <th>NumAliphaticCarbocycles</th>
      <th>NumAliphaticHeterocycles</th>
      <th>NumAliphaticRings</th>
      <th>NumAmideBonds</th>
      <th>NumAromaticHeterocycles</th>
      <th>NumAtomStereoCenters</th>
      <th>NumBridgeheadAtoms</th>
      <th>NumHAcceptors</th>
      <th>NumHeteroatoms</th>
      <th>NumHeterocycles</th>
      <th>NumRotatableBonds</th>
      <th>NumSaturatedCarbocycles</th>
      <th>NumSaturatedHeterocycles</th>
      <th>NumSaturatedRings</th>
      <th>NumSpiroAtoms</th>
      <th>NumUnspecifiedAtomStereoCenters</th>
      <th>RingCount</th>
      <th>MolLogP</th>
      <th>fr_Al_COO</th>
      <th>fr_Al_OH</th>
      <th>fr_ArN</th>
      <th>fr_Ar_COO</th>
      <th>fr_Ar_N</th>
      <th>fr_Ar_NH</th>
      <th>fr_Ar_OH</th>
      <th>fr_COO</th>
      <th>fr_C_S</th>
      <th>fr_HOCCN</th>
      <th>fr_Imine</th>
      <th>fr_NH0</th>
      <th>fr_NH1</th>
      <th>fr_NH2</th>
      <th>fr_N_O</th>
      <th>fr_Ndealkylation1</th>
      <th>fr_Ndealkylation2</th>
      <th>fr_SH</th>
      <th>fr_aldehyde</th>
      <th>fr_alkyl_carbamate</th>
      <th>fr_alkyl_halide</th>
      <th>fr_allylic_oxid</th>
      <th>fr_amidine</th>
      <th>fr_aniline</th>
      <th>fr_aryl_methyl</th>
      <th>fr_azide</th>
      <th>fr_azo</th>
      <th>fr_barbitur</th>
      <th>fr_benzodiazepine</th>
      <th>fr_bicyclic</th>
      <th>fr_dihydropyridine</th>
      <th>fr_epoxide</th>
      <th>fr_ester</th>
      <th>fr_ether</th>
      <th>fr_furan</th>
      <th>fr_guanido</th>
      <th>fr_hdrzine</th>
      <th>fr_hdrzone</th>
      <th>fr_imidazole</th>
      <th>fr_imide</th>
      <th>fr_isocyan</th>
      <th>fr_ketone</th>
      <th>fr_ketone_Topliss</th>
      <th>fr_lactam</th>
      <th>fr_lactone</th>
      <th>fr_methoxy</th>
      <th>fr_morpholine</th>
      <th>fr_nitro</th>
      <th>fr_nitro_arom_nonortho</th>
      <th>fr_nitroso</th>
      <th>fr_oxazole</th>
      <th>fr_oxime</th>
      <th>fr_para_hydroxylation</th>
      <th>fr_phos_acid</th>
      <th>fr_piperdine</th>
      <th>fr_piperzine</th>
      <th>fr_priamide</th>
      <th>fr_pyridine</th>
      <th>fr_quatN</th>
      <th>fr_sulfide</th>
      <th>fr_sulfonamd</th>
      <th>fr_sulfone</th>
      <th>fr_term_acetylene</th>
      <th>fr_tetrazole</th>
      <th>fr_thiazole</th>
      <th>fr_thiocyan</th>
      <th>fr_thiophene</th>
      <th>fr_unbrch_alkane</th>
      <th>fr_urea</th>
      <th>graph_diameter</th>
      <th>avg_shortest_path</th>
      <th>num_cycles</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>0</th>
      <td>*CC(*)c1ccccc1C(=O)OCCCCCC</td>
      <td>-0.079300</td>
      <td>0.286111</td>
      <td>1.166843</td>
      <td>-0.012523</td>
      <td>-1.071713</td>
      <td>0.163018</td>
      <td>0.682536</td>
      <td>-0.197186</td>
      <td>0.183880</td>
      <td>0.189475</td>
      <td>0.141950</td>
      <td>-0.412391</td>
      <td>0.567338</td>
      <td>-1.731983</td>
      <td>-1.263579</td>
      <td>-0.892521</td>
      <td>-0.509234</td>
      <td>-0.814473</td>
      <td>-0.241643</td>
      <td>-0.040514</td>
      <td>0.333833</td>
      <td>0.308393</td>
      <td>0.342894</td>
      <td>0.073258</td>
      <td>-0.424253</td>
      <td>0.350735</td>
      <td>3.655020</td>
      <td>0.408520</td>
      <td>-0.869495</td>
      <td>-0.127450</td>
      <td>0.172020</td>
      <td>-1.099791</td>
      <td>-0.535998</td>
      <td>-1.033138</td>
      <td>1.320194</td>
      <td>0.228485</td>
      <td>-0.733769</td>
      <td>0.392224</td>
      <td>-0.180938</td>
      <td>-0.575902</td>
      <td>-0.050752</td>
      <td>0.284477</td>
      <td>0.826569</td>
      <td>0.483455</td>
      <td>0.339715</td>
      <td>0.598356</td>
      <td>0.202709</td>
      <td>0.358879</td>
      <td>0.417120</td>
      <td>-0.245442</td>
      <td>0.841483</td>
      <td>0.112200</td>
      <td>-0.420497</td>
      <td>0.131553</td>
      <td>-0.399371</td>
      <td>0.522187</td>
      <td>0.080629</td>
      <td>0.259108</td>
      <td>0.068084</td>
      <td>-0.098800</td>
      <td>-0.193909</td>
      <td>0.254731</td>
      <td>0.217700</td>
      <td>0.856684</td>
      <td>-0.558963</td>
      <td>-0.080455</td>
      <td>0.107039</td>
      <td>-0.098637</td>
      <td>-0.297451</td>
      <td>-0.897401</td>
      <td>-0.371902</td>
      <td>0.381586</td>
      <td>-0.208931</td>
      <td>-0.094115</td>
      <td>-0.567079</td>
      <td>-0.346349</td>
      <td>-0.174739</td>
      <td>-1.228698</td>
      <td>0.355632</td>
      <td>-0.641956</td>
      <td>0.811140</td>
      <td>-0.837952</td>
      <td>0.138294</td>
      <td>0.759691</td>
      <td>-0.192586</td>
      <td>-0.070625</td>
      <td>0.519774</td>
      <td>0.654744</td>
      <td>-0.420848</td>
      <td>0.248993</td>
      <td>-1.954547</td>
      <td>-0.106925</td>
      <td>1.012255</td>
      <td>0.280273</td>
      <td>-0.818657</td>
      <td>0.152214</td>
      <td>-0.384890</td>
      <td>1.640919</td>
      <td>-0.076390</td>
      <td>-0.370478</td>
      <td>-0.296101</td>
      <td>-0.267078</td>
      <td>-0.565219</td>
      <td>0.389257</td>
      <td>0.648088</td>
      <td>-0.612271</td>
      <td>0.786650</td>
      <td>-1.245299</td>
      <td>-0.597319</td>
      <td>1.586822</td>
      <td>0.230232</td>
      <td>-0.827148</td>
      <td>1.051069</td>
      <td>-0.612800</td>
      <td>0.329340</td>
      <td>0.793714</td>
      <td>-0.048814</td>
      <td>0.659348</td>
      <td>0.732718</td>
      <td>0.263236</td>
      <td>-0.427487</td>
      <td>0.336727</td>
      <td>-0.872196</td>
      <td>-0.162730</td>
      <td>-0.726387</td>
      <td>-0.037904</td>
      <td>-0.124431</td>
      <td>0.202469</td>
      <td>0.237172</td>
      <td>0.300319</td>
      <td>-0.723541</td>
      <td>0.228527</td>
      <td>0.082672</td>
      <td>-0.372104</td>
      <td>-0.524441</td>
      <td>-0.372201</td>
      <td>0.312914</td>
      <td>0.032664</td>
      <td>-0.266385</td>
      <td>0.078486</td>
      <td>-0.243260</td>
      <td>-0.308133</td>
      <td>0.563263</td>
      <td>-1.423227</td>
      <td>-0.165738</td>
      <td>-0.073250</td>
      <td>-0.291559</td>
      <td>-1.135750</td>
      <td>-0.840831</td>
      <td>-0.455650</td>
      <td>0.272489</td>
      <td>-0.673378</td>
      <td>1.320159</td>
      <td>-0.187634</td>
      <td>-0.147390</td>
      <td>-0.040174</td>
      <td>-0.224130</td>
      <td>-1.560892</td>
      <td>-0.192358</td>
      <td>0.260545</td>
      <td>0.165975</td>
      <td>-0.027258</td>
      <td>0.658239</td>
      <td>0.384326</td>
      <td>0.161208</td>
      <td>-0.118147</td>
      <td>-0.482675</td>
      <td>-0.778511</td>
      <td>0.544790</td>
      <td>1.444787</td>
      <td>1.632841</td>
      <td>-0.744183</td>
      <td>0.366169</td>
      <td>1.164919</td>
      <td>0.412256</td>
      <td>0.301338</td>
      <td>-0.206865</td>
      <td>-0.368743</td>
      <td>-0.370965</td>
      <td>0.329253</td>
      <td>-0.154772</td>
      <td>0.710071</td>
      <td>-0.221909</td>
      <td>-0.577512</td>
      <td>-0.038543</td>
      <td>0.308179</td>
      <td>-0.622168</td>
      <td>-0.754897</td>
      <td>-0.771249</td>
      <td>1.036167</td>
      <td>0.393861</td>
      <td>0.447057</td>
      <td>0.176914</td>
      <td>-1.396654</td>
      <td>1.049264</td>
      <td>-0.285159</td>
      <td>0.674831</td>
      <td>0.375367</td>
      <td>-0.241947</td>
      <td>0.848275</td>
      <td>-0.347903</td>
      <td>-0.217679</td>
      <td>0.246021</td>
      <td>-1.254591</td>
      <td>-0.400482</td>
      <td>-0.298289</td>
      <td>-1.288555</td>
      <td>-1.230489</td>
      <td>-0.508792</td>
      <td>0.352795</td>
      <td>-0.448151</td>
      <td>-0.153587</td>
      <td>0.871828</td>
      <td>-1.066001</td>
      <td>-0.739233</td>
      <td>0.374843</td>
      <td>-0.548835</td>
      <td>0.214061</td>
      <td>0.121837</td>
      <td>1.321551</td>
      <td>-0.026239</td>
      <td>-0.398862</td>
      <td>-0.005173</td>
      <td>-0.220623</td>
      <td>0.299416</td>
      <td>0.142081</td>
      <td>0.705827</td>
      <td>0.320119</td>
      <td>0.257541</td>
      <td>-0.004974</td>
      <td>-0.960203</td>
      <td>0.759105</td>
      <td>-0.059349</td>
      <td>0.503374</td>
      <td>0.963222</td>
      <td>-0.000815</td>
      <td>-0.742063</td>
      <td>-0.147180</td>
      <td>0.339380</td>
      <td>-0.679027</td>
      <td>-0.273664</td>
      <td>0.863326</td>
      <td>-0.429931</td>
      <td>-0.178750</td>
      <td>-0.192118</td>
      <td>-0.307007</td>
      <td>-0.658606</td>
      <td>-0.910228</td>
      <td>0.432797</td>
      <td>0.236907</td>
      <td>-0.134555</td>
      <td>-0.277663</td>
      <td>-0.323987</td>
      <td>0.044426</td>
      <td>-0.224487</td>
      <td>0.398777</td>
      <td>-0.153577</td>
      <td>-0.372679</td>
      <td>0.644052</td>
      <td>-0.552716</td>
      <td>0.544236</td>
      <td>-0.131865</td>
      <td>-0.090611</td>
      <td>-0.260690</td>
      <td>-0.332477</td>
      <td>0.335452</td>
      <td>0.286801</td>
      <td>-0.175828</td>
      <td>-0.065739</td>
      <td>-1.252849</td>
      <td>-0.949213</td>
      <td>-0.213055</td>
      <td>0.033037</td>
      <td>0.623246</td>
      <td>-0.598013</td>
      <td>-0.362284</td>
      <td>0.102373</td>
      <td>0.452427</td>
      <td>-0.762158</td>
      <td>-1.075348</td>
      <td>1.007036</td>
      <td>0.244067</td>
      <td>0.527337</td>
      <td>0.179456</td>
      <td>0.766281</td>
      <td>-0.742941</td>
      <td>0.169664</td>
      <td>0.623965</td>
      <td>-0.073520</td>
      <td>0.364561</td>
      <td>0.251708</td>
      <td>0.048839</td>
      <td>0.796285</td>
      <td>0.738283</td>
      <td>0.027552</td>
      <td>-0.021705</td>
      <td>1.905310</td>
      <td>-0.956847</td>
      <td>0.031523</td>
      <td>0.113455</td>
      <td>1.075359</td>
      <td>0.094211</td>
      <td>0.777747</td>
      <td>-0.183724</td>
      <td>0.362765</td>
      <td>0.491476</td>
      <td>-0.030013</td>
      <td>-0.195108</td>
      <td>-0.554211</td>
      <td>-0.245901</td>
      <td>-0.110235</td>
      <td>-0.485334</td>
      <td>0.014703</td>
      <td>0.846519</td>
      <td>0.445626</td>
      <td>-0.025101</td>
      <td>-0.407886</td>
      <td>-0.801838</td>
      <td>0.015955</td>
      <td>-0.071176</td>
      <td>0.528291</td>
      <td>0.433065</td>
      <td>-0.365562</td>
      <td>-1.167320</td>
      <td>0.330350</td>
      <td>0.558286</td>
      <td>-0.004110</td>
      <td>-0.287298</td>
      <td>0.293857</td>
      <td>0.185867</td>
      <td>-0.166374</td>
      <td>-0.928384</td>
      <td>0.472410</td>
      <td>-0.201259</td>
      <td>0.019662</td>
      <td>0.719694</td>
      <td>-0.103643</td>
      <td>0.346030</td>
      <td>-0.223758</td>
      <td>-0.635258</td>
      <td>0.090868</td>
      <td>0.554258</td>
      <td>0.115192</td>
      <td>0.973435</td>
      <td>0.215062</td>
      <td>0.083731</td>
      <td>0.741199</td>
      <td>0.982583</td>
      <td>-0.241418</td>
      <td>-0.205205</td>
      <td>-0.053674</td>
      <td>-0.289510</td>
      <td>0.316761</td>
      <td>1.506577</td>
      <td>-0.224178</td>
      <td>0.487021</td>
      <td>0.161288</td>
      <td>0.261382</td>
      <td>1.293190</td>
      <td>0.074254</td>
      <td>0.118212</td>
      <td>-0.017349</td>
      <td>-0.781418</td>
      <td>-0.347456</td>
      <td>0.541499</td>
      <td>0.272726</td>
      <td>-0.030715</td>
      <td>0.143263</td>
      <td>0.691935</td>
      <td>0.398703</td>
      <td>-0.435533</td>
      <td>-0.246953</td>
      <td>-0.500240</td>
      <td>-0.938217</td>
      <td>-0.796696</td>
      <td>0.608961</td>
      <td>-0.123757</td>
      <td>0.371607</td>
      <td>-0.407252</td>
      <td>-0.206241</td>
      <td>-0.133831</td>
      <td>-0.438827</td>
      <td>-0.168227</td>
      <td>-0.633727</td>
      <td>0.127147</td>
      <td>0.322453</td>
      <td>-0.402696</td>
      <td>-0.662358</td>
      <td>-0.474645</td>
      <td>-0.320454</td>
      <td>-0.024676</td>
      <td>0.431533</td>
      <td>-0.012651</td>
      <td>-0.634128</td>
      <td>0.459573</td>
      <td>0.668604</td>
      <td>-0.139245</td>
      <td>0.734275</td>
      <td>-0.374829</td>
      <td>1.225282</td>
      <td>-0.489425</td>
      <td>0.661970</td>
      <td>0.135759</td>
      <td>0.051196</td>
      <td>1.036442</td>
      <td>0.112374</td>
      <td>0.900007</td>
      <td>-0.097032</td>
      <td>-1.007551</td>
      <td>0.084324</td>
      <td>0.477539</td>
      <td>0.688149</td>
      <td>0.846720</td>
      <td>-1.372651</td>
      <td>0.505230</td>
      <td>0.279689</td>
      <td>-0.489519</td>
      <td>-0.284210</td>
      <td>0.574415</td>
      <td>0.286863</td>
      <td>0.015574</td>
      <td>-0.030323</td>
      <td>0.239288</td>
      <td>-0.920608</td>
      <td>0.362491</td>
      <td>-0.134576</td>
      <td>0.629482</td>
      <td>-0.677678</td>
      <td>-0.425131</td>
      <td>-0.661549</td>
      <td>-0.928930</td>
      <td>-0.430972</td>
      <td>-0.438607</td>
      <td>0.394528</td>
      <td>0.471740</td>
      <td>-0.851062</td>
      <td>0.013116</td>
      <td>-1.082687</td>
      <td>-0.266774</td>
      <td>0.653054</td>
      <td>-1.425100</td>
      <td>0.178626</td>
      <td>0.022579</td>
      <td>-0.191847</td>
      <td>0.695237</td>
      <td>-0.733109</td>
      <td>0.535693</td>
      <td>0.785310</td>
      <td>-0.475671</td>
      <td>0.437174</td>
      <td>-0.177694</td>
      <td>-0.500449</td>
      <td>0.534812</td>
      <td>0.790953</td>
      <td>-0.990227</td>
      <td>-0.234922</td>
      <td>0.401338</td>
      <td>0.513488</td>
      <td>-0.495207</td>
      <td>-0.497801</td>
      <td>0.704348</td>
      <td>0.650440</td>
      <td>-0.484786</td>
      <td>-0.138916</td>
      <td>-0.587965</td>
      <td>-0.083074</td>
      <td>-0.249114</td>
      <td>0.538840</td>
      <td>0.043500</td>
      <td>0.353143</td>
      <td>1.163709</td>
      <td>0.552208</td>
      <td>0.082076</td>
      <td>0.180756</td>
      <td>-0.563821</td>
      <td>-0.000298</td>
      <td>-0.116590</td>
      <td>-0.573182</td>
      <td>-0.357537</td>
      <td>0.369504</td>
      <td>0.120355</td>
      <td>-0.728191</td>
      <td>0.204065</td>
      <td>0.293695</td>
      <td>-0.453717</td>
      <td>0.043054</td>
      <td>-0.106093</td>
      <td>-0.936277</td>
      <td>-1.061163</td>
      <td>0.573552</td>
      <td>0.162491</td>
      <td>-0.725497</td>
      <td>-0.428313</td>
      <td>0.092127</td>
      <td>0.067477</td>
      <td>0.428459</td>
      <td>0.313368</td>
      <td>0.505872</td>
      <td>0.191395</td>
      <td>-1.740697</td>
      <td>-0.426707</td>
      <td>-0.911588</td>
      <td>0.173682</td>
      <td>1.806722</td>
      <td>0.064242</td>
      <td>-0.017984</td>
      <td>1.663403</td>
      <td>-0.773311</td>
      <td>0.194389</td>
      <td>-0.310104</td>
      <td>0.334190</td>
      <td>0.023139</td>
      <td>-0.085081</td>
      <td>0.304919</td>
      <td>-0.213980</td>
      <td>-0.085412</td>
      <td>0.569699</td>
      <td>-0.665316</td>
      <td>-0.838655</td>
      <td>-0.285807</td>
      <td>-1.037296</td>
      <td>0.569058</td>
      <td>-0.607321</td>
      <td>-0.453992</td>
      <td>-0.397174</td>
      <td>-0.423578</td>
      <td>-0.206664</td>
      <td>-0.132086</td>
      <td>-0.068815</td>
      <td>0.088956</td>
      <td>-0.533692</td>
      <td>-0.015059</td>
      <td>0.391733</td>
      <td>0.139234</td>
      <td>0.867176</td>
      <td>0.975728</td>
      <td>-0.865460</td>
      <td>0.308496</td>
      <td>1.619330</td>
      <td>-1.298789</td>
      <td>-0.230783</td>
      <td>0.813499</td>
      <td>-0.385013</td>
      <td>-0.056147</td>
      <td>-0.764204</td>
      <td>-0.085941</td>
      <td>-0.597126</td>
      <td>0.888142</td>
      <td>0.324702</td>
      <td>0.937443</td>
      <td>0.113262</td>
      <td>0.134833</td>
      <td>-0.039004</td>
      <td>0.625099</td>
      <td>-0.142924</td>
      <td>0.698002</td>
      <td>0.458408</td>
      <td>-0.913561</td>
      <td>0.962035</td>
      <td>0.104238</td>
      <td>0.680206</td>
      <td>-0.355734</td>
      <td>0.981105</td>
      <td>-0.895263</td>
      <td>0.196372</td>
      <td>-0.225950</td>
      <td>0.634890</td>
      <td>0.062162</td>
      <td>0.645504</td>
      <td>-0.600889</td>
      <td>-0.499191</td>
      <td>0.544078</td>
      <td>-0.829499</td>
      <td>-0.759932</td>
      <td>0.178300</td>
      <td>0.445338</td>
      <td>-0.123843</td>
      <td>-0.715411</td>
      <td>0.284449</td>
      <td>1.077093</td>
      <td>0.779005</td>
      <td>-0.189376</td>
      <td>0.544757</td>
      <td>0.487257</td>
      <td>-0.491961</td>
      <td>-1.142638</td>
      <td>-0.484696</td>
      <td>-0.423204</td>
      <td>1.118247</td>
      <td>-0.856846</td>
      <td>0.961260</td>
      <td>-0.770222</td>
      <td>-0.307131</td>
      <td>0.000619</td>
      <td>-0.639594</td>
      <td>-0.592017</td>
      <td>1.135363</td>
      <td>-0.179406</td>
      <td>0.185088</td>
      <td>0.409243</td>
      <td>12.144536</td>
      <td>0.105927</td>
      <td>-0.105927</td>
      <td>0.500278</td>
      <td>13.705882</td>
      <td>232.323</td>
      <td>0</td>
      <td>1.411765</td>
      <td>2.456411</td>
      <td>2.563477</td>
      <td>393.486893</td>
      <td>4.261290</td>
      <td>5.852071</td>
      <td>3.320840</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>103.451541</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>9.531400</td>
      <td>5.969305</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>45.951583</td>
      <td>6.606882</td>
      <td>35.392371</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>12.576187</td>
      <td>4.736863</td>
      <td>0.000000</td>
      <td>54.949285</td>
      <td>6.923737</td>
      <td>0.000000</td>
      <td>26.30</td>
      <td>0.000000</td>
      <td>4.794537</td>
      <td>0.0</td>
      <td>5.969305</td>
      <td>6.606882</td>
      <td>24.825916</td>
      <td>25.328832</td>
      <td>0.000000</td>
      <td>12.132734</td>
      <td>19.056471</td>
      <td>4.736863</td>
      <td>6.120445</td>
      <td>0.000000</td>
      <td>12.144536</td>
      <td>0.000000</td>
      <td>2.105648</td>
      <td>-0.105927</td>
      <td>5.344954</td>
      <td>4.148954</td>
      <td>0.000000</td>
      <td>0.533333</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>1</td>
      <td>0</td>
      <td>2</td>
      <td>4</td>
      <td>0</td>
      <td>8</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>1</td>
      <td>1</td>
      <td>3.98170</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>1</td>
      <td>1</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>3</td>
      <td>0</td>
      <td>12</td>
      <td>4.736842</td>
      <td>1</td>
    </tr>
    <tr>
      <th>1</th>
      <td>*Nc1ccc([C@H](CCC)c2ccc(C3(c4ccc([C@@H](CCC)c5...</td>
      <td>0.623136</td>
      <td>0.098905</td>
      <td>0.299460</td>
      <td>0.449475</td>
      <td>0.042230</td>
      <td>0.305405</td>
      <td>-0.010887</td>
      <td>0.400121</td>
      <td>0.956288</td>
      <td>0.708514</td>
      <td>-0.177268</td>
      <td>0.774144</td>
      <td>0.057432</td>
      <td>0.307162</td>
      <td>-0.115404</td>
      <td>-0.662430</td>
      <td>-0.450452</td>
      <td>0.650379</td>
      <td>-0.214999</td>
      <td>-0.055090</td>
      <td>0.190739</td>
      <td>0.445144</td>
      <td>-0.025343</td>
      <td>0.420465</td>
      <td>0.095457</td>
      <td>0.341924</td>
      <td>1.212051</td>
      <td>-0.502878</td>
      <td>0.402680</td>
      <td>-0.198102</td>
      <td>0.190750</td>
      <td>0.480020</td>
      <td>-0.573106</td>
      <td>0.787108</td>
      <td>-0.346836</td>
      <td>0.257113</td>
      <td>-0.233893</td>
      <td>-0.608010</td>
      <td>0.069968</td>
      <td>-0.372178</td>
      <td>0.471894</td>
      <td>0.720111</td>
      <td>0.286000</td>
      <td>-0.091067</td>
      <td>-0.170013</td>
      <td>-0.160354</td>
      <td>-0.061181</td>
      <td>-0.209122</td>
      <td>-0.134394</td>
      <td>0.433472</td>
      <td>0.880622</td>
      <td>1.127154</td>
      <td>0.256581</td>
      <td>0.249880</td>
      <td>-0.405353</td>
      <td>-0.281441</td>
      <td>-0.302779</td>
      <td>0.466147</td>
      <td>-0.760828</td>
      <td>-0.112952</td>
      <td>-0.295876</td>
      <td>0.008971</td>
      <td>-0.446050</td>
      <td>0.136942</td>
      <td>0.637289</td>
      <td>0.590488</td>
      <td>0.283372</td>
      <td>-0.217530</td>
      <td>-0.010537</td>
      <td>0.382581</td>
      <td>-0.209738</td>
      <td>-0.023269</td>
      <td>-0.418552</td>
      <td>-0.587927</td>
      <td>-0.411194</td>
      <td>-0.147432</td>
      <td>0.322919</td>
      <td>-0.643674</td>
      <td>-0.089363</td>
      <td>0.260361</td>
      <td>-0.356045</td>
      <td>-0.120951</td>
      <td>0.481422</td>
      <td>0.791898</td>
      <td>0.490479</td>
      <td>-0.183600</td>
      <td>-0.257605</td>
      <td>0.412233</td>
      <td>-0.078001</td>
      <td>0.186956</td>
      <td>-0.191884</td>
      <td>-0.168413</td>
      <td>0.255242</td>
      <td>0.091430</td>
      <td>0.673855</td>
      <td>-0.335907</td>
      <td>0.380964</td>
      <td>-0.264473</td>
      <td>-0.673625</td>
      <td>-0.237901</td>
      <td>0.112609</td>
      <td>0.444091</td>
      <td>-0.918799</td>
      <td>0.246300</td>
      <td>-0.135537</td>
      <td>-0.175043</td>
      <td>-0.253303</td>
      <td>-0.299894</td>
      <td>0.149999</td>
      <td>0.593667</td>
      <td>0.761253</td>
      <td>-0.575779</td>
      <td>-0.097394</td>
      <td>-0.000816</td>
      <td>-0.501194</td>
      <td>-0.640717</td>
      <td>0.897073</td>
      <td>-0.628050</td>
      <td>-0.248820</td>
      <td>-0.272859</td>
      <td>-0.950085</td>
      <td>-0.163700</td>
      <td>-0.035908</td>
      <td>-0.276775</td>
      <td>-0.154454</td>
      <td>-0.574646</td>
      <td>-0.473842</td>
      <td>0.038363</td>
      <td>-0.056747</td>
      <td>0.384487</td>
      <td>-0.484181</td>
      <td>0.093614</td>
      <td>0.498703</td>
      <td>-0.533747</td>
      <td>-0.711806</td>
      <td>-0.919410</td>
      <td>-0.485425</td>
      <td>-0.296220</td>
      <td>0.172724</td>
      <td>-0.721100</td>
      <td>0.826611</td>
      <td>-0.883099</td>
      <td>-0.254514</td>
      <td>0.590013</td>
      <td>0.559604</td>
      <td>-0.564570</td>
      <td>0.740034</td>
      <td>-0.148647</td>
      <td>-0.298357</td>
      <td>-0.012597</td>
      <td>-0.708821</td>
      <td>-0.391837</td>
      <td>0.191563</td>
      <td>-1.004537</td>
      <td>-0.738738</td>
      <td>0.671492</td>
      <td>0.671719</td>
      <td>-0.128921</td>
      <td>-0.526010</td>
      <td>0.079613</td>
      <td>0.463478</td>
      <td>-0.668246</td>
      <td>-1.075709</td>
      <td>-0.365326</td>
      <td>-0.414533</td>
      <td>-0.013119</td>
      <td>0.401365</td>
      <td>-0.033869</td>
      <td>0.217658</td>
      <td>0.224537</td>
      <td>-0.055596</td>
      <td>0.030737</td>
      <td>-0.653208</td>
      <td>0.147581</td>
      <td>-0.280040</td>
      <td>-0.413608</td>
      <td>0.144482</td>
      <td>-0.650332</td>
      <td>-0.299538</td>
      <td>0.284785</td>
      <td>-0.187970</td>
      <td>0.364331</td>
      <td>0.150967</td>
      <td>-0.188751</td>
      <td>-0.446770</td>
      <td>-0.262638</td>
      <td>0.143024</td>
      <td>-0.238441</td>
      <td>-0.578647</td>
      <td>0.272771</td>
      <td>0.024205</td>
      <td>0.226895</td>
      <td>0.647332</td>
      <td>-0.636249</td>
      <td>-0.587917</td>
      <td>-0.231556</td>
      <td>0.912565</td>
      <td>1.203607</td>
      <td>0.432418</td>
      <td>0.518319</td>
      <td>0.473687</td>
      <td>-0.958097</td>
      <td>0.203794</td>
      <td>-0.151759</td>
      <td>-0.018686</td>
      <td>0.192080</td>
      <td>-1.021953</td>
      <td>0.287902</td>
      <td>-0.579640</td>
      <td>0.164815</td>
      <td>0.056889</td>
      <td>-0.691057</td>
      <td>-0.448987</td>
      <td>-0.073829</td>
      <td>-1.255278</td>
      <td>0.800211</td>
      <td>0.079377</td>
      <td>-0.012886</td>
      <td>0.021282</td>
      <td>0.396812</td>
      <td>-0.289695</td>
      <td>-0.175229</td>
      <td>-0.635356</td>
      <td>-0.512838</td>
      <td>1.119194</td>
      <td>-0.249533</td>
      <td>0.984064</td>
      <td>0.973100</td>
      <td>0.586006</td>
      <td>0.822577</td>
      <td>0.559683</td>
      <td>-0.190305</td>
      <td>0.331482</td>
      <td>0.477602</td>
      <td>0.625904</td>
      <td>0.513253</td>
      <td>-0.650584</td>
      <td>-1.099899</td>
      <td>0.367165</td>
      <td>-0.021345</td>
      <td>0.640862</td>
      <td>-0.316657</td>
      <td>-0.589543</td>
      <td>0.371063</td>
      <td>0.532771</td>
      <td>0.030706</td>
      <td>0.121678</td>
      <td>-0.000449</td>
      <td>-0.129630</td>
      <td>0.124080</td>
      <td>0.756095</td>
      <td>0.079764</td>
      <td>-0.563743</td>
      <td>0.310583</td>
      <td>-0.055115</td>
      <td>-0.116182</td>
      <td>-0.347540</td>
      <td>0.120028</td>
      <td>0.114732</td>
      <td>-0.295688</td>
      <td>0.165481</td>
      <td>-0.321854</td>
      <td>-1.013071</td>
      <td>0.604600</td>
      <td>-0.337336</td>
      <td>0.376763</td>
      <td>0.249412</td>
      <td>0.112343</td>
      <td>0.096768</td>
      <td>-0.190153</td>
      <td>-0.177083</td>
      <td>0.162477</td>
      <td>0.237535</td>
      <td>0.546911</td>
      <td>-0.034891</td>
      <td>-0.571519</td>
      <td>0.277861</td>
      <td>0.465566</td>
      <td>0.115715</td>
      <td>0.093002</td>
      <td>0.162762</td>
      <td>-0.653169</td>
      <td>0.355135</td>
      <td>-0.381567</td>
      <td>-0.349790</td>
      <td>-0.626262</td>
      <td>0.442894</td>
      <td>0.286028</td>
      <td>-0.078250</td>
      <td>0.158042</td>
      <td>-0.188913</td>
      <td>0.152614</td>
      <td>-0.240869</td>
      <td>-0.557733</td>
      <td>-1.111950</td>
      <td>0.132458</td>
      <td>2.400062</td>
      <td>-0.346703</td>
      <td>-0.576592</td>
      <td>-0.340219</td>
      <td>0.065149</td>
      <td>0.313043</td>
      <td>0.030145</td>
      <td>-0.192104</td>
      <td>-0.338162</td>
      <td>-0.126533</td>
      <td>0.793557</td>
      <td>0.334537</td>
      <td>0.034523</td>
      <td>0.463801</td>
      <td>-1.411424</td>
      <td>-0.359793</td>
      <td>0.531513</td>
      <td>0.249348</td>
      <td>-0.083826</td>
      <td>-0.420200</td>
      <td>-0.434105</td>
      <td>0.186430</td>
      <td>-0.921737</td>
      <td>0.047174</td>
      <td>0.057826</td>
      <td>0.844248</td>
      <td>0.780342</td>
      <td>0.070998</td>
      <td>-0.069889</td>
      <td>0.145794</td>
      <td>0.531431</td>
      <td>0.676340</td>
      <td>0.844901</td>
      <td>-1.133567</td>
      <td>0.053619</td>
      <td>0.452403</td>
      <td>0.482003</td>
      <td>-0.284129</td>
      <td>1.237693</td>
      <td>1.294347</td>
      <td>-0.485198</td>
      <td>-0.415301</td>
      <td>-0.009781</td>
      <td>-0.412536</td>
      <td>-0.058068</td>
      <td>-0.321041</td>
      <td>0.691596</td>
      <td>-0.374837</td>
      <td>0.258833</td>
      <td>0.147456</td>
      <td>0.292692</td>
      <td>-0.017483</td>
      <td>-0.545623</td>
      <td>-0.626546</td>
      <td>0.672998</td>
      <td>-0.367186</td>
      <td>0.654898</td>
      <td>1.331991</td>
      <td>-1.260641</td>
      <td>-0.209006</td>
      <td>-0.466502</td>
      <td>0.294200</td>
      <td>0.577445</td>
      <td>0.706991</td>
      <td>-0.023744</td>
      <td>-0.709787</td>
      <td>-0.367622</td>
      <td>0.330320</td>
      <td>1.214613</td>
      <td>-0.515938</td>
      <td>-0.359165</td>
      <td>-0.133161</td>
      <td>-0.129540</td>
      <td>-0.126839</td>
      <td>0.632480</td>
      <td>-0.387935</td>
      <td>0.241902</td>
      <td>-0.633366</td>
      <td>-0.523176</td>
      <td>-0.527181</td>
      <td>0.452584</td>
      <td>1.803421</td>
      <td>-0.048306</td>
      <td>0.021186</td>
      <td>-0.369667</td>
      <td>-1.071339</td>
      <td>0.063582</td>
      <td>0.146086</td>
      <td>-0.298028</td>
      <td>-0.204777</td>
      <td>-0.505969</td>
      <td>-0.328057</td>
      <td>-1.205907</td>
      <td>0.047525</td>
      <td>-0.451212</td>
      <td>0.167448</td>
      <td>0.010507</td>
      <td>0.755667</td>
      <td>0.032288</td>
      <td>-0.386867</td>
      <td>0.165278</td>
      <td>0.301298</td>
      <td>0.193379</td>
      <td>0.068926</td>
      <td>0.371230</td>
      <td>0.287795</td>
      <td>-0.985284</td>
      <td>-0.223304</td>
      <td>-0.626542</td>
      <td>0.795625</td>
      <td>-0.296651</td>
      <td>0.694300</td>
      <td>-0.579589</td>
      <td>-0.262564</td>
      <td>0.222891</td>
      <td>0.795642</td>
      <td>0.733044</td>
      <td>-0.284334</td>
      <td>-0.109595</td>
      <td>-0.108401</td>
      <td>-0.390273</td>
      <td>-0.218412</td>
      <td>0.355905</td>
      <td>0.300517</td>
      <td>-1.113616</td>
      <td>-0.675710</td>
      <td>-0.689336</td>
      <td>-0.561894</td>
      <td>0.187945</td>
      <td>0.086031</td>
      <td>-0.411544</td>
      <td>0.776602</td>
      <td>-0.110127</td>
      <td>-1.146778</td>
      <td>0.171478</td>
      <td>-0.103220</td>
      <td>-0.267962</td>
      <td>0.130565</td>
      <td>-0.165949</td>
      <td>-0.461319</td>
      <td>-0.049661</td>
      <td>-0.675637</td>
      <td>0.360346</td>
      <td>-0.757141</td>
      <td>-0.436090</td>
      <td>0.370495</td>
      <td>0.212080</td>
      <td>0.493824</td>
      <td>-0.048274</td>
      <td>0.029956</td>
      <td>-0.161615</td>
      <td>0.444228</td>
      <td>0.390152</td>
      <td>-0.029008</td>
      <td>0.488520</td>
      <td>0.928508</td>
      <td>0.744242</td>
      <td>0.740107</td>
      <td>0.113603</td>
      <td>0.455303</td>
      <td>0.096427</td>
      <td>0.462614</td>
      <td>-0.512192</td>
      <td>-1.193843</td>
      <td>-0.038171</td>
      <td>-0.256195</td>
      <td>-0.465930</td>
      <td>-0.376453</td>
      <td>-0.517453</td>
      <td>-0.360362</td>
      <td>-0.658929</td>
      <td>0.140715</td>
      <td>0.644416</td>
      <td>0.981935</td>
      <td>0.334368</td>
      <td>0.250171</td>
      <td>0.099848</td>
      <td>-0.304732</td>
      <td>-1.017176</td>
      <td>0.273059</td>
      <td>-0.732619</td>
      <td>-1.103645</td>
      <td>-0.397611</td>
      <td>0.659932</td>
      <td>-0.525215</td>
      <td>-0.152209</td>
      <td>-0.303714</td>
      <td>0.075419</td>
      <td>-0.419280</td>
      <td>0.214068</td>
      <td>-0.174816</td>
      <td>-0.039391</td>
      <td>-0.633083</td>
      <td>-0.279430</td>
      <td>0.513096</td>
      <td>-0.728802</td>
      <td>0.086171</td>
      <td>1.135764</td>
      <td>-0.224654</td>
      <td>-0.196687</td>
      <td>-0.055113</td>
      <td>0.156502</td>
      <td>0.095315</td>
      <td>-0.499497</td>
      <td>0.199974</td>
      <td>0.285876</td>
      <td>-0.144737</td>
      <td>1.079733</td>
      <td>-0.348892</td>
      <td>-0.881111</td>
      <td>1.976175</td>
      <td>-0.461430</td>
      <td>-0.444836</td>
      <td>-0.765195</td>
      <td>0.389889</td>
      <td>-0.025223</td>
      <td>0.283937</td>
      <td>0.486037</td>
      <td>-0.077996</td>
      <td>0.224939</td>
      <td>0.384686</td>
      <td>0.399144</td>
      <td>0.148860</td>
      <td>-0.237975</td>
      <td>-1.098469</td>
      <td>0.095633</td>
      <td>0.326284</td>
      <td>1.045473</td>
      <td>0.814254</td>
      <td>-0.622515</td>
      <td>0.194302</td>
      <td>0.181139</td>
      <td>-0.154810</td>
      <td>0.506349</td>
      <td>0.027980</td>
      <td>0.066735</td>
      <td>-1.514790</td>
      <td>-0.360951</td>
      <td>0.510457</td>
      <td>0.077493</td>
      <td>-0.009347</td>
      <td>0.193790</td>
      <td>0.704940</td>
      <td>-0.023667</td>
      <td>-0.179928</td>
      <td>0.239870</td>
      <td>-0.305943</td>
      <td>-0.298562</td>
      <td>0.150385</td>
      <td>-0.195162</td>
      <td>0.325353</td>
      <td>-0.074046</td>
      <td>0.530248</td>
      <td>0.102485</td>
      <td>-0.771303</td>
      <td>-0.066522</td>
      <td>0.470143</td>
      <td>-0.641614</td>
      <td>-0.543484</td>
      <td>-0.424398</td>
      <td>0.521366</td>
      <td>-0.349863</td>
      <td>-0.473901</td>
      <td>0.862803</td>
      <td>0.382852</td>
      <td>0.540601</td>
      <td>0.352421</td>
      <td>0.914544</td>
      <td>1.322797</td>
      <td>-1.372588</td>
      <td>0.634645</td>
      <td>-0.260575</td>
      <td>-0.315568</td>
      <td>-0.406799</td>
      <td>-0.485108</td>
      <td>0.240797</td>
      <td>-0.364827</td>
      <td>0.097697</td>
      <td>-0.254104</td>
      <td>0.156099</td>
      <td>0.011574</td>
      <td>-0.679159</td>
      <td>0.128990</td>
      <td>0.037632</td>
      <td>-0.046042</td>
      <td>-0.475576</td>
      <td>0.390851</td>
      <td>-0.244099</td>
      <td>0.517740</td>
      <td>1.042168</td>
      <td>-0.047910</td>
      <td>0.018597</td>
      <td>0.362442</td>
      <td>0.539500</td>
      <td>-0.143496</td>
      <td>-0.037339</td>
      <td>-1.015265</td>
      <td>0.468242</td>
      <td>0.059887</td>
      <td>0.093207</td>
      <td>-0.293192</td>
      <td>-0.280732</td>
      <td>-0.211099</td>
      <td>-0.120041</td>
      <td>3.523412</td>
      <td>0.098918</td>
      <td>0.098918</td>
      <td>0.125364</td>
      <td>16.777778</td>
      <td>598.919</td>
      <td>0</td>
      <td>0.577778</td>
      <td>3.135512</td>
      <td>1.451540</td>
      <td>1368.308170</td>
      <td>10.636376</td>
      <td>14.715582</td>
      <td>7.162396</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>222.907896</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.0</td>
      <td>32.607024</td>
      <td>18.759549</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>11.374773</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>5.917906</td>
      <td>115.071874</td>
      <td>11.467335</td>
      <td>130.442582</td>
      <td>0.000000</td>
      <td>11.467335</td>
      <td>11.374773</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>5.414990</td>
      <td>5.917906</td>
      <td>143.037592</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>24.06</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>5.414990</td>
      <td>11.835812</td>
      <td>5.917906</td>
      <td>121.805341</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>129.300420</td>
      <td>0.000000</td>
      <td>0.326442</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>18.579143</td>
      <td>1.794202</td>
      <td>15.456662</td>
      <td>6.971589</td>
      <td>0.000000</td>
      <td>0.441860</td>
      <td>2</td>
      <td>1</td>
      <td>0</td>
      <td>1</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>2</td>
      <td>4</td>
      <td>0</td>
      <td>16</td>
      <td>1</td>
      <td>0</td>
      <td>1</td>
      <td>0</td>
      <td>0</td>
      <td>5</td>
      <td>12.35960</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>22</td>
      <td>8.364477</td>
      <td>5</td>
    </tr>
    <tr>
      <th>2</th>
      <td>*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(Oc6ccc(...</td>
      <td>0.535358</td>
      <td>0.011227</td>
      <td>0.346034</td>
      <td>0.224502</td>
      <td>0.360709</td>
      <td>-0.251822</td>
      <td>0.563062</td>
      <td>0.729303</td>
      <td>0.684787</td>
      <td>0.400244</td>
      <td>-0.706586</td>
      <td>1.043188</td>
      <td>-0.357401</td>
      <td>-0.175397</td>
      <td>0.105199</td>
      <td>-0.078835</td>
      <td>-0.871502</td>
      <td>1.401016</td>
      <td>0.232547</td>
      <td>-0.059740</td>
      <td>0.164947</td>
      <td>1.028633</td>
      <td>0.524101</td>
      <td>0.125356</td>
      <td>0.621602</td>
      <td>-0.592935</td>
      <td>0.095408</td>
      <td>0.043370</td>
      <td>-0.263838</td>
      <td>-0.196164</td>
      <td>0.185971</td>
      <td>1.047944</td>
      <td>-0.171430</td>
      <td>1.034604</td>
      <td>-0.608208</td>
      <td>-0.495526</td>
      <td>-1.075194</td>
      <td>-0.358933</td>
      <td>0.103263</td>
      <td>-0.706990</td>
      <td>0.601134</td>
      <td>-0.297404</td>
      <td>0.495750</td>
      <td>-0.352589</td>
      <td>-0.684066</td>
      <td>-0.206918</td>
      <td>0.442205</td>
      <td>-0.443689</td>
      <td>-1.205985</td>
      <td>0.533911</td>
      <td>0.505950</td>
      <td>0.111828</td>
      <td>0.664409</td>
      <td>0.294350</td>
      <td>-0.374972</td>
      <td>-0.378763</td>
      <td>-0.272895</td>
      <td>0.446803</td>
      <td>-0.540448</td>
      <td>-0.981334</td>
      <td>0.004960</td>
      <td>0.589700</td>
      <td>-1.311208</td>
      <td>0.957034</td>
      <td>1.066627</td>
      <td>0.475518</td>
      <td>-0.018658</td>
      <td>-0.406216</td>
      <td>-0.081646</td>
      <td>0.452551</td>
      <td>-0.566564</td>
      <td>0.266778</td>
      <td>0.522817</td>
      <td>-0.701847</td>
      <td>-0.557670</td>
      <td>-0.280021</td>
      <td>-0.306510</td>
      <td>0.058812</td>
      <td>-0.499353</td>
      <td>-0.479730</td>
      <td>-0.964017</td>
      <td>0.103480</td>
      <td>-0.049515</td>
      <td>-0.401672</td>
      <td>0.551643</td>
      <td>0.775659</td>
      <td>-0.244854</td>
      <td>0.015731</td>
      <td>-0.306718</td>
      <td>0.324607</td>
      <td>0.214400</td>
      <td>-0.322809</td>
      <td>0.005379</td>
      <td>-0.382433</td>
      <td>0.512824</td>
      <td>-0.289829</td>
      <td>0.696189</td>
      <td>-0.850301</td>
      <td>-0.872577</td>
      <td>-0.198907</td>
      <td>-0.154659</td>
      <td>-0.043685</td>
      <td>-0.215117</td>
      <td>-0.304584</td>
      <td>0.909633</td>
      <td>0.765225</td>
      <td>-1.315344</td>
      <td>0.507410</td>
      <td>0.317197</td>
      <td>0.928634</td>
      <td>1.600932</td>
      <td>-1.107399</td>
      <td>-0.057298</td>
      <td>0.214253</td>
      <td>-0.334336</td>
      <td>-0.207574</td>
      <td>0.797908</td>
      <td>-0.656817</td>
      <td>0.508849</td>
      <td>-0.706018</td>
      <td>-1.255997</td>
      <td>-0.494380</td>
      <td>0.363648</td>
      <td>0.260767</td>
      <td>-0.243692</td>
      <td>-0.490836</td>
      <td>0.121244</td>
      <td>0.148881</td>
      <td>-0.606578</td>
      <td>0.488917</td>
      <td>-1.720508</td>
      <td>0.117484</td>
      <td>0.322084</td>
      <td>-0.977390</td>
      <td>-0.473354</td>
      <td>-0.572928</td>
      <td>-0.764539</td>
      <td>0.676984</td>
      <td>-0.810201</td>
      <td>-1.091182</td>
      <td>1.567472</td>
      <td>-0.231106</td>
      <td>-0.893987</td>
      <td>0.680414</td>
      <td>1.061572</td>
      <td>-0.119109</td>
      <td>0.445201</td>
      <td>-0.180188</td>
      <td>-0.102628</td>
      <td>-0.143309</td>
      <td>-1.993773</td>
      <td>-0.510275</td>
      <td>-0.095149</td>
      <td>-0.120077</td>
      <td>0.143525</td>
      <td>0.458155</td>
      <td>0.561968</td>
      <td>-0.420827</td>
      <td>-0.501846</td>
      <td>-0.249871</td>
      <td>-0.239789</td>
      <td>0.505841</td>
      <td>-0.577488</td>
      <td>0.047014</td>
      <td>-0.752228</td>
      <td>0.142253</td>
      <td>0.521683</td>
      <td>-0.831281</td>
      <td>0.061826</td>
      <td>1.173661</td>
      <td>0.583307</td>
      <td>-0.449564</td>
      <td>-1.204527</td>
      <td>-0.650550</td>
      <td>-0.663336</td>
      <td>-0.353872</td>
      <td>0.152247</td>
      <td>-0.642179</td>
      <td>-0.174780</td>
      <td>-0.016906</td>
      <td>-0.949450</td>
      <td>-0.449638</td>
      <td>-0.042742</td>
      <td>-0.158856</td>
      <td>-0.806129</td>
      <td>-1.203741</td>
      <td>0.174995</td>
      <td>-1.199419</td>
      <td>-0.549562</td>
      <td>-0.046916</td>
      <td>-0.423895</td>
      <td>0.044450</td>
      <td>0.350426</td>
      <td>-0.818097</td>
      <td>-0.438084</td>
      <td>0.596727</td>
      <td>0.921969</td>
      <td>0.616382</td>
      <td>0.352854</td>
      <td>-0.173466</td>
      <td>0.889304</td>
      <td>-0.359012</td>
      <td>-0.036818</td>
      <td>-0.195790</td>
      <td>-0.332708</td>
      <td>0.223701</td>
      <td>-0.942359</td>
      <td>0.893297</td>
      <td>0.217302</td>
      <td>0.134286</td>
      <td>-0.508501</td>
      <td>-0.327347</td>
      <td>-1.240189</td>
      <td>0.736232</td>
      <td>-1.054666</td>
      <td>0.552755</td>
      <td>-0.244820</td>
      <td>-0.638105</td>
      <td>0.164959</td>
      <td>-0.322623</td>
      <td>0.695439</td>
      <td>-0.559530</td>
      <td>-1.168204</td>
      <td>0.112887</td>
      <td>1.160789</td>
      <td>0.922326</td>
      <td>0.320379</td>
      <td>0.766050</td>
      <td>0.596191</td>
      <td>0.368680</td>
      <td>0.050401</td>
      <td>-1.346926</td>
      <td>-0.763572</td>
      <td>-0.446444</td>
      <td>0.735212</td>
      <td>0.804675</td>
      <td>-0.958659</td>
      <td>-0.911082</td>
      <td>0.416366</td>
      <td>-0.031096</td>
      <td>0.475806</td>
      <td>-0.722479</td>
      <td>0.158374</td>
      <td>-0.236415</td>
      <td>0.529206</td>
      <td>0.073452</td>
      <td>0.427179</td>
      <td>-0.553749</td>
      <td>-0.785797</td>
      <td>-0.743612</td>
      <td>0.995653</td>
      <td>0.281008</td>
      <td>-0.818622</td>
      <td>0.669041</td>
      <td>-0.003459</td>
      <td>-0.421623</td>
      <td>0.616898</td>
      <td>0.298582</td>
      <td>-0.189195</td>
      <td>0.271986</td>
      <td>-0.195637</td>
      <td>0.383960</td>
      <td>-0.264333</td>
      <td>0.678402</td>
      <td>0.378787</td>
      <td>0.036758</td>
      <td>-0.378549</td>
      <td>-0.054877</td>
      <td>0.834418</td>
      <td>1.256418</td>
      <td>-0.862578</td>
      <td>-0.338295</td>
      <td>-0.137402</td>
      <td>1.018920</td>
      <td>0.643010</td>
      <td>-0.177217</td>
      <td>-0.442551</td>
      <td>-0.495964</td>
      <td>-0.388639</td>
      <td>-0.088484</td>
      <td>0.420446</td>
      <td>0.561296</td>
      <td>0.202845</td>
      <td>-0.078560</td>
      <td>-0.417726</td>
      <td>-0.602743</td>
      <td>0.036215</td>
      <td>-0.135405</td>
      <td>-0.644516</td>
      <td>0.208164</td>
      <td>0.622524</td>
      <td>0.486170</td>
      <td>-1.060556</td>
      <td>-0.239297</td>
      <td>-1.539891</td>
      <td>0.201162</td>
      <td>2.515026</td>
      <td>0.021378</td>
      <td>-0.252188</td>
      <td>0.021248</td>
      <td>0.157835</td>
      <td>0.142499</td>
      <td>0.162519</td>
      <td>-1.369365</td>
      <td>0.038512</td>
      <td>-0.485286</td>
      <td>-0.572488</td>
      <td>-0.901663</td>
      <td>0.388750</td>
      <td>0.203640</td>
      <td>-0.480953</td>
      <td>-1.631275</td>
      <td>-0.030246</td>
      <td>-0.167001</td>
      <td>-0.039476</td>
      <td>-1.006135</td>
      <td>0.082185</td>
      <td>-0.508681</td>
      <td>-0.896751</td>
      <td>0.301622</td>
      <td>0.308267</td>
      <td>0.910063</td>
      <td>-0.705604</td>
      <td>-0.190067</td>
      <td>-0.124001</td>
      <td>-0.260425</td>
      <td>1.194199</td>
      <td>0.401435</td>
      <td>1.091595</td>
      <td>-1.124549</td>
      <td>-0.678893</td>
      <td>1.867621</td>
      <td>0.287684</td>
      <td>-1.235395</td>
      <td>1.630901</td>
      <td>1.046364</td>
      <td>-0.452614</td>
      <td>0.019531</td>
      <td>-0.510772</td>
      <td>-0.426651</td>
      <td>0.979058</td>
      <td>-0.706453</td>
      <td>-0.071641</td>
      <td>0.345773</td>
      <td>0.896557</td>
      <td>-0.041677</td>
      <td>-0.983593</td>
      <td>-0.147743</td>
      <td>0.307207</td>
      <td>-1.105580</td>
      <td>0.151539</td>
      <td>-0.400798</td>
      <td>0.648991</td>
      <td>0.465229</td>
      <td>-0.821635</td>
      <td>-0.351701</td>
      <td>0.821198</td>
      <td>0.372337</td>
      <td>0.601062</td>
      <td>-0.110538</td>
      <td>-0.546753</td>
      <td>-0.751393</td>
      <td>0.905429</td>
      <td>0.619740</td>
      <td>1.187855</td>
      <td>-0.567903</td>
      <td>0.009493</td>
      <td>0.162947</td>
      <td>-0.410111</td>
      <td>0.271982</td>
      <td>-0.547318</td>
      <td>-0.048622</td>
      <td>0.187111</td>
      <td>0.096876</td>
      <td>-0.799094</td>
      <td>-0.111899</td>
      <td>-0.319410</td>
      <td>0.641845</td>
      <td>0.120172</td>
      <td>0.549351</td>
      <td>0.039732</td>
      <td>-1.025473</td>
      <td>1.287444</td>
      <td>0.295798</td>
      <td>-0.162988</td>
      <td>0.666896</td>
      <td>-0.798936</td>
      <td>-0.488178</td>
      <td>-1.012842</td>
      <td>0.813242</td>
      <td>0.925908</td>
      <td>-0.352074</td>
      <td>0.646179</td>
      <td>0.286498</td>
      <td>0.503767</td>
      <td>-0.442461</td>
      <td>0.118279</td>
      <td>0.009241</td>
      <td>-0.463589</td>
      <td>0.299820</td>
      <td>0.595466</td>
      <td>-0.200465</td>
      <td>-0.015382</td>
      <td>-0.393459</td>
      <td>-0.666916</td>
      <td>0.853344</td>
      <td>-0.108474</td>
      <td>1.077658</td>
      <td>-0.546762</td>
      <td>0.465675</td>
      <td>0.080772</td>
      <td>1.001601</td>
      <td>0.626080</td>
      <td>0.344398</td>
      <td>-0.526747</td>
      <td>-0.020098</td>
      <td>0.234334</td>
      <td>-0.637872</td>
      <td>0.303958</td>
      <td>0.215013</td>
      <td>-0.904312</td>
      <td>-0.518785</td>
      <td>-0.763233</td>
      <td>-0.450233</td>
      <td>0.376385</td>
      <td>0.113420</td>
      <td>-0.413906</td>
      <td>0.918697</td>
      <td>-0.053596</td>
      <td>-1.318410</td>
      <td>0.467037</td>
      <td>0.669838</td>
      <td>-0.198371</td>
      <td>0.551824</td>
      <td>-1.228341</td>
      <td>0.115647</td>
      <td>0.149033</td>
      <td>-0.504709</td>
      <td>0.088772</td>
      <td>-0.908012</td>
      <td>-0.718938</td>
      <td>0.096505</td>
      <td>0.272867</td>
      <td>0.703840</td>
      <td>0.050659</td>
      <td>0.575007</td>
      <td>-0.465394</td>
      <td>1.053061</td>
      <td>1.159342</td>
      <td>0.177787</td>
      <td>1.146798</td>
      <td>0.436297</td>
      <td>0.070215</td>
      <td>0.999204</td>
      <td>0.148346</td>
      <td>0.467310</td>
      <td>1.090014</td>
      <td>-0.342938</td>
      <td>0.023396</td>
      <td>-0.255432</td>
      <td>-0.266805</td>
      <td>-0.148834</td>
      <td>0.549889</td>
      <td>-0.185687</td>
      <td>-1.345542</td>
      <td>-0.232326</td>
      <td>-1.012087</td>
      <td>-0.544417</td>
      <td>0.724590</td>
      <td>1.155097</td>
      <td>-0.425453</td>
      <td>0.275198</td>
      <td>-0.099100</td>
      <td>-0.084546</td>
      <td>-0.204178</td>
      <td>0.505556</td>
      <td>0.554675</td>
      <td>-0.649567</td>
      <td>0.231874</td>
      <td>1.096294</td>
      <td>-0.591795</td>
      <td>-0.579749</td>
      <td>0.019616</td>
      <td>0.281727</td>
      <td>-0.438688</td>
      <td>0.617762</td>
      <td>-0.343814</td>
      <td>-0.867305</td>
      <td>-0.893060</td>
      <td>0.154264</td>
      <td>0.269821</td>
      <td>-1.115280</td>
      <td>-0.310504</td>
      <td>0.539172</td>
      <td>-0.491246</td>
      <td>-0.189708</td>
      <td>-0.084566</td>
      <td>-0.421861</td>
      <td>-0.249220</td>
      <td>-0.379036</td>
      <td>0.276173</td>
      <td>-0.539894</td>
      <td>-0.264418</td>
      <td>1.241980</td>
      <td>-0.288136</td>
      <td>-0.962496</td>
      <td>1.656721</td>
      <td>-0.752003</td>
      <td>-0.189024</td>
      <td>-1.079454</td>
      <td>0.120749</td>
      <td>-0.195717</td>
      <td>0.043600</td>
      <td>0.449033</td>
      <td>-0.325703</td>
      <td>0.312492</td>
      <td>0.422746</td>
      <td>0.726378</td>
      <td>1.035505</td>
      <td>0.306763</td>
      <td>-1.328989</td>
      <td>-0.694147</td>
      <td>1.110143</td>
      <td>1.407753</td>
      <td>1.021646</td>
      <td>0.494072</td>
      <td>-0.341257</td>
      <td>-0.009038</td>
      <td>-1.192445</td>
      <td>-0.069809</td>
      <td>0.886013</td>
      <td>0.086623</td>
      <td>-0.622184</td>
      <td>0.893956</td>
      <td>-0.039157</td>
      <td>-0.230038</td>
      <td>0.409664</td>
      <td>0.233872</td>
      <td>0.024632</td>
      <td>0.756961</td>
      <td>0.386462</td>
      <td>0.522316</td>
      <td>0.666277</td>
      <td>-0.401384</td>
      <td>-0.040793</td>
      <td>0.241062</td>
      <td>0.046944</td>
      <td>0.851451</td>
      <td>0.586450</td>
      <td>-0.124922</td>
      <td>-0.106213</td>
      <td>-0.606704</td>
      <td>0.930409</td>
      <td>-1.436533</td>
      <td>-0.319613</td>
      <td>0.321761</td>
      <td>0.018733</td>
      <td>-0.716667</td>
      <td>0.320018</td>
      <td>1.399314</td>
      <td>0.818442</td>
      <td>-0.732807</td>
      <td>0.636372</td>
      <td>0.143111</td>
      <td>1.648443</td>
      <td>0.283183</td>
      <td>0.697935</td>
      <td>0.423416</td>
      <td>-0.210294</td>
      <td>0.389991</td>
      <td>-1.228383</td>
      <td>0.434890</td>
      <td>-0.638399</td>
      <td>0.389859</td>
      <td>0.706226</td>
      <td>0.783215</td>
      <td>0.092527</td>
      <td>0.043700</td>
      <td>0.329160</td>
      <td>-0.909485</td>
      <td>0.409342</td>
      <td>-0.267139</td>
      <td>-0.208642</td>
      <td>-0.818247</td>
      <td>0.198075</td>
      <td>0.726563</td>
      <td>0.886845</td>
      <td>0.596593</td>
      <td>0.204266</td>
      <td>-0.303381</td>
      <td>-0.145162</td>
      <td>0.271026</td>
      <td>-1.137240</td>
      <td>0.578171</td>
      <td>0.121307</td>
      <td>0.487683</td>
      <td>0.049848</td>
      <td>-0.446077</td>
      <td>-0.424801</td>
      <td>-0.007285</td>
      <td>13.714745</td>
      <td>0.107441</td>
      <td>-3.829434</td>
      <td>0.092387</td>
      <td>16.301370</td>
      <td>1003.207</td>
      <td>0</td>
      <td>0.397260</td>
      <td>3.440031</td>
      <td>0.743739</td>
      <td>3592.712301</td>
      <td>16.833145</td>
      <td>19.899082</td>
      <td>10.377039</td>
      <td>9.473726</td>
      <td>22.998047</td>
      <td>0.0</td>
      <td>9.837253</td>
      <td>0.000000</td>
      <td>222.531582</td>
      <td>0.000000</td>
      <td>8.417797</td>
      <td>0.0</td>
      <td>0.0</td>
      <td>43.527933</td>
      <td>96.764951</td>
      <td>5.414990</td>
      <td>9.790967</td>
      <td>40.947247</td>
      <td>43.676159</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>76.363497</td>
      <td>0.000000</td>
      <td>227.523761</td>
      <td>40.246583</td>
      <td>25.383483</td>
      <td>0.000000</td>
      <td>40.246583</td>
      <td>0.000000</td>
      <td>22.618839</td>
      <td>29.884034</td>
      <td>0.000000</td>
      <td>73.620379</td>
      <td>0.000000</td>
      <td>12.152040</td>
      <td>122.27</td>
      <td>19.674506</td>
      <td>21.630131</td>
      <td>0.0</td>
      <td>30.780169</td>
      <td>40.246583</td>
      <td>67.218863</td>
      <td>23.614092</td>
      <td>97.061873</td>
      <td>60.682977</td>
      <td>48.530937</td>
      <td>19.317116</td>
      <td>72.241292</td>
      <td>0.000000</td>
      <td>14.038931</td>
      <td>5.415568</td>
      <td>5.844813</td>
      <td>4.224702</td>
      <td>11.839224</td>
      <td>0.000000</td>
      <td>-7.512616</td>
      <td>0.145161</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>9</td>
      <td>13</td>
      <td>0</td>
      <td>15</td>
      <td>2</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>10</td>
      <td>14.21700</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>3</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>1</td>
      <td>1</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>45</td>
      <td>15.419820</td>
      <td>10</td>
    </tr>
    <tr>
      <th>3</th>
      <td>*Nc1ccc(-c2c(-c3ccc(C)cc3)c(-c3ccc(C)cc3)c(N*)...</td>
      <td>-0.028022</td>
      <td>-0.080552</td>
      <td>0.415525</td>
      <td>0.186767</td>
      <td>0.072924</td>
      <td>-0.129440</td>
      <td>1.039699</td>
      <td>0.303087</td>
      <td>1.003591</td>
      <td>0.185523</td>
      <td>-0.741992</td>
      <td>0.500496</td>
      <td>0.025780</td>
      <td>-0.065233</td>
      <td>-0.423814</td>
      <td>-0.697438</td>
      <td>-0.633862</td>
      <td>-0.021778</td>
      <td>-0.487633</td>
      <td>-0.064094</td>
      <td>0.418592</td>
      <td>0.029498</td>
      <td>0.017040</td>
      <td>0.595374</td>
      <td>0.122654</td>
      <td>0.105278</td>
      <td>1.461985</td>
      <td>-0.210754</td>
      <td>-0.577107</td>
      <td>-0.454950</td>
      <td>0.778053</td>
      <td>-0.013151</td>
      <td>-0.018060</td>
      <td>1.206268</td>
      <td>0.141336</td>
      <td>0.269048</td>
      <td>0.342521</td>
      <td>-0.033471</td>
      <td>-0.317409</td>
      <td>0.533548</td>
      <td>-0.264010</td>
      <td>0.921534</td>
      <td>-0.004354</td>
      <td>-0.526162</td>
      <td>0.686108</td>
      <td>0.315969</td>
      <td>0.833555</td>
      <td>0.054497</td>
      <td>0.674952</td>
      <td>0.785908</td>
      <td>0.846106</td>
      <td>1.249816</td>
      <td>0.359013</td>
      <td>-0.317570</td>
      <td>-0.743510</td>
      <td>-0.500657</td>
      <td>0.019141</td>
      <td>-0.175238</td>
      <td>-0.576406</td>
      <td>0.057991</td>
      <td>-0.357968</td>
      <td>-0.353413</td>
      <td>-0.981364</td>
      <td>-0.201246</td>
      <td>0.269977</td>
      <td>0.274681</td>
      <td>0.156595</td>
      <td>-0.102605</td>
      <td>0.518593</td>
      <td>-0.232895</td>
      <td>0.453758</td>
      <td>0.361276</td>
      <td>-0.356326</td>
      <td>-0.129518</td>
      <td>-0.620823</td>
      <td>-0.528163</td>
      <td>-0.396643</td>
      <td>-0.051178</td>
      <td>0.100471</td>
      <td>-0.016231</td>
      <td>-0.258227</td>
      <td>0.077023</td>
      <td>-0.185975</td>
      <td>0.152618</td>
      <td>0.372287</td>
      <td>-0.162981</td>
      <td>0.222055</td>
      <td>0.149407</td>
      <td>-0.748970</td>
      <td>0.688194</td>
      <td>-0.310593</td>
      <td>-0.184631</td>
      <td>-0.720894</td>
      <td>-0.303656</td>
      <td>0.343034</td>
      <td>-0.484457</td>
      <td>0.016727</td>
      <td>1.059650</td>
      <td>-0.135760</td>
      <td>-0.659794</td>
      <td>0.505253</td>
      <td>0.078696</td>
      <td>-0.127825</td>
      <td>0.710521</td>
      <td>-0.461043</td>
      <td>0.042814</td>
      <td>-0.138190</td>
      <td>-0.298987</td>
      <td>0.782607</td>
      <td>1.475390</td>
      <td>0.079084</td>
      <td>-0.727253</td>
      <td>0.201675</td>
      <td>-0.954075</td>
      <td>-0.431926</td>
      <td>-0.283887</td>
      <td>-0.096423</td>
      <td>0.089555</td>
      <td>-0.242087</td>
      <td>0.607178</td>
      <td>-0.620844</td>
      <td>0.236711</td>
      <td>0.500023</td>
      <td>0.299577</td>
      <td>-0.602530</td>
      <td>-0.757431</td>
      <td>-0.497633</td>
      <td>0.136328</td>
      <td>0.095892</td>
      <td>-0.582466</td>
      <td>-1.839027</td>
      <td>0.543873</td>
      <td>-0.258369</td>
      <td>-0.274141</td>
      <td>-0.347389</td>
      <td>0.468971</td>
      <td>-0.734916</td>
      <td>0.261490</td>
      <td>-0.167636</td>
      <td>-0.212869</td>
      <td>0.137806</td>
      <td>-0.343622</td>
      <td>0.993816</td>
      <td>0.434019</td>
      <td>0.348654</td>
      <td>-0.760498</td>
      <td>-0.075769</td>
      <td>-0.759466</td>
      <td>0.467306</td>
      <td>-0.306927</td>
      <td>-0.322689</td>
      <td>-0.287411</td>
      <td>0.914420</td>
      <td>-0.101720</td>
      <td>-0.151221</td>
      <td>0.334999</td>
      <td>0.167470</td>
      <td>-0.264654</td>
      <td>-0.118814</td>
      <td>0.305980</td>
      <td>-0.689109</td>
      <td>-1.055459</td>
      <td>-0.841838</td>
      <td>-0.371370</td>
      <td>-0.711392</td>
      <td>-0.295866</td>
      <td>0.274298</td>
      <td>0.284553</td>
      <td>0.223647</td>
      <td>0.142016</td>
      <td>0.303210</td>
      <td>-0.214250</td>
      <td>0.531633</td>
      <td>0.881869</td>
      <td>-0.084633</td>
      <td>0.201974</td>
      <td>0.335162</td>
      <td>-0.081187</td>
      <td>-0.161872</td>
      <td>-0.019279</td>
      <td>-0.338803</td>
      <td>1.276873</td>
      <td>0.303657</td>
      <td>-0.429949</td>
      <td>-0.615197</td>
      <td>0.466071</td>
      <td>-0.622509</td>
      <td>-0.743068</td>
      <td>-0.231377</td>
      <td>0.667964</td>
      <td>-0.273304</td>
      <td>0.269609</td>
      <td>0.227408</td>
      <td>-0.197849</td>
      <td>0.332939</td>
      <td>-0.161905</td>
      <td>0.942043</td>
      <td>0.931850</td>
      <td>-1.092100</td>
      <td>0.924517</td>
      <td>1.002808</td>
      <td>-0.382527</td>
      <td>0.301658</td>
      <td>-0.454747</td>
      <td>-0.307929</td>
      <td>0.221408</td>
      <td>-0.439433</td>
      <td>0.430437</td>
      <td>0.046361</td>
      <td>-0.517339</td>
      <td>0.145717</td>
      <td>-0.400849</td>
      <td>-0.667095</td>
      <td>-0.012357</td>
      <td>-1.207588</td>
      <td>0.807736</td>
      <td>0.406357</td>
      <td>0.064524</td>
      <td>0.383894</td>
      <td>0.164604</td>
      <td>-0.502739</td>
      <td>0.040856</td>
      <td>-0.882684</td>
      <td>0.359495</td>
      <td>0.440222</td>
      <td>0.485814</td>
      <td>0.221195</td>
      <td>1.360515</td>
      <td>1.599172</td>
      <td>0.731003</td>
      <td>-0.477798</td>
      <td>0.070876</td>
      <td>0.191761</td>
      <td>0.532749</td>
      <td>1.105603</td>
      <td>0.267540</td>
      <td>-0.639553</td>
      <td>-0.855283</td>
      <td>0.147026</td>
      <td>0.057897</td>
      <td>0.785535</td>
      <td>-0.081700</td>
      <td>-0.545349</td>
      <td>0.213000</td>
      <td>0.592005</td>
      <td>0.748316</td>
      <td>-0.563368</td>
      <td>-0.928531</td>
      <td>-0.510442</td>
      <td>-0.473006</td>
      <td>0.454142</td>
      <td>-0.805740</td>
      <td>0.356387</td>
      <td>-0.116305</td>
      <td>-0.320876</td>
      <td>0.477414</td>
      <td>0.302238</td>
      <td>-0.231163</td>
      <td>0.351962</td>
      <td>-0.358893</td>
      <td>-0.003318</td>
      <td>-0.891355</td>
      <td>-2.044916</td>
      <td>0.606713</td>
      <td>-0.667708</td>
      <td>0.059822</td>
      <td>0.389123</td>
      <td>-0.300478</td>
      <td>0.057675</td>
      <td>-0.246191</td>
      <td>-0.580048</td>
      <td>0.680889</td>
      <td>-0.141961</td>
      <td>-0.106902</td>
      <td>-0.550858</td>
      <td>-0.320739</td>
      <td>-0.134181</td>
      <td>-0.026509</td>
      <td>-0.066690</td>
      <td>-0.095640</td>
      <td>-0.065769</td>
      <td>-0.442901</td>
      <td>0.761149</td>
      <td>-0.018681</td>
      <td>-0.110448</td>
      <td>-1.095402</td>
      <td>0.368176</td>
      <td>0.109955</td>
      <td>-0.566827</td>
      <td>-0.582586</td>
      <td>0.209241</td>
      <td>-0.000042</td>
      <td>0.138035</td>
      <td>0.086425</td>
      <td>-0.554942</td>
      <td>-0.717621</td>
      <td>2.202401</td>
      <td>-0.408691</td>
      <td>-0.295622</td>
      <td>-0.117732</td>
      <td>0.183252</td>
      <td>-0.108103</td>
      <td>-0.106051</td>
      <td>-1.006895</td>
      <td>0.364127</td>
      <td>0.251180</td>
      <td>-0.319689</td>
      <td>-0.693574</td>
      <td>-0.017798</td>
      <td>0.245907</td>
      <td>0.162638</td>
      <td>-0.278162</td>
      <td>-0.179982</td>
      <td>0.094159</td>
      <td>-0.291661</td>
      <td>-0.216718</td>
      <td>-0.561475</td>
      <td>-0.014981</td>
      <td>-0.530955</td>
      <td>-0.006375</td>
      <td>-0.030234</td>
      <td>0.518931</td>
      <td>0.083313</td>
      <td>0.454087</td>
      <td>-0.113490</td>
      <td>0.578737</td>
      <td>1.085818</td>
      <td>-0.584714</td>
      <td>0.736271</td>
      <td>-0.461733</td>
      <td>0.464219</td>
      <td>0.050495</td>
      <td>0.005987</td>
      <td>0.453299</td>
      <td>0.798243</td>
      <td>1.709933</td>
      <td>-1.126989</td>
      <td>-0.249561</td>
      <td>-0.079504</td>
      <td>-0.845536</td>
      <td>-0.700666</td>
      <td>-0.149325</td>
      <td>0.450397</td>
      <td>0.041743</td>
      <td>0.301564</td>
      <td>-0.642822</td>
      <td>0.609112</td>
      <td>-0.245087</td>
      <td>0.247043</td>
      <td>-0.684046</td>
      <td>0.311036</td>
      <td>-0.548956</td>
      <td>0.555952</td>
      <td>1.887118</td>
      <td>-1.116340</td>
      <td>-0.277535</td>
      <td>0.317047</td>
      <td>0.042217</td>
      <td>0.710105</td>
      <td>0.262369</td>
      <td>-0.249614</td>
      <td>0.084665</td>
      <td>-0.738998</td>
      <td>0.667374</td>
      <td>0.133357</td>
      <td>-0.978729</td>
      <td>-0.445564</td>
      <td>0.135429</td>
      <td>0.860580</td>
      <td>-0.557871</td>
      <td>0.242296</td>
      <td>-0.277121</td>
      <td>-0.074369</td>
      <td>-1.176803</td>
      <td>0.231120</td>
      <td>-0.050961</td>
      <td>0.352287</td>
      <td>1.143143</td>
      <td>-0.561528</td>
      <td>0.590353</td>
      <td>-0.160088</td>
      <td>-0.348541</td>
      <td>0.369237</td>
      <td>0.918736</td>
      <td>-0.388912</td>
      <td>0.248872</td>
      <td>-0.605054</td>
      <td>-0.792266</td>
      <td>-1.485384</td>
      <td>0.871934</td>
      <td>0.321424</td>
      <td>0.237906</td>
      <td>-0.361960</td>
      <td>0.286155</td>
      <td>0.169783</td>
      <td>-0.058782</td>
      <td>-0.087182</td>
      <td>-0.099654</td>
      <td>-1.295256</td>
      <td>0.367107</td>
      <td>-0.099565</td>
      <td>0.222276</td>
      <td>-0.075341</td>
      <td>-0.063840</td>
      <td>-0.305425</td>
      <td>0.314852</td>
      <td>0.064779</td>
      <td>-0.297900</td>
      <td>0.461134</td>
      <td>-1.108811</td>
      <td>0.134607</td>
      <td>0.425517</td>
      <td>0.091881</td>
      <td>-0.541444</td>
      <td>0.083247</td>
      <td>-0.438550</td>
      <td>-0.614242</td>
      <td>0.714394</td>
      <td>-0.530811</td>
      <td>0.513687</td>
      <td>-0.295127</td>
      <td>-0.238534</td>
      <td>0.032495</td>
      <td>-0.650351</td>
      <td>-0.290390</td>
      <td>-0.663335</td>
      <td>0.134318</td>
      <td>-0.208988</td>
      <td>-0.769950</td>
      <td>-0.638198</td>
      <td>-0.217096</td>
      <td>-0.185611</td>
      <td>-0.864971</td>
      <td>0.213282</td>
      <td>-0.480346</td>
      <td>-0.388458</td>
      <td>-0.289738</td>
      <td>-1.067928</td>
      <td>0.562125</td>
      <td>-0.771466</td>
      <td>-0.439350</td>
      <td>0.115832</td>
      <td>0.259984</td>
      <td>0.661913</td>
      <td>1.398491</td>
      <td>0.077505</td>
      <td>-0.257406</td>
      <td>-0.120901</td>
      <td>-0.135660</td>
      <td>-0.349126</td>
      <td>-0.454890</td>
      <td>0.374445</td>
      <td>1.001620</td>
      <td>0.020246</td>
      <td>-1.164380</td>
      <td>0.677624</td>
      <td>-0.675024</td>
      <td>0.479976</td>
      <td>0.176144</td>
      <td>-0.632569</td>
      <td>0.721223</td>
      <td>-0.169555</td>
      <td>-0.309634</td>
      <td>-1.007182</td>
      <td>-1.132522</td>
      <td>-0.022704</td>
      <td>-0.251030</td>
      <td>0.213851</td>
      <td>0.162568</td>
      <td>0.877426</td>
      <td>0.360379</td>
      <td>0.341531</td>
      <td>0.324068</td>
      <td>-0.095818</td>
      <td>-0.533504</td>
      <td>0.241093</td>
      <td>-0.328424</td>
      <td>-0.731825</td>
      <td>0.445186</td>
      <td>0.404116</td>
      <td>-1.151567</td>
      <td>0.810269</td>
      <td>-0.520411</td>
      <td>0.391120</td>
      <td>-0.144799</td>
      <td>0.986076</td>
      <td>0.585793</td>
      <td>-0.467388</td>
      <td>-0.268722</td>
      <td>-0.086748</td>
      <td>-0.469381</td>
      <td>-0.772691</td>
      <td>0.680349</td>
      <td>0.034303</td>
      <td>-0.267351</td>
      <td>0.225955</td>
      <td>0.128668</td>
      <td>0.661509</td>
      <td>0.060459</td>
      <td>-0.529763</td>
      <td>-0.107971</td>
      <td>-0.231323</td>
      <td>-0.247448</td>
      <td>0.975092</td>
      <td>-0.761429</td>
      <td>-0.530032</td>
      <td>1.752743</td>
      <td>-0.182009</td>
      <td>0.358469</td>
      <td>-0.813893</td>
      <td>1.410308</td>
      <td>-0.054949</td>
      <td>-0.002532</td>
      <td>0.543068</td>
      <td>-0.178573</td>
      <td>-0.252189</td>
      <td>-0.030120</td>
      <td>-0.013982</td>
      <td>0.779594</td>
      <td>-0.325455</td>
      <td>-0.673586</td>
      <td>0.740969</td>
      <td>0.325987</td>
      <td>0.402764</td>
      <td>0.720558</td>
      <td>0.094328</td>
      <td>0.199247</td>
      <td>-0.058965</td>
      <td>0.164704</td>
      <td>-0.245245</td>
      <td>0.400885</td>
      <td>0.638725</td>
      <td>-1.261253</td>
      <td>0.473364</td>
      <td>0.326285</td>
      <td>-0.070620</td>
      <td>0.024775</td>
      <td>0.771061</td>
      <td>0.385674</td>
      <td>0.181822</td>
      <td>-0.155560</td>
      <td>0.327995</td>
      <td>-0.054430</td>
      <td>0.402235</td>
      <td>0.458426</td>
      <td>0.225718</td>
      <td>0.016444</td>
      <td>-0.197430</td>
      <td>-0.211057</td>
      <td>0.579698</td>
      <td>-0.655260</td>
      <td>0.354905</td>
      <td>0.184630</td>
      <td>-0.095312</td>
      <td>-0.529115</td>
      <td>-0.789998</td>
      <td>0.179581</td>
      <td>0.182480</td>
      <td>0.541619</td>
      <td>0.946776</td>
      <td>0.186959</td>
      <td>-0.131545</td>
      <td>-0.455468</td>
      <td>-0.872809</td>
      <td>0.910635</td>
      <td>-0.459866</td>
      <td>-0.009037</td>
      <td>-0.347245</td>
      <td>0.814138</td>
      <td>-0.192103</td>
      <td>-0.093455</td>
      <td>-0.118107</td>
      <td>-1.014525</td>
      <td>0.678258</td>
      <td>-0.066656</td>
      <td>0.749890</td>
      <td>-0.896060</td>
      <td>-0.044841</td>
      <td>0.221419</td>
      <td>-0.567997</td>
      <td>-0.195760</td>
      <td>-0.521557</td>
      <td>-0.046510</td>
      <td>-0.259608</td>
      <td>0.352229</td>
      <td>0.066496</td>
      <td>-0.260983</td>
      <td>0.152289</td>
      <td>0.179300</td>
      <td>0.134199</td>
      <td>0.236673</td>
      <td>0.275723</td>
      <td>-1.192024</td>
      <td>1.237189</td>
      <td>0.329102</td>
      <td>0.342117</td>
      <td>0.352382</td>
      <td>0.347773</td>
      <td>-0.512114</td>
      <td>0.766325</td>
      <td>3.978671</td>
      <td>0.054569</td>
      <td>-0.202102</td>
      <td>0.209590</td>
      <td>11.523810</td>
      <td>542.726</td>
      <td>0</td>
      <td>0.333333</td>
      <td>3.051372</td>
      <td>1.888931</td>
      <td>1790.652271</td>
      <td>10.012433</td>
      <td>10.392685</td>
      <td>4.653369</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>249.752719</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>11.374773</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>27.694949</td>
      <td>11.467335</td>
      <td>143.581147</td>
      <td>55.634515</td>
      <td>11.467335</td>
      <td>11.374773</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>27.694949</td>
      <td>22.253806</td>
      <td>0.000000</td>
      <td>55.634515</td>
      <td>24.06</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>89.263093</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>160.489625</td>
      <td>0.000000</td>
      <td>-0.147532</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>27.622010</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>8.669733</td>
      <td>0.000000</td>
      <td>0.100000</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>4</td>
      <td>0</td>
      <td>7</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>6</td>
      <td>11.00768</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>4</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>13</td>
      <td>6.004228</td>
      <td>6</td>
    </tr>
    <tr>
      <th>4</th>
      <td>*Oc1ccc(OC(=O)c2cc(OCCCCCCCCCOCC3CCCN3c3ccc([N...</td>
      <td>0.323456</td>
      <td>0.772586</td>
      <td>0.386539</td>
      <td>0.048928</td>
      <td>0.481003</td>
      <td>-0.283030</td>
      <td>0.079848</td>
      <td>0.197975</td>
      <td>0.956270</td>
      <td>0.682721</td>
      <td>-0.368510</td>
      <td>1.009021</td>
      <td>-1.270735</td>
      <td>-0.267640</td>
      <td>-0.233844</td>
      <td>-1.210203</td>
      <td>-0.816003</td>
      <td>1.551518</td>
      <td>0.785232</td>
      <td>0.076447</td>
      <td>0.017730</td>
      <td>0.112157</td>
      <td>0.897030</td>
      <td>0.875061</td>
      <td>0.644346</td>
      <td>-0.111329</td>
      <td>0.575103</td>
      <td>0.516992</td>
      <td>0.280291</td>
      <td>0.300855</td>
      <td>-0.242236</td>
      <td>0.774888</td>
      <td>-0.231786</td>
      <td>0.591304</td>
      <td>-0.438052</td>
      <td>-0.564220</td>
      <td>-0.210694</td>
      <td>-0.318544</td>
      <td>0.290624</td>
      <td>-0.816716</td>
      <td>0.828891</td>
      <td>0.001195</td>
      <td>0.150504</td>
      <td>0.412268</td>
      <td>-1.090692</td>
      <td>0.137405</td>
      <td>0.878525</td>
      <td>-0.812574</td>
      <td>-1.519914</td>
      <td>-0.185504</td>
      <td>0.225171</td>
      <td>0.160554</td>
      <td>0.815732</td>
      <td>0.276307</td>
      <td>0.248803</td>
      <td>-0.342660</td>
      <td>-0.449753</td>
      <td>-0.385022</td>
      <td>-1.168329</td>
      <td>-0.921686</td>
      <td>-0.527090</td>
      <td>0.808297</td>
      <td>-0.855929</td>
      <td>0.130601</td>
      <td>0.749978</td>
      <td>1.271351</td>
      <td>-0.071747</td>
      <td>-0.935057</td>
      <td>-0.053033</td>
      <td>0.762110</td>
      <td>-0.748607</td>
      <td>-0.059108</td>
      <td>1.180702</td>
      <td>-1.099589</td>
      <td>-0.948410</td>
      <td>-0.734586</td>
      <td>0.635407</td>
      <td>-0.141151</td>
      <td>-0.418939</td>
      <td>-0.178697</td>
      <td>-0.844557</td>
      <td>-0.187525</td>
      <td>-0.103368</td>
      <td>-0.242855</td>
      <td>0.684091</td>
      <td>0.011924</td>
      <td>0.092539</td>
      <td>-0.456563</td>
      <td>-0.131757</td>
      <td>-0.534090</td>
      <td>0.235800</td>
      <td>-0.197390</td>
      <td>0.302049</td>
      <td>0.049328</td>
      <td>-0.003643</td>
      <td>0.325612</td>
      <td>0.227539</td>
      <td>-0.802713</td>
      <td>-1.214568</td>
      <td>0.294397</td>
      <td>-0.583980</td>
      <td>0.380575</td>
      <td>-0.724097</td>
      <td>-0.430305</td>
      <td>1.511758</td>
      <td>0.757906</td>
      <td>-1.389867</td>
      <td>0.416948</td>
      <td>0.044172</td>
      <td>0.872102</td>
      <td>1.327892</td>
      <td>-1.054470</td>
      <td>-0.028384</td>
      <td>0.697021</td>
      <td>-0.136594</td>
      <td>-0.202348</td>
      <td>1.511546</td>
      <td>-1.243684</td>
      <td>0.263128</td>
      <td>-1.433390</td>
      <td>-0.925884</td>
      <td>-0.884792</td>
      <td>-0.637644</td>
      <td>1.037147</td>
      <td>-0.117449</td>
      <td>-1.016345</td>
      <td>1.141593</td>
      <td>0.759252</td>
      <td>-0.810711</td>
      <td>0.905581</td>
      <td>-2.660094</td>
      <td>0.333272</td>
      <td>1.454076</td>
      <td>-0.792495</td>
      <td>-1.339515</td>
      <td>-0.196319</td>
      <td>-0.540185</td>
      <td>0.525782</td>
      <td>-0.725895</td>
      <td>-1.659007</td>
      <td>1.750986</td>
      <td>0.581341</td>
      <td>-1.275510</td>
      <td>-0.071592</td>
      <td>0.889073</td>
      <td>-0.192285</td>
      <td>0.349641</td>
      <td>-0.470932</td>
      <td>0.231483</td>
      <td>0.849415</td>
      <td>-0.668341</td>
      <td>-0.545568</td>
      <td>-0.615997</td>
      <td>-0.211090</td>
      <td>-0.775042</td>
      <td>1.182619</td>
      <td>-0.074363</td>
      <td>-1.199648</td>
      <td>-0.433666</td>
      <td>-1.226278</td>
      <td>0.641453</td>
      <td>0.010039</td>
      <td>-0.803596</td>
      <td>-0.723297</td>
      <td>-1.601090</td>
      <td>-0.027234</td>
      <td>1.058062</td>
      <td>-1.043442</td>
      <td>-0.083401</td>
      <td>0.810765</td>
      <td>0.567987</td>
      <td>-0.364889</td>
      <td>-1.667680</td>
      <td>0.222939</td>
      <td>-0.320892</td>
      <td>0.099331</td>
      <td>0.619392</td>
      <td>-0.239532</td>
      <td>-0.708161</td>
      <td>-0.632428</td>
      <td>-1.636409</td>
      <td>-0.135736</td>
      <td>0.283493</td>
      <td>-0.197793</td>
      <td>-1.114758</td>
      <td>-1.211681</td>
      <td>0.353042</td>
      <td>-1.342913</td>
      <td>-1.622461</td>
      <td>0.030191</td>
      <td>0.321704</td>
      <td>0.745573</td>
      <td>0.162898</td>
      <td>-1.157992</td>
      <td>-1.542758</td>
      <td>0.336626</td>
      <td>0.902547</td>
      <td>0.689437</td>
      <td>1.557853</td>
      <td>0.192885</td>
      <td>0.646809</td>
      <td>-0.216059</td>
      <td>0.385206</td>
      <td>-0.563259</td>
      <td>-0.036592</td>
      <td>0.171146</td>
      <td>-0.886134</td>
      <td>0.207762</td>
      <td>0.189809</td>
      <td>-0.422294</td>
      <td>-0.329299</td>
      <td>-0.281013</td>
      <td>-0.500972</td>
      <td>1.036327</td>
      <td>-0.898523</td>
      <td>0.703245</td>
      <td>-0.654654</td>
      <td>0.705442</td>
      <td>-0.397604</td>
      <td>-0.231113</td>
      <td>0.806720</td>
      <td>-0.082496</td>
      <td>-1.162582</td>
      <td>0.229351</td>
      <td>1.390550</td>
      <td>1.001570</td>
      <td>0.237422</td>
      <td>1.621518</td>
      <td>0.064797</td>
      <td>0.567036</td>
      <td>0.116644</td>
      <td>-0.833335</td>
      <td>-1.006507</td>
      <td>-0.296934</td>
      <td>0.492672</td>
      <td>1.290939</td>
      <td>-1.545618</td>
      <td>-0.138803</td>
      <td>0.659129</td>
      <td>0.453242</td>
      <td>0.715649</td>
      <td>-0.859054</td>
      <td>0.009233</td>
      <td>-0.383611</td>
      <td>-0.057288</td>
      <td>-0.560714</td>
      <td>0.014999</td>
      <td>-0.460230</td>
      <td>-0.289730</td>
      <td>-1.005527</td>
      <td>0.862879</td>
      <td>0.336887</td>
      <td>-0.811319</td>
      <td>0.273050</td>
      <td>-0.008443</td>
      <td>-0.797954</td>
      <td>1.006920</td>
      <td>0.272820</td>
      <td>-0.835166</td>
      <td>0.666434</td>
      <td>0.472506</td>
      <td>0.453626</td>
      <td>-0.287401</td>
      <td>1.116223</td>
      <td>0.291698</td>
      <td>0.371337</td>
      <td>-0.774072</td>
      <td>-0.099993</td>
      <td>0.581595</td>
      <td>1.331282</td>
      <td>-0.255290</td>
      <td>0.005498</td>
      <td>-0.516882</td>
      <td>0.858396</td>
      <td>0.406599</td>
      <td>-0.072314</td>
      <td>-0.344028</td>
      <td>-0.587158</td>
      <td>0.039840</td>
      <td>-0.161357</td>
      <td>1.331976</td>
      <td>0.335097</td>
      <td>-0.100706</td>
      <td>-0.259921</td>
      <td>-0.075431</td>
      <td>-0.694635</td>
      <td>-0.115643</td>
      <td>0.260637</td>
      <td>-0.995851</td>
      <td>0.204790</td>
      <td>0.019907</td>
      <td>0.303785</td>
      <td>-0.596376</td>
      <td>-0.962781</td>
      <td>-1.855764</td>
      <td>0.440289</td>
      <td>2.227218</td>
      <td>-0.609238</td>
      <td>0.041948</td>
      <td>0.034242</td>
      <td>-0.524096</td>
      <td>0.576113</td>
      <td>0.363212</td>
      <td>-0.863858</td>
      <td>-0.484575</td>
      <td>-0.475575</td>
      <td>0.176486</td>
      <td>-0.115634</td>
      <td>-0.283519</td>
      <td>0.661865</td>
      <td>-0.710654</td>
      <td>-1.294225</td>
      <td>0.093957</td>
      <td>0.151895</td>
      <td>0.224292</td>
      <td>-1.839996</td>
      <td>-0.288921</td>
      <td>-0.137508</td>
      <td>-0.274795</td>
      <td>-0.035396</td>
      <td>0.086671</td>
      <td>0.406306</td>
      <td>-0.643248</td>
      <td>0.583969</td>
      <td>-0.338633</td>
      <td>0.510605</td>
      <td>1.722347</td>
      <td>0.089333</td>
      <td>0.316294</td>
      <td>-0.897962</td>
      <td>-0.601863</td>
      <td>1.288212</td>
      <td>0.686687</td>
      <td>-1.106199</td>
      <td>1.470055</td>
      <td>0.744059</td>
      <td>-0.491998</td>
      <td>-0.203508</td>
      <td>0.418443</td>
      <td>-0.743655</td>
      <td>1.061211</td>
      <td>-0.148230</td>
      <td>-0.608157</td>
      <td>-0.295852</td>
      <td>1.345382</td>
      <td>0.068043</td>
      <td>-0.799498</td>
      <td>-0.626036</td>
      <td>-0.565879</td>
      <td>-1.035985</td>
      <td>0.526970</td>
      <td>0.606670</td>
      <td>1.038157</td>
      <td>0.641401</td>
      <td>-1.330611</td>
      <td>-0.608644</td>
      <td>1.045284</td>
      <td>0.692075</td>
      <td>0.551626</td>
      <td>-0.772872</td>
      <td>-0.772197</td>
      <td>-0.623218</td>
      <td>0.866605</td>
      <td>0.236209</td>
      <td>1.215714</td>
      <td>-1.653800</td>
      <td>0.689364</td>
      <td>0.686067</td>
      <td>-0.394112</td>
      <td>0.656420</td>
      <td>-0.118852</td>
      <td>-1.105597</td>
      <td>0.739839</td>
      <td>0.171980</td>
      <td>-0.528595</td>
      <td>0.029419</td>
      <td>-0.807721</td>
      <td>1.215308</td>
      <td>0.166460</td>
      <td>-0.207849</td>
      <td>0.067795</td>
      <td>-1.255595</td>
      <td>1.052737</td>
      <td>-0.136321</td>
      <td>-0.149279</td>
      <td>1.052849</td>
      <td>-0.733359</td>
      <td>0.245689</td>
      <td>-1.052915</td>
      <td>0.315443</td>
      <td>1.416277</td>
      <td>-0.683395</td>
      <td>-0.157673</td>
      <td>0.901015</td>
      <td>0.225359</td>
      <td>-0.434480</td>
      <td>0.616690</td>
      <td>-0.313324</td>
      <td>0.566059</td>
      <td>-0.016831</td>
      <td>0.718053</td>
      <td>-0.076529</td>
      <td>-0.128260</td>
      <td>-0.222344</td>
      <td>0.308953</td>
      <td>1.164957</td>
      <td>-0.721460</td>
      <td>1.148702</td>
      <td>-1.023593</td>
      <td>0.528262</td>
      <td>0.307897</td>
      <td>0.766743</td>
      <td>0.362857</td>
      <td>0.387408</td>
      <td>0.179084</td>
      <td>-0.783186</td>
      <td>-0.273387</td>
      <td>0.195696</td>
      <td>0.797569</td>
      <td>0.730339</td>
      <td>-1.457498</td>
      <td>-0.612265</td>
      <td>-0.821990</td>
      <td>-0.137639</td>
      <td>0.520463</td>
      <td>-0.526389</td>
      <td>-0.336302</td>
      <td>1.695502</td>
      <td>-0.605621</td>
      <td>-1.024113</td>
      <td>1.305905</td>
      <td>0.396805</td>
      <td>0.140655</td>
      <td>0.889238</td>
      <td>-0.571520</td>
      <td>0.286238</td>
      <td>-0.108018</td>
      <td>-0.906553</td>
      <td>-0.189621</td>
      <td>-0.964412</td>
      <td>-0.132734</td>
      <td>0.051607</td>
      <td>-0.074221</td>
      <td>0.399862</td>
      <td>-1.080039</td>
      <td>0.797139</td>
      <td>-0.978408</td>
      <td>1.452604</td>
      <td>0.628091</td>
      <td>0.071627</td>
      <td>0.985966</td>
      <td>0.681617</td>
      <td>-0.081677</td>
      <td>0.692063</td>
      <td>0.300380</td>
      <td>0.255595</td>
      <td>2.063870</td>
      <td>0.366959</td>
      <td>0.061742</td>
      <td>-0.587219</td>
      <td>-0.437656</td>
      <td>0.151672</td>
      <td>0.482839</td>
      <td>0.011816</td>
      <td>-1.435575</td>
      <td>0.196079</td>
      <td>-0.518011</td>
      <td>-0.378029</td>
      <td>0.870996</td>
      <td>1.049979</td>
      <td>-0.333790</td>
      <td>0.084514</td>
      <td>-0.102233</td>
      <td>0.092446</td>
      <td>-0.551008</td>
      <td>0.340350</td>
      <td>0.105296</td>
      <td>-1.262446</td>
      <td>0.011304</td>
      <td>0.851788</td>
      <td>-0.410942</td>
      <td>-0.804363</td>
      <td>0.566777</td>
      <td>-0.072651</td>
      <td>-1.177064</td>
      <td>0.926176</td>
      <td>-0.249265</td>
      <td>-0.319707</td>
      <td>-0.267015</td>
      <td>-0.710472</td>
      <td>0.279911</td>
      <td>-1.130472</td>
      <td>-0.556989</td>
      <td>0.458059</td>
      <td>-0.896230</td>
      <td>-0.204939</td>
      <td>-0.645438</td>
      <td>-0.293469</td>
      <td>-0.754964</td>
      <td>-0.357063</td>
      <td>0.932564</td>
      <td>-0.701900</td>
      <td>-1.116435</td>
      <td>1.326835</td>
      <td>0.072081</td>
      <td>-0.984443</td>
      <td>1.880576</td>
      <td>-0.736887</td>
      <td>-0.648010</td>
      <td>-1.200081</td>
      <td>-0.035490</td>
      <td>0.009921</td>
      <td>0.284724</td>
      <td>0.457373</td>
      <td>-0.815244</td>
      <td>-0.001282</td>
      <td>-0.144064</td>
      <td>0.586186</td>
      <td>1.498026</td>
      <td>0.675890</td>
      <td>-1.684096</td>
      <td>-0.955427</td>
      <td>0.139095</td>
      <td>1.342030</td>
      <td>0.608290</td>
      <td>0.523483</td>
      <td>-0.463347</td>
      <td>0.633195</td>
      <td>-1.449158</td>
      <td>-0.095759</td>
      <td>0.525527</td>
      <td>0.417797</td>
      <td>-0.805992</td>
      <td>1.000409</td>
      <td>-0.311992</td>
      <td>0.303378</td>
      <td>0.552820</td>
      <td>0.423284</td>
      <td>0.321952</td>
      <td>0.592844</td>
      <td>-0.397171</td>
      <td>0.930970</td>
      <td>0.704158</td>
      <td>-0.470991</td>
      <td>0.281200</td>
      <td>-0.020204</td>
      <td>0.736295</td>
      <td>1.357577</td>
      <td>0.244609</td>
      <td>-0.415514</td>
      <td>-0.023708</td>
      <td>-0.550218</td>
      <td>0.562090</td>
      <td>-0.734950</td>
      <td>-0.152218</td>
      <td>0.879321</td>
      <td>-0.439503</td>
      <td>-1.050429</td>
      <td>-0.318238</td>
      <td>1.487931</td>
      <td>0.616048</td>
      <td>-0.403156</td>
      <td>0.819758</td>
      <td>0.503268</td>
      <td>2.293994</td>
      <td>0.249153</td>
      <td>0.994836</td>
      <td>0.500227</td>
      <td>-0.122992</td>
      <td>0.785568</td>
      <td>-0.814575</td>
      <td>0.472981</td>
      <td>-0.930922</td>
      <td>-0.675449</td>
      <td>-0.048042</td>
      <td>1.107362</td>
      <td>-0.385580</td>
      <td>0.587645</td>
      <td>0.113074</td>
      <td>0.245343</td>
      <td>0.532538</td>
      <td>-0.200504</td>
      <td>0.118574</td>
      <td>-0.390616</td>
      <td>0.031447</td>
      <td>0.201827</td>
      <td>-0.112767</td>
      <td>0.737748</td>
      <td>-0.214000</td>
      <td>-0.017068</td>
      <td>-0.189297</td>
      <td>0.076343</td>
      <td>-0.386076</td>
      <td>1.102986</td>
      <td>0.494934</td>
      <td>0.462252</td>
      <td>-0.440645</td>
      <td>-0.339849</td>
      <td>-0.057771</td>
      <td>-0.572652</td>
      <td>13.703218</td>
      <td>0.068062</td>
      <td>-0.686332</td>
      <td>0.014164</td>
      <td>15.885714</td>
      <td>965.154</td>
      <td>0</td>
      <td>0.557143</td>
      <td>4.071253</td>
      <td>0.862097</td>
      <td>2305.266872</td>
      <td>15.917240</td>
      <td>27.216224</td>
      <td>15.886846</td>
      <td>19.273545</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>11.374773</td>
      <td>208.369254</td>
      <td>20.228637</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>56.369576</td>
      <td>61.943518</td>
      <td>35.144068</td>
      <td>48.226539</td>
      <td>35.005011</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>127.658471</td>
      <td>62.530624</td>
      <td>116.284678</td>
      <td>22.998047</td>
      <td>29.116936</td>
      <td>22.749545</td>
      <td>22.998047</td>
      <td>0.000000</td>
      <td>86.916574</td>
      <td>9.473726</td>
      <td>20.228637</td>
      <td>136.290767</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>182.28</td>
      <td>5.969305</td>
      <td>29.817711</td>
      <td>0.0</td>
      <td>39.112847</td>
      <td>55.281356</td>
      <td>159.002350</td>
      <td>0.000000</td>
      <td>60.663671</td>
      <td>24.265468</td>
      <td>9.799819</td>
      <td>28.790842</td>
      <td>29.433681</td>
      <td>0.000000</td>
      <td>52.672699</td>
      <td>27.589702</td>
      <td>2.927775</td>
      <td>1.302622</td>
      <td>18.810591</td>
      <td>5.473191</td>
      <td>0.000000</td>
      <td>0.518519</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>14</td>
      <td>18</td>
      <td>2</td>
      <td>34</td>
      <td>0</td>
      <td>2</td>
      <td>2</td>
      <td>0</td>
      <td>2</td>
      <td>6</td>
      <td>11.84500</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>4</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>1</td>
      <td>5</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>12</td>
      <td>0</td>
      <td>43</td>
      <td>15.644757</td>
      <td>6</td>
    </tr>
    <tr>
      <th>...</th>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
      <td>...</td>
    </tr>
    <tr>
      <th>10075</th>
      <td>*c1cccc(OCCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5...</td>
      <td>0.616178</td>
      <td>-0.372379</td>
      <td>0.827211</td>
      <td>-0.220061</td>
      <td>-0.160907</td>
      <td>-0.112867</td>
      <td>0.510957</td>
      <td>0.577699</td>
      <td>0.315607</td>
      <td>-0.580286</td>
      <td>0.204469</td>
      <td>0.571226</td>
      <td>0.015522</td>
      <td>-0.023693</td>
      <td>0.625814</td>
      <td>-0.761342</td>
      <td>-0.636597</td>
      <td>-0.067812</td>
      <td>-0.415489</td>
      <td>0.545913</td>
      <td>0.473940</td>
      <td>0.905620</td>
      <td>-0.318485</td>
      <td>-0.212192</td>
      <td>0.397408</td>
      <td>0.733399</td>
      <td>1.221423</td>
      <td>0.283195</td>
      <td>-0.840059</td>
      <td>0.146222</td>
      <td>0.640815</td>
      <td>0.106558</td>
      <td>0.354297</td>
      <td>-0.246557</td>
      <td>0.205284</td>
      <td>-0.210262</td>
      <td>-0.857292</td>
      <td>0.223168</td>
      <td>-0.251894</td>
      <td>-0.567201</td>
      <td>0.363251</td>
      <td>-0.413099</td>
      <td>-0.147866</td>
      <td>-0.275415</td>
      <td>0.148785</td>
      <td>-0.168571</td>
      <td>-0.002715</td>
      <td>-0.156968</td>
      <td>-0.337097</td>
      <td>-0.105100</td>
      <td>-0.089041</td>
      <td>0.682495</td>
      <td>0.144343</td>
      <td>-0.051195</td>
      <td>0.014959</td>
      <td>-0.000235</td>
      <td>-0.263062</td>
      <td>0.615970</td>
      <td>-0.407384</td>
      <td>-0.577887</td>
      <td>-1.260933</td>
      <td>-0.332232</td>
      <td>-0.322691</td>
      <td>0.395709</td>
      <td>0.432264</td>
      <td>-0.356137</td>
      <td>0.256040</td>
      <td>-0.517709</td>
      <td>-0.089578</td>
      <td>-0.625072</td>
      <td>-0.183146</td>
      <td>-0.238677</td>
      <td>0.419303</td>
      <td>0.507382</td>
      <td>-0.196561</td>
      <td>0.020726</td>
      <td>-0.187398</td>
      <td>-0.498005</td>
      <td>-0.281962</td>
      <td>-1.521897</td>
      <td>0.093389</td>
      <td>-0.055460</td>
      <td>-0.517049</td>
      <td>-0.394374</td>
      <td>0.118537</td>
      <td>-0.125365</td>
      <td>-0.207415</td>
      <td>-0.016055</td>
      <td>-0.327515</td>
      <td>-0.133943</td>
      <td>-0.178374</td>
      <td>-0.421882</td>
      <td>-0.295139</td>
      <td>0.270281</td>
      <td>-0.310637</td>
      <td>-0.450279</td>
      <td>0.811303</td>
      <td>0.932409</td>
      <td>-0.201798</td>
      <td>-0.316869</td>
      <td>-0.908754</td>
      <td>0.117433</td>
      <td>0.530375</td>
      <td>0.260520</td>
      <td>-0.376776</td>
      <td>-0.267058</td>
      <td>0.479851</td>
      <td>0.139955</td>
      <td>0.384326</td>
      <td>0.985000</td>
      <td>1.018749</td>
      <td>-1.166946</td>
      <td>-0.053196</td>
      <td>-0.380904</td>
      <td>-0.942394</td>
      <td>0.082390</td>
      <td>0.231086</td>
      <td>0.391872</td>
      <td>-0.682215</td>
      <td>0.143533</td>
      <td>-0.598567</td>
      <td>-0.200245</td>
      <td>-0.197146</td>
      <td>-0.400229</td>
      <td>-0.675775</td>
      <td>-0.136041</td>
      <td>-0.062027</td>
      <td>-0.379198</td>
      <td>-0.353183</td>
      <td>-0.077733</td>
      <td>-1.086660</td>
      <td>1.060779</td>
      <td>-0.819484</td>
      <td>-1.799221</td>
      <td>-0.806428</td>
      <td>-0.887297</td>
      <td>0.156700</td>
      <td>0.696324</td>
      <td>0.111072</td>
      <td>-0.498760</td>
      <td>1.453640</td>
      <td>-0.205071</td>
      <td>0.116372</td>
      <td>-0.663767</td>
      <td>0.680156</td>
      <td>0.613494</td>
      <td>0.119756</td>
      <td>-0.830333</td>
      <td>0.332440</td>
      <td>-0.018190</td>
      <td>-0.336802</td>
      <td>-0.484241</td>
      <td>0.210735</td>
      <td>-0.567414</td>
      <td>0.388507</td>
      <td>-0.110614</td>
      <td>0.466471</td>
      <td>0.150954</td>
      <td>0.546763</td>
      <td>0.090211</td>
      <td>-0.655835</td>
      <td>0.033135</td>
      <td>0.113850</td>
      <td>0.728874</td>
      <td>0.229733</td>
      <td>-0.062490</td>
      <td>0.817401</td>
      <td>-0.715517</td>
      <td>0.123251</td>
      <td>1.339944</td>
      <td>0.394191</td>
      <td>-0.059797</td>
      <td>-0.446348</td>
      <td>-0.000336</td>
      <td>-0.406413</td>
      <td>-0.861016</td>
      <td>-0.727955</td>
      <td>-0.619865</td>
      <td>-0.584157</td>
      <td>0.439021</td>
      <td>-0.413226</td>
      <td>0.519443</td>
      <td>-0.682748</td>
      <td>0.214396</td>
      <td>0.054919</td>
      <td>-1.015310</td>
      <td>-0.030946</td>
      <td>-0.904060</td>
      <td>-1.092322</td>
      <td>0.366349</td>
      <td>-0.367003</td>
      <td>-0.217758</td>
      <td>0.339714</td>
      <td>-0.916309</td>
      <td>0.482513</td>
      <td>-0.577502</td>
      <td>0.646305</td>
      <td>1.136849</td>
      <td>0.482648</td>
      <td>0.172281</td>
      <td>1.036665</td>
      <td>-1.274155</td>
      <td>-0.222621</td>
      <td>-0.793749</td>
      <td>-0.389236</td>
      <td>-0.060236</td>
      <td>-1.269582</td>
      <td>0.367682</td>
      <td>-0.010121</td>
      <td>0.280298</td>
      <td>0.056048</td>
      <td>0.055877</td>
      <td>-0.064449</td>
      <td>-0.215293</td>
      <td>-0.389586</td>
      <td>0.065534</td>
      <td>0.536466</td>
      <td>-0.294482</td>
      <td>0.465579</td>
      <td>0.662476</td>
      <td>0.031301</td>
      <td>-0.174525</td>
      <td>-0.445866</td>
      <td>0.529891</td>
      <td>1.369746</td>
      <td>0.508223</td>
      <td>0.594332</td>
      <td>0.862818</td>
      <td>0.109160</td>
      <td>0.413358</td>
      <td>0.031872</td>
      <td>-0.508232</td>
      <td>-0.117902</td>
      <td>-0.383465</td>
      <td>0.290055</td>
      <td>-0.042472</td>
      <td>0.227383</td>
      <td>-0.546792</td>
      <td>0.585610</td>
      <td>-0.717776</td>
      <td>0.590231</td>
      <td>0.494783</td>
      <td>1.299348</td>
      <td>-1.172259</td>
      <td>0.569381</td>
      <td>-0.103920</td>
      <td>-0.059860</td>
      <td>-0.289710</td>
      <td>-0.040313</td>
      <td>0.310012</td>
      <td>-0.357653</td>
      <td>-0.320246</td>
      <td>-0.935720</td>
      <td>0.726856</td>
      <td>0.961206</td>
      <td>0.514978</td>
      <td>0.036912</td>
      <td>-0.284885</td>
      <td>-0.417269</td>
      <td>0.307077</td>
      <td>0.007805</td>
      <td>-0.248514</td>
      <td>0.268277</td>
      <td>0.636303</td>
      <td>0.211678</td>
      <td>-0.278750</td>
      <td>0.198968</td>
      <td>0.325429</td>
      <td>0.458616</td>
      <td>0.062874</td>
      <td>-0.962899</td>
      <td>-0.304764</td>
      <td>-0.204432</td>
      <td>-0.025204</td>
      <td>0.268840</td>
      <td>-0.409197</td>
      <td>-0.072771</td>
      <td>0.622296</td>
      <td>-0.169646</td>
      <td>0.124009</td>
      <td>1.002557</td>
      <td>0.426718</td>
      <td>-0.215037</td>
      <td>-0.020707</td>
      <td>-0.141954</td>
      <td>-0.236400</td>
      <td>0.290615</td>
      <td>-0.619618</td>
      <td>-0.547441</td>
      <td>0.301200</td>
      <td>0.041925</td>
      <td>0.699936</td>
      <td>0.195976</td>
      <td>-0.383207</td>
      <td>-0.907184</td>
      <td>-0.214402</td>
      <td>2.200909</td>
      <td>-0.312563</td>
      <td>-0.915065</td>
      <td>0.043337</td>
      <td>0.212300</td>
      <td>0.266848</td>
      <td>0.514077</td>
      <td>-0.785817</td>
      <td>0.329056</td>
      <td>-0.196299</td>
      <td>-0.490946</td>
      <td>-0.773095</td>
      <td>-0.278293</td>
      <td>0.404505</td>
      <td>0.365144</td>
      <td>-1.371800</td>
      <td>0.655832</td>
      <td>0.732119</td>
      <td>-0.191706</td>
      <td>-0.320038</td>
      <td>0.553846</td>
      <td>-0.402527</td>
      <td>-0.957251</td>
      <td>-0.415041</td>
      <td>0.412197</td>
      <td>0.601548</td>
      <td>-0.446541</td>
      <td>-0.300442</td>
      <td>-0.476229</td>
      <td>-0.528278</td>
      <td>0.362354</td>
      <td>0.198180</td>
      <td>0.497343</td>
      <td>-0.913899</td>
      <td>-0.127155</td>
      <td>0.689704</td>
      <td>-0.238646</td>
      <td>-0.172493</td>
      <td>1.108683</td>
      <td>1.037371</td>
      <td>-0.506778</td>
      <td>-0.020496</td>
      <td>-0.065087</td>
      <td>-0.330379</td>
      <td>0.559624</td>
      <td>-0.218849</td>
      <td>0.210761</td>
      <td>0.374307</td>
      <td>1.266718</td>
      <td>-0.235842</td>
      <td>-0.069574</td>
      <td>-0.485794</td>
      <td>-0.290889</td>
      <td>-0.291528</td>
      <td>-0.289026</td>
      <td>-0.728397</td>
      <td>0.173265</td>
      <td>1.204371</td>
      <td>0.499214</td>
      <td>-0.189054</td>
      <td>0.424398</td>
      <td>-0.113700</td>
      <td>0.360033</td>
      <td>0.469654</td>
      <td>-0.207827</td>
      <td>-0.793032</td>
      <td>0.599065</td>
      <td>0.540117</td>
      <td>0.753244</td>
      <td>0.008980</td>
      <td>-0.158306</td>
      <td>0.234995</td>
      <td>-0.533956</td>
      <td>-0.486578</td>
      <td>-0.113325</td>
      <td>0.554826</td>
      <td>0.540038</td>
      <td>-0.670827</td>
      <td>0.406082</td>
      <td>0.981819</td>
      <td>0.259358</td>
      <td>-0.160234</td>
      <td>-1.108034</td>
      <td>0.024555</td>
      <td>0.028324</td>
      <td>-1.044833</td>
      <td>0.875568</td>
      <td>-0.426326</td>
      <td>0.391547</td>
      <td>0.266457</td>
      <td>-0.463706</td>
      <td>-0.450052</td>
      <td>-0.472539</td>
      <td>0.862161</td>
      <td>0.613252</td>
      <td>0.552633</td>
      <td>0.554034</td>
      <td>0.262729</td>
      <td>0.996067</td>
      <td>0.720839</td>
      <td>0.026816</td>
      <td>-0.172422</td>
      <td>-0.694585</td>
      <td>0.225201</td>
      <td>0.084322</td>
      <td>0.069017</td>
      <td>-0.470959</td>
      <td>-0.518491</td>
      <td>0.161418</td>
      <td>0.550598</td>
      <td>0.043541</td>
      <td>-0.048277</td>
      <td>-1.224783</td>
      <td>0.230476</td>
      <td>0.813802</td>
      <td>0.615724</td>
      <td>-0.515396</td>
      <td>0.335814</td>
      <td>0.370126</td>
      <td>-0.289566</td>
      <td>-0.245997</td>
      <td>-0.265226</td>
      <td>0.004082</td>
      <td>-0.141697</td>
      <td>-0.934689</td>
      <td>-0.175549</td>
      <td>-0.006342</td>
      <td>0.391669</td>
      <td>0.177749</td>
      <td>0.430677</td>
      <td>0.010312</td>
      <td>0.242740</td>
      <td>0.235943</td>
      <td>-1.153407</td>
      <td>-0.806559</td>
      <td>0.696440</td>
      <td>-0.084603</td>
      <td>0.154196</td>
      <td>-0.667880</td>
      <td>0.137536</td>
      <td>0.367816</td>
      <td>-0.454290</td>
      <td>-0.460461</td>
      <td>-0.961644</td>
      <td>0.099618</td>
      <td>-0.062371</td>
      <td>0.762004</td>
      <td>0.699142</td>
      <td>-0.123786</td>
      <td>-0.153035</td>
      <td>-0.612949</td>
      <td>-0.173216</td>
      <td>0.307143</td>
      <td>0.211644</td>
      <td>0.234898</td>
      <td>-0.526869</td>
      <td>0.429817</td>
      <td>0.683001</td>
      <td>-0.123276</td>
      <td>0.117275</td>
      <td>-0.425007</td>
      <td>0.373305</td>
      <td>-0.381587</td>
      <td>-0.108203</td>
      <td>0.417717</td>
      <td>-0.656543</td>
      <td>0.091165</td>
      <td>0.232034</td>
      <td>-0.486220</td>
      <td>-0.621355</td>
      <td>-0.799634</td>
      <td>1.038014</td>
      <td>-0.072339</td>
      <td>0.439365</td>
      <td>0.132623</td>
      <td>-0.226423</td>
      <td>-0.056602</td>
      <td>-0.189336</td>
      <td>0.253008</td>
      <td>-0.119866</td>
      <td>-0.073500</td>
      <td>-0.325865</td>
      <td>0.509039</td>
      <td>0.568038</td>
      <td>-0.485626</td>
      <td>0.254580</td>
      <td>0.233677</td>
      <td>-0.085338</td>
      <td>0.044397</td>
      <td>-0.332724</td>
      <td>-0.180575</td>
      <td>-0.227590</td>
      <td>-0.340963</td>
      <td>0.235771</td>
      <td>0.361477</td>
      <td>-1.044890</td>
      <td>0.013079</td>
      <td>0.045640</td>
      <td>-0.368658</td>
      <td>0.212665</td>
      <td>0.076585</td>
      <td>-0.149284</td>
      <td>0.119938</td>
      <td>-1.535850</td>
      <td>-0.068407</td>
      <td>-0.054570</td>
      <td>0.303971</td>
      <td>1.529405</td>
      <td>-0.065847</td>
      <td>-0.038063</td>
      <td>1.462884</td>
      <td>0.720484</td>
      <td>0.400291</td>
      <td>-0.791893</td>
      <td>0.325785</td>
      <td>0.065543</td>
      <td>-0.145494</td>
      <td>0.412533</td>
      <td>-0.507172</td>
      <td>-0.269686</td>
      <td>0.037403</td>
      <td>-0.317275</td>
      <td>0.215611</td>
      <td>-0.246216</td>
      <td>-1.741887</td>
      <td>1.024020</td>
      <td>0.804052</td>
      <td>0.528054</td>
      <td>0.328539</td>
      <td>-0.173758</td>
      <td>-0.266566</td>
      <td>-0.137294</td>
      <td>-0.697208</td>
      <td>-0.233514</td>
      <td>0.694885</td>
      <td>-0.438571</td>
      <td>-0.936507</td>
      <td>1.521490</td>
      <td>-0.148623</td>
      <td>-0.655283</td>
      <td>0.283887</td>
      <td>0.438366</td>
      <td>0.608226</td>
      <td>-0.894489</td>
      <td>-0.130209</td>
      <td>0.289991</td>
      <td>0.572482</td>
      <td>0.426662</td>
      <td>0.365971</td>
      <td>0.299149</td>
      <td>-0.429969</td>
      <td>0.507616</td>
      <td>0.069578</td>
      <td>-0.584111</td>
      <td>0.177558</td>
      <td>-0.660472</td>
      <td>0.242163</td>
      <td>-0.352641</td>
      <td>-0.192247</td>
      <td>-0.189735</td>
      <td>0.821864</td>
      <td>-0.549371</td>
      <td>-0.061711</td>
      <td>-0.041448</td>
      <td>0.209019</td>
      <td>-0.999063</td>
      <td>0.452706</td>
      <td>0.590211</td>
      <td>1.066579</td>
      <td>0.014787</td>
      <td>0.302994</td>
      <td>0.260799</td>
      <td>0.107097</td>
      <td>0.472471</td>
      <td>-0.754539</td>
      <td>0.420975</td>
      <td>0.458195</td>
      <td>0.874052</td>
      <td>0.804564</td>
      <td>-0.214528</td>
      <td>1.007026</td>
      <td>0.109441</td>
      <td>0.190958</td>
      <td>-0.157900</td>
      <td>0.048731</td>
      <td>-0.304235</td>
      <td>0.341696</td>
      <td>-0.561626</td>
      <td>0.184201</td>
      <td>0.081990</td>
      <td>0.696627</td>
      <td>-0.448489</td>
      <td>0.089371</td>
      <td>-0.710795</td>
      <td>-0.249469</td>
      <td>0.061249</td>
      <td>-0.374331</td>
      <td>0.201323</td>
      <td>-0.027021</td>
      <td>0.538210</td>
      <td>0.475453</td>
      <td>-0.298343</td>
      <td>-0.955092</td>
      <td>-0.607418</td>
      <td>13.535196</td>
      <td>0.183519</td>
      <td>-0.654316</td>
      <td>0.180649</td>
      <td>14.571429</td>
      <td>558.590</td>
      <td>0</td>
      <td>0.595238</td>
      <td>3.414785</td>
      <td>1.069058</td>
      <td>1806.498818</td>
      <td>10.039516</td>
      <td>10.302518</td>
      <td>4.604295</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>236.890600</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>6.066367</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>28.651875</td>
      <td>35.382472</td>
      <td>0.0</td>
      <td>5.316789</td>
      <td>0.000000</td>
      <td>25.683286</td>
      <td>18.113674</td>
      <td>107.182945</td>
      <td>22.625927</td>
      <td>20.440003</td>
      <td>5.687386</td>
      <td>11.499024</td>
      <td>0.000000</td>
      <td>42.159271</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>67.115241</td>
      <td>0.000000</td>
      <td>11.126903</td>
      <td>93.22</td>
      <td>11.814359</td>
      <td>19.178149</td>
      <td>0.0</td>
      <td>17.377811</td>
      <td>52.467919</td>
      <td>36.332708</td>
      <td>11.383156</td>
      <td>54.597304</td>
      <td>12.132734</td>
      <td>18.199101</td>
      <td>9.473726</td>
      <td>11.334820</td>
      <td>0.000000</td>
      <td>53.433918</td>
      <td>1.330932</td>
      <td>2.943064</td>
      <td>0.167546</td>
      <td>3.930859</td>
      <td>1.239711</td>
      <td>0.000000</td>
      <td>0.176471</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>2</td>
      <td>4</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>6</td>
      <td>10</td>
      <td>2</td>
      <td>11</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>6</td>
      <td>5.40730</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>1</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>3</td>
      <td>0</td>
      <td>24</td>
      <td>9.179704</td>
      <td>6</td>
    </tr>
    <tr>
      <th>10076</th>
      <td>*c1cccc(OCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C...</td>
      <td>0.551999</td>
      <td>-0.303311</td>
      <td>0.695626</td>
      <td>-0.198829</td>
      <td>-0.179051</td>
      <td>-0.081943</td>
      <td>0.475633</td>
      <td>0.587329</td>
      <td>0.345637</td>
      <td>-0.573298</td>
      <td>0.226352</td>
      <td>0.621363</td>
      <td>0.027854</td>
      <td>-0.008845</td>
      <td>0.580361</td>
      <td>-0.741851</td>
      <td>-0.720492</td>
      <td>-0.111044</td>
      <td>-0.363148</td>
      <td>0.510125</td>
      <td>0.462826</td>
      <td>0.849587</td>
      <td>-0.370492</td>
      <td>-0.189764</td>
      <td>0.385089</td>
      <td>0.755585</td>
      <td>1.248168</td>
      <td>0.277124</td>
      <td>-0.804517</td>
      <td>0.122267</td>
      <td>0.647079</td>
      <td>0.136563</td>
      <td>0.374036</td>
      <td>-0.191279</td>
      <td>0.216886</td>
      <td>-0.190615</td>
      <td>-0.857093</td>
      <td>0.202705</td>
      <td>-0.273867</td>
      <td>-0.610918</td>
      <td>0.337783</td>
      <td>-0.409525</td>
      <td>-0.098018</td>
      <td>-0.256670</td>
      <td>0.220673</td>
      <td>-0.089579</td>
      <td>-0.047271</td>
      <td>-0.032687</td>
      <td>-0.277280</td>
      <td>-0.116091</td>
      <td>-0.141555</td>
      <td>0.609150</td>
      <td>0.113340</td>
      <td>-0.040438</td>
      <td>-0.039240</td>
      <td>-0.095387</td>
      <td>-0.283798</td>
      <td>0.612702</td>
      <td>-0.393060</td>
      <td>-0.599197</td>
      <td>-1.310162</td>
      <td>-0.291358</td>
      <td>-0.339494</td>
      <td>0.438837</td>
      <td>0.394168</td>
      <td>-0.353355</td>
      <td>0.280939</td>
      <td>-0.480089</td>
      <td>-0.151918</td>
      <td>-0.555410</td>
      <td>-0.184681</td>
      <td>-0.260124</td>
      <td>0.379403</td>
      <td>0.476163</td>
      <td>-0.194530</td>
      <td>0.004664</td>
      <td>-0.147360</td>
      <td>-0.432367</td>
      <td>-0.293606</td>
      <td>-1.551955</td>
      <td>0.055084</td>
      <td>-0.081331</td>
      <td>-0.449213</td>
      <td>-0.431826</td>
      <td>0.044065</td>
      <td>-0.108603</td>
      <td>-0.219945</td>
      <td>0.023413</td>
      <td>-0.344181</td>
      <td>-0.094390</td>
      <td>-0.166694</td>
      <td>-0.423153</td>
      <td>-0.293181</td>
      <td>0.308562</td>
      <td>-0.379672</td>
      <td>-0.519660</td>
      <td>0.766020</td>
      <td>0.937071</td>
      <td>-0.201918</td>
      <td>-0.316611</td>
      <td>-0.937227</td>
      <td>0.096595</td>
      <td>0.459008</td>
      <td>0.191502</td>
      <td>-0.343536</td>
      <td>-0.212370</td>
      <td>0.460815</td>
      <td>0.140163</td>
      <td>0.400009</td>
      <td>0.875311</td>
      <td>1.030659</td>
      <td>-1.095279</td>
      <td>0.034686</td>
      <td>-0.467029</td>
      <td>-0.990298</td>
      <td>0.076111</td>
      <td>0.225022</td>
      <td>0.339576</td>
      <td>-0.623047</td>
      <td>0.072392</td>
      <td>-0.543293</td>
      <td>-0.243470</td>
      <td>-0.168640</td>
      <td>-0.416092</td>
      <td>-0.667105</td>
      <td>-0.144381</td>
      <td>-0.086137</td>
      <td>-0.312102</td>
      <td>-0.298672</td>
      <td>-0.080761</td>
      <td>-1.029680</td>
      <td>1.007537</td>
      <td>-0.755191</td>
      <td>-1.760491</td>
      <td>-0.815766</td>
      <td>-0.849118</td>
      <td>0.094387</td>
      <td>0.666649</td>
      <td>0.130125</td>
      <td>-0.603296</td>
      <td>1.408723</td>
      <td>-0.228017</td>
      <td>0.166661</td>
      <td>-0.655063</td>
      <td>0.669764</td>
      <td>0.536950</td>
      <td>0.072470</td>
      <td>-0.798143</td>
      <td>0.328302</td>
      <td>0.043216</td>
      <td>-0.273023</td>
      <td>-0.521973</td>
      <td>0.305415</td>
      <td>-0.627841</td>
      <td>0.355837</td>
      <td>-0.079894</td>
      <td>0.479035</td>
      <td>0.143263</td>
      <td>0.610938</td>
      <td>0.016215</td>
      <td>-0.590326</td>
      <td>0.022508</td>
      <td>0.057377</td>
      <td>0.681190</td>
      <td>0.206663</td>
      <td>-0.026828</td>
      <td>0.804893</td>
      <td>-0.642391</td>
      <td>0.112651</td>
      <td>1.336583</td>
      <td>0.312147</td>
      <td>-0.056600</td>
      <td>-0.502406</td>
      <td>-0.049746</td>
      <td>-0.375830</td>
      <td>-0.820795</td>
      <td>-0.705227</td>
      <td>-0.561021</td>
      <td>-0.589587</td>
      <td>0.480706</td>
      <td>-0.344668</td>
      <td>0.515808</td>
      <td>-0.713311</td>
      <td>0.232520</td>
      <td>0.066572</td>
      <td>-0.938447</td>
      <td>0.013620</td>
      <td>-0.892629</td>
      <td>-1.050370</td>
      <td>0.346639</td>
      <td>-0.386860</td>
      <td>-0.133935</td>
      <td>0.377671</td>
      <td>-0.943123</td>
      <td>0.424204</td>
      <td>-0.547837</td>
      <td>0.644905</td>
      <td>1.115971</td>
      <td>0.469830</td>
      <td>0.227112</td>
      <td>0.902045</td>
      <td>-1.296723</td>
      <td>-0.239965</td>
      <td>-0.769455</td>
      <td>-0.435336</td>
      <td>-0.072975</td>
      <td>-1.199274</td>
      <td>0.330504</td>
      <td>-0.058537</td>
      <td>0.312769</td>
      <td>0.072004</td>
      <td>0.107751</td>
      <td>-0.021124</td>
      <td>-0.213911</td>
      <td>-0.395956</td>
      <td>0.051693</td>
      <td>0.482149</td>
      <td>-0.251104</td>
      <td>0.442092</td>
      <td>0.658092</td>
      <td>0.041297</td>
      <td>-0.186713</td>
      <td>-0.474448</td>
      <td>0.604986</td>
      <td>1.382544</td>
      <td>0.455883</td>
      <td>0.684773</td>
      <td>0.810065</td>
      <td>0.156897</td>
      <td>0.450438</td>
      <td>0.040176</td>
      <td>-0.498122</td>
      <td>-0.125038</td>
      <td>-0.284849</td>
      <td>0.279697</td>
      <td>-0.014410</td>
      <td>0.216504</td>
      <td>-0.513738</td>
      <td>0.539395</td>
      <td>-0.731843</td>
      <td>0.532510</td>
      <td>0.486923</td>
      <td>1.228699</td>
      <td>-1.119600</td>
      <td>0.553774</td>
      <td>-0.192214</td>
      <td>-0.080538</td>
      <td>-0.292806</td>
      <td>-0.077586</td>
      <td>0.353359</td>
      <td>-0.304373</td>
      <td>-0.349171</td>
      <td>-0.945945</td>
      <td>0.752956</td>
      <td>0.931142</td>
      <td>0.500938</td>
      <td>0.039624</td>
      <td>-0.246636</td>
      <td>-0.310066</td>
      <td>0.320841</td>
      <td>-0.011141</td>
      <td>-0.240811</td>
      <td>0.255539</td>
      <td>0.690148</td>
      <td>0.178633</td>
      <td>-0.253069</td>
      <td>0.190380</td>
      <td>0.264550</td>
      <td>0.467191</td>
      <td>0.061234</td>
      <td>-0.955835</td>
      <td>-0.203506</td>
      <td>-0.244385</td>
      <td>-0.019487</td>
      <td>0.229594</td>
      <td>-0.457369</td>
      <td>0.026137</td>
      <td>0.597931</td>
      <td>-0.159790</td>
      <td>0.062175</td>
      <td>1.013808</td>
      <td>0.447916</td>
      <td>-0.180201</td>
      <td>-0.025639</td>
      <td>-0.147854</td>
      <td>-0.275514</td>
      <td>0.317958</td>
      <td>-0.648757</td>
      <td>-0.549903</td>
      <td>0.237165</td>
      <td>0.026414</td>
      <td>0.655565</td>
      <td>0.149111</td>
      <td>-0.374751</td>
      <td>-0.884393</td>
      <td>-0.277285</td>
      <td>2.253196</td>
      <td>-0.297094</td>
      <td>-0.948057</td>
      <td>-0.030938</td>
      <td>0.207844</td>
      <td>0.257533</td>
      <td>0.556511</td>
      <td>-0.788636</td>
      <td>0.296756</td>
      <td>-0.242649</td>
      <td>-0.450633</td>
      <td>-0.726511</td>
      <td>-0.289316</td>
      <td>0.451518</td>
      <td>0.374336</td>
      <td>-1.332244</td>
      <td>0.653405</td>
      <td>0.750666</td>
      <td>-0.259494</td>
      <td>-0.260930</td>
      <td>0.554837</td>
      <td>-0.338322</td>
      <td>-0.939984</td>
      <td>-0.359451</td>
      <td>0.423440</td>
      <td>0.604143</td>
      <td>-0.389208</td>
      <td>-0.308636</td>
      <td>-0.484644</td>
      <td>-0.545992</td>
      <td>0.350792</td>
      <td>0.270433</td>
      <td>0.517303</td>
      <td>-0.752403</td>
      <td>-0.132921</td>
      <td>0.708733</td>
      <td>-0.186164</td>
      <td>-0.233301</td>
      <td>1.090791</td>
      <td>1.111387</td>
      <td>-0.514096</td>
      <td>-0.088778</td>
      <td>-0.039628</td>
      <td>-0.401279</td>
      <td>0.544004</td>
      <td>-0.214157</td>
      <td>0.231987</td>
      <td>0.310581</td>
      <td>1.222133</td>
      <td>-0.285924</td>
      <td>-0.065949</td>
      <td>-0.491327</td>
      <td>-0.333714</td>
      <td>-0.289891</td>
      <td>-0.262624</td>
      <td>-0.662276</td>
      <td>0.185207</td>
      <td>1.162569</td>
      <td>0.445772</td>
      <td>-0.258106</td>
      <td>0.416414</td>
      <td>-0.098158</td>
      <td>0.322540</td>
      <td>0.465350</td>
      <td>-0.218009</td>
      <td>-0.687455</td>
      <td>0.572080</td>
      <td>0.499708</td>
      <td>0.765414</td>
      <td>-0.008531</td>
      <td>-0.116102</td>
      <td>0.235766</td>
      <td>-0.554271</td>
      <td>-0.508254</td>
      <td>-0.015086</td>
      <td>0.509726</td>
      <td>0.542249</td>
      <td>-0.690373</td>
      <td>0.315965</td>
      <td>0.840297</td>
      <td>0.256769</td>
      <td>-0.100504</td>
      <td>-1.051446</td>
      <td>-0.008044</td>
      <td>0.009313</td>
      <td>-1.052204</td>
      <td>0.819606</td>
      <td>-0.393767</td>
      <td>0.397069</td>
      <td>0.276633</td>
      <td>-0.365039</td>
      <td>-0.515293</td>
      <td>-0.486400</td>
      <td>0.872785</td>
      <td>0.570721</td>
      <td>0.560240</td>
      <td>0.558013</td>
      <td>0.232251</td>
      <td>0.980590</td>
      <td>0.689010</td>
      <td>0.029295</td>
      <td>-0.140242</td>
      <td>-0.662691</td>
      <td>0.226448</td>
      <td>0.113214</td>
      <td>0.048817</td>
      <td>-0.385799</td>
      <td>-0.428410</td>
      <td>0.089791</td>
      <td>0.547284</td>
      <td>0.061968</td>
      <td>0.002452</td>
      <td>-1.229584</td>
      <td>0.240191</td>
      <td>0.878320</td>
      <td>0.645287</td>
      <td>-0.545210</td>
      <td>0.217281</td>
      <td>0.353964</td>
      <td>-0.287001</td>
      <td>-0.249022</td>
      <td>-0.260528</td>
      <td>-0.012725</td>
      <td>-0.100586</td>
      <td>-0.956388</td>
      <td>-0.194482</td>
      <td>-0.002704</td>
      <td>0.412386</td>
      <td>0.032609</td>
      <td>0.502419</td>
      <td>0.018459</td>
      <td>0.278252</td>
      <td>0.269180</td>
      <td>-1.116240</td>
      <td>-0.713984</td>
      <td>0.778574</td>
      <td>-0.043775</td>
      <td>0.172654</td>
      <td>-0.684065</td>
      <td>0.132998</td>
      <td>0.484402</td>
      <td>-0.429973</td>
      <td>-0.390243</td>
      <td>-0.912091</td>
      <td>0.068653</td>
      <td>-0.103938</td>
      <td>0.798183</td>
      <td>0.681155</td>
      <td>-0.128305</td>
      <td>-0.092319</td>
      <td>-0.550944</td>
      <td>-0.090561</td>
      <td>0.320732</td>
      <td>0.200091</td>
      <td>0.301519</td>
      <td>-0.471677</td>
      <td>0.482521</td>
      <td>0.652191</td>
      <td>-0.108905</td>
      <td>0.138278</td>
      <td>-0.399240</td>
      <td>0.385504</td>
      <td>-0.344450</td>
      <td>-0.168377</td>
      <td>0.412273</td>
      <td>-0.677244</td>
      <td>0.121258</td>
      <td>0.289780</td>
      <td>-0.467556</td>
      <td>-0.597925</td>
      <td>-0.756221</td>
      <td>1.061599</td>
      <td>-0.020503</td>
      <td>0.474405</td>
      <td>0.189142</td>
      <td>-0.245862</td>
      <td>0.002965</td>
      <td>-0.223899</td>
      <td>0.270057</td>
      <td>-0.125905</td>
      <td>-0.057594</td>
      <td>-0.285087</td>
      <td>0.505376</td>
      <td>0.586031</td>
      <td>-0.562202</td>
      <td>0.273379</td>
      <td>0.217424</td>
      <td>-0.116816</td>
      <td>0.058435</td>
      <td>-0.346205</td>
      <td>-0.234037</td>
      <td>-0.181446</td>
      <td>-0.340679</td>
      <td>0.279302</td>
      <td>0.342875</td>
      <td>-1.036065</td>
      <td>-0.002205</td>
      <td>0.037703</td>
      <td>-0.379392</td>
      <td>0.207108</td>
      <td>-0.047304</td>
      <td>-0.147235</td>
      <td>0.133393</td>
      <td>-1.556590</td>
      <td>-0.011551</td>
      <td>-0.026068</td>
      <td>0.211351</td>
      <td>1.500643</td>
      <td>-0.077225</td>
      <td>-0.081594</td>
      <td>1.459598</td>
      <td>0.656195</td>
      <td>0.394879</td>
      <td>-0.793465</td>
      <td>0.352861</td>
      <td>0.056542</td>
      <td>-0.155895</td>
      <td>0.407403</td>
      <td>-0.567856</td>
      <td>-0.310073</td>
      <td>0.045925</td>
      <td>-0.261260</td>
      <td>0.174083</td>
      <td>-0.255695</td>
      <td>-1.674649</td>
      <td>1.050343</td>
      <td>0.790778</td>
      <td>0.503869</td>
      <td>0.357036</td>
      <td>-0.195439</td>
      <td>-0.337705</td>
      <td>-0.161124</td>
      <td>-0.715499</td>
      <td>-0.186346</td>
      <td>0.674294</td>
      <td>-0.394688</td>
      <td>-1.042178</td>
      <td>1.448996</td>
      <td>-0.103759</td>
      <td>-0.718598</td>
      <td>0.324853</td>
      <td>0.511914</td>
      <td>0.574627</td>
      <td>-0.926497</td>
      <td>-0.113703</td>
      <td>0.270446</td>
      <td>0.532466</td>
      <td>0.471403</td>
      <td>0.293513</td>
      <td>0.362214</td>
      <td>-0.457939</td>
      <td>0.476220</td>
      <td>0.095844</td>
      <td>-0.610631</td>
      <td>0.139574</td>
      <td>-0.701271</td>
      <td>0.273788</td>
      <td>-0.315571</td>
      <td>-0.294913</td>
      <td>-0.232344</td>
      <td>0.749943</td>
      <td>-0.498153</td>
      <td>-0.031448</td>
      <td>-0.076291</td>
      <td>0.115680</td>
      <td>-0.982414</td>
      <td>0.392848</td>
      <td>0.618370</td>
      <td>1.047461</td>
      <td>0.000711</td>
      <td>0.299114</td>
      <td>0.299159</td>
      <td>0.083985</td>
      <td>0.443846</td>
      <td>-0.686642</td>
      <td>0.333866</td>
      <td>0.472946</td>
      <td>0.795789</td>
      <td>0.776276</td>
      <td>-0.246890</td>
      <td>0.971203</td>
      <td>0.076102</td>
      <td>0.198004</td>
      <td>-0.160140</td>
      <td>0.104478</td>
      <td>-0.281275</td>
      <td>0.315206</td>
      <td>-0.640012</td>
      <td>0.236968</td>
      <td>0.114738</td>
      <td>0.702484</td>
      <td>-0.452242</td>
      <td>0.147459</td>
      <td>-0.727075</td>
      <td>-0.162746</td>
      <td>0.061233</td>
      <td>-0.342900</td>
      <td>0.277376</td>
      <td>-0.077759</td>
      <td>0.497563</td>
      <td>0.513481</td>
      <td>-0.340613</td>
      <td>-0.990040</td>
      <td>-0.610522</td>
      <td>13.523115</td>
      <td>0.170148</td>
      <td>-0.654527</td>
      <td>0.205731</td>
      <td>14.634146</td>
      <td>544.563</td>
      <td>0</td>
      <td>0.609756</td>
      <td>3.397762</td>
      <td>1.101853</td>
      <td>1789.180747</td>
      <td>9.828335</td>
      <td>9.745139</td>
      <td>4.430582</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>236.536146</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>28.651875</td>
      <td>35.382472</td>
      <td>0.0</td>
      <td>5.316789</td>
      <td>0.000000</td>
      <td>19.262465</td>
      <td>18.113674</td>
      <td>107.182945</td>
      <td>22.625927</td>
      <td>20.440003</td>
      <td>5.687386</td>
      <td>11.499024</td>
      <td>0.000000</td>
      <td>42.159271</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>60.694420</td>
      <td>0.000000</td>
      <td>11.126903</td>
      <td>93.22</td>
      <td>11.814359</td>
      <td>19.178149</td>
      <td>0.0</td>
      <td>17.377811</td>
      <td>52.467919</td>
      <td>29.911886</td>
      <td>11.383156</td>
      <td>54.597304</td>
      <td>18.199101</td>
      <td>12.132734</td>
      <td>9.473726</td>
      <td>11.295157</td>
      <td>0.000000</td>
      <td>53.390206</td>
      <td>1.329432</td>
      <td>2.925357</td>
      <td>0.127349</td>
      <td>2.737915</td>
      <td>1.179723</td>
      <td>0.000000</td>
      <td>0.151515</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>2</td>
      <td>4</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>6</td>
      <td>10</td>
      <td>2</td>
      <td>10</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>6</td>
      <td>5.01720</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>1</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>23</td>
      <td>8.818383</td>
      <td>6</td>
    </tr>
    <tr>
      <th>10077</th>
      <td>*c1cccc(OCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C(...</td>
      <td>0.563495</td>
      <td>-0.335861</td>
      <td>0.695644</td>
      <td>-0.189717</td>
      <td>-0.185641</td>
      <td>-0.064083</td>
      <td>0.482744</td>
      <td>0.592324</td>
      <td>0.406181</td>
      <td>-0.575052</td>
      <td>0.268687</td>
      <td>0.674287</td>
      <td>0.048724</td>
      <td>0.092107</td>
      <td>0.578326</td>
      <td>-0.761869</td>
      <td>-0.689455</td>
      <td>-0.149589</td>
      <td>-0.395424</td>
      <td>0.535310</td>
      <td>0.396437</td>
      <td>0.864678</td>
      <td>-0.325530</td>
      <td>-0.192917</td>
      <td>0.366370</td>
      <td>0.815100</td>
      <td>1.256964</td>
      <td>0.245132</td>
      <td>-0.790748</td>
      <td>0.077144</td>
      <td>0.664309</td>
      <td>0.159111</td>
      <td>0.373687</td>
      <td>-0.178497</td>
      <td>0.242307</td>
      <td>-0.077036</td>
      <td>-0.857530</td>
      <td>0.172434</td>
      <td>-0.277898</td>
      <td>-0.601465</td>
      <td>0.311546</td>
      <td>-0.376549</td>
      <td>-0.058021</td>
      <td>-0.236901</td>
      <td>0.212629</td>
      <td>-0.084292</td>
      <td>-0.089670</td>
      <td>-0.025505</td>
      <td>-0.263775</td>
      <td>-0.143063</td>
      <td>-0.121128</td>
      <td>0.620498</td>
      <td>0.073585</td>
      <td>-0.055297</td>
      <td>-0.049270</td>
      <td>-0.164330</td>
      <td>-0.310126</td>
      <td>0.641702</td>
      <td>-0.430449</td>
      <td>-0.667502</td>
      <td>-1.323565</td>
      <td>-0.303816</td>
      <td>-0.315975</td>
      <td>0.376335</td>
      <td>0.461453</td>
      <td>-0.374427</td>
      <td>0.320952</td>
      <td>-0.494266</td>
      <td>-0.163075</td>
      <td>-0.636206</td>
      <td>-0.132711</td>
      <td>-0.272882</td>
      <td>0.376697</td>
      <td>0.534431</td>
      <td>-0.184275</td>
      <td>-0.002769</td>
      <td>-0.141284</td>
      <td>-0.511948</td>
      <td>-0.330821</td>
      <td>-1.554407</td>
      <td>0.067480</td>
      <td>-0.055482</td>
      <td>-0.421222</td>
      <td>-0.407621</td>
      <td>0.004771</td>
      <td>-0.103943</td>
      <td>-0.228071</td>
      <td>0.059499</td>
      <td>-0.359675</td>
      <td>-0.081893</td>
      <td>-0.212812</td>
      <td>-0.416312</td>
      <td>-0.287694</td>
      <td>0.312645</td>
      <td>-0.351858</td>
      <td>-0.597028</td>
      <td>0.736227</td>
      <td>0.940782</td>
      <td>-0.184464</td>
      <td>-0.353935</td>
      <td>-0.959378</td>
      <td>0.123864</td>
      <td>0.430741</td>
      <td>0.208164</td>
      <td>-0.456936</td>
      <td>-0.223803</td>
      <td>0.503537</td>
      <td>0.126056</td>
      <td>0.395534</td>
      <td>0.926082</td>
      <td>0.995189</td>
      <td>-1.033044</td>
      <td>-0.025851</td>
      <td>-0.416006</td>
      <td>-1.015725</td>
      <td>0.044057</td>
      <td>0.174833</td>
      <td>0.314073</td>
      <td>-0.658409</td>
      <td>0.122536</td>
      <td>-0.544418</td>
      <td>-0.297480</td>
      <td>-0.197776</td>
      <td>-0.420936</td>
      <td>-0.725812</td>
      <td>-0.133011</td>
      <td>-0.120828</td>
      <td>-0.331234</td>
      <td>-0.252004</td>
      <td>-0.089449</td>
      <td>-0.993558</td>
      <td>1.068733</td>
      <td>-0.830864</td>
      <td>-1.783122</td>
      <td>-0.785761</td>
      <td>-0.892098</td>
      <td>0.112680</td>
      <td>0.648430</td>
      <td>0.168319</td>
      <td>-0.548742</td>
      <td>1.429388</td>
      <td>-0.259922</td>
      <td>0.118441</td>
      <td>-0.581717</td>
      <td>0.636849</td>
      <td>0.568510</td>
      <td>0.047231</td>
      <td>-0.763851</td>
      <td>0.382340</td>
      <td>0.001086</td>
      <td>-0.291861</td>
      <td>-0.603842</td>
      <td>0.280756</td>
      <td>-0.726615</td>
      <td>0.389916</td>
      <td>-0.071728</td>
      <td>0.519927</td>
      <td>0.194558</td>
      <td>0.588169</td>
      <td>0.080978</td>
      <td>-0.653472</td>
      <td>0.014377</td>
      <td>0.074611</td>
      <td>0.723078</td>
      <td>0.264032</td>
      <td>-0.070205</td>
      <td>0.799869</td>
      <td>-0.630742</td>
      <td>0.107268</td>
      <td>1.360367</td>
      <td>0.304768</td>
      <td>0.004982</td>
      <td>-0.460520</td>
      <td>-0.065204</td>
      <td>-0.413748</td>
      <td>-0.767623</td>
      <td>-0.686909</td>
      <td>-0.568482</td>
      <td>-0.571243</td>
      <td>0.477257</td>
      <td>-0.334924</td>
      <td>0.602968</td>
      <td>-0.675408</td>
      <td>0.321448</td>
      <td>0.037979</td>
      <td>-0.886171</td>
      <td>-0.026779</td>
      <td>-0.920990</td>
      <td>-0.988085</td>
      <td>0.344654</td>
      <td>-0.357025</td>
      <td>-0.122360</td>
      <td>0.403823</td>
      <td>-0.881092</td>
      <td>0.406374</td>
      <td>-0.542296</td>
      <td>0.661503</td>
      <td>1.108273</td>
      <td>0.428035</td>
      <td>0.204461</td>
      <td>0.884736</td>
      <td>-1.345814</td>
      <td>-0.270413</td>
      <td>-0.846542</td>
      <td>-0.487445</td>
      <td>-0.050847</td>
      <td>-1.246097</td>
      <td>0.353777</td>
      <td>-0.024452</td>
      <td>0.346493</td>
      <td>0.048758</td>
      <td>0.111678</td>
      <td>-0.042495</td>
      <td>-0.227828</td>
      <td>-0.352632</td>
      <td>0.091157</td>
      <td>0.511680</td>
      <td>-0.261157</td>
      <td>0.409051</td>
      <td>0.591438</td>
      <td>0.068759</td>
      <td>-0.220529</td>
      <td>-0.478557</td>
      <td>0.546374</td>
      <td>1.377692</td>
      <td>0.434991</td>
      <td>0.653271</td>
      <td>0.829443</td>
      <td>0.194864</td>
      <td>0.475794</td>
      <td>0.058895</td>
      <td>-0.461744</td>
      <td>-0.080800</td>
      <td>-0.289837</td>
      <td>0.270365</td>
      <td>-0.055600</td>
      <td>0.252657</td>
      <td>-0.540631</td>
      <td>0.609659</td>
      <td>-0.732147</td>
      <td>0.605374</td>
      <td>0.555576</td>
      <td>1.253884</td>
      <td>-1.129057</td>
      <td>0.599816</td>
      <td>-0.136244</td>
      <td>0.000511</td>
      <td>-0.295080</td>
      <td>-0.101659</td>
      <td>0.338265</td>
      <td>-0.380234</td>
      <td>-0.356991</td>
      <td>-0.985537</td>
      <td>0.728051</td>
      <td>0.908158</td>
      <td>0.576242</td>
      <td>0.022262</td>
      <td>-0.239684</td>
      <td>-0.281578</td>
      <td>0.280369</td>
      <td>-0.052871</td>
      <td>-0.291708</td>
      <td>0.252211</td>
      <td>0.740910</td>
      <td>0.191847</td>
      <td>-0.325752</td>
      <td>0.239583</td>
      <td>0.311915</td>
      <td>0.499563</td>
      <td>0.039295</td>
      <td>-0.982614</td>
      <td>-0.280482</td>
      <td>-0.258128</td>
      <td>-0.028632</td>
      <td>0.270295</td>
      <td>-0.433361</td>
      <td>0.001233</td>
      <td>0.560672</td>
      <td>-0.208373</td>
      <td>0.083738</td>
      <td>0.957717</td>
      <td>0.393687</td>
      <td>-0.227536</td>
      <td>0.002999</td>
      <td>-0.168796</td>
      <td>-0.246760</td>
      <td>0.396696</td>
      <td>-0.679814</td>
      <td>-0.564954</td>
      <td>0.284587</td>
      <td>0.010779</td>
      <td>0.674405</td>
      <td>0.195567</td>
      <td>-0.357758</td>
      <td>-0.875644</td>
      <td>-0.362729</td>
      <td>2.296327</td>
      <td>-0.272269</td>
      <td>-0.952598</td>
      <td>-0.014474</td>
      <td>0.226386</td>
      <td>0.169228</td>
      <td>0.583077</td>
      <td>-0.781698</td>
      <td>0.360985</td>
      <td>-0.304118</td>
      <td>-0.465286</td>
      <td>-0.709717</td>
      <td>-0.315821</td>
      <td>0.471332</td>
      <td>0.445799</td>
      <td>-1.339828</td>
      <td>0.699451</td>
      <td>0.695452</td>
      <td>-0.272872</td>
      <td>-0.300469</td>
      <td>0.587247</td>
      <td>-0.344664</td>
      <td>-0.936127</td>
      <td>-0.350210</td>
      <td>0.446583</td>
      <td>0.677103</td>
      <td>-0.366346</td>
      <td>-0.332926</td>
      <td>-0.445367</td>
      <td>-0.513625</td>
      <td>0.352378</td>
      <td>0.277598</td>
      <td>0.544623</td>
      <td>-0.771514</td>
      <td>-0.167191</td>
      <td>0.721485</td>
      <td>-0.182673</td>
      <td>-0.187549</td>
      <td>1.090745</td>
      <td>1.144348</td>
      <td>-0.511939</td>
      <td>-0.041158</td>
      <td>-0.067585</td>
      <td>-0.392318</td>
      <td>0.562769</td>
      <td>-0.305613</td>
      <td>0.237171</td>
      <td>0.369746</td>
      <td>1.241951</td>
      <td>-0.336656</td>
      <td>-0.054885</td>
      <td>-0.566226</td>
      <td>-0.235861</td>
      <td>-0.298905</td>
      <td>-0.214603</td>
      <td>-0.708892</td>
      <td>0.173306</td>
      <td>1.220613</td>
      <td>0.403542</td>
      <td>-0.195678</td>
      <td>0.417657</td>
      <td>-0.067249</td>
      <td>0.318722</td>
      <td>0.553526</td>
      <td>-0.244388</td>
      <td>-0.638787</td>
      <td>0.543675</td>
      <td>0.555124</td>
      <td>0.769604</td>
      <td>-0.024002</td>
      <td>-0.123754</td>
      <td>0.204297</td>
      <td>-0.483344</td>
      <td>-0.514159</td>
      <td>-0.075618</td>
      <td>0.515816</td>
      <td>0.535895</td>
      <td>-0.656900</td>
      <td>0.315496</td>
      <td>0.887710</td>
      <td>0.326492</td>
      <td>-0.061898</td>
      <td>-1.085942</td>
      <td>0.062632</td>
      <td>-0.020963</td>
      <td>-1.038729</td>
      <td>0.851201</td>
      <td>-0.353574</td>
      <td>0.472100</td>
      <td>0.216356</td>
      <td>-0.325357</td>
      <td>-0.580029</td>
      <td>-0.471352</td>
      <td>0.903755</td>
      <td>0.519593</td>
      <td>0.608066</td>
      <td>0.594127</td>
      <td>0.296171</td>
      <td>1.042027</td>
      <td>0.671713</td>
      <td>0.043668</td>
      <td>-0.168250</td>
      <td>-0.650057</td>
      <td>0.191861</td>
      <td>0.103798</td>
      <td>0.069622</td>
      <td>-0.419664</td>
      <td>-0.395939</td>
      <td>0.069560</td>
      <td>0.538694</td>
      <td>0.074448</td>
      <td>-0.021722</td>
      <td>-1.260396</td>
      <td>0.220725</td>
      <td>0.869838</td>
      <td>0.638720</td>
      <td>-0.575179</td>
      <td>0.244591</td>
      <td>0.320732</td>
      <td>-0.354879</td>
      <td>-0.198534</td>
      <td>-0.269075</td>
      <td>0.023721</td>
      <td>-0.168541</td>
      <td>-0.964859</td>
      <td>-0.193729</td>
      <td>0.020280</td>
      <td>0.450700</td>
      <td>0.034006</td>
      <td>0.598335</td>
      <td>0.040648</td>
      <td>0.234812</td>
      <td>0.272104</td>
      <td>-1.152274</td>
      <td>-0.735315</td>
      <td>0.810679</td>
      <td>-0.112185</td>
      <td>0.121687</td>
      <td>-0.749591</td>
      <td>0.110276</td>
      <td>0.511708</td>
      <td>-0.423115</td>
      <td>-0.437762</td>
      <td>-0.940517</td>
      <td>0.083028</td>
      <td>-0.110941</td>
      <td>0.813228</td>
      <td>0.699168</td>
      <td>-0.156358</td>
      <td>-0.085404</td>
      <td>-0.651209</td>
      <td>-0.138130</td>
      <td>0.307312</td>
      <td>0.231228</td>
      <td>0.286759</td>
      <td>-0.533505</td>
      <td>0.473612</td>
      <td>0.635527</td>
      <td>-0.121335</td>
      <td>0.174304</td>
      <td>-0.422486</td>
      <td>0.380603</td>
      <td>-0.302204</td>
      <td>-0.124588</td>
      <td>0.414655</td>
      <td>-0.651015</td>
      <td>0.110542</td>
      <td>0.256765</td>
      <td>-0.457067</td>
      <td>-0.631417</td>
      <td>-0.688181</td>
      <td>1.022640</td>
      <td>-0.056549</td>
      <td>0.435986</td>
      <td>0.242844</td>
      <td>-0.230450</td>
      <td>0.087055</td>
      <td>-0.261788</td>
      <td>0.203314</td>
      <td>-0.183406</td>
      <td>-0.060560</td>
      <td>-0.278155</td>
      <td>0.477060</td>
      <td>0.560582</td>
      <td>-0.595061</td>
      <td>0.304169</td>
      <td>0.177181</td>
      <td>-0.125511</td>
      <td>0.094831</td>
      <td>-0.354813</td>
      <td>-0.216984</td>
      <td>-0.192595</td>
      <td>-0.416544</td>
      <td>0.319640</td>
      <td>0.361415</td>
      <td>-1.027170</td>
      <td>0.035468</td>
      <td>0.019992</td>
      <td>-0.332438</td>
      <td>0.174050</td>
      <td>-0.068321</td>
      <td>-0.149111</td>
      <td>0.159169</td>
      <td>-1.535910</td>
      <td>-0.048187</td>
      <td>-0.004038</td>
      <td>0.249216</td>
      <td>1.470121</td>
      <td>-0.094419</td>
      <td>-0.095662</td>
      <td>1.434593</td>
      <td>0.682738</td>
      <td>0.449242</td>
      <td>-0.771831</td>
      <td>0.375701</td>
      <td>0.024605</td>
      <td>-0.200955</td>
      <td>0.382817</td>
      <td>-0.561635</td>
      <td>-0.352076</td>
      <td>0.043867</td>
      <td>-0.301824</td>
      <td>0.217656</td>
      <td>-0.209702</td>
      <td>-1.674236</td>
      <td>1.023211</td>
      <td>0.762741</td>
      <td>0.517230</td>
      <td>0.324251</td>
      <td>-0.182829</td>
      <td>-0.335168</td>
      <td>-0.141977</td>
      <td>-0.630104</td>
      <td>-0.247409</td>
      <td>0.690819</td>
      <td>-0.399046</td>
      <td>-1.088202</td>
      <td>1.446095</td>
      <td>-0.023062</td>
      <td>-0.726991</td>
      <td>0.294734</td>
      <td>0.456264</td>
      <td>0.589008</td>
      <td>-0.884290</td>
      <td>-0.109864</td>
      <td>0.252027</td>
      <td>0.514594</td>
      <td>0.468715</td>
      <td>0.257160</td>
      <td>0.374642</td>
      <td>-0.487446</td>
      <td>0.431631</td>
      <td>0.058735</td>
      <td>-0.632105</td>
      <td>0.145589</td>
      <td>-0.687829</td>
      <td>0.265469</td>
      <td>-0.370827</td>
      <td>-0.326417</td>
      <td>-0.249539</td>
      <td>0.794534</td>
      <td>-0.521088</td>
      <td>-0.037375</td>
      <td>-0.101593</td>
      <td>0.119510</td>
      <td>-0.979964</td>
      <td>0.382913</td>
      <td>0.537173</td>
      <td>1.051068</td>
      <td>-0.021849</td>
      <td>0.332122</td>
      <td>0.310315</td>
      <td>0.097000</td>
      <td>0.389602</td>
      <td>-0.725323</td>
      <td>0.380790</td>
      <td>0.488035</td>
      <td>0.820341</td>
      <td>0.790473</td>
      <td>-0.233882</td>
      <td>0.993431</td>
      <td>0.072830</td>
      <td>0.247352</td>
      <td>-0.200399</td>
      <td>0.122468</td>
      <td>-0.271341</td>
      <td>0.313220</td>
      <td>-0.624356</td>
      <td>0.248470</td>
      <td>0.211323</td>
      <td>0.648857</td>
      <td>-0.481027</td>
      <td>0.138786</td>
      <td>-0.759260</td>
      <td>-0.221648</td>
      <td>0.090163</td>
      <td>-0.376218</td>
      <td>0.335613</td>
      <td>-0.095099</td>
      <td>0.481115</td>
      <td>0.479452</td>
      <td>-0.317410</td>
      <td>-1.053976</td>
      <td>-0.645858</td>
      <td>13.509907</td>
      <td>0.153775</td>
      <td>-0.654822</td>
      <td>0.231529</td>
      <td>14.700000</td>
      <td>530.536</td>
      <td>0</td>
      <td>0.625000</td>
      <td>3.381024</td>
      <td>1.136990</td>
      <td>1771.874701</td>
      <td>9.617263</td>
      <td>9.198655</td>
      <td>4.011584</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>230.115324</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>28.651875</td>
      <td>35.382472</td>
      <td>0.0</td>
      <td>5.316789</td>
      <td>0.000000</td>
      <td>12.841643</td>
      <td>18.113674</td>
      <td>107.182945</td>
      <td>22.625927</td>
      <td>20.440003</td>
      <td>5.687386</td>
      <td>11.499024</td>
      <td>0.000000</td>
      <td>42.159271</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>54.273598</td>
      <td>0.000000</td>
      <td>11.126903</td>
      <td>93.22</td>
      <td>11.814359</td>
      <td>19.178149</td>
      <td>0.0</td>
      <td>17.377811</td>
      <td>52.467919</td>
      <td>23.491065</td>
      <td>11.383156</td>
      <td>60.663671</td>
      <td>12.132734</td>
      <td>12.132734</td>
      <td>9.473726</td>
      <td>11.242988</td>
      <td>0.000000</td>
      <td>53.341945</td>
      <td>1.327747</td>
      <td>2.903519</td>
      <td>0.074564</td>
      <td>1.634625</td>
      <td>1.090070</td>
      <td>0.000000</td>
      <td>0.125000</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>2</td>
      <td>4</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>6</td>
      <td>10</td>
      <td>2</td>
      <td>9</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>6</td>
      <td>4.62710</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>1</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>1</td>
      <td>0</td>
      <td>22</td>
      <td>8.461092</td>
      <td>6</td>
    </tr>
    <tr>
      <th>10078</th>
      <td>*c1cccc(Oc2cccc(Oc3cccc(N4C(=O)c5ccc(Oc6ccc(Sc...</td>
      <td>0.498171</td>
      <td>-0.012972</td>
      <td>0.531258</td>
      <td>0.223804</td>
      <td>0.248206</td>
      <td>-0.160753</td>
      <td>0.281438</td>
      <td>0.157734</td>
      <td>0.479053</td>
      <td>-0.023833</td>
      <td>-0.107557</td>
      <td>1.081434</td>
      <td>-0.414554</td>
      <td>-0.038614</td>
      <td>0.531237</td>
      <td>-0.601774</td>
      <td>-1.206939</td>
      <td>0.910802</td>
      <td>0.503433</td>
      <td>0.529281</td>
      <td>0.098454</td>
      <td>1.136599</td>
      <td>0.195479</td>
      <td>0.194286</td>
      <td>0.742961</td>
      <td>-0.366123</td>
      <td>0.860240</td>
      <td>0.115545</td>
      <td>-0.085287</td>
      <td>-0.450780</td>
      <td>0.205796</td>
      <td>0.756310</td>
      <td>-0.036488</td>
      <td>0.556692</td>
      <td>-0.424523</td>
      <td>-0.566868</td>
      <td>-1.310900</td>
      <td>-0.501850</td>
      <td>-0.224796</td>
      <td>-0.765368</td>
      <td>0.546953</td>
      <td>-0.367955</td>
      <td>0.366966</td>
      <td>-0.390705</td>
      <td>-0.755560</td>
      <td>-0.172190</td>
      <td>0.506879</td>
      <td>-0.186050</td>
      <td>-1.147877</td>
      <td>0.187593</td>
      <td>0.209335</td>
      <td>0.519669</td>
      <td>0.323823</td>
      <td>0.302032</td>
      <td>0.001218</td>
      <td>-0.359398</td>
      <td>-0.296333</td>
      <td>0.483574</td>
      <td>-0.790757</td>
      <td>-0.917403</td>
      <td>-0.515059</td>
      <td>-0.106169</td>
      <td>-0.772183</td>
      <td>0.508716</td>
      <td>0.800041</td>
      <td>0.340868</td>
      <td>-0.004924</td>
      <td>-0.611724</td>
      <td>-0.085961</td>
      <td>0.118545</td>
      <td>-0.591209</td>
      <td>0.433792</td>
      <td>0.545638</td>
      <td>-0.422545</td>
      <td>-0.267819</td>
      <td>-0.452903</td>
      <td>0.277941</td>
      <td>0.138812</td>
      <td>-0.619962</td>
      <td>-0.608126</td>
      <td>-0.995795</td>
      <td>-0.028980</td>
      <td>-0.137828</td>
      <td>-0.068430</td>
      <td>0.534288</td>
      <td>0.401099</td>
      <td>-0.379149</td>
      <td>-0.200921</td>
      <td>-0.241683</td>
      <td>-0.022862</td>
      <td>0.110307</td>
      <td>-0.277331</td>
      <td>0.055432</td>
      <td>-0.097106</td>
      <td>0.200659</td>
      <td>0.032236</td>
      <td>0.823370</td>
      <td>-0.255831</td>
      <td>-0.716563</td>
      <td>-0.308821</td>
      <td>-0.207808</td>
      <td>-0.200687</td>
      <td>-0.018399</td>
      <td>0.098098</td>
      <td>0.623665</td>
      <td>0.486721</td>
      <td>-1.070934</td>
      <td>0.245703</td>
      <td>0.083358</td>
      <td>0.810030</td>
      <td>1.550581</td>
      <td>-1.122437</td>
      <td>-0.011974</td>
      <td>0.202915</td>
      <td>-0.782574</td>
      <td>-0.197943</td>
      <td>0.823410</td>
      <td>-0.879084</td>
      <td>-0.011986</td>
      <td>-0.618248</td>
      <td>-1.183151</td>
      <td>-0.387682</td>
      <td>0.207631</td>
      <td>0.518865</td>
      <td>-0.142045</td>
      <td>-0.883212</td>
      <td>0.669766</td>
      <td>-0.240476</td>
      <td>-0.251489</td>
      <td>0.584105</td>
      <td>-1.785111</td>
      <td>0.625050</td>
      <td>-0.050403</td>
      <td>-1.348700</td>
      <td>-0.986220</td>
      <td>-0.622051</td>
      <td>-0.341079</td>
      <td>0.685198</td>
      <td>-0.368885</td>
      <td>-0.959265</td>
      <td>1.403927</td>
      <td>-0.220950</td>
      <td>-0.955664</td>
      <td>0.349077</td>
      <td>0.682713</td>
      <td>-0.288489</td>
      <td>0.452425</td>
      <td>-0.375791</td>
      <td>0.498759</td>
      <td>0.139924</td>
      <td>-1.268148</td>
      <td>-0.666015</td>
      <td>-0.191858</td>
      <td>-0.573640</td>
      <td>0.026685</td>
      <td>0.455582</td>
      <td>0.313600</td>
      <td>-0.333579</td>
      <td>0.104208</td>
      <td>-0.558463</td>
      <td>-0.190323</td>
      <td>0.284577</td>
      <td>-0.194791</td>
      <td>0.128426</td>
      <td>-0.608169</td>
      <td>0.406931</td>
      <td>0.895264</td>
      <td>-0.961967</td>
      <td>0.190351</td>
      <td>1.350123</td>
      <td>0.686568</td>
      <td>-0.279891</td>
      <td>-1.202826</td>
      <td>-0.489005</td>
      <td>-0.351114</td>
      <td>-0.904151</td>
      <td>-0.044474</td>
      <td>-0.405027</td>
      <td>-0.020301</td>
      <td>0.354320</td>
      <td>-1.048716</td>
      <td>-0.658060</td>
      <td>-0.214049</td>
      <td>0.437322</td>
      <td>-0.533336</td>
      <td>-1.427845</td>
      <td>0.001555</td>
      <td>-1.402527</td>
      <td>-0.642802</td>
      <td>0.311100</td>
      <td>0.297687</td>
      <td>0.012913</td>
      <td>0.063775</td>
      <td>-0.761106</td>
      <td>-0.730045</td>
      <td>0.374216</td>
      <td>1.014165</td>
      <td>1.216302</td>
      <td>1.020146</td>
      <td>-0.458028</td>
      <td>0.516614</td>
      <td>-0.779438</td>
      <td>0.243564</td>
      <td>-0.444225</td>
      <td>-0.597184</td>
      <td>0.188072</td>
      <td>-1.071603</td>
      <td>0.408762</td>
      <td>0.425809</td>
      <td>0.084705</td>
      <td>-0.123040</td>
      <td>-0.294552</td>
      <td>-0.904451</td>
      <td>0.942259</td>
      <td>-0.843236</td>
      <td>-0.040780</td>
      <td>-0.252515</td>
      <td>-0.462010</td>
      <td>0.382257</td>
      <td>0.154339</td>
      <td>0.464104</td>
      <td>-0.526791</td>
      <td>-1.124444</td>
      <td>-0.111046</td>
      <td>1.338027</td>
      <td>0.905376</td>
      <td>0.173550</td>
      <td>0.764889</td>
      <td>-0.014846</td>
      <td>0.483808</td>
      <td>0.426599</td>
      <td>-1.132618</td>
      <td>-0.672976</td>
      <td>-0.572813</td>
      <td>0.506950</td>
      <td>0.741505</td>
      <td>-0.385327</td>
      <td>-0.671202</td>
      <td>0.857758</td>
      <td>-0.075177</td>
      <td>0.721694</td>
      <td>-0.412925</td>
      <td>0.483623</td>
      <td>-0.639697</td>
      <td>0.734115</td>
      <td>-0.072498</td>
      <td>0.308754</td>
      <td>-0.081153</td>
      <td>-0.458466</td>
      <td>-0.483102</td>
      <td>0.333534</td>
      <td>0.345522</td>
      <td>-1.096769</td>
      <td>0.608397</td>
      <td>-0.054708</td>
      <td>-0.482211</td>
      <td>0.908046</td>
      <td>-0.049340</td>
      <td>-0.623351</td>
      <td>0.723592</td>
      <td>0.294466</td>
      <td>0.162587</td>
      <td>-0.302653</td>
      <td>0.785858</td>
      <td>0.194512</td>
      <td>0.103003</td>
      <td>-0.631043</td>
      <td>0.437572</td>
      <td>0.717559</td>
      <td>1.079090</td>
      <td>-0.668323</td>
      <td>-0.592381</td>
      <td>-0.043749</td>
      <td>0.642720</td>
      <td>0.687187</td>
      <td>-0.374106</td>
      <td>-0.584802</td>
      <td>-0.633958</td>
      <td>-0.627270</td>
      <td>0.032428</td>
      <td>0.633811</td>
      <td>0.224769</td>
      <td>0.204603</td>
      <td>-0.127556</td>
      <td>-0.512768</td>
      <td>-0.394965</td>
      <td>0.045474</td>
      <td>0.012354</td>
      <td>-0.297480</td>
      <td>0.482510</td>
      <td>0.502125</td>
      <td>-0.037630</td>
      <td>-0.408995</td>
      <td>-0.730836</td>
      <td>-1.618471</td>
      <td>0.011490</td>
      <td>3.093988</td>
      <td>-0.626435</td>
      <td>-0.415893</td>
      <td>0.026395</td>
      <td>0.210604</td>
      <td>-0.206844</td>
      <td>0.323216</td>
      <td>-1.089707</td>
      <td>0.142824</td>
      <td>-0.519357</td>
      <td>-0.652017</td>
      <td>-0.828642</td>
      <td>-0.242051</td>
      <td>-0.020875</td>
      <td>-0.295013</td>
      <td>-1.103870</td>
      <td>0.307825</td>
      <td>0.158511</td>
      <td>-0.039506</td>
      <td>-1.461576</td>
      <td>0.127386</td>
      <td>-0.405284</td>
      <td>-0.750636</td>
      <td>-0.053059</td>
      <td>0.290712</td>
      <td>0.990393</td>
      <td>-0.680900</td>
      <td>0.094528</td>
      <td>-0.257355</td>
      <td>-0.321779</td>
      <td>0.837658</td>
      <td>0.809381</td>
      <td>0.740331</td>
      <td>-0.891785</td>
      <td>-0.690767</td>
      <td>1.518455</td>
      <td>0.574499</td>
      <td>-0.872433</td>
      <td>1.564773</td>
      <td>0.989904</td>
      <td>-0.308978</td>
      <td>-0.223110</td>
      <td>-0.049140</td>
      <td>-0.724908</td>
      <td>1.307570</td>
      <td>-0.683930</td>
      <td>-0.638712</td>
      <td>0.059499</td>
      <td>0.974612</td>
      <td>0.134719</td>
      <td>-0.628033</td>
      <td>-0.172641</td>
      <td>-0.038005</td>
      <td>-0.602068</td>
      <td>0.388629</td>
      <td>-0.023445</td>
      <td>0.840870</td>
      <td>0.523350</td>
      <td>-0.330073</td>
      <td>-0.177467</td>
      <td>0.761205</td>
      <td>0.181797</td>
      <td>0.502041</td>
      <td>-0.270665</td>
      <td>-0.082616</td>
      <td>-0.738765</td>
      <td>1.084965</td>
      <td>0.681325</td>
      <td>1.079946</td>
      <td>-1.111599</td>
      <td>0.639391</td>
      <td>0.322005</td>
      <td>-0.583744</td>
      <td>0.378207</td>
      <td>-0.021189</td>
      <td>-0.414675</td>
      <td>0.920311</td>
      <td>0.069744</td>
      <td>-0.358815</td>
      <td>0.307318</td>
      <td>-0.499400</td>
      <td>0.664541</td>
      <td>-0.236628</td>
      <td>0.466655</td>
      <td>-0.077413</td>
      <td>-1.283791</td>
      <td>1.144776</td>
      <td>0.037101</td>
      <td>-0.386961</td>
      <td>0.911004</td>
      <td>-0.603920</td>
      <td>-0.753906</td>
      <td>-0.843132</td>
      <td>0.733918</td>
      <td>0.561808</td>
      <td>-0.006047</td>
      <td>0.181994</td>
      <td>0.282706</td>
      <td>0.686890</td>
      <td>-0.266012</td>
      <td>0.301245</td>
      <td>-0.241323</td>
      <td>-0.184117</td>
      <td>-0.076802</td>
      <td>0.683208</td>
      <td>-0.450162</td>
      <td>-0.124338</td>
      <td>-0.421635</td>
      <td>-0.266469</td>
      <td>0.921856</td>
      <td>-0.501520</td>
      <td>0.685558</td>
      <td>-0.882166</td>
      <td>0.590724</td>
      <td>0.484261</td>
      <td>0.841183</td>
      <td>0.092095</td>
      <td>0.809915</td>
      <td>-0.426354</td>
      <td>-0.172781</td>
      <td>0.190075</td>
      <td>-0.062322</td>
      <td>0.650091</td>
      <td>0.010122</td>
      <td>-1.199904</td>
      <td>-0.491314</td>
      <td>-0.516819</td>
      <td>-0.162600</td>
      <td>0.090851</td>
      <td>0.203286</td>
      <td>-0.364839</td>
      <td>1.023489</td>
      <td>-0.103253</td>
      <td>-1.326957</td>
      <td>0.485752</td>
      <td>0.974102</td>
      <td>-0.228147</td>
      <td>0.342530</td>
      <td>-0.641922</td>
      <td>0.460936</td>
      <td>0.300045</td>
      <td>-0.136921</td>
      <td>0.148815</td>
      <td>-1.322432</td>
      <td>-0.144711</td>
      <td>0.171124</td>
      <td>0.081574</td>
      <td>0.475565</td>
      <td>-0.565956</td>
      <td>0.561345</td>
      <td>-0.999862</td>
      <td>1.023990</td>
      <td>1.150825</td>
      <td>0.274586</td>
      <td>0.741419</td>
      <td>-0.597265</td>
      <td>0.330013</td>
      <td>1.156340</td>
      <td>0.199728</td>
      <td>0.479578</td>
      <td>1.536973</td>
      <td>-0.294712</td>
      <td>0.212036</td>
      <td>-0.155476</td>
      <td>0.001260</td>
      <td>-0.510498</td>
      <td>0.347624</td>
      <td>-0.297153</td>
      <td>-0.918452</td>
      <td>-0.293628</td>
      <td>-0.573561</td>
      <td>-0.451955</td>
      <td>0.287462</td>
      <td>0.205190</td>
      <td>-0.632471</td>
      <td>0.458675</td>
      <td>-0.110801</td>
      <td>-0.117253</td>
      <td>-0.142374</td>
      <td>0.254105</td>
      <td>0.357354</td>
      <td>-0.543141</td>
      <td>0.109878</td>
      <td>0.747532</td>
      <td>-0.798574</td>
      <td>-0.388326</td>
      <td>-0.007804</td>
      <td>0.474129</td>
      <td>-0.694329</td>
      <td>0.271937</td>
      <td>-0.381635</td>
      <td>-0.568054</td>
      <td>-0.330298</td>
      <td>-0.111264</td>
      <td>0.348828</td>
      <td>-1.019380</td>
      <td>-0.048518</td>
      <td>0.483235</td>
      <td>-0.377910</td>
      <td>-0.605225</td>
      <td>-0.316553</td>
      <td>-0.270212</td>
      <td>-0.083333</td>
      <td>-0.820274</td>
      <td>0.517423</td>
      <td>-0.370262</td>
      <td>-0.723508</td>
      <td>1.148779</td>
      <td>-0.065261</td>
      <td>-0.704028</td>
      <td>1.548249</td>
      <td>-0.654657</td>
      <td>-0.011421</td>
      <td>-0.768140</td>
      <td>0.270917</td>
      <td>-0.190886</td>
      <td>-0.091004</td>
      <td>0.363209</td>
      <td>-0.468869</td>
      <td>0.121042</td>
      <td>0.137770</td>
      <td>0.085168</td>
      <td>1.094716</td>
      <td>0.670233</td>
      <td>-2.017074</td>
      <td>0.109181</td>
      <td>1.060104</td>
      <td>1.226787</td>
      <td>0.642110</td>
      <td>0.525605</td>
      <td>-0.065552</td>
      <td>0.318466</td>
      <td>-1.305665</td>
      <td>-0.359834</td>
      <td>0.812437</td>
      <td>0.575913</td>
      <td>-0.914834</td>
      <td>1.169850</td>
      <td>0.151327</td>
      <td>-0.491500</td>
      <td>0.435567</td>
      <td>-0.341999</td>
      <td>0.363304</td>
      <td>0.252580</td>
      <td>0.014723</td>
      <td>0.249469</td>
      <td>0.917554</td>
      <td>-0.447231</td>
      <td>-0.262724</td>
      <td>0.127017</td>
      <td>0.262839</td>
      <td>0.938128</td>
      <td>0.429672</td>
      <td>-0.516955</td>
      <td>0.404672</td>
      <td>-1.063760</td>
      <td>0.253872</td>
      <td>-1.095232</td>
      <td>-0.119084</td>
      <td>0.235230</td>
      <td>-0.188650</td>
      <td>-1.350977</td>
      <td>-0.560675</td>
      <td>1.140114</td>
      <td>0.976192</td>
      <td>-0.938404</td>
      <td>1.171172</td>
      <td>0.531730</td>
      <td>1.900507</td>
      <td>0.737357</td>
      <td>0.667764</td>
      <td>0.621782</td>
      <td>-0.384273</td>
      <td>0.793444</td>
      <td>-1.191379</td>
      <td>0.698834</td>
      <td>-0.623340</td>
      <td>-0.007624</td>
      <td>0.762209</td>
      <td>1.098214</td>
      <td>0.417315</td>
      <td>0.009803</td>
      <td>0.461323</td>
      <td>-0.320220</td>
      <td>0.592778</td>
      <td>0.151802</td>
      <td>0.158346</td>
      <td>-0.964442</td>
      <td>0.164251</td>
      <td>1.212529</td>
      <td>0.631737</td>
      <td>0.369931</td>
      <td>-0.440473</td>
      <td>-0.342053</td>
      <td>-0.597651</td>
      <td>-0.159050</td>
      <td>-0.939518</td>
      <td>1.022957</td>
      <td>0.550525</td>
      <td>0.771015</td>
      <td>-0.070177</td>
      <td>-0.203459</td>
      <td>-0.446585</td>
      <td>-0.711671</td>
      <td>13.691748</td>
      <td>0.060361</td>
      <td>-0.578421</td>
      <td>0.118817</td>
      <td>13.543860</td>
      <td>766.787</td>
      <td>0</td>
      <td>0.403509</td>
      <td>3.610901</td>
      <td>0.821127</td>
      <td>2833.349277</td>
      <td>13.461585</td>
      <td>13.589371</td>
      <td>6.723865</td>
      <td>4.736863</td>
      <td>11.499024</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>11.814359</td>
      <td>240.022275</td>
      <td>9.589074</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>42.464569</td>
      <td>0.000000</td>
      <td>11.126903</td>
      <td>38.125601</td>
      <td>47.144357</td>
      <td>0.0</td>
      <td>5.316789</td>
      <td>0.000000</td>
      <td>9.790967</td>
      <td>4.899910</td>
      <td>179.979350</td>
      <td>45.996095</td>
      <td>29.913729</td>
      <td>5.687386</td>
      <td>45.996095</td>
      <td>11.761885</td>
      <td>28.945508</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>41.431955</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>111.68</td>
      <td>11.814359</td>
      <td>19.178149</td>
      <td>0.0</td>
      <td>22.941262</td>
      <td>57.060872</td>
      <td>20.440389</td>
      <td>11.383156</td>
      <td>78.491923</td>
      <td>78.862772</td>
      <td>12.132734</td>
      <td>18.947452</td>
      <td>23.761187</td>
      <td>1.559725</td>
      <td>55.003001</td>
      <td>1.343194</td>
      <td>1.747344</td>
      <td>3.180401</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>2</td>
      <td>4</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>9</td>
      <td>13</td>
      <td>2</td>
      <td>11</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>9</td>
      <td>10.04250</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>1</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>4</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>33</td>
      <td>12.172998</td>
      <td>9</td>
    </tr>
    <tr>
      <th>10079</th>
      <td>*c1cccc(P(C)(=O)c2cccc(N3C(=O)c4ccc(Oc5ccc(C(C...</td>
      <td>0.369039</td>
      <td>-0.043249</td>
      <td>0.660890</td>
      <td>-0.402744</td>
      <td>0.137932</td>
      <td>-0.053266</td>
      <td>0.473362</td>
      <td>0.678875</td>
      <td>0.715495</td>
      <td>0.264186</td>
      <td>-0.207188</td>
      <td>1.086668</td>
      <td>-0.302259</td>
      <td>-0.408743</td>
      <td>0.424341</td>
      <td>-0.447788</td>
      <td>-0.775311</td>
      <td>0.895909</td>
      <td>0.459388</td>
      <td>0.390488</td>
      <td>0.183156</td>
      <td>1.328502</td>
      <td>-0.104055</td>
      <td>0.065179</td>
      <td>0.624927</td>
      <td>0.002537</td>
      <td>0.851317</td>
      <td>-0.147777</td>
      <td>-0.217687</td>
      <td>-0.272178</td>
      <td>0.621886</td>
      <td>0.597654</td>
      <td>-0.120206</td>
      <td>0.443553</td>
      <td>-0.516789</td>
      <td>-0.286603</td>
      <td>-1.111177</td>
      <td>-0.687048</td>
      <td>0.117683</td>
      <td>-0.509797</td>
      <td>0.315087</td>
      <td>-0.383803</td>
      <td>0.600521</td>
      <td>-0.531451</td>
      <td>-0.840974</td>
      <td>0.059546</td>
      <td>0.060555</td>
      <td>0.037196</td>
      <td>-0.947848</td>
      <td>0.140316</td>
      <td>0.254109</td>
      <td>0.445181</td>
      <td>0.214252</td>
      <td>0.210050</td>
      <td>-0.035355</td>
      <td>-0.568592</td>
      <td>-0.606020</td>
      <td>0.629480</td>
      <td>-0.396130</td>
      <td>-0.850572</td>
      <td>-0.844694</td>
      <td>-0.097834</td>
      <td>-0.770516</td>
      <td>0.634392</td>
      <td>0.652205</td>
      <td>-0.046640</td>
      <td>0.216075</td>
      <td>-0.627928</td>
      <td>-0.347609</td>
      <td>0.140176</td>
      <td>-0.556661</td>
      <td>0.446955</td>
      <td>0.594255</td>
      <td>-0.280132</td>
      <td>-0.135167</td>
      <td>-0.481475</td>
      <td>0.136407</td>
      <td>0.162482</td>
      <td>-0.610041</td>
      <td>-0.679850</td>
      <td>-0.463511</td>
      <td>-0.088266</td>
      <td>0.169667</td>
      <td>-0.413728</td>
      <td>0.719203</td>
      <td>0.127904</td>
      <td>-0.524719</td>
      <td>-0.475547</td>
      <td>-0.581035</td>
      <td>-0.113144</td>
      <td>0.254157</td>
      <td>-0.020054</td>
      <td>-0.145538</td>
      <td>0.304256</td>
      <td>-0.026201</td>
      <td>-0.387912</td>
      <td>1.083003</td>
      <td>0.095266</td>
      <td>-0.760514</td>
      <td>-0.118205</td>
      <td>-0.291681</td>
      <td>-0.208840</td>
      <td>-0.044419</td>
      <td>-0.158034</td>
      <td>0.348098</td>
      <td>0.670340</td>
      <td>-0.767062</td>
      <td>0.253347</td>
      <td>0.423309</td>
      <td>0.881346</td>
      <td>1.402677</td>
      <td>-1.343613</td>
      <td>-0.120802</td>
      <td>0.048719</td>
      <td>-0.691341</td>
      <td>-0.671780</td>
      <td>0.611386</td>
      <td>-0.331789</td>
      <td>-0.099400</td>
      <td>-1.218476</td>
      <td>-1.368940</td>
      <td>-0.234212</td>
      <td>-0.113268</td>
      <td>0.586402</td>
      <td>-0.268784</td>
      <td>-0.462765</td>
      <td>0.124791</td>
      <td>-0.140996</td>
      <td>-0.487228</td>
      <td>0.683517</td>
      <td>-1.249701</td>
      <td>0.639691</td>
      <td>-0.240641</td>
      <td>-1.218180</td>
      <td>-0.983482</td>
      <td>-0.716460</td>
      <td>-0.614366</td>
      <td>0.707718</td>
      <td>-0.312659</td>
      <td>-1.182350</td>
      <td>1.261393</td>
      <td>-0.243395</td>
      <td>-0.732019</td>
      <td>0.650010</td>
      <td>0.758688</td>
      <td>0.197676</td>
      <td>0.458859</td>
      <td>-0.387125</td>
      <td>0.662059</td>
      <td>-0.050143</td>
      <td>-1.034317</td>
      <td>-0.665217</td>
      <td>0.130746</td>
      <td>-0.779774</td>
      <td>-0.384037</td>
      <td>0.220153</td>
      <td>0.059598</td>
      <td>-0.330662</td>
      <td>-0.155693</td>
      <td>-0.573480</td>
      <td>-0.533617</td>
      <td>0.549843</td>
      <td>-0.237640</td>
      <td>0.190595</td>
      <td>-0.427708</td>
      <td>0.624859</td>
      <td>0.655301</td>
      <td>-0.454380</td>
      <td>0.293191</td>
      <td>0.990904</td>
      <td>0.576746</td>
      <td>-0.438055</td>
      <td>-1.209643</td>
      <td>-0.541841</td>
      <td>-0.469823</td>
      <td>-0.539115</td>
      <td>-0.026693</td>
      <td>-0.649362</td>
      <td>-0.265729</td>
      <td>0.130621</td>
      <td>-1.084508</td>
      <td>-0.313099</td>
      <td>-0.219248</td>
      <td>0.298435</td>
      <td>-0.391245</td>
      <td>-1.137087</td>
      <td>-0.268155</td>
      <td>-1.224360</td>
      <td>-0.821644</td>
      <td>0.544793</td>
      <td>-0.017826</td>
      <td>0.108273</td>
      <td>0.072510</td>
      <td>-0.977927</td>
      <td>-0.697379</td>
      <td>0.520672</td>
      <td>1.043342</td>
      <td>0.819093</td>
      <td>0.679145</td>
      <td>-0.100335</td>
      <td>0.544495</td>
      <td>-0.973113</td>
      <td>-0.306225</td>
      <td>-0.607068</td>
      <td>-0.277880</td>
      <td>-0.000262</td>
      <td>-0.943749</td>
      <td>0.389079</td>
      <td>0.093718</td>
      <td>0.341879</td>
      <td>-0.407147</td>
      <td>-0.115192</td>
      <td>-0.762897</td>
      <td>0.838706</td>
      <td>-0.529754</td>
      <td>0.003675</td>
      <td>-0.176784</td>
      <td>-0.541776</td>
      <td>0.514480</td>
      <td>-0.123412</td>
      <td>0.412839</td>
      <td>-0.524611</td>
      <td>-1.293336</td>
      <td>-0.531038</td>
      <td>1.258891</td>
      <td>0.912249</td>
      <td>0.566534</td>
      <td>0.853961</td>
      <td>0.224777</td>
      <td>0.516537</td>
      <td>0.129973</td>
      <td>-1.165987</td>
      <td>-0.506349</td>
      <td>-0.275049</td>
      <td>0.762200</td>
      <td>0.769296</td>
      <td>-0.473387</td>
      <td>-0.756377</td>
      <td>0.604247</td>
      <td>-0.161140</td>
      <td>0.740915</td>
      <td>-0.167840</td>
      <td>0.590262</td>
      <td>-0.941078</td>
      <td>0.673430</td>
      <td>-0.026137</td>
      <td>0.512550</td>
      <td>-0.263398</td>
      <td>-0.371042</td>
      <td>-0.268270</td>
      <td>0.606282</td>
      <td>-0.224472</td>
      <td>-0.844111</td>
      <td>0.772485</td>
      <td>0.109809</td>
      <td>-0.330664</td>
      <td>0.314628</td>
      <td>-0.120404</td>
      <td>-0.546395</td>
      <td>0.752435</td>
      <td>0.224702</td>
      <td>0.206314</td>
      <td>0.237566</td>
      <td>0.999643</td>
      <td>0.435517</td>
      <td>-0.468175</td>
      <td>-0.083159</td>
      <td>0.186367</td>
      <td>0.674037</td>
      <td>0.975376</td>
      <td>-0.321813</td>
      <td>-0.311429</td>
      <td>0.158849</td>
      <td>0.382845</td>
      <td>0.627684</td>
      <td>-0.373657</td>
      <td>-0.075749</td>
      <td>-0.463588</td>
      <td>-0.381451</td>
      <td>0.537346</td>
      <td>0.521875</td>
      <td>0.183289</td>
      <td>0.169223</td>
      <td>-0.054709</td>
      <td>-0.514342</td>
      <td>-0.275630</td>
      <td>0.521645</td>
      <td>-0.326867</td>
      <td>-0.571025</td>
      <td>0.475274</td>
      <td>-0.015652</td>
      <td>0.579151</td>
      <td>-0.561114</td>
      <td>-0.552252</td>
      <td>-1.630036</td>
      <td>-0.023692</td>
      <td>2.696522</td>
      <td>0.159561</td>
      <td>-0.346912</td>
      <td>0.013391</td>
      <td>0.054682</td>
      <td>0.065660</td>
      <td>0.543885</td>
      <td>-1.195165</td>
      <td>0.447343</td>
      <td>-0.367580</td>
      <td>-0.492598</td>
      <td>-0.409670</td>
      <td>0.132501</td>
      <td>0.421190</td>
      <td>-0.139593</td>
      <td>-1.263308</td>
      <td>0.489597</td>
      <td>0.389798</td>
      <td>-0.172062</td>
      <td>-1.211872</td>
      <td>0.147059</td>
      <td>-0.081647</td>
      <td>-1.157706</td>
      <td>0.001447</td>
      <td>0.484984</td>
      <td>1.075757</td>
      <td>-0.423902</td>
      <td>-0.138894</td>
      <td>-0.616977</td>
      <td>-0.478212</td>
      <td>1.017780</td>
      <td>0.723508</td>
      <td>0.756592</td>
      <td>-0.735426</td>
      <td>-0.494695</td>
      <td>1.613677</td>
      <td>0.152777</td>
      <td>-0.976822</td>
      <td>1.569541</td>
      <td>0.900090</td>
      <td>-0.274098</td>
      <td>-0.332246</td>
      <td>-0.384452</td>
      <td>-0.276587</td>
      <td>1.141283</td>
      <td>-0.322410</td>
      <td>-0.295992</td>
      <td>-0.002009</td>
      <td>1.259238</td>
      <td>-0.098514</td>
      <td>-0.544605</td>
      <td>-0.172737</td>
      <td>0.243965</td>
      <td>-0.781328</td>
      <td>0.621464</td>
      <td>-0.354980</td>
      <td>0.634054</td>
      <td>0.295871</td>
      <td>-0.412947</td>
      <td>-0.396564</td>
      <td>0.527912</td>
      <td>-0.031362</td>
      <td>0.444909</td>
      <td>0.234547</td>
      <td>-0.177920</td>
      <td>-0.796240</td>
      <td>1.240339</td>
      <td>1.150383</td>
      <td>1.337692</td>
      <td>-0.642223</td>
      <td>0.503551</td>
      <td>0.129641</td>
      <td>-0.613797</td>
      <td>0.032454</td>
      <td>0.220131</td>
      <td>-0.396629</td>
      <td>0.693286</td>
      <td>0.075490</td>
      <td>-0.374080</td>
      <td>0.334748</td>
      <td>-0.176680</td>
      <td>0.432170</td>
      <td>-0.079749</td>
      <td>0.242946</td>
      <td>0.221320</td>
      <td>-0.900356</td>
      <td>1.055851</td>
      <td>-0.011652</td>
      <td>-0.172500</td>
      <td>0.689498</td>
      <td>-0.649486</td>
      <td>-0.094378</td>
      <td>-0.908535</td>
      <td>0.870725</td>
      <td>0.342956</td>
      <td>0.296066</td>
      <td>0.560035</td>
      <td>0.636201</td>
      <td>0.540190</td>
      <td>-0.124791</td>
      <td>-0.085206</td>
      <td>-0.210804</td>
      <td>-0.393770</td>
      <td>-0.168207</td>
      <td>0.559881</td>
      <td>-0.605798</td>
      <td>-0.374425</td>
      <td>-0.396779</td>
      <td>-0.595774</td>
      <td>0.926226</td>
      <td>0.053677</td>
      <td>0.878096</td>
      <td>-1.003994</td>
      <td>0.242938</td>
      <td>0.611902</td>
      <td>0.844095</td>
      <td>-0.600902</td>
      <td>0.740815</td>
      <td>-0.241503</td>
      <td>0.057335</td>
      <td>0.087881</td>
      <td>-0.529639</td>
      <td>0.327657</td>
      <td>0.040051</td>
      <td>-1.055550</td>
      <td>-0.381083</td>
      <td>-0.462068</td>
      <td>0.044250</td>
      <td>0.242302</td>
      <td>0.123518</td>
      <td>-0.424119</td>
      <td>1.040155</td>
      <td>-0.256748</td>
      <td>-1.182931</td>
      <td>0.244309</td>
      <td>0.879464</td>
      <td>-0.271464</td>
      <td>0.221808</td>
      <td>-0.470630</td>
      <td>0.154710</td>
      <td>0.407809</td>
      <td>-0.098548</td>
      <td>0.177527</td>
      <td>-1.298912</td>
      <td>-0.229619</td>
      <td>0.198479</td>
      <td>0.602800</td>
      <td>0.396099</td>
      <td>-0.238013</td>
      <td>0.522437</td>
      <td>-0.875104</td>
      <td>0.830756</td>
      <td>1.300736</td>
      <td>0.029667</td>
      <td>0.327922</td>
      <td>-0.255597</td>
      <td>0.564263</td>
      <td>0.889812</td>
      <td>0.348440</td>
      <td>0.517732</td>
      <td>0.487833</td>
      <td>-0.107553</td>
      <td>-0.255619</td>
      <td>0.189561</td>
      <td>0.107297</td>
      <td>-0.579687</td>
      <td>0.431200</td>
      <td>0.276307</td>
      <td>-1.313200</td>
      <td>-0.460165</td>
      <td>-0.476275</td>
      <td>0.057798</td>
      <td>0.281974</td>
      <td>0.779415</td>
      <td>-0.536593</td>
      <td>-0.034783</td>
      <td>0.082456</td>
      <td>-0.035031</td>
      <td>-0.107160</td>
      <td>0.052643</td>
      <td>0.256213</td>
      <td>-0.260574</td>
      <td>-0.128079</td>
      <td>0.839780</td>
      <td>-0.669934</td>
      <td>-0.151643</td>
      <td>0.265855</td>
      <td>0.340587</td>
      <td>-0.141833</td>
      <td>0.174553</td>
      <td>-0.622808</td>
      <td>-0.498428</td>
      <td>-0.389983</td>
      <td>-0.010768</td>
      <td>0.389972</td>
      <td>-1.316319</td>
      <td>-0.248646</td>
      <td>0.505854</td>
      <td>-0.544716</td>
      <td>-0.223339</td>
      <td>-0.340018</td>
      <td>-0.146952</td>
      <td>-0.108015</td>
      <td>-0.622416</td>
      <td>0.181660</td>
      <td>-0.627802</td>
      <td>-0.671028</td>
      <td>1.175888</td>
      <td>0.009531</td>
      <td>-0.482256</td>
      <td>1.151246</td>
      <td>0.046935</td>
      <td>-0.063049</td>
      <td>-1.192843</td>
      <td>0.179404</td>
      <td>-0.014395</td>
      <td>-0.196558</td>
      <td>0.501363</td>
      <td>-0.426860</td>
      <td>0.277684</td>
      <td>0.113147</td>
      <td>0.040683</td>
      <td>0.460575</td>
      <td>0.122547</td>
      <td>-2.071166</td>
      <td>0.083549</td>
      <td>1.231856</td>
      <td>1.507224</td>
      <td>0.634347</td>
      <td>0.337673</td>
      <td>-0.445342</td>
      <td>0.195165</td>
      <td>-1.482522</td>
      <td>-0.176988</td>
      <td>0.688208</td>
      <td>0.043829</td>
      <td>-1.140020</td>
      <td>1.103954</td>
      <td>0.117298</td>
      <td>-0.625397</td>
      <td>0.473467</td>
      <td>-0.308458</td>
      <td>0.222662</td>
      <td>-0.172761</td>
      <td>0.326434</td>
      <td>0.247866</td>
      <td>0.940201</td>
      <td>-0.534463</td>
      <td>-0.328762</td>
      <td>0.170813</td>
      <td>-0.119969</td>
      <td>1.041682</td>
      <td>0.096864</td>
      <td>-0.683999</td>
      <td>0.407326</td>
      <td>-0.910444</td>
      <td>0.478007</td>
      <td>-1.285237</td>
      <td>-0.651004</td>
      <td>-0.042002</td>
      <td>-0.070483</td>
      <td>-0.740397</td>
      <td>-0.133831</td>
      <td>0.966545</td>
      <td>0.569861</td>
      <td>-1.241007</td>
      <td>0.652104</td>
      <td>0.504914</td>
      <td>1.567455</td>
      <td>0.101282</td>
      <td>0.677266</td>
      <td>0.600910</td>
      <td>-0.579663</td>
      <td>0.703269</td>
      <td>-1.022629</td>
      <td>0.760606</td>
      <td>-0.452359</td>
      <td>0.759498</td>
      <td>0.354656</td>
      <td>0.382120</td>
      <td>0.975273</td>
      <td>-0.174147</td>
      <td>0.447511</td>
      <td>-0.630674</td>
      <td>0.433006</td>
      <td>0.273373</td>
      <td>0.334696</td>
      <td>-1.089139</td>
      <td>0.021949</td>
      <td>1.026674</td>
      <td>1.363261</td>
      <td>0.026831</td>
      <td>-0.389176</td>
      <td>-0.211221</td>
      <td>-0.317796</td>
      <td>0.341007</td>
      <td>-0.969590</td>
      <td>1.129823</td>
      <td>0.086033</td>
      <td>0.912231</td>
      <td>0.207724</td>
      <td>-0.033727</td>
      <td>-0.786453</td>
      <td>-0.687229</td>
      <td>14.004152</td>
      <td>0.054998</td>
      <td>-2.954693</td>
      <td>0.111504</td>
      <td>15.388889</td>
      <td>730.713</td>
      <td>0</td>
      <td>0.555556</td>
      <td>3.568684</td>
      <td>0.950153</td>
      <td>2678.255071</td>
      <td>12.546484</td>
      <td>11.981679</td>
      <td>5.522707</td>
      <td>4.736863</td>
      <td>5.749512</td>
      <td>0.0</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>277.581345</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>0.0</td>
      <td>0.0</td>
      <td>12.132734</td>
      <td>12.132734</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>33.216923</td>
      <td>53.133445</td>
      <td>0.0</td>
      <td>5.316789</td>
      <td>0.000000</td>
      <td>19.262465</td>
      <td>11.564735</td>
      <td>166.840784</td>
      <td>22.998047</td>
      <td>31.049082</td>
      <td>5.687386</td>
      <td>22.998047</td>
      <td>7.141893</td>
      <td>35.610333</td>
      <td>9.980039</td>
      <td>0.000000</td>
      <td>66.406332</td>
      <td>0.000000</td>
      <td>0.000000</td>
      <td>110.29</td>
      <td>18.956252</td>
      <td>23.743197</td>
      <td>0.0</td>
      <td>28.356253</td>
      <td>45.116876</td>
      <td>21.331353</td>
      <td>11.383156</td>
      <td>67.328496</td>
      <td>66.730038</td>
      <td>19.913841</td>
      <td>9.473726</td>
      <td>25.581224</td>
      <td>0.000000</td>
      <td>53.177618</td>
      <td>2.699111</td>
      <td>3.450967</td>
      <td>0.739258</td>
      <td>0.000000</td>
      <td>5.974132</td>
      <td>-2.954693</td>
      <td>0.090909</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>2</td>
      <td>4</td>
      <td>0</td>
      <td>1</td>
      <td>0</td>
      <td>7</td>
      <td>12</td>
      <td>2</td>
      <td>9</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>1</td>
      <td>8</td>
      <td>7.57630</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>1</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>2</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>0</td>
      <td>29</td>
      <td>10.912987</td>
      <td>8</td>
    </tr>
  </tbody>
</table>
<p>10080 rows × 761 columns</p>
</div>




```python
# Let’s define a reusable function to train and evaluate our machine learning model.

def model(train_d,test_d,model,target,submission=False):
    # We divide the data into training and validation sets for model evaluation
    X=train_d.drop(target,axis=1)
    y=train_d[target].copy()
    X_train,X_test,y_train,y_test=train_test_split(X,y,test_size=0.2,random_state=10)

    Model=model()
    if submission==False:
       Model.fit(X_train,y_train)
       y_pred=Model.predict(X_test)
       return mean_absolute_error(y_pred,y_test)         # We assess our model performance using MAE metric
    if submission==True:
       Model.fit(X,y)
       submission=Model.predict(test_d)
       return submission
```

# Model Evaluation


```python
from sklearn.ensemble import (
    RandomForestRegressor,
    ExtraTreesRegressor,
    GradientBoostingRegressor,
    HistGradientBoostingRegressor
)
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from catboost import CatBoostRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet, HuberRegressor
from sklearn.neighbors import KNeighborsRegressor
from sklearn.svm import SVR
from sklearn.ensemble import AdaBoostRegressor
```

## Tg


```python
models_to_compare = {
    'RandomForest': lambda: RandomForestRegressor(),
    'ExtraTrees': lambda: ExtraTreesRegressor(),
    'GradientBoosting': lambda: GradientBoostingRegressor(),
    'HistGradientBoosting': lambda: HistGradientBoostingRegressor(),
    'XGBoost': lambda: XGBRegressor(verbosity=0),
    'LightGBM': lambda: LGBMRegressor(verbose=-1),
    'CatBoost': lambda: CatBoostRegressor(verbose=0)
}

for name, model_init in models_to_compare.items():
    try:
        score = model(tg, test, model_init, 'Tg', submission=False)
        print(f'{name:<20}: MAE = {score:.5f}')
    except Exception as e:
        print(f'{name:<20}: Failed with error: {e}')
```

    RandomForest        : MAE = 38.55721
    ExtraTrees          : MAE = 35.44718
    GradientBoosting    : MAE = 39.44760
    HistGradientBoosting: MAE = 36.55973
    XGBoost             : MAE = 40.93735
    LightGBM            : MAE = 37.30797
    CatBoost            : MAE = 35.97764


## FFV


```python
models_to_compare = {
    'RandomForest': lambda: RandomForestRegressor(),
    'ExtraTrees': lambda: ExtraTreesRegressor(),
    'GradientBoosting': lambda: GradientBoostingRegressor(),
    'HistGradientBoosting': lambda: HistGradientBoostingRegressor(),
    'XGBoost': lambda: XGBRegressor(verbosity=0),
    'LightGBM': lambda: LGBMRegressor(verbose=-1),
    'CatBoost': lambda: CatBoostRegressor(verbose=0)
}

for name, model_cls in models_to_compare.items():
    try:
        score = model(ffv, test, model_cls, 'FFV', submission=False)
        print(f'{name:<20}: MAE = {score:.5f}')
    except Exception as e:
        print(f'{name:<20}: Failed with error: {e}')
```

    RandomForest        : MAE = 0.00786
    ExtraTrees          : MAE = 0.00680
    GradientBoosting    : MAE = 0.00893
    HistGradientBoosting: MAE = 0.00712
    XGBoost             : MAE = 0.00745
    LightGBM            : MAE = 0.00721
    CatBoost            : MAE = 0.00639


## Tc


```python
models_to_compare = {
    'RandomForest': lambda: RandomForestRegressor(),
    'ExtraTrees': lambda: ExtraTreesRegressor(),
    'GradientBoosting': lambda: GradientBoostingRegressor(),
    'HistGradientBoosting': lambda: HistGradientBoostingRegressor(),
    'XGBoost': lambda: XGBRegressor(verbosity=0),
    'LightGBM': lambda: LGBMRegressor(verbose=-1),
    'CatBoost': lambda: CatBoostRegressor(verbose=0)
}

for name, model_cls in models_to_compare.items():
    try:
        score = model(tc, test, model_cls, 'Tc', submission=False)
        print(f'{name:<20}: MAE = {score:.5f}')
    except Exception as e:
        print(f'{name:<20}: Failed with error: {e}')
```

    RandomForest        : MAE = 0.03393
    ExtraTrees          : MAE = 0.03397
    GradientBoosting    : MAE = 0.03439
    HistGradientBoosting: MAE = 0.03430
    XGBoost             : MAE = 0.03504
    LightGBM            : MAE = 0.03376
    CatBoost            : MAE = 0.03271



```python

```

## Density


```python
models_to_compare = {
    'RandomForest': lambda: RandomForestRegressor(),
    'ExtraTrees': lambda: ExtraTreesRegressor(),
    'GradientBoosting': lambda: GradientBoostingRegressor(),
    'HistGradientBoosting': lambda: HistGradientBoostingRegressor(),
    'XGBoost': lambda: XGBRegressor(verbosity=0),
    'LightGBM': lambda: LGBMRegressor(verbose=-1),
    'CatBoost': lambda: CatBoostRegressor(verbose=0)
}

for name, model_cls in models_to_compare.items():
    try:
        score = model(density, test, model_cls, 'Density', submission=False)
        print(f'{name:<20}: MAE = {score:.5f}')
    except Exception as e:
        print(f'{name:<20}: Failed with error: {e}')
```

    RandomForest        : MAE = 0.05562
    ExtraTrees          : MAE = 0.04356
    GradientBoosting    : MAE = 0.05003
    HistGradientBoosting: MAE = 0.05060
    XGBoost             : MAE = 0.05586
    LightGBM            : MAE = 0.04848
    CatBoost            : MAE = 0.04514


## Rg


```python
models_to_compare = {
    'RandomForest': lambda: RandomForestRegressor(),
    'ExtraTrees': lambda: ExtraTreesRegressor(),
    'GradientBoosting': lambda: GradientBoostingRegressor(),
    'HistGradientBoosting': lambda: HistGradientBoostingRegressor(),
    'XGBoost': lambda: XGBRegressor(verbosity=0),
    'LightGBM': lambda: LGBMRegressor(verbose=-1),
    'CatBoost': lambda: CatBoostRegressor(verbose=0)
}

for name, model_cls in models_to_compare.items():
    try:
        score = model(rg, test, model_cls, 'Rg', submission=False)
        print(f'{name:<20}: MAE = {score:.5f}')
    except Exception as e:
        print(f'{name:<20}: Failed with error: {e}')
```

    RandomForest        : MAE = 1.61364
    ExtraTrees          : MAE = 1.57211
    GradientBoosting    : MAE = 1.56786
    HistGradientBoosting: MAE = 1.57238
    XGBoost             : MAE = 1.71030
    LightGBM            : MAE = 1.62575
    CatBoost            : MAE = 1.55117


# Final Model For Submission


```python
from catboost import CatBoostRegressor
from sklearn.ensemble import ExtraTreesRegressor

sub = {
    'id': ID,
    'Tg': model(tg, test, lambda: ExtraTreesRegressor(), 'Tg', submission=True),
    'FFV': model(ffv, test, lambda: CatBoostRegressor(verbose=0), 'FFV', submission=True),
    'Tc': model(tc, test, lambda: CatBoostRegressor(verbose=0), 'Tc', submission=True),
    'Density': model(density, test, lambda: ExtraTreesRegressor(), 'Density', submission=True),
    'Rg': model(rg, test, lambda: ExtraTreesRegressor(), 'Rg', submission=True)  # ← 修正ここ
}
```


```python
submission=pd.DataFrame(sub)
submission
```




<div>
<style scoped>
    .dataframe tbody tr th:only-of-type {
        vertical-align: middle;
    }

    .dataframe tbody tr th {
        vertical-align: top;
    }

    .dataframe thead th {
        text-align: right;
    }
</style>
<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>id</th>
      <th>Tg</th>
      <th>FFV</th>
      <th>Tc</th>
      <th>Density</th>
      <th>Rg</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>0</th>
      <td>1109053969</td>
      <td>195.234045</td>
      <td>0.369132</td>
      <td>0.214392</td>
      <td>1.148728</td>
      <td>21.571706</td>
    </tr>
    <tr>
      <th>1</th>
      <td>1422188626</td>
      <td>161.982490</td>
      <td>0.377761</td>
      <td>0.239479</td>
      <td>1.097845</td>
      <td>22.053685</td>
    </tr>
    <tr>
      <th>2</th>
      <td>2032016830</td>
      <td>119.497294</td>
      <td>0.351962</td>
      <td>0.228728</td>
      <td>1.099815</td>
      <td>19.810475</td>
    </tr>
  </tbody>
</table>
</div>




```python
submission.to_csv('submission.csv',index=False)
```


```python

```

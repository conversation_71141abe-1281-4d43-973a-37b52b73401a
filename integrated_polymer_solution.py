"""
NEURIPs Open Polymer Prediction 2025 - Integrated Solution
Multi-task learning approach for polymer property prediction from SMILES
Integrates: train_polymer_model.py, polymer_competition_solution.py, 
kaggle_polymer_predict.py, polymer_dataset_analyzer.py
"""

# =============================================================================
# CELL 1: Import Dependencies and Setup
# =============================================================================

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Core ML libraries
from sklearn.model_selection import KFold, StratifiedKFold
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_absolute_error
from sklearn.ensemble import HistGradientBoostingRegressor, ExtraTreesRegressor
import joblib

# XGBoost and optimization
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    print("XGBoost not available - falling back to sklearn models")
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    XGBOOST_AVAILABLE = False

try:
    import optuna
    OPTUNA_AVAILABLE = True
except ImportError:
    print("Optuna not available - using default hyperparameters")
    OPTUNA_AVAILABLE = False

# Molecular processing
try:
    from rdkit import Chem
    from rdkit.Chem import Descriptors, rdMolDescriptors, Crippen
    from rdkit.Chem import AllChem, rdmolops
    from rdkit.ML.Descriptors import MoleculeDescriptors
    RDKIT_AVAILABLE = True
except ImportError:
    print("RDKit not available - using fallback features")
    RDKIT_AVAILABLE = False

# Graph analysis
try:
    import networkx as nx
    NETWORKX_AVAILABLE = True
except ImportError:
    print("NetworkX not available - skipping graph features")
    NETWORKX_AVAILABLE = False

# PolyBERT embeddings
try:
    from sentence_transformers import SentenceTransformer
    POLYBERT_AVAILABLE = True
except ImportError:
    print("SentenceTransformers not available - skipping PolyBERT embeddings")
    POLYBERT_AVAILABLE = False

import os
import json
from pathlib import Path
import time
from tqdm import tqdm

# =============================================================================
# CELL 2: Configuration and Constants
# =============================================================================

class CFG:
    TARGETS = ['Tg', 'FFV', 'Tc', 'Density', 'Rg']
    SEED = 42
    FOLDS = 5
    WORK_DIR = Path(".")  # Current directory

# Features to exclude based on analysis
USELESS_COLS = [
    # NaN > 80%
    'Tg', 'Tc', 'Density', 'Rg',
    'MaxPartialCharge', 'MinPartialCharge', 'MaxAbsPartialCharge', 'MinAbsPartialCharge',
    'BCUT2D_MWHI', 'BCUT2D_MWLOW', 'BCUT2D_CHGHI', 'BCUT2D_CHGLO',
    'BCUT2D_LOGPHI', 'BCUT2D_LOGPLOW', 'BCUT2D_MRHI', 'BCUT2D_MRLOW',
    
    # Constant columns
    'SMR_VSA8', 'SlogP_VSA9', 'fr_isothiocyan', 'fr_prisulfonamd',
    
    # High correlation (|r| > 0.95)
    'MaxEStateIndex', 'HeavyAtomMolWt', 'ExactMolWt', 'NumValenceElectrons',
    'FpDensityMorgan2', 'FpDensityMorgan3',
    'Chi0', 'Chi0n', 'Chi0v', 'Chi1', 'Chi1n', 'Chi1v',
    'Chi2n', 'Chi2v', 'Chi3n', 'Chi3v', 'Chi4n', 'Chi4v',
    'HallKierAlpha', 'Kappa1', 'LabuteASA', 'SlogP_VSA6', 'VSA_EState6',
    'HeavyAtomCount', 'NOCount', 'NumAromaticCarbocycles', 'NumAromaticRings',
    'NumHDonors', 'Phi', 'MolMR',
    'fr_Al_OH_noTert', 'fr_COO2', 'fr_C_O', 'fr_C_O_noCOO',
    'fr_Nhpyrrole', 'fr_amide', 'fr_benzene', 'fr_diazo',
    'fr_halogen', 'fr_nitrile', 'fr_nitro_arom',
    'fr_phenol', 'fr_phenol_noOrthoHbond', 'fr_phos_ester'
]

# GPU detection
def detect_gpu():
    """Detect if GPU is available for XGBoost"""
    try:
        import GPUtil
        gpus = GPUtil.getGPUs()
        if gpus:
            print(f"GPU detected: {gpus[0].name}")
            return True
    except:
        pass
    
    try:
        import torch
        if torch.cuda.is_available():
            print(f"CUDA GPU detected: {torch.cuda.get_device_name(0)}")
            return True
    except:
        pass
    
    print("No GPU detected, using CPU")
    return False

GPU_AVAILABLE = detect_gpu()

# =============================================================================
# CELL 3: Data Loading and Preprocessing Functions
# =============================================================================

def make_smile_canonical(smile):
    """Convert SMILES to canonical form to avoid duplicates"""
    try:
        mol = Chem.MolFromSmiles(smile)
        if mol is None:
            return np.nan
        canon_smile = Chem.MolToSmiles(mol, canonical=True)
        return canon_smile
    except:
        return np.nan

def load_main_data():
    """Load main training and test datasets"""
    print("=== Loading Main Datasets ===")
    
    train = pd.read_csv('train.csv')
    test = pd.read_csv('test.csv')
    
    print(f"Train shape: {train.shape}")
    print(f"Test shape: {test.shape}")
    
    # Canonicalize SMILES
    train['SMILES'] = train['SMILES'].apply(make_smile_canonical)
    test['SMILES'] = test['SMILES'].apply(make_smile_canonical)
    
    return train, test

def load_supplementary_data():
    """Load supplementary datasets"""
    print("=== Loading Supplementary Datasets ===")
    
    datasets = {}
    
    # Load supplementary datasets if available
    supplement_files = [
        ('dataset1.csv', 'train_supplement/dataset1.csv'),
        ('dataset2.csv', 'train_supplement/dataset2.csv'), 
        ('dataset3.csv', 'train_supplement/dataset3.csv'),
        ('dataset4.csv', 'train_supplement/dataset4.csv')
    ]
    
    for name, path in supplement_files:
        try:
            df = pd.read_csv(path)
            df['SMILES'] = df['SMILES'].apply(make_smile_canonical)
            datasets[name] = df
            print(f"Loaded {name}: {df.shape}")
        except FileNotFoundError:
            print(f"File not found: {path}")
    
    return datasets

def add_extra_data(df_train, df_extra, target):
    """Add extra data for specific target"""
    if df_extra is None or df_extra.empty:
        return df_train
        
    n_samples_before = len(df_train[df_train[target].notnull()])
    
    # Group by SMILES and take mean for duplicates
    df_extra = df_extra.groupby('SMILES', as_index=False)[target].mean()
    
    cross_smiles = set(df_extra['SMILES']) & set(df_train['SMILES'])
    unique_smiles_extra = set(df_extra['SMILES']) - set(df_train['SMILES'])

    # Priority to competition data - remove overlaps from extra data
    for smile in df_train[df_train[target].notnull()]['SMILES'].tolist():
        if smile in cross_smiles:
            cross_smiles.remove(smile)

    # Fill missing values for competition SMILES
    for smile in cross_smiles:
        df_train.loc[df_train['SMILES']==smile, target] = df_extra[df_extra['SMILES']==smile][target].values[0]
    
    # Add unique SMILES from extra data
    extra_rows = df_extra[df_extra['SMILES'].isin(unique_smiles_extra)].copy()
    if not extra_rows.empty:
        # Add missing columns with NaN
        for col in df_train.columns:
            if col not in extra_rows.columns:
                extra_rows[col] = np.nan
        
        df_train = pd.concat([df_train, extra_rows], axis=0, ignore_index=True)

    n_samples_after = len(df_train[df_train[target].notnull()])
    print(f'For target "{target}" added {n_samples_after-n_samples_before} new samples!')
    print(f'New unique SMILES: {len(unique_smiles_extra)}')
    
    return df_train

# =============================================================================
# CELL 4: Feature Engineering Functions
# =============================================================================

def extract_molecular_features(smiles_list):
    """Extract comprehensive molecular features from SMILES"""
    print("Extracting molecular features...")
    
    if not RDKIT_AVAILABLE:
        print("RDKit not available, using fallback features")
        return extract_fallback_features_batch(smiles_list)
    
    # Get descriptor names (excluding useless ones)
    descriptor_names = [desc[0] for desc in Descriptors._descList if desc[0] not in USELESS_COLS]
    calc = MoleculeDescriptors.MolecularDescriptorCalculator(descriptor_names)
    
    features_list = []
    for smiles in tqdm(smiles_list, desc="Computing molecular descriptors"):
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is not None:
                descriptors = calc.CalcDescriptors(mol)
                features_list.append(descriptors)
            else:
                features_list.append([np.nan] * len(descriptor_names))
        except Exception as e:
            print(f"Error processing SMILES {smiles}: {e}")
            features_list.append([np.nan] * len(descriptor_names))
    
    features_df = pd.DataFrame(features_list, columns=descriptor_names)
    print(f"Extracted {features_df.shape[1]} molecular features")
    return features_df

def extract_graph_features(smiles_list):
    """Extract graph-based features"""
    if not NETWORKX_AVAILABLE or not RDKIT_AVAILABLE:
        print("NetworkX or RDKit not available, skipping graph features")
        return pd.DataFrame()
    
    print("Extracting graph features...")
    graph_features = []
    
    for smiles in tqdm(smiles_list, desc="Computing graph features"):
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is not None:
                adj = rdmolops.GetAdjacencyMatrix(mol)
                G = nx.from_numpy_array(adj)
                
                diameter = nx.diameter(G) if nx.is_connected(G) else 0
                avg_shortest = nx.average_shortest_path_length(G) if nx.is_connected(G) else 0
                num_cycles = len(list(nx.cycle_basis(G)))
                
                graph_features.append([diameter, avg_shortest, num_cycles])
            else:
                graph_features.append([0, 0, 0])
        except Exception as e:
            print(f"Error computing graph features for {smiles}: {e}")
            graph_features.append([0, 0, 0])
    
    graph_df = pd.DataFrame(graph_features, columns=['graph_diameter', 'avg_shortest_path', 'num_cycles'])
    print(f"Extracted {graph_df.shape[1]} graph features")
    return graph_df

def extract_polybert_embeddings(smiles_list, model_path=None):
    """Extract PolyBERT embeddings"""
    if not POLYBERT_AVAILABLE:
        print("SentenceTransformers not available, skipping PolyBERT embeddings")
        return pd.DataFrame()
    
    try:
        # Try to load PolyBERT model
        if model_path and os.path.exists(model_path):
            model = SentenceTransformer(model_path)
        else:
            print("PolyBERT model path not found, skipping embeddings")
            return pd.DataFrame()
        
        print("Extracting PolyBERT embeddings...")
        embeddings = model.encode(smiles_list, show_progress_bar=True)
        
        embedding_df = pd.DataFrame(embeddings, columns=[f'emb_{i}' for i in range(embeddings.shape[1])])
        print(f"Extracted {embedding_df.shape[1]} PolyBERT features")
        return embedding_df
        
    except Exception as e:
        print(f"Error extracting PolyBERT embeddings: {e}")
        return pd.DataFrame()

def extract_fallback_features_batch(smiles_list):
    """Extract basic features when RDKit is not available"""
    features_list = []
    
    for smiles in smiles_list:
        features = {}
        
        if smiles and pd.notna(smiles):
            features['smiles_length'] = len(smiles)
            features['num_carbons'] = smiles.count('C')
            features['num_nitrogens'] = smiles.count('N')
            features['num_oxygens'] = smiles.count('O')
            features['num_sulfurs'] = smiles.count('S')
            features['num_rings_approx'] = smiles.count('1') + smiles.count('2')
            features['num_branches'] = smiles.count('(')
            features['num_double_bonds'] = smiles.count('=')
            features['num_triple_bonds'] = smiles.count('#')
            features['aromatic_carbons'] = smiles.count('c')
            features['aromatic_nitrogens'] = smiles.count('n')
        else:
            for key in ['smiles_length', 'num_carbons', 'num_nitrogens', 'num_oxygens', 
                       'num_sulfurs', 'num_rings_approx', 'num_branches', 'num_double_bonds',
                       'num_triple_bonds', 'aromatic_carbons', 'aromatic_nitrogens']:
                features[key] = 0
        
        features_list.append(features)
    
    return pd.DataFrame(features_list)

# =============================================================================
# CELL 5: Main Polymer Predictor Class
# =============================================================================

class PolymerPropertyPredictor:
    """Complete solution for polymer property prediction competition"""
    
    def __init__(self, work_dir=None):
        self.work_dir = Path(work_dir) if work_dir else CFG.WORK_DIR
        self.target_properties = CFG.TARGETS
        self.models = {}
        self.scalers = {}
        self.feature_columns = None
        self.property_weights = None
    
    def load_and_integrate_data(self):
        """Load and integrate all available datasets"""
        print("=== Loading and Integrating Data ===")
        
        # Load main data
        train_df, _ = load_main_data()
        
        # Load supplementary data
        datasets = load_supplementary_data()
        
        return train_df, datasets
    
    def integrate_supplementary_data(self, train_df, datasets):
        """Integrate supplementary datasets with main training data"""
        print("=== Integrating Supplementary Data ===")
        
        integrated_data = train_df.copy()
        integrated_data['data_source'] = 'main'
        
        # Process each supplementary dataset
        for dataset_name, dataset in datasets.items():
            if 'SMILES' in dataset.columns:
                dataset = dataset.copy()
                dataset['data_source'] = dataset_name
                
                # Identify property columns
                property_cols = [col for col in dataset.columns 
                               if col not in ['SMILES', 'id', 'data_source']]
                
                if property_cols:
                    print(f"Processing {dataset_name} with properties: {property_cols}")
                    
                    for prop in property_cols:
                        if prop in self.target_properties or prop.replace('_mean', '') in self.target_properties:
                            # Standardize column names
                            if '_mean' in prop:
                                prop_standard = prop.replace('_mean', '')
                                dataset = dataset.rename(columns={prop: prop_standard})
                                prop = prop_standard
                            
                            # Add to integrated dataset
                            integrated_data = add_extra_data(integrated_data, dataset[[col for col in dataset.columns if col in ['SMILES', prop]]], prop)
        
        print(f"Final integrated dataset: {integrated_data.shape}")
        return integrated_data
    
    def prepare_features(self, df, polybert_model_path=None):
        """Prepare all features for the dataset"""
        print("=== Feature Engineering ===")
        
        smiles_list = df['SMILES'].tolist()
        
        # Extract molecular descriptors
        molecular_features = extract_molecular_features(smiles_list)
        
        # Extract graph features
        graph_features = extract_graph_features(smiles_list)
        
        # Extract PolyBERT embeddings
        polybert_features = extract_polybert_embeddings(smiles_list, polybert_model_path)
        
        # Combine all features
        feature_dfs = [molecular_features]
        
        if not graph_features.empty:
            feature_dfs.append(graph_features)
        
        if not polybert_features.empty:
            feature_dfs.append(polybert_features)
        
        if len(feature_dfs) > 1:
            X = pd.concat(feature_dfs, axis=1)
        else:
            X = feature_dfs[0]
        
        # Handle infinite values
        X = X.replace([np.inf, -np.inf], np.nan)
        
        # Store feature columns
        self.feature_columns = list(X.columns)
        
        print(f"Total features: {X.shape[1]}")
        return X
    
    def calculate_property_weights(self, y):
        """Calculate weights for weighted MAE metric"""
        print("=== Calculating Property Weights ===")
        
        weights = {}
        total_tasks = len(self.target_properties)
        
        for prop in self.target_properties:
            available_data = y[prop].dropna()
            n_samples = len(available_data)
            
            if n_samples > 0:
                # Use IQR for robust range estimation
                q75, q25 = np.percentile(available_data, [75, 25])
                prop_range = q75 - q25
                if prop_range == 0:
                    prop_range = available_data.std()
                if prop_range == 0:
                    prop_range = 1.0
                
                # Calculate weight
                weight = (1.0 / np.sqrt(n_samples)) / prop_range
                weights[prop] = weight
                print(f"{prop}: {n_samples} samples, range={prop_range:.4f}, weight={weight:.6f}")
            else:
                weights[prop] = 0.0
                print(f"{prop}: No samples available")
        
        # Normalize weights
        total_weight = sum(weights.values())
        if total_weight > 0:
            for prop in weights:
                weights[prop] = (weights[prop] / total_weight) * total_tasks
        
        self.property_weights = weights
        print(f"Normalized weights: {weights}")
    
    def get_default_params(self, prop):
        """Get default parameters when Optuna is not available"""
        if XGBOOST_AVAILABLE:
            params = {
                'objective': 'reg:squarederror',
                'eval_metric': 'mae',
                'tree_method': 'gpu_hist' if GPU_AVAILABLE else 'hist',
                'n_estimators': 500,
                'max_depth': 6,
                'learning_rate': 0.1,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'reg_alpha': 1,
                'reg_lambda': 1,
                'random_state': CFG.SEED
            }
            if GPU_AVAILABLE:
                params['gpu_id'] = 0
            return params
        else:
            return {
                'n_estimators': 200,
                'max_depth': 8,
                'min_samples_split': 5,
                'min_samples_leaf': 2,
                'random_state': CFG.SEED
            }
    
    def optimize_hyperparameters(self, X, y, prop, n_trials=50):
        """Optimize hyperparameters using Optuna"""
        if not OPTUNA_AVAILABLE:
            print(f"Optuna not available, using default hyperparameters for {prop}")
            return self.get_default_params(prop)
        
        print(f"Optimizing hyperparameters for {prop} with {n_trials} trials...")
        
        def objective(trial):
            mask = y[prop].notna()
            if mask.sum() < 20:
                return float('inf')
            
            X_prop = X[mask]
            y_prop = y.loc[mask, prop]
            
            if XGBOOST_AVAILABLE:
                params = {
                    'objective': 'reg:squarederror',
                    'eval_metric': 'mae',
                    'tree_method': 'gpu_hist' if GPU_AVAILABLE else 'hist',
                    'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
                    'max_depth': trial.suggest_int('max_depth', 3, 10),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                    'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                    'reg_alpha': trial.suggest_float('reg_alpha', 0, 10),
                    'reg_lambda': trial.suggest_float('reg_lambda', 0, 10),
                    'random_state': CFG.SEED
                }
                if GPU_AVAILABLE:
                    params['gpu_id'] = 0
            else:
                params = {
                    'n_estimators': trial.suggest_int('n_estimators', 50, 300),
                    'max_depth': trial.suggest_int('max_depth', 3, 15),
                    'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                    'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                    'random_state': CFG.SEED
                }
            
            # Cross-validation
            kf = KFold(n_splits=3, shuffle=True, random_state=CFG.SEED)
            cv_scores = []
            
            for train_idx, val_idx in kf.split(X_prop):
                X_train, X_val = X_prop.iloc[train_idx], X_prop.iloc[val_idx]
                y_train, y_val = y_prop.iloc[train_idx], y_prop.iloc[val_idx]
                
                scaler = RobustScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_val_scaled = scaler.transform(X_val)
                
                if XGBOOST_AVAILABLE:
                    model = xgb.XGBRegressor(**params)
                    try:
                        model.fit(
                            X_train_scaled, y_train,
                            eval_set=[(X_val_scaled, y_val)],
                            callbacks=[xgb.callback.EarlyStopping(rounds=50, save_best=True)],
                            verbose=False
                        )
                    except:
                        model.fit(X_train_scaled, y_train)
                else:
                    from sklearn.ensemble import RandomForestRegressor
                    model = RandomForestRegressor(**params)
                    model.fit(X_train_scaled, y_train)
                
                val_pred = model.predict(X_val_scaled)
                mae = mean_absolute_error(y_val, val_pred)
                cv_scores.append(mae)
            
            return np.mean(cv_scores)
        
        study = optuna.create_study(direction='minimize')
        study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
        
        print(f"Best parameters for {prop}: {study.best_params}")
        print(f"Best CV MAE for {prop}: {study.best_value:.4f}")
        
        return study.best_params
    
    def train_models(self, X, y, cv_folds=5, n_trials=50):
        """Train models with hyperparameter optimization"""
        print("=== Training Multi-task Models ===")
        
        # Calculate property weights
        self.calculate_property_weights(y)
        
        # Optimize hyperparameters for each property
        optimized_params = {}
        for prop in self.target_properties:
            mask = y[prop].notna()
            if mask.sum() > 20:
                print(f"\n=== Optimizing {prop} ===")
                optimized_params[prop] = self.optimize_hyperparameters(X, y, prop, n_trials)
            else:
                print(f"\n=== {prop}: Insufficient data ({mask.sum()} samples), using defaults ===")
                optimized_params[prop] = self.get_default_params(prop)
        
        # Cross-validation with optimized parameters
        print(f"\n=== Cross-validation with {cv_folds} folds ===")
        kf = KFold(n_splits=cv_folds, shuffle=True, random_state=CFG.SEED)
        cv_scores = {prop: [] for prop in self.target_properties}
        
        for fold, (train_idx, val_idx) in enumerate(kf.split(X)):
            print(f"\nFold {fold + 1}/{cv_folds}")
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            for prop in self.target_properties:
                train_mask = y_train[prop].notna()
                val_mask = y_val[prop].notna()
                
                if train_mask.sum() > 10 and val_mask.sum() > 0:
                    scaler = RobustScaler()
                    X_train_scaled = scaler.fit_transform(X_train[train_mask])
                    X_val_scaled = scaler.transform(X_val[val_mask])
                    
                    if XGBOOST_AVAILABLE:
                        model = xgb.XGBRegressor(**optimized_params[prop])
                        try:
                            model.fit(
                                X_train_scaled, y_train.loc[train_mask, prop],
                                eval_set=[(X_val_scaled, y_val.loc[val_mask, prop])],
                                callbacks=[xgb.callback.EarlyStopping(rounds=50, save_best=True)],
                                verbose=False
                            )
                        except:
                            model.fit(X_train_scaled, y_train.loc[train_mask, prop])
                    else:
                        from sklearn.ensemble import RandomForestRegressor
                        model = RandomForestRegressor(**optimized_params[prop])
                        model.fit(X_train_scaled, y_train.loc[train_mask, prop])
                    
                    val_pred = model.predict(X_val_scaled)
                    mae = mean_absolute_error(y_val.loc[val_mask, prop], val_pred)
                    cv_scores[prop].append(mae)
                    print(f"  {prop}: MAE = {mae:.4f}")
        
        # Print CV results
        print(f"\n=== Cross-validation Results ===")
        for prop in self.target_properties:
            if cv_scores[prop]:
                mean_mae = np.mean(cv_scores[prop])
                std_mae = np.std(cv_scores[prop])
                print(f"{prop}: {mean_mae:.4f} ± {std_mae:.4f}")
        
        # Train final models on full data
        print(f"\n=== Training Final Models ===")
        for prop in self.target_properties:
            mask = y[prop].notna()
            if mask.sum() > 10:
                print(f"Training final model for {prop}...")
                
                scaler = RobustScaler()
                X_scaled = scaler.fit_transform(X[mask])
                
                if XGBOOST_AVAILABLE:
                    model = xgb.XGBRegressor(**optimized_params[prop])
                    model.fit(X_scaled, y.loc[mask, prop])
                else:
                    from sklearn.ensemble import RandomForestRegressor
                    model = RandomForestRegressor(**optimized_params[prop])
                    model.fit(X_scaled, y.loc[mask, prop])
                
                self.models[prop] = model
                self.scalers[prop] = scaler
                print(f"  {prop} model trained successfully")
    
    def predict(self, smiles_list, polybert_model_path=None):
        """Make predictions for new SMILES"""
        print("=== Making Predictions ===")
        
        # Create temporary dataframe
        temp_df = pd.DataFrame({'SMILES': smiles_list})
        
        # Extract features
        X = self.prepare_features(temp_df, polybert_model_path)
        
        # Make predictions
        predictions = {}
        for prop in self.target_properties:
            if prop in self.models:
                X_scaled = self.scalers[prop].transform(X)
                pred = self.models[prop].predict(X_scaled)
                predictions[prop] = pred
            else:
                predictions[prop] = np.zeros(len(smiles_list))
        
        return pd.DataFrame(predictions)
    
    def create_submission(self, test_df, predictions_df):
        """Create submission format"""
        submission = pd.DataFrame()
        submission['id'] = test_df['id']
        
        for prop in self.target_properties:
            submission[prop] = predictions_df[prop] if prop in predictions_df.columns else 0.0
        
        return submission
    
    def save_model(self, filepath):
        """Save trained model"""
        model_data = {
            'models': self.models,
            'scalers': self.scalers,
            'feature_columns': self.feature_columns,
            'property_weights': self.property_weights,
            'target_properties': self.target_properties
        }
        joblib.dump(model_data, filepath)
        print(f"Model saved to {filepath}")
    
    def load_model(self, filepath):
        """Load trained model"""
        model_data = joblib.load(filepath)
        self.models = model_data['models']
        self.scalers = model_data['scalers']
        self.feature_columns = model_data['feature_columns']
        self.property_weights = model_data['property_weights']
        self.target_properties = model_data['target_properties']
        print(f"Model loaded from {filepath}")

# =============================================================================
# CELL 6: Training Pipeline
# =============================================================================

def main_training_pipeline():
    """Main training pipeline"""
    print("=== NEURIPs Polymer Property Prediction - Training Pipeline ===")
    
    # Initialize predictor
    predictor = PolymerPropertyPredictor()
    
    # Load and integrate data
    train_df, datasets = predictor.load_and_integrate_data()
    if train_df.empty:
        print("No training data found. Please ensure data files are in the correct location.")
        return
    
    integrated_data = predictor.integrate_supplementary_data(train_df, datasets)
    
    # Prepare features (add PolyBERT model path if available)
    polybert_path = None  # Set to your PolyBERT model path if available
    X = predictor.prepare_features(integrated_data, polybert_path)
    
    # Prepare targets
    y = integrated_data[CFG.TARGETS].copy()
    
    # Train models with optimization
    predictor.train_models(X, y, cv_folds=5, n_trials=50)
    
    # Save trained model
    predictor.save_model('polymer_model.pkl')
    
    print("Training completed successfully!")
    return predictor

# =============================================================================
# CELL 7: Prediction Pipeline
# =============================================================================

def predict_pipeline(test_df=None):
    """Prediction pipeline for Kaggle submission"""
    print("=== NEURIPs Polymer Property Prediction - Prediction Pipeline ===")
    
    # Load test data if not provided
    if test_df is None:
        try:
            test_df = pd.read_csv('test.csv')
            test_df['SMILES'] = test_df['SMILES'].apply(make_smile_canonical)
        except FileNotFoundError:
            print("test.csv not found. Creating sample data for testing...")
            test_df = pd.DataFrame({
                'id': [1, 2, 3],
                'SMILES': ['*CCO*', '*CC(C)O*', '*CCCC*']
            })
    
    print(f"Test data shape: {test_df.shape}")
    
    # Initialize predictor
    predictor = PolymerPropertyPredictor()
    
    # Try to load pre-trained model
    model_path = 'polymer_model.pkl'
    if os.path.exists(model_path):
        predictor.load_model(model_path)
        print("Loaded pre-trained model")
    else:
        print("No pre-trained model found, training on available data...")
        predictor = main_training_pipeline()
    
    # Make predictions
    polybert_path = None  # Set to your PolyBERT model path if available
    predictions_df = predictor.predict(test_df['SMILES'].tolist(), polybert_path)
    
    # Create submission
    submission = predictor.create_submission(test_df, predictions_df)
    
    # Save submission
    submission.to_csv('submission.csv', index=False)
    print("Submission saved to submission.csv")
    print(submission.head())
    
    return submission

# =============================================================================
# CELL 8: Dataset Analysis Functions
# =============================================================================

def analyze_dataset():
    """Comprehensive dataset analysis"""
    print("=== Dataset Analysis ===")
    
    try:
        train = pd.read_csv('train.csv')
        print(f"Training data shape: {train.shape}")
        print(f"Columns: {list(train.columns)}")
        
        # Missing value analysis
        print("\nMissing values:")
        missing = train.isnull().sum()
        missing_pct = (missing / len(train)) * 100
        missing_df = pd.DataFrame({
            'Missing Count': missing,
            'Missing %': missing_pct
        }).sort_values('Missing %', ascending=False)
        print(missing_df[missing_df['Missing Count'] > 0])
        
        # Target distribution
        print("\nTarget statistics:")
        for target in CFG.TARGETS:
            if target in train.columns:
                available = train[target].notna().sum()
                print(f"{target}: {available} samples ({available/len(train)*100:.1f}%)")
                if available > 0:
                    print(f"  Range: [{train[target].min():.3f}, {train[target].max():.3f}]")
                    print(f"  Mean: {train[target].mean():.3f}")
        
        # SMILES analysis
        print(f"\nSMILES analysis:")
        print(f"Unique SMILES: {train['SMILES'].nunique()}")
        print(f"Average SMILES length: {train['SMILES'].str.len().mean():.1f}")
        
        # Check for supplementary data
        supplement_dir = Path('train_supplement')
        if supplement_dir.exists():
            print(f"\nSupplementary datasets:")
            for file in supplement_dir.glob('*.csv'):
                try:
                    df = pd.read_csv(file)
                    print(f"  {file.name}: {df.shape}")
                except:
                    print(f"  {file.name}: Error reading file")
        
    except FileNotFoundError:
        print("train.csv not found. Please ensure the data file is in the current directory.")

# =============================================================================
# CELL 9: Main Execution
# =============================================================================

if __name__ == "__main__":
    # Analyze dataset first
    analyze_dataset()
    
    # Run training pipeline
    print("\n" + "="*50)
    predictor = main_training_pipeline()
    
    # Run prediction pipeline
    print("\n" + "="*50)
    submission = predict_pipeline()
    
    print("\nPipeline completed successfully!")
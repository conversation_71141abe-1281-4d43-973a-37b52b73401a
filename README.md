# NEURIPs Open Polymer Prediction 2025 - Solution

Complete multi-task learning solution for polymer property prediction from SMILES molecular representations.

## Overview

This solution predicts five key polymer properties:
- **Tg**: Glass transition temperature
- **FFV**: Fractional free volume  
- **Tc**: Thermal conductivity
- **Density**: Polymer density
- **Rg**: Radius of gyration

## Files Structure

```
/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/Kaggle/NEURIPs_polymer/
├── polymer_competition_solution.py  # Main solution class
├── train_polymer_model.py          # Training pipeline
├── kaggle_polymer_predict.py       # Kaggle submission script
├── validate_data.py               # Data validation utility
├── quick_test.py                  # Test suite
├── README.md                      # This file
├── train.csv                      # Main training data
├── test.csv                       # Test data
├── sample_submission.csv          # Submission format
└── train_supplement/              # Supplementary datasets
    ├── dataset1.csv
    ├── dataset2.csv
    ├── dataset3.csv
    └── dataset4.csv
```

## Quick Start

1. **Validate data files**:
```bash
python validate_data.py
```

2. **Run quick tests**:
```bash
python quick_test.py
```

3. **Train the model**:
```bash
python train_polymer_model.py
```

4. **Make predictions**:
```bash
python kaggle_polymer_predict.py
```

## Key Features

- **Multi-task learning**: Handles sparse target matrix with missing values
- **Comprehensive molecular features**: RDKit descriptors with fallback features
- **Supplementary data integration**: Leverages all 4 additional datasets
- **Weighted MAE optimization**: Matches competition evaluation metric
- **Robust cross-validation**: Accounts for sparse targets

## Dependencies

- pandas
- numpy
- scikit-learn
- joblib
- rdkit-pypi (optional, fallback features available)

## Competition Details

- **Evaluation**: Weighted Mean Absolute Error (wMAE)
- **Test set**: ~1,500 polymers (hidden)
- **Properties**: 5 targets with varying data availability
- **Input**: SMILES molecular representations

## Model Architecture

- **Individual models** per property with shared preprocessing
- **Random Forest** for sparse properties
- **Gradient Boosting** for properties with more data
- **Robust scaling** for molecular descriptors
- **Cross-validation** with proper sparse handling

## Usage Notes

- The solution automatically handles missing RDKit by using fallback features
- All file paths are configured for the specified working directory
- Models are saved as `polymer_model.pkl` after training
- Submission format matches competition requirements
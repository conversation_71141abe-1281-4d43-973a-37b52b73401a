{"cells": [{"cell_type": "code", "execution_count": null, "id": "2b012f8f-79f9-43d5-9d9d-0a3f1526a310", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "99b3a16a-2587-4a52-8996-9f18f1f3512d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bc9a13fe-3176-40ea-ae81-6e866221a016", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8cc89bf7-6f95-4e46-9e43-148b854bc0f5", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "NEURIPs Open Polymer Prediction 2025 - Integrated Solution\n", "Multi-task learning approach for polymer property prediction from SMILES\n", "Integrates: train_polymer_model.py, polymer_competition_solution.py, \n", "kaggle_polymer_predict.py, polymer_dataset_analyzer.py\n", "\"\"\"\n", "\n", "# =============================================================================\n", "# CELL 1: Import Dependencies and Setup\n", "# =============================================================================\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Core ML libraries\n", "from sklearn.model_selection import KFold, StratifiedKFold\n", "from sklearn.preprocessing import StandardScaler, RobustScaler\n", "from sklearn.metrics import mean_absolute_error\n", "from sklearn.ensemble import HistGradientBoostingRegressor, ExtraTreesRegressor\n", "import joblib\n", "\n", "# XGBoost and optimization\n", "try:\n", "    import xgboost as xgb\n", "    XGBOOST_AVAILABLE = True\n", "except ImportError:\n", "    print(\"XGBoost not available - falling back to sklearn models\")\n", "    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "    XGBOOST_AVAILABLE = False\n", "\n", "try:\n", "    import optuna\n", "    OPTUNA_AVAILABLE = True\n", "except ImportError:\n", "    print(\"Optuna not available - using default hyperparameters\")\n", "    OPTUNA_AVAILABLE = False\n", "\n", "# Molecular processing\n", "try:\n", "    from rdkit import Chem\n", "    from rdkit.Chem import Descriptors, rdMolDescriptors, Crippen\n", "    from rdkit.Chem import AllChem, rdmolops\n", "    from rdkit.ML.Descriptors import MoleculeDescriptors\n", "    RDKIT_AVAILABLE = True\n", "except ImportError:\n", "    print(\"RDKit not available - using fallback features\")\n", "    RDKIT_AVAILABLE = False\n", "\n", "# Graph analysis\n", "try:\n", "    import networkx as nx\n", "    NETWORKX_AVAILABLE = True\n", "except ImportError:\n", "    print(\"NetworkX not available - skipping graph features\")\n", "    NETWORKX_AVAILABLE = False\n", "\n", "# PolyBERT embeddings\n", "try:\n", "    from sentence_transformers import SentenceTransformer\n", "    POLYBERT_AVAILABLE = True\n", "except ImportError:\n", "    print(\"SentenceTransformers not available - skipping PolyBERT embeddings\")\n", "    POLYBERT_AVAILABLE = False\n", "\n", "import os\n", "import json\n", "from pathlib import Path\n", "import time\n", "from tqdm import tqdm\n", "\n", "# =============================================================================\n", "# CELL 2: Configuration and Constants\n", "# =============================================================================\n", "\n", "class CFG:\n", "    TARGETS = ['Tg', 'FFV', 'Tc', 'Density', 'Rg']\n", "    SEED = 42\n", "    FOLDS = 5\n", "    WORK_DIR = Path(\".\")  # Current directory\n", "\n", "# Features to exclude based on analysis\n", "USELESS_COLS = [\n", "    # NaN > 80%\n", "    'Tg', 'Tc', 'Density', 'Rg',\n", "    'MaxPartialCharge', 'MinPartialCharge', 'MaxAbsPartialCharge', 'MinAbsPartialCharge',\n", "    'BCUT2D_MWHI', 'BCUT2D_MWLOW', 'BCUT2D_CHGHI', 'BCUT2D_CHGLO',\n", "    'BCUT2D_LOGPHI', 'BCUT2D_LOGPLOW', 'BCUT2D_MRHI', 'BCUT2D_MRLOW',\n", "    \n", "    # Constant columns\n", "    'SMR_VSA8', 'SlogP_VSA9', 'fr_isothiocyan', 'fr_prisulfonamd',\n", "    \n", "    # High correlation (|r| > 0.95)\n", "    'MaxEStateIndex', 'HeavyAtomMolWt', 'ExactMolWt', 'NumValenceElectrons',\n", "    'FpDensityMorgan2', 'FpDensityMorgan3',\n", "    'Chi0', 'Chi0n', 'Chi0v', 'Chi1', 'Chi1n', 'Chi1v',\n", "    'Chi2n', 'Chi2v', 'Chi3n', 'Chi3v', 'Chi4n', 'Chi4v',\n", "    'HallKierAlpha', 'Kappa1', 'LabuteASA', 'SlogP_VSA6', 'VSA_EState6',\n", "    'HeavyAtomCount', 'NOCount', 'NumAromaticCarbocycles', 'NumAromaticRings',\n", "    'NumHDonors', 'Phi', 'MolMR',\n", "    'fr_Al_OH_noTert', 'fr_COO2', 'fr_C_O', 'fr_C_O_noCOO',\n", "    'fr_N<PERSON>yrrole', 'fr_amide', 'fr_benzene', 'fr_diazo',\n", "    'fr_halogen', 'fr_nitrile', 'fr_nitro_arom',\n", "    'fr_phenol', 'fr_phenol_noOrthoHbond', 'fr_phos_ester'\n", "]\n", "\n", "# GPU detection\n", "def detect_gpu():\n", "    \"\"\"Detect if GPU is available for XGBoost\"\"\"\n", "    try:\n", "        import GPUtil\n", "        gpus = GPUtil.getGPUs()\n", "        if gpus:\n", "            print(f\"GPU detected: {gpus[0].name}\")\n", "            return True\n", "    except:\n", "        pass\n", "    \n", "    try:\n", "        import torch\n", "        if torch.cuda.is_available():\n", "            print(f\"CUDA GPU detected: {torch.cuda.get_device_name(0)}\")\n", "            return True\n", "    except:\n", "        pass\n", "    \n", "    print(\"No GPU detected, using CPU\")\n", "    return False\n", "\n", "GPU_AVAILABLE = detect_gpu()\n", "\n", "# =============================================================================\n", "# CELL 3: Data Loading and Preprocessing Functions\n", "# =============================================================================\n", "\n", "def make_smile_canonical(smile):\n", "    \"\"\"Convert SMILES to canonical form to avoid duplicates\"\"\"\n", "    try:\n", "        mol = Chem.Mol<PERSON>rom<PERSON>(smile)\n", "        if mol is None:\n", "            return np.nan\n", "        canon_smile = Chem.MolToSmiles(mol, canonical=True)\n", "        return canon_smile\n", "    except:\n", "        return np.nan\n", "\n", "def load_main_data():\n", "    \"\"\"Load main training and test datasets\"\"\"\n", "    print(\"=== Loading Main Datasets ===\")\n", "    \n", "    train = pd.read_csv('train.csv')\n", "    test = pd.read_csv('test.csv')\n", "    \n", "    print(f\"Train shape: {train.shape}\")\n", "    print(f\"Test shape: {test.shape}\")\n", "    \n", "    # Canonicalize SMILES\n", "    train['SMILES'] = train['SMILES'].apply(make_smile_canonical)\n", "    test['SMILES'] = test['SMILES'].apply(make_smile_canonical)\n", "    \n", "    return train, test\n", "\n", "def load_supplementary_data():\n", "    \"\"\"Load supplementary datasets\"\"\"\n", "    print(\"=== Loading Supplementary Datasets ===\")\n", "    \n", "    datasets = {}\n", "    \n", "    # Load supplementary datasets if available\n", "    supplement_files = [\n", "        ('dataset1.csv', 'train_supplement/dataset1.csv'),\n", "        ('dataset2.csv', 'train_supplement/dataset2.csv'), \n", "        ('dataset3.csv', 'train_supplement/dataset3.csv'),\n", "        ('dataset4.csv', 'train_supplement/dataset4.csv')\n", "    ]\n", "    \n", "    for name, path in supplement_files:\n", "        try:\n", "            df = pd.read_csv(path)\n", "            df['SMILES'] = df['SMILES'].apply(make_smile_canonical)\n", "            datasets[name] = df\n", "            print(f\"Loaded {name}: {df.shape}\")\n", "        except FileNotFoundError:\n", "            print(f\"File not found: {path}\")\n", "    \n", "    return datasets\n", "\n", "def add_extra_data(df_train, df_extra, target):\n", "    \"\"\"Add extra data for specific target\"\"\"\n", "    if df_extra is None or df_extra.empty:\n", "        return df_train\n", "        \n", "    n_samples_before = len(df_train[df_train[target].notnull()])\n", "    \n", "    # Group by SMILES and take mean for duplicates\n", "    df_extra = df_extra.groupby('SMILES', as_index=False)[target].mean()\n", "    \n", "    cross_smiles = set(df_extra['SMILES']) & set(df_train['SMILES'])\n", "    unique_smiles_extra = set(df_extra['SMILES']) - set(df_train['SMILES'])\n", "\n", "    # Priority to competition data - remove overlaps from extra data\n", "    for smile in df_train[df_train[target].notnull()]['SMILES'].tolist():\n", "        if smile in cross_smiles:\n", "            cross_smiles.remove(smile)\n", "\n", "    # Fill missing values for competition SMILES\n", "    for smile in cross_smiles:\n", "        df_train.loc[df_train['SMILES']==smile, target] = df_extra[df_extra['SMILES']==smile][target].values[0]\n", "    \n", "    # Add unique SMILES from extra data\n", "    extra_rows = df_extra[df_extra['SMILES'].isin(unique_smiles_extra)].copy()\n", "    if not extra_rows.empty:\n", "        # Add missing columns with NaN\n", "        for col in df_train.columns:\n", "            if col not in extra_rows.columns:\n", "                extra_rows[col] = np.nan\n", "        \n", "        df_train = pd.concat([df_train, extra_rows], axis=0, ignore_index=True)\n", "\n", "    n_samples_after = len(df_train[df_train[target].notnull()])\n", "    print(f'For target \"{target}\" added {n_samples_after-n_samples_before} new samples!')\n", "    print(f'New unique SMILES: {len(unique_smiles_extra)}')\n", "    \n", "    return df_train\n", "\n", "# =============================================================================\n", "# CELL 4: Feature Engineering Functions\n", "# =============================================================================\n", "\n", "def extract_molecular_features(smiles_list):\n", "    \"\"\"Extract comprehensive molecular features from SMILES\"\"\"\n", "    print(\"Extracting molecular features...\")\n", "    \n", "    if not RDKIT_AVAILABLE:\n", "        print(\"RDKit not available, using fallback features\")\n", "        return extract_fallback_features_batch(smiles_list)\n", "    \n", "    # Get descriptor names (excluding useless ones)\n", "    descriptor_names = [desc[0] for desc in Descriptors._descList if desc[0] not in USELESS_COLS]\n", "    calc = MoleculeDescriptors.MolecularDescriptorCalculator(descriptor_names)\n", "    \n", "    features_list = []\n", "    for smiles in tqdm(smiles_list, desc=\"Computing molecular descriptors\"):\n", "        try:\n", "            mol = Chem.Mo<PERSON>rom<PERSON>(smiles)\n", "            if mol is not None:\n", "                descriptors = calc.CalcDescriptors(mol)\n", "                features_list.append(descriptors)\n", "            else:\n", "                features_list.append([np.nan] * len(descriptor_names))\n", "        except Exception as e:\n", "            print(f\"Error processing SMILES {smiles}: {e}\")\n", "            features_list.append([np.nan] * len(descriptor_names))\n", "    \n", "    features_df = pd.DataFrame(features_list, columns=descriptor_names)\n", "    print(f\"Extracted {features_df.shape[1]} molecular features\")\n", "    return features_df\n", "\n", "def extract_graph_features(smiles_list):\n", "    \"\"\"Extract graph-based features\"\"\"\n", "    if not NETWORKX_AVAILABLE or not RDKIT_AVAILABLE:\n", "        print(\"NetworkX or RDKit not available, skipping graph features\")\n", "        return pd.DataFrame()\n", "    \n", "    print(\"Extracting graph features...\")\n", "    graph_features = []\n", "    \n", "    for smiles in tqdm(smiles_list, desc=\"Computing graph features\"):\n", "        try:\n", "            mol = Chem.Mo<PERSON>rom<PERSON>(smiles)\n", "            if mol is not None:\n", "                adj = rdmolops.GetAdjacencyMatrix(mol)\n", "                G = nx.from_numpy_array(adj)\n", "                \n", "                diameter = nx.diameter(G) if nx.is_connected(G) else 0\n", "                avg_shortest = nx.average_shortest_path_length(G) if nx.is_connected(G) else 0\n", "                num_cycles = len(list(nx.cycle_basis(G)))\n", "                \n", "                graph_features.append([diameter, avg_shortest, num_cycles])\n", "            else:\n", "                graph_features.append([0, 0, 0])\n", "        except Exception as e:\n", "            print(f\"Error computing graph features for {smiles}: {e}\")\n", "            graph_features.append([0, 0, 0])\n", "    \n", "    graph_df = pd.DataFrame(graph_features, columns=['graph_diameter', 'avg_shortest_path', 'num_cycles'])\n", "    print(f\"Extracted {graph_df.shape[1]} graph features\")\n", "    return graph_df\n", "\n", "def extract_polybert_embeddings(smiles_list, model_path=None):\n", "    \"\"\"Extract PolyBERT embeddings\"\"\"\n", "    if not POLYBERT_AVAILABLE:\n", "        print(\"SentenceTransformers not available, skipping PolyBERT embeddings\")\n", "        return pd.DataFrame()\n", "    \n", "    try:\n", "        # Try to load PolyBERT model\n", "        if model_path and os.path.exists(model_path):\n", "            model = SentenceTransformer(model_path)\n", "        else:\n", "            print(\"PolyBERT model path not found, skipping embeddings\")\n", "            return pd.DataFrame()\n", "        \n", "        print(\"Extracting PolyBERT embeddings...\")\n", "        embeddings = model.encode(smiles_list, show_progress_bar=True)\n", "        \n", "        embedding_df = pd.DataFrame(embeddings, columns=[f'emb_{i}' for i in range(embeddings.shape[1])])\n", "        print(f\"Extracted {embedding_df.shape[1]} PolyBERT features\")\n", "        return embedding_df\n", "        \n", "    except Exception as e:\n", "        print(f\"Error extracting PolyBERT embeddings: {e}\")\n", "        return pd.DataFrame()\n", "\n", "def extract_fallback_features_batch(smiles_list):\n", "    \"\"\"Extract basic features when RDKit is not available\"\"\"\n", "    features_list = []\n", "    \n", "    for smiles in smiles_list:\n", "        features = {}\n", "        \n", "        if smiles and pd.notna(smiles):\n", "            features['smiles_length'] = len(smiles)\n", "            features['num_carbons'] = smiles.count('C')\n", "            features['num_nitrogens'] = smiles.count('N')\n", "            features['num_oxygens'] = smiles.count('O')\n", "            features['num_sulfurs'] = smiles.count('S')\n", "            features['num_rings_approx'] = smiles.count('1') + smiles.count('2')\n", "            features['num_branches'] = smiles.count('(')\n", "            features['num_double_bonds'] = smiles.count('=')\n", "            features['num_triple_bonds'] = smiles.count('#')\n", "            features['aromatic_carbons'] = smiles.count('c')\n", "            features['aromatic_nitrogens'] = smiles.count('n')\n", "        else:\n", "            for key in ['smiles_length', 'num_carbons', 'num_nitrogens', 'num_oxygens', \n", "                       'num_sulfurs', 'num_rings_approx', 'num_branches', 'num_double_bonds',\n", "                       'num_triple_bonds', 'aromatic_carbons', 'aromatic_nitrogens']:\n", "                features[key] = 0\n", "        \n", "        features_list.append(features)\n", "    \n", "    return pd.DataFrame(features_list)\n", "\n", "# =============================================================================\n", "# CELL 5: Main Polymer Predictor Class\n", "# =============================================================================\n", "\n", "class PolymerPropertyPredictor:\n", "    \"\"\"Complete solution for polymer property prediction competition\"\"\"\n", "    \n", "    def __init__(self, work_dir=None):\n", "        self.work_dir = Path(work_dir) if work_dir else CFG.WORK_DIR\n", "        self.target_properties = CFG.TARGETS\n", "        self.models = {}\n", "        self.scalers = {}\n", "        self.feature_columns = None\n", "        self.property_weights = None\n", "    \n", "    def load_and_integrate_data(self):\n", "        \"\"\"Load and integrate all available datasets\"\"\"\n", "        print(\"=== Loading and Integrating Data ===\")\n", "        \n", "        # Load main data\n", "        train_df, _ = load_main_data()\n", "        \n", "        # Load supplementary data\n", "        datasets = load_supplementary_data()\n", "        \n", "        return train_df, datasets\n", "    \n", "    def integrate_supplementary_data(self, train_df, datasets):\n", "        \"\"\"Integrate supplementary datasets with main training data\"\"\"\n", "        print(\"=== Integrating Supplementary Data ===\")\n", "        \n", "        integrated_data = train_df.copy()\n", "        integrated_data['data_source'] = 'main'\n", "        \n", "        # Process each supplementary dataset\n", "        for dataset_name, dataset in datasets.items():\n", "            if 'SMILES' in dataset.columns:\n", "                dataset = dataset.copy()\n", "                dataset['data_source'] = dataset_name\n", "                \n", "                # Identify property columns\n", "                property_cols = [col for col in dataset.columns \n", "                               if col not in ['SMILES', 'id', 'data_source']]\n", "                \n", "                if property_cols:\n", "                    print(f\"Processing {dataset_name} with properties: {property_cols}\")\n", "                    \n", "                    for prop in property_cols:\n", "                        if prop in self.target_properties or prop.replace('_mean', '') in self.target_properties:\n", "                            # Standardize column names\n", "                            if '_mean' in prop:\n", "                                prop_standard = prop.replace('_mean', '')\n", "                                dataset = dataset.rename(columns={prop: prop_standard})\n", "                                prop = prop_standard\n", "                            \n", "                            # Add to integrated dataset\n", "                            integrated_data = add_extra_data(integrated_data, dataset[[col for col in dataset.columns if col in ['SMILES', prop]]], prop)\n", "        \n", "        print(f\"Final integrated dataset: {integrated_data.shape}\")\n", "        return integrated_data\n", "        \n", "    def prepare_features(self, df, polybert_model_path=None):\n", "        \"\"\"Prepare all features for the dataset\"\"\"\n", "        print(\"=== Feature Engineering ===\")\n", "        \n", "        smiles_list = df['SMILES'].tolist()\n", "        \n", "        # Extract molecular descriptors\n", "        molecular_features = extract_molecular_features(smiles_list)\n", "        \n", "        # Extract graph features\n", "        graph_features = extract_graph_features(smiles_list)\n", "        \n", "        # Extract PolyBERT embeddings\n", "        polybert_features = extract_polybert_embeddings(smiles_list, polybert_model_path)\n", "        \n", "        # Combine all features\n", "        feature_dfs = [molecular_features]\n", "        \n", "        if not graph_features.empty:\n", "            feature_dfs.append(graph_features)\n", "        \n", "        if not polybert_features.empty:\n", "            feature_dfs.append(polybert_features)\n", "        \n", "        if len(feature_dfs) > 1:\n", "            X = pd.concat(feature_dfs, axis=1)\n", "        else:\n", "            X = feature_dfs[0]\n", "        \n", "        # Replace infinite values with very large finite values\n", "        X = X.replace(np.inf, 1e308).replace(-np.inf, -1e308)\n", "        \n", "        # Store feature columns\n", "        self.feature_columns = list(X.columns)\n", "        \n", "        print(f\"Total features: {X.shape[1]}\")\n", "        return X\n", "    \n", "    def calculate_property_weights(self, y):\n", "        \"\"\"Calculate weights for weighted MAE metric\"\"\"\n", "        print(\"=== Calculating Property Weights ===\")\n", "        \n", "        weights = {}\n", "        total_tasks = len(self.target_properties)\n", "        \n", "        for prop in self.target_properties:\n", "            available_data = y[prop].dropna()\n", "            n_samples = len(available_data)\n", "            \n", "            if n_samples > 0:\n", "                # Use IQR for robust range estimation\n", "                q75, q25 = np.percentile(available_data, [75, 25])\n", "                prop_range = q75 - q25\n", "                if prop_range == 0:\n", "                    prop_range = available_data.std()\n", "                if prop_range == 0:\n", "                    prop_range = 1.0\n", "                \n", "                # Calculate weight\n", "                weight = (1.0 / np.sqrt(n_samples)) / prop_range\n", "                weights[prop] = weight\n", "                print(f\"{prop}: {n_samples} samples, range={prop_range:.4f}, weight={weight:.6f}\")\n", "            else:\n", "                weights[prop] = 0.0\n", "                print(f\"{prop}: No samples available\")\n", "        \n", "        # Normalize weights\n", "        total_weight = sum(weights.values())\n", "        if total_weight > 0:\n", "            for prop in weights:\n", "                weights[prop] = (weights[prop] / total_weight) * total_tasks\n", "        \n", "        self.property_weights = weights\n", "        print(f\"Normalized weights: {weights}\")\n", "    \n", "    def get_default_params(self, prop):\n", "        \"\"\"Get default parameters when Optuna is not available\"\"\"\n", "        if XGBOOST_AVAILABLE:\n", "            params = {\n", "                'objective': 'reg:squarederror',\n", "                'eval_metric': 'mae',\n", "                'tree_method': 'gpu_hist' if GPU_AVAILABLE else 'hist',\n", "                'n_estimators': 500,\n", "                'max_depth': 6,\n", "                'learning_rate': 0.1,\n", "                'subsample': 0.8,\n", "                'colsample_bytree': 0.8,\n", "                'reg_alpha': 1,\n", "                'reg_lambda': 1,\n", "                'random_state': CFG.SEED\n", "            }\n", "            if GPU_AVAILABLE:\n", "                params['gpu_id'] = 0\n", "            return params\n", "        else:\n", "            return {\n", "                'n_estimators': 200,\n", "                'max_depth': 8,\n", "                'min_samples_split': 5,\n", "                'min_samples_leaf': 2,\n", "                'random_state': CFG.SEED\n", "            }\n", "    \n", "    def optimize_hyperparameters(self, X, y, prop, n_trials=50):\n", "        \"\"\"Optimize hyperparameters using Optuna\"\"\"\n", "        if not OPTUNA_AVAILABLE:\n", "            print(f\"Optuna not available, using default hyperparameters for {prop}\")\n", "            return self.get_default_params(prop)\n", "        \n", "        print(f\"Optimizing hyperparameters for {prop} with {n_trials} trials...\")\n", "        \n", "        def objective(trial):\n", "            mask = y[prop].notna()\n", "            if mask.sum() < 20:\n", "                return float('inf')\n", "            \n", "            X_prop = X[mask]\n", "            # y_prop = y.loc[mask, prop]\n", "            y_prop = y.loc[mask, prop].replace([np.inf, -np.inf], [1e308, -1e308])  # Replace inf in targets\n", "            if XGBOOST_AVAILABLE:\n", "                params = {\n", "                    'objective': 'reg:squarederror',\n", "                    'eval_metric': 'mae',\n", "                    'tree_method': 'gpu_hist' if GPU_AVAILABLE else 'hist',\n", "                    'n_estimators': trial.suggest_int('n_estimators', 100, 1000),\n", "                    'max_depth': trial.suggest_int('max_depth', 3, 10),\n", "                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),\n", "                    'subsample': trial.suggest_float('subsample', 0.6, 1.0),\n", "                    'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),\n", "                    'reg_alpha': trial.suggest_float('reg_alpha', 0, 10),\n", "                    'reg_lambda': trial.suggest_float('reg_lambda', 0, 10),\n", "                    'random_state': CFG.SEED\n", "                }\n", "                if GPU_AVAILABLE:\n", "                    params['gpu_id'] = 0\n", "            else:\n", "                params = {\n", "                    'n_estimators': trial.suggest_int('n_estimators', 50, 300),\n", "                    'max_depth': trial.suggest_int('max_depth', 3, 15),\n", "                    'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),\n", "                    'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),\n", "                    'random_state': CFG.SEED\n", "                }\n", "            \n", "            # Cross-validation\n", "            kf = KFold(n_splits=3, shuffle=True, random_state=CFG.SEED)\n", "            cv_scores = []\n", "            \n", "            for train_idx, val_idx in kf.split(X_prop):\n", "                X_train, X_val = X_prop.iloc[train_idx], X_prop.iloc[val_idx]\n", "                y_train, y_val = y_prop.iloc[train_idx], y_prop.iloc[val_idx]\n", "                \n", "                scaler = RobustScaler()\n", "                X_train_scaled = scaler.fit_transform(X_train)\n", "                X_val_scaled = scaler.transform(X_val)\n", "                \n", "                if XGBOOST_AVAILABLE:\n", "                    model = xgb.XGBRegressor(**params)\n", "                    try:\n", "                        model.fit(\n", "                            X_train_scaled, y_train,\n", "                            eval_set=[(X_val_scaled, y_val)],\n", "                            callbacks=[xgb.callback.EarlyStopping(rounds=50, save_best=True)],\n", "                            verbose=False\n", "                        )\n", "                    except:\n", "                        model.fit(X_train_scaled, y_train)\n", "                else:\n", "                    from sklearn.ensemble import RandomForestRegressor\n", "                    model = RandomForestRegressor(**params)\n", "                    model.fit(X_train_scaled, y_train)\n", "                \n", "                val_pred = model.predict(X_val_scaled)\n", "                mae = mean_absolute_error(y_val, val_pred)\n", "                cv_scores.append(mae)\n", "            \n", "            return np.mean(cv_scores)\n", "        \n", "        study = optuna.create_study(direction='minimize')\n", "        study.optimize(objective, n_trials=n_trials, show_progress_bar=True)\n", "        \n", "        print(f\"Best parameters for {prop}: {study.best_params}\")\n", "        print(f\"Best CV MAE for {prop}: {study.best_value:.4f}\")\n", "        \n", "        return study.best_params\n", "    \n", "    def train_models(self, X, y, cv_folds=5, n_trials=50):\n", "        \"\"\"Train models with hyperparameter optimization\"\"\"\n", "        print(\"=== Training Multi-task Models ===\")\n", "        \n", "        # Calculate property weights\n", "        self.calculate_property_weights(y)\n", "        \n", "        # Optimize hyperparameters for each property\n", "        optimized_params = {}\n", "        for prop in self.target_properties:\n", "            mask = y[prop].notna()\n", "            if mask.sum() > 20:\n", "                print(f\"\\n=== Optimizing {prop} ===\")\n", "                optimized_params[prop] = self.optimize_hyperparameters(X, y, prop, n_trials)\n", "            else:\n", "                print(f\"\\n=== {prop}: Insufficient data ({mask.sum()} samples), using defaults ===\")\n", "                optimized_params[prop] = self.get_default_params(prop)\n", "        \n", "        # Cross-validation with optimized parameters\n", "        print(f\"\\n=== Cross-validation with {cv_folds} folds ===\")\n", "        kf = KFold(n_splits=cv_folds, shuffle=True, random_state=CFG.SEED)\n", "        cv_scores = {prop: [] for prop in self.target_properties}\n", "        \n", "        for fold, (train_idx, val_idx) in enumerate(kf.split(X)):\n", "            print(f\"\\nFold {fold + 1}/{cv_folds}\")\n", "            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]\n", "            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]\n", "            \n", "            for prop in self.target_properties:\n", "                train_mask = y_train[prop].notna()\n", "                val_mask = y_val[prop].notna()\n", "                \n", "                if train_mask.sum() > 10 and val_mask.sum() > 0:\n", "                    scaler = RobustScaler()\n", "                    X_train_scaled = scaler.fit_transform(X_train[train_mask])\n", "                    X_val_scaled = scaler.transform(X_val[val_mask])\n", "                    \n", "                    if XGBOOST_AVAILABLE:\n", "                        model = xgb.XGBRegressor(**optimized_params[prop])\n", "                        try:\n", "                            model.fit(\n", "                                X_train_scaled, y_train.loc[train_mask, prop],\n", "                                eval_set=[(X_val_scaled, y_val.loc[val_mask, prop])],\n", "                                callbacks=[xgb.callback.EarlyStopping(rounds=50, save_best=True)],\n", "                                verbose=False\n", "                            )\n", "                        except:\n", "                            model.fit(X_train_scaled, y_train.loc[train_mask, prop])\n", "                    else:\n", "                        from sklearn.ensemble import RandomForestRegressor\n", "                        model = RandomForestRegressor(**optimized_params[prop])\n", "                        model.fit(X_train_scaled, y_train.loc[train_mask, prop])\n", "                    \n", "                    val_pred = model.predict(X_val_scaled)\n", "                    mae = mean_absolute_error(y_val.loc[val_mask, prop], val_pred)\n", "                    cv_scores[prop].append(mae)\n", "                    print(f\"  {prop}: MAE = {mae:.4f}\")\n", "        \n", "        # Print CV results\n", "        print(f\"\\n=== Cross-validation Results ===\")\n", "        for prop in self.target_properties:\n", "            if cv_scores[prop]:\n", "                mean_mae = np.mean(cv_scores[prop])\n", "                std_mae = np.std(cv_scores[prop])\n", "                print(f\"{prop}: {mean_mae:.4f} ± {std_mae:.4f}\")\n", "        \n", "        # Train final models on full data\n", "        print(f\"\\n=== Training Final Models ===\")\n", "        for prop in self.target_properties:\n", "            mask = y[prop].notna()\n", "            if mask.sum() > 10:\n", "                print(f\"Training final model for {prop}...\")\n", "                \n", "                scaler = RobustScaler()\n", "                X_scaled = scaler.fit_transform(X[mask])\n", "                \n", "                if XGBOOST_AVAILABLE:\n", "                    model = xgb.XGBRegressor(**optimized_params[prop])\n", "                    model.fit(X_scaled, y.loc[mask, prop])\n", "                else:\n", "                    from sklearn.ensemble import RandomForestRegressor\n", "                    model = RandomForestRegressor(**optimized_params[prop])\n", "                    model.fit(X_scaled, y.loc[mask, prop])\n", "                \n", "                self.models[prop] = model\n", "                self.scalers[prop] = scaler\n", "                print(f\"  {prop} model trained successfully\")\n", "    \n", "    def predict(self, smiles_list, polybert_model_path=None):\n", "        \"\"\"Make predictions for new SMILES\"\"\"\n", "        print(\"=== Making Predictions ===\")\n", "        \n", "        # Create temporary dataframe\n", "        temp_df = pd.DataFrame({'SMILES': smiles_list})\n", "        \n", "        # Extract features\n", "        X = self.prepare_features(temp_df, polybert_model_path)\n", "        \n", "        # Make predictions\n", "        predictions = {}\n", "        for prop in self.target_properties:\n", "            if prop in self.models:\n", "                X_scaled = self.scalers[prop].transform(X)\n", "                pred = self.models[prop].predict(X_scaled)\n", "                predictions[prop] = pred\n", "            else:\n", "                predictions[prop] = np.zeros(len(smiles_list))\n", "        \n", "        return pd.<PERSON><PERSON><PERSON><PERSON>(predictions)\n", "    \n", "    def create_submission(self, test_df, predictions_df):\n", "        \"\"\"Create submission format\"\"\"\n", "        submission = pd.DataFrame()\n", "        submission['id'] = test_df['id']\n", "        \n", "        for prop in self.target_properties:\n", "            submission[prop] = predictions_df[prop] if prop in predictions_df.columns else 0.0\n", "        \n", "        return submission\n", "    \n", "    def save_model(self, filepath):\n", "        \"\"\"Save trained model\"\"\"\n", "        model_data = {\n", "            'models': self.models,\n", "            'scalers': self.scalers,\n", "            'feature_columns': self.feature_columns,\n", "            'property_weights': self.property_weights,\n", "            'target_properties': self.target_properties\n", "        }\n", "        joblib.dump(model_data, filepath)\n", "        print(f\"Model saved to {filepath}\")\n", "    \n", "    def load_model(self, filepath):\n", "        \"\"\"Load trained model\"\"\"\n", "        model_data = joblib.load(filepath)\n", "        self.models = model_data['models']\n", "        self.scalers = model_data['scalers']\n", "        self.feature_columns = model_data['feature_columns']\n", "        self.property_weights = model_data['property_weights']\n", "        self.target_properties = model_data['target_properties']\n", "        print(f\"Model loaded from {filepath}\")\n", "\n", "# =============================================================================\n", "# CELL 6: Training Pipeline\n", "# =============================================================================\n", "\n", "def main_training_pipeline():\n", "    \"\"\"Main training pipeline\"\"\"\n", "    print(\"=== NEURIPs Polymer Property Prediction - Training Pipeline ===\")\n", "    \n", "    # Initialize predictor\n", "    predictor = PolymerPropertyPredictor()\n", "    \n", "    # Load and integrate data\n", "    train_df, datasets = predictor.load_and_integrate_data()\n", "    if train_df.empty:\n", "        print(\"No training data found. Please ensure data files are in the correct location.\")\n", "        return\n", "    \n", "    integrated_data = predictor.integrate_supplementary_data(train_df, datasets)\n", "    \n", "    # Prepare features (add PolyBERT model path if available)\n", "    polybert_path = None  # Set to your PolyBERT model path if available\n", "    X = predictor.prepare_features(integrated_data, polybert_path)\n", "    \n", "    # Prepare targets\n", "    y = integrated_data[CFG.TARGETS].copy()\n", "    \n", "    # Train models with optimization\n", "    predictor.train_models(X, y, cv_folds=5, n_trials=50)\n", "    \n", "    # Save trained model\n", "    predictor.save_model('polymer_model.pkl')\n", "    \n", "    print(\"Training completed successfully!\")\n", "    return predictor\n", "\n", "# =============================================================================\n", "# CELL 7: Prediction Pipeline\n", "# =============================================================================\n", "\n", "def predict_pipeline(test_df=None):\n", "    \"\"\"Prediction pipeline for Kaggle submission\"\"\"\n", "    print(\"=== NEURIPs Polymer Property Prediction - Prediction Pipeline ===\")\n", "    \n", "    # Load test data if not provided\n", "    if test_df is None:\n", "        try:\n", "            test_df = pd.read_csv('test.csv')\n", "            test_df['SMILES'] = test_df['SMILES'].apply(make_smile_canonical)\n", "        except FileNotFoundError:\n", "            print(\"test.csv not found. Creating sample data for testing...\")\n", "            test_df = pd.DataFrame({\n", "                'id': [1, 2, 3],\n", "                'SMILES': ['*CCO*', '*CC(C)O*', '*CCCC*']\n", "            })\n", "    \n", "    print(f\"Test data shape: {test_df.shape}\")\n", "    \n", "    # Initialize predictor\n", "    predictor = PolymerPropertyPredictor()\n", "    \n", "    # Try to load pre-trained model\n", "    model_path = 'polymer_model.pkl'\n", "    if os.path.exists(model_path):\n", "        predictor.load_model(model_path)\n", "        print(\"Loaded pre-trained model\")\n", "    else:\n", "        print(\"No pre-trained model found, training on available data...\")\n", "        predictor = main_training_pipeline()\n", "    \n", "    # Make predictions\n", "    polybert_path = None  # Set to your PolyBERT model path if available\n", "    predictions_df = predictor.predict(test_df['SMILES'].tolist(), polybert_path)\n", "    \n", "    # Create submission\n", "    submission = predictor.create_submission(test_df, predictions_df)\n", "    \n", "    # Save submission\n", "    submission.to_csv('submission.csv', index=False)\n", "    print(\"Submission saved to submission.csv\")\n", "    print(submission.head())\n", "    \n", "    return submission\n", "\n", "# =============================================================================\n", "# CELL 8: Dataset Analysis Functions\n", "# =============================================================================\n", "\n", "def analyze_dataset():\n", "    \"\"\"Comprehensive dataset analysis\"\"\"\n", "    print(\"=== Dataset Analysis ===\")\n", "    \n", "    try:\n", "        train = pd.read_csv('train.csv')\n", "        print(f\"Training data shape: {train.shape}\")\n", "        print(f\"Columns: {list(train.columns)}\")\n", "        \n", "        # Missing value analysis\n", "        print(\"\\nMissing values:\")\n", "        missing = train.isnull().sum()\n", "        missing_pct = (missing / len(train)) * 100\n", "        missing_df = pd.DataFrame({\n", "            'Missing Count': missing,\n", "            'Missing %': missing_pct\n", "        }).sort_values('Missing %', ascending=False)\n", "        print(missing_df[missing_df['Missing Count'] > 0])\n", "        \n", "        # Target distribution\n", "        print(\"\\nTarget statistics:\")\n", "        for target in CFG.TARGETS:\n", "            if target in train.columns:\n", "                available = train[target].notna().sum()\n", "                print(f\"{target}: {available} samples ({available/len(train)*100:.1f}%)\")\n", "                if available > 0:\n", "                    print(f\"  Range: [{train[target].min():.3f}, {train[target].max():.3f}]\")\n", "                    print(f\"  Mean: {train[target].mean():.3f}\")\n", "        \n", "        # SMILES analysis\n", "        print(f\"\\nSMILES analysis:\")\n", "        print(f\"Unique SMILES: {train['SMILES'].nunique()}\")\n", "        print(f\"Average SMILES length: {train['SMILES'].str.len().mean():.1f}\")\n", "        \n", "        # Check for supplementary data\n", "        supplement_dir = Path('train_supplement')\n", "        if supplement_dir.exists():\n", "            print(f\"\\nSupplementary datasets:\")\n", "            for file in supplement_dir.glob('*.csv'):\n", "                try:\n", "                    df = pd.read_csv(file)\n", "                    print(f\"  {file.name}: {df.shape}\")\n", "                except:\n", "                    print(f\"  {file.name}: Error reading file\")\n", "        \n", "    except FileNotFoundError:\n", "        print(\"train.csv not found. Please ensure the data file is in the current directory.\")\n", "\n", "# =============================================================================\n", "# CELL 9: Main Execution\n", "# =============================================================================\n", "\n", "if __name__ == \"__main__\":\n", "    # Analyze dataset first\n", "    analyze_dataset()\n", "    \n", "    # Run training pipeline\n", "    print(\"\\n\" + \"=\"*50)\n", "    predictor = main_training_pipeline()\n", "    \n", "    # Run prediction pipeline\n", "    print(\"\\n\" + \"=\"*50)\n", "    submission = predict_pipeline()\n", "    \n", "    print(\"\\nPipeline completed successfully!\")"]}], "metadata": {"kernelspec": {"display_name": "Py310FinRL", "language": "python", "name": "py310"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.5"}}, "nbformat": 4, "nbformat_minor": 5}
"""
Data validation script for NEURIPs Polymer Competition
"""

import os
import pandas as pd
from pathlib import Path

# Set working directory
WORK_DIR = Path("/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/Kaggle/NEURIPs_polymer")
os.chdir(WORK_DIR)

def validate_data_files():
    """Validate that all required data files exist and are readable"""
    print("=== Data File Validation ===")
    print(f"Working directory: {WORK_DIR}")
    
    required_files = ['train.csv', 'test.csv', 'sample_submission.csv']
    supplement_files = [f'train_supplement/dataset{i}.csv' for i in range(1, 5)]
    
    # Check main files
    print("\nMain data files:")
    for file in required_files:
        file_path = WORK_DIR / file
        if file_path.exists():
            try:
                df = pd.read_csv(file_path)
                print(f"✓ {file}: {df.shape} - {list(df.columns)}")
            except Exception as e:
                print(f"✗ {file}: Error reading - {e}")
        else:
            print(f"✗ {file}: Not found")
    
    # Check supplement files
    print("\nSupplement data files:")
    for file in supplement_files:
        file_path = WORK_DIR / file
        if file_path.exists():
            try:
                df = pd.read_csv(file_path)
                print(f"✓ {file}: {df.shape} - {list(df.columns)}")
            except Exception as e:
                print(f"✗ {file}: Error reading - {e}")
        else:
            print(f"✗ {file}: Not found")
    
    # Check directory structure
    print(f"\nDirectory contents:")
    if WORK_DIR.exists():
        for item in sorted(WORK_DIR.iterdir()):
            if item.is_file():
                size_mb = item.stat().st_size / (1024 * 1024)
                print(f"  📄 {item.name} ({size_mb:.2f} MB)")
            elif item.is_dir():
                print(f"  📁 {item.name}/")
                # Show subdirectory contents
                for subitem in sorted(item.iterdir()):
                    if subitem.is_file():
                        size_mb = subitem.stat().st_size / (1024 * 1024)
                        print(f"    📄 {subitem.name} ({size_mb:.2f} MB)")
    else:
        print(f"Working directory does not exist: {WORK_DIR}")

if __name__ == "__main__":
    validate_data_files()
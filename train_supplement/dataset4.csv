SMILES,FFV
*C(=O)NNC(=O)c1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(C(=O)NNC(=O)c3ccc(*)nc3)cc2)cc1,0.37272461
*C(=O)NNC(=O)c1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(C(=O)NNC(=O)c3cncc(*)c3)cc2)cc1,0.36547823
*C(=O)Nc1cc(NC(=O)c2ccc3[nH]c(-c4cc(-c5nc6cc(*)ccc6[nH]5)cc(N5C(=O)c6ccccc6C5=O)c4)nc3c2)cc(-c2nc3ccccc3[nH]2)c1,0.37637691
*C(=O)Nc1ccc(-c2cc(-c3ccccc3)cc(-c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4cccc(P(=O)(c6ccccc6)c6cccc(N7C(=O)c8ccc(*)cc8C7=O)c6)c4)C5=O)cc3)n2)cc1,0.37693882
*C(=O)Nc1ccc(-c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1,0.35523486
*C(=O)Nc1ccc(-c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(S(=O)(=O)c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1,0.35424303
*C(=O)Nc1ccc(-c2sc(-c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6cccc7c(Oc8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)cccc67)cc4)C5=O)cc3)c(-c3ccccc3)c2-c2ccccc2)cc1,0.38574846
*C(=O)Nc1ccc(C(c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3cccc5c(N6C(=O)c7ccc(*)cc7C6=O)cccc35)C4=O)cc2)(C(F)(F)F)C(F)(F)F)cc1,0.36906594
*C(=O)Nc1ccc(C2(c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(-c6ccc(N7C(=O)c8ccc(*)cc8C7=O)c(OC)c6)cc4OC)C5=O)cc3)c3ccccc3-c3ccccc32)cc1,0.37058306
*C(=O)Nc1ccc(C2(c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc4)C5=O)cc3)c3ccccc3-c3ccccc32)cc1,0.37211646
*C(=O)Nc1ccc(N=Nc2ccc([N+](=O)[O-])cc2)c(NC(=O)c2ccc3c(c2)C(=O)N(c2c(C)cc(Cc4cc(C)c(N5C(=O)c6ccc(*)cc6C5=O)c(C)c4)cc2C)C3=O)c1,0.37657696
*C(=O)Nc1ccc(Oc2ccc(-c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(C)c(N7C(=O)c8ccc(*)cc8C7=O)c5)C6=O)cc4)cc3)cc2)cc1,0.36178667
*C(=O)Nc1ccc(Oc2ccc(-c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(C8(c9ccc(Oc%10ccc(N%11C(=O)c%12ccc(*)cc%12C%11=O)cc%10)cc9)CC9CC8C8CCCC98)cc7)cc5)C6=O)cc4)cc3C)c(C)c2)cc1,0.37683611
*C(=O)Nc1ccc(Oc2ccc(-c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5cccc(N7C(=O)c8ccc(*)cc8C7=O)c5)C6=O)cc4)cc3)cc2)cc1,0.35193775
*C(=O)Nc1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(NC(=O)c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc4)cc3)cc2)cc1,0.36224354
*C(=O)Nc1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7cccc(N8C(=O)c9ccc(*)cc9C8=O)c7)cc5)C6=O)cc4)cc3)cc2)cc1,0.36079128
*C(=O)Nc1ccc(Oc2ccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(-c6sc(-c7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)c(-c7ccccc7)c6-c6ccccc6)cc4)C5=O)cc3)cc2)cc1,0.38060777
*C(=O)Nc1ccc(Oc2cccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(C6(c7ccc(Oc8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)cc7)NC(=O)c7ccccc76)cc5)cc3)C4=O)c2)cc1,0.36268788
*C(=O)Nc1ccc(Oc2cccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4cccc(Oc6cccc(Oc7cccc(N8C(=O)c9ccc(*)cc9C8=O)c7)c6C#N)c4)C5=O)cc3)c2)cc1,0.35558372
*C(=O)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(-c5sc(-c6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)c(-c6ccccc6)c5-c5ccccc5)cc3)C4=O)cc2)cc1,0.39133819
*C(=O)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(NC(=O)Nc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1,0.34470216
*C(=O)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3cccc(N5C(=O)c6ccc(*)cc6C5=O)c3)C4=O)cc2)cc1,0.35285588
*C(=O)Nc1ccc(Sc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1,0.35778072
*C(=O)Nc1cccc(C=CC(=O)C=Cc2cccc(NC(=O)c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)c2)c1,0.34241742
*C(=O)Nc1cccc(NC(=O)c2ccc3[nH]c(-c4cccc(-c5nc6cc(*)ccc6[nH]5)c4)nc3c2)n1,0.36871452
*C(=O)Nc1cccc(Oc2ccc(C(=O)c3ccc(Oc4cccc(NC(=O)c5ccc(*)nc5)c4)cc3)cc2)c1,0.3539953
*C(=O)Nc1cccc(Oc2cccc(Oc3cccc(Oc4cccc(Oc5cccc(NC(=O)c6ccc7c(c6)C(=O)N(c6ccc(Oc8cccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)c8C#N)cc6)C7=O)c5)c4C#N)c3)c2C#N)c1,0.3612591
*C(=O)Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1,0.36144044
*C(=O)Oc1ccc(OC(=O)c2ccc3c(c2)C(=O)N(c2ccc(Cc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)c(C(C)(C)C)c1,0.36805651
*C(=O)Oc1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(OC(=O)c3ccc(*)s3)cc2)cc1,0.38403914
*C(=O)c1cc(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)cc(C(C)(C)C)c1,0.38251574
*C(=O)c1cc2c(cc1Cl)C(=O)N(c1cc(Cl)c(N3C(=O)c4cc(*)c(Cl)cc4C3=O)cc1Cl)C2=O,0.40503796
*C(=O)c1ccc(-c2ccc(C(C)(C)c3ccc(-c4ccc(C(=O)c5ccc6c(c5)C(=O)N(c5ccc(Cc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc5)C6=O)cc4)cc3)cc2)cc1,0.38251752
*C(=O)c1ccc(C(=O)N2CC(C)N(*)CC2C)cc1,0.36842025
*C(=O)c1ccc(Oc2ccc(Oc3ccc(C(=O)c4ccc5c(c4)C(=O)N(c4ccc(N6C(=O)c7ccc(*)cc7C6=O)cc4)C5=O)cc3)cc2)cc1,0.36785413
*C(=O)c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(C(=O)c5ccc6c(c5)C(=O)N(c5ccc(Cc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc5)C6=O)cc4)cc3)cc2)cc1,0.37320978
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(N3C(=O)c4ccc(*)cc4C3=O)cc1)C2=O,0.37740339
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(NC(=O)c3cccc(C(=O)Nc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)c3)cc1)C2=O,0.35291653
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4ccc(C(=O)c5ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc5)cc4)cc3)cc1)C2=O,0.37475153
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3cccc4c(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cccc34)cc1)C2=O,0.3770815
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(NC(=O)c3cccc(C(=O)Nc4cccc(N5C(=O)c6ccc(*)cc6C5=O)c4)c3)c1)C2=O,0.3482104
*C(=O)c1cccc(C(=O)N2CCN(*)CC2)c1,0.34759038
*C(=O)c1cccc(C(=O)c2ccc(C=C3CCC(=Cc4ccc(*)cc4)C3=O)cc2)c1,0.37124566
*C(=O)c1cccc(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)c1,0.36604695
*C1C(=O)N(C2CCCCC2)C(=O)C1*,0.39675252
*C1C(=O)N(CCCOc2ccc(-c3ccc(C#N)cc3)cc2)C(=O)C1*,0.34332558
*C1C(=O)N(c2cc(Br)c(O[Si](c3ccccc3)(c3ccccc3)c3ccccc3)c(Br)c2)C(=O)C1*,0.40469568
*C1C(=O)N(c2ccc(O[Si](c3ccccc3)(c3ccccc3)c3ccccc3)cc2)C(=O)C1*,0.40006119
*C1C=C(CCCCCC)C(*)S1,0.42189435
*C=CC1CC(*)C(C(=O)OCCCCCCCCOc2ccc(C=Cc3ccc([N+](=O)[O-])cc3)cc2)C1,0.36024408
*C=Cc1cc(OCCc2ccccc2)c(*)cc1OC,0.36576533
*C=Cc1ccc(C=Cc2ccc3c(c2)C(CCCCCCOc2ccc4ccc(=O)oc4c2)(CCCCCCOc2ccc4ccc(=O)oc4c2)c2cc(*)ccc2-3)cc1,0.35196182
*C=Cc1ccc(C=Cc2ccc3c(c2)Sc2cc(*)ccc2N3c2ccc(OCCCCCCCCCCCC)cc2)s1,0.39219852
*C=Cc1sc(-c2ccc(-c3sc(C=CC4=CC(=C(C#N)C#N)C=C(*)O4)c(CCCCCC)c3CCCCCC)s2)c(CCCCCC)c1CCCCCC,0.41455567
*CC#CC#CCOc1ccc(C(=O)OCCN(CCOC(=O)c2ccc(O*)cc2)c2ccc(N=Nc3ccc(C#N)cc3)cc2)cc1,0.36879149
*CC#CC#CCOc1ccc(C(=O)OCCN(CCOC(=O)c2ccc(O*)cc2)c2ccc(N=Nc3ccc([N+](=O)[O-])cc3)cc2)cc1,0.35856006
*CC#CC#CCOc1cccc(C(=O)OCCN(CCOC(=O)c2cccc(O*)c2)c2ccc(N=Nc3ccc(C#N)cc3)cc2)c1,0.36722522
*CC(*)(C)C(=O)OC(COc1cccc2ccccc12)COc1cccc2ccccc12,0.35180615
*CC(*)(C)C(=O)OC1CCCCCCCCC1,0.40440294
*CC(*)(C)C(=O)OCC1CO1,0.33062914
*CC(*)(C)C(=O)OCC1OC(n2ccc(=O)[nH]c2=O)C(O)C1O,0.28114011
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(C(=O)Oc2ccc(C(=O)C=Cc3c(C)c4ccccc4n3C)cc2)cc1,0.36171157
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(C(=O)Oc2ccc(C(=O)C=Cc3c(C)c4ccccc4n3CCCC)cc2)cc1,0.36405968
*CC(*)(C)C(=O)OCCCCCCCCOc1ccc(C(=O)Oc2ccc(C(=O)C=Cc3c(C)c4ccccc4n3C)cc2)cc1,0.35752069
*CC(*)(C)C(=O)OCCCCCCCCOc1ccc(C(=O)Oc2ccc(C(=O)C=Cc3c(C)c4ccccc4n3CCCC)cc2)cc1,0.36425846
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(C(=O)C=Cc3c(C)c4ccccc4n3C)cc2)cc1,0.35645942
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(C(=O)C=Cc3c(C)c4ccccc4n3CCCC)cc2)cc1,0.36019365
*CC(*)(C)C(=O)OCCC[Si]12O[Si]3(CC(C)C)O[Si]4(CC(C)C)O[Si](CC(C)C)(O1)O[Si]1(CC(C)C)O[Si](CC(C)C)(O2)O[Si](CC(C)C)(O3)O[Si](CC(C)C)(O4)O1,0.40617403
*CC(*)(CC(=O)OC1CCCCCCC1)C(=O)OC1CCCCCCC1,0.36818557
*CC(*)(CC(=O)OCCC1CCCCC1)C(=O)OCCC1CCCCC1,0.3743525
*CC(*)(F)C(=O)OCC(F)(F)C(F)(F)C(F)(F)F,0.32178633
*CC(*)C(=O)N1CCCCC1,0.36828508
*CC(*)C(=O)Nc1ccc2c(c1)C(=O)c1ccccc1C2=O,0.32709343
*CC(*)C(=O)OCC1(CC)COC(c2ccccc2)OC1,0.34648032
*CC(*)C(=O)OCCOc1ccc(C(=O)Oc2ccc(OC(=O)c3ccc(OCCCC)cc3)cc2)cc1,0.3436788
*CC(*)C(=O)OCCOc1ccc(C(=O)Oc2ccc(OC(=O)c3ccc(OCCCCC)cc3)cc2)cc1,0.34788271
*CC(*)C(=O)OCCOc1ccc(C(C)(C)c2ccc(OCCO)cc2)cc1,0.33916196
*CC(*)C(=O)Oc1ccc(C(=O)OCc2ccccc2)cc1,0.33941879
*CC(*)C(=O)Oc1ccc(C(=O)Oc2ccc(OC(=O)c3ccc(OCCCCC)cc3)cc2)cc1,0.34978241
*CC(*)N1CCCCCC1=O,0.36209846
*CC(*)c1ccc(C(=O)CCN2CCCCC2)cc1,0.37311156
*CC(*)c1ccc(C(=O)N(CC)CC)cc1,0.36711341
*CC(*)c1ccc(C(=O)N2CCOCC2)cc1,0.35986197
*CC(*)c1ccc(COCCOCCCC)cc1,0.37958253
*CC(*)c1ccc(COc2ccc(-c3ccc(-c4ccc(C)s4)c4nsnc34)cc2)cc1,0.36773
*CC(*)n1cncn1,0.33624313
*CC(=O)Nc1ccc(Oc2cccc(Oc3ccc(NC(=O)CN4C(=O)c5ccc(C(c6ccc7c(c6)C(=O)N(*)C7=O)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)c2)cc1,0.34064566
*CC(=O)OC(=O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1,0.33458907
*CC(C)(C)C1C(=O)N(C2CCCCC2)C(=O)C1*,0.36558232
*CC(C)(C)COC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1,0.3487602
*CC(C)(C)CS(=O)(=O)CC(C)(C)COC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1,0.35233663
*CC(C)N1C(=O)C2C3C=CC(C4C(=O)N(*)C(=O)C34)C2C1=O,0.3634472
*CC(CCCCCCCCCCCCCCCC)C(CCCCCCCCCCCCCCCC)COC(=O)c1ccc(C(=O)O*)cc1,0.39720442
*CC(CO)(CCl)COc1ccc(C(c2ccc(O*)cc2)(C(F)(F)F)C(F)(F)F)cc1,0.35329338
*CC(COc1c(Cl)cc(C(C)(C)c2cc(Cl)c(O*)c(Cl)c2)cc1Cl)OC(C)=O,0.36697598
*CC(COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1)OC(=O)C=Cc1ccccc1,0.35543793
*CC(COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1)OC(=O)CC,0.35519812
*CC(COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1)OC(=O)CCl,0.35599023
*CC(COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1)OC(C)=O,0.35142781
*CC(F)(F)C1(F)CC(C(O)(C(F)(F)F)C(F)(F)F)CC1*,0.32084568
*CC(O)CN(C)S(=O)(=O)c1cccc(S(=O)(=O)N(C)CC(O)COc2ccc(C(C)(C)c3ccc(O*)cc3)cc2)c1,0.34239354
*CC(O)CN(C)S(=O)(=O)c1cccc(S(=O)(=O)N(C)CC(O)COc2ccc(O*)cc2)c1,0.33491941
*CC(O)CN(CC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1)c1ccc(N=Nc2ccc([N+](=O)[O-])cc2)cc1,0.35223599
*CC(O)CN(CCO)CC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1,0.33466845
*CC(O)COC(=O)/C=C\C(=O)Oc1ccc(C(C)(C)c2ccc(O*)cc2)cc1,0.33533983
*CC(O)COC(=O)CCCCC(=O)OCC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1,0.33384251
*CC(O)COC(=O)CCCCCCC(=O)OCC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1,0.34100579
*CC(O)COC(=O)CCCCCCCCC(=O)OCC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1,0.3435317
*CC(O)COC(=O)CCCCCCCCCCC(=O)OCC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1,0.34941747
*CC(O)COc1c(C)cc(C(C)(C)c2cc(C)c(O*)c(C)c2)cc1C,0.37509406
*CC(O)COc1c(Cl)cc(C(C)(C)c2cc(Cl)c(O*)c(Cl)c2)cc1Cl,0.36746955
*CC(O)COc1ccc(C(C)(C)c2ccc(O*)c(Cl)c2)cc1Cl,0.36275869
*CC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1,0.35095055
*CC(O)COc1ccc(C(C)(CC)c2ccc(O*)cc2)cc1,0.35394579
*CC(O)COc1ccc(Cc2ccc(O*)cc2)cc1,0.34455061
*CC(O)COc1ccc(S(=O)(=O)c2ccc(OCC(O)COc3c(C)cc(S(=O)(=O)c4cc(C)c(O*)c(C)c4)cc3C)cc2)cc1,0.35617654
*CC(O)COc1ccc(S(=O)(=O)c2ccc(OCC(O)COc3ccc(S(=O)(=O)c4ccc(O*)c(C)c4)cc3C)cc2)cc1,0.34891923
*CC(OC(=O)Oc1ccc(C(=O)OC)cc1)C(COC(=O)O*)OC(=O)Oc1ccc(C(=O)OC)cc1,0.3223452
*CC(c1ccccn1)C(c1ccccc1)C(*)c1ccccn1,0.39661394
*CC1(C)CC(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)CC(C)(C)C1,0.37465828
*CC1CC(CNC(=O)c2cccc(C(=O)N*)c2)CC(C(C)(C)C)C1,0.3514179
*CC1CCC(CNC(=O)c2cc(C(=O)N*)cc(C(C)(C)C)c2)CC1,0.3576586
*CC1CCC(CNC(=O)c2cccc(C(=O)N*)c2)CC1,0.34399294
*CC1CCC(COC(=O)C2CCC(C(=O)O*)CC2)CC1,0.35005916
*CC1CCC(COC(C)OC(=O)c2ccc(C(=O)OC(C)O*)c3ccccc23)CC1,0.35502936
*CC1CCCC(CNC(=O)c2cc(C(=O)N*)cc(C(C)(C)C)c2)C1,0.35610431
*CC1CCCC(CNC(=O)c2cccc(C(=O)N*)c2)C1,0.33940105
*CC=CCOC(=O)C(Cc1ccccc1)NC(=O)C=CC(=O)NC(Cc1ccccc1)C(=O)O*,0.33875482
*CCC1CC2C(CC(*)C2OC(=O)CCCCCCCCCCOc2ccc(-c3ccc(C#N)cc3)cc2)C1OC(=O)CCCCCCCCCCOc1ccc(-c2ccc(C#N)cc2)cc1,0.36522309
*CCCC(C)CN1C(=O)c2ccc(C(=O)Oc3ccc4cc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)ccc4c3)cc2C1=O,0.34177327
*CCCC1(CCCNC(=O)CCC2(CCC(=O)N*)c3ccccc3-c3ccccc32)c2ccccc2-c2ccccc21,0.35305757
*CCCCCC(=O)NNC(=O)c1ccc(C(=O)NNC(=O)CCCCCOc2ccc(O*)c(C)c2)cc1,0.34281562
*CCCCCCCCCCC(=O)Nc1ccc(Cc2ccc(NC(=O)CCCCCCCCCCN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1,0.35192407
*CCCCCCCCCCCCCCCCOC(=O)CCCCCCC(=O)O*,0.37544747
*CCCCCCCCCCCCCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1,0.36655412
*CCCCCCCCCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1,0.35988995
*CCCCCCCCCCCN1C(=O)c2ccc(Oc3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O,0.3550053
*CCCCCCCCCCCNC(=O)CCCCCCCCC(=O)N*,0.37104926
*CCCCCCCCCCN1C(=O)C2C3C=CC(C4C(=O)N(*)C(=O)C34)C2C1=O,0.34804179
*CCCCCCCCCCN1C(=O)c2ccc(Oc3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O,0.34969426
*CCCCCCCCCCNC(=O)c1ccc(Cc2ccc(C(=O)N*)cc2)cc1,0.35614349
*CCCCCCCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1,0.35710365
*CCCCCCCCCCOc1c(OC)cc(C=C2CCCC(=Cc3cc(OC)c(O*)c(OC)c3)C2=O)cc1OC,0.3627628
*CCCCCCCCCCOc1ccc(C=C2CCCC(=Cc3ccc(O*)c(OC)c3)C2=O)cc1OC,0.36585975
*CCCCCCCCCCOc1ccc(C=C2CCCC(=Cc3ccc(O*)cc3)C2=O)cc1,0.37076764
*CCCCCCCCCCOc1ccc(C=Cc2ccc(OCCCCCCCCCCOP(=O)(O*)OCCCCCCCCCCOc3ccc(N=Nc4ccc(Cl)cc4)cc3)cc2)cc1,0.37552981
*CCCCCCCCCCOc1ccc(C=Cc2ccc(OCCCCCCCCCCOP(=O)(O*)OCCCCCCCCCCOc3ccc(N=Nc4ccc(OC)cc4)cc3)cc2)cc1,0.374166
*CCCCCCCCCCOc1ccc(C=Cc2ccc(OCCCCCCCCCCOP(=O)(O*)OCCCCCCCCCCOc3ccc(N=Nc4ccc([N+](=O)[O-])cc4)cc3)cc2)cc1,0.36876796
*CCCCCCCCCCOc1ccc(C=Cc2ccc(OCCCCCCCCCCOP(=O)(O*)OCCCCCCCCCCOc3ccc(N=Nc4ccccc4)cc3)cc2)cc1,0.37909933
*CCCCCCCCCCc1ccc(-c2c(-c3ccccc3)cc(-c3cccc(-c4cc(-c5ccccc5)c(-c5ccc(*)cc5)c(-c5ccccc5)c4-c4ccccc4)c3)c(-c3ccccc3)c2-c2ccccc2)cc1,0.39826604
*CCCCCCCCCN1C(=O)c2ccc(Oc3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O,0.34911033
*CCCCCCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1,0.35367211
*CCCCCCCCN1C(=O)c2ccc(Oc3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O,0.34901426
*CCCCCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1,0.35257805
*CCCCCCCCOC(=O)Nc1ccc(NC(=O)OCCCCCCCCOc2ccc(-c3ccc(O*)cc3)cc2)c(C)c1,0.3579984
*CCCCCCCCOc1c(OC)cc(C=C2CCCC(=Cc3cc(OC)c(O*)c(OC)c3)C2=O)cc1OC,0.35816729
*CCCCCCCCOc1ccc(C(C)(C)c2ccc(O*)cc2)cc1,0.36752126
*CCCCCCCCOc1ccc(C(c2ccc(O*)cc2)(C(F)(F)F)C(F)(F)F)cc1,0.36265916
*CCCCCCCCOc1ccc(C=C2CCCC(=Cc3ccc(O*)c(OC)c3)C2=O)cc1OC,0.36354284
*CCCCCCCCOc1ccc(C=C2CCCC(=Cc3ccc(O*)cc3)C2=O)cc1,0.37127154
*CCCCCCCCc1nc2cc(NC(=O)CCCCC(=O)Nc3ccc4oc(*)nc4c3)ccc2o1,0.35552869
*CCCCCCCN1C(=O)c2ccc(Oc3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O,0.3473574
*CCCCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1,0.34774213
*CCCCCCN1C(=O)C(=O)N(c2ccc(C)c(N3C(=O)C(=O)N(*)C3=O)c2)C1=O,0.33065809
*CCCCCCN1C(=O)C(=O)N(c2ccc(Oc3ccc(N4C(=O)C(=O)N(*)C4=O)cc3)cc2)C1=O,0.33381102
*CCCCCCN1C(=O)c2ccc(Oc3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O,0.34767207
*CCCCCCNC(=O)c1cc(NC(=O)c2ccc(NC(=O)C(CC(C)C)N3C(=O)c4ccccc4C3=O)cc2)cc(C(=O)N*)c1,0.33657316
*CCCCCCNC(=O)c1ccc(C(C)(CC)c2ccc(C(=O)N*)cc2)cc1,0.35362781
*CCCCCCOC(=O)C(CCCCCOc1cc(-c2ccccn2)nc(-c2ccccn2)c1)C(=O)OCCCCCCOc1ccc(-c2ccc(O*)cc2)cc1,0.36437086
*CCCCCCOC(=O)C(Cc1ccccc1)NC(=O)C=CC(=O)NC(Cc1ccccc1)C(=O)O*,0.34396826
*CCCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1,0.34648888
*CCCCCCOc1c(OC)cc(C=C2CCCC(=Cc3cc(OC)c(O*)c(OC)c3)C2=O)cc1OC,0.35644797
*CCCCCCOc1ccc(C(=O)N(C(=O)c2ccc(O*)cc2)c2ccc(Oc3ccccc3)cc2)cc1,0.36783158
*CCCCCCOc1ccc(C(C)(C)c2ccc(O*)cc2)cc1,0.36632136
*CCCCCCOc1ccc(C=C2CCCC(=Cc3ccc(O*)c(OC)c3)C2=O)cc1OC,0.35895367
*CCCCCCOc1ccc(C=C2CCCC(=Cc3ccc(O*)cc3)C2=O)cc1,0.36783098
*CCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1,0.34273288
*CCCCN1C(=O)C2C3C=CC(C4C(=O)N(*)C(=O)C34)C2C1=O,0.34543609
*CCCCNC(=O)c1ccc(C(C)(CC)c2ccc(C(=O)N*)cc2)cc1,0.34798992
*CCCCOC(=O)C(Cc1ccccc1)NC(=O)C=CC(=O)NC(Cc1ccccc1)C(=O)O*,0.33930122
*CCCCOC(=O)CCCCCNC(=O)CCCCC(=O)NCCCCCC(=O)O*,0.34021952
*CCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1,0.34189824
*CCCCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1,0.33930265
*CCCCOC(=O)c1ccc(C(C)(C)c2ccc(C(=O)O*)cc2)cc1,0.35363706
*CCCCOCCCCOCCCCOC(=O)c1ccc(N=Cc2cc(OCCCCCC)c(C=Cc3cc(OCCCCCC)c(C=Cc4cc(OCCCCCC)c(C=Nc5ccc(C(=O)O*)cc5)cc4OCCCCCC)cc3OCCCCCC)cc2OCCCCCC)cc1,0.38309201
*CCCN1C(=O)C2C3C=CC(C4C(=O)N(*)C(=O)C34)C2C1=O,0.34806971
*CCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1,0.33832602
*CCCOCCCCOCCCN1C(=O)c2ccc(C(=O)Oc3ccc(OC(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2C1=O,0.33583724
*CCCOCCCCOCCCN1C(=O)c2ccc(C(=O)Oc3ccc4cc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)ccc4c3)cc2C1=O,0.33819968
*CCN(CCOC(=O)NCC1(C)CC(NC(=O)O*)CC(C)(C)C1)c1ccc(N=Nc2ccc(C#N)cc2)cc1,0.36385839
*CCN(CCOC(=O)NCC1(C)CC(NC(=O)O*)CC(C)(C)C1)c1ccc(N=Nc2ccc([N+](=O)[O-])cc2)cc1,0.34598899
*CCN(CCOC(=O)Nc1ccc(-c2ccc(NC(=O)O*)c(C)c2)cc1C)c1ccc(N=Nc2ccc([N+](=O)[O-])cc2)cc1,0.34910461
*CCN(CCOC(=O)Nc1ccc(-c2ccc(NC(=O)O*)c(OC)c2)cc1OC)c1ccc(N=Nc2ccc([N+](=O)[O-])cc2)cc1,0.34489269
*CCN(CCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1)c1ccccc1,0.34479226
*CCN(CCOC(=O)OCc1ccc(COC(=O)O*)cc1)c1ccc(N=Nc2ccc([N+](=O)[O-])cc2)cc1,0.34805417
*CCN(CCOC(=O)c1cc(OCCN(C)c2ccc(S(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc2)cc(C(=O)O*)c1)c1ccc(S(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc1,0.34325537
*CCN(CCOC(=O)c1cc(OCCN(C)c2ccc(S(=O)(=O)CCCCCCCC)cc2)cc(C(=O)O*)c1)c1ccc(S(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc1,0.35536738
*CCN(CCOC(=O)c1cc(OCc2c(F)c(F)c(OC)c(F)c2F)cc(C(=O)O*)c1)c1ccc(C=Cc2ccc(C=CC3=C(C#N)C(=C(C#N)C#N)OC3(c3ccccc3)C(F)(F)F)s2)cc1,0.36032332
*CCNC(=O)Nc1ccc(Cc2ccc(NC(=O)NCCOCCO*)cc2)cc1,0.33154374
*CCNC(=O)c1ccc([Si](C)(C)c2ccc(C(=O)NCCN(*)c3ccc(/C=C/c4ccc([N+](=O)[O-])cc4)cc3)cc2)cc1,0.35386775
*CCNC(=O)c1ccc([Si](CCCC)(CCCC)c2ccc(C(=O)NCCN(*)c3ccc(/C=C/c4ccc([N+](=O)[O-])cc4)cc3)cc2)cc1,0.35921769
*CCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1,0.33628572
*CCOC(=O)c1ccc(C(C)(CC)c2ccc(C(=O)O*)cc2)cc1,0.35349899
*CCOCCOCCN1C(=O)c2ccc(C(=O)Oc3ccc(OC(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2C1=O,0.33052641
*CCOCCOCCN1C(=O)c2ccc(C(=O)Oc3ccc4cc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)ccc4c3)cc2C1=O,0.3345641
*CCOCCOCCOCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1,0.33889511
*CCOCCOCCOCCOCCOCCOc1ccc(C(=O)Nc2cc(NC(=O)c3ccc(O*)cc3)cc(-c3nc4ccccc4[nH]3)c2)cc1,0.34429063
*CCOCCOCCOCCOCCOc1ccc(C(=O)Nc2cc(NC(=O)c3ccc(O*)cc3)cc(-c3nc4ccccc4[nH]3)c2)cc1,0.34659235
*CCOCCOCCOCCOc1ccc(C(=O)Nc2cc(NC(=O)c3ccc(O*)cc3)cc(-c3nc4ccccc4[nH]3)c2)cc1,0.3483589
*CCOCCOCCOc1ccc(C(=O)Nc2cc(NC(=O)c3ccc(O*)cc3)cc(-c3nc4ccccc4[nH]3)c2)cc1,0.34154325
*CCOCCOc1ccc(C(=O)Nc2cc(NC(=O)c3ccc(O*)cc3)cc(-c3nc4ccccc4[nH]3)c2)cc1,0.34638057
*CCOc1ccc(C(=O)Nc2cc(NC(=O)c3ccc(O*)cc3)cc(-c3nc4ccccc4[nH]3)c2)cc1,0.35204064
*CN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(Cc3nnc(-c5ccc(-c6nnc(*)o6)cc5)o3)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O,0.37085226
*CNC(=O)OCc1cocc1COC(=O)NCc1ccc(C(C)(C)c2ccc(*)o2)o1,0.33476958
*COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1,0.35264631
*COc1ccc(C(c2ccc(O*)cc2)(C(F)(F)F)C(F)(F)F)cc1,0.34402643
*Cc1cc2cc(C(=O)Nc3ccc(-c4ccc(NC(=O)c5cc6cc(*)c(OC(=O)COc7ccc8ccc(=O)oc8c7)cc6oc5=O)cc4)cc3)c(=O)oc2cc1OC(=O)COc1ccc2ccc(=O)oc2c1,0.32178966
*Cc1ccc(COC(=O)c2cccc(C(=O)O*)c2)cc1,0.34174571
*Cc1ccc(COP(=O)(N=Nc2ccc(-c3ccc(N=NP(=O)(O*)OC)cc3)cc2)OC)cc1,0.35769989
*Cc1ccc(C[n+]2ccc(-c3cc[n+](*)cc3)cc2)cc1,0.36770634
*Cc1ccc2nc(-c3cc(-c4nc5ccc(*)cc5c(=O)o4)cc(N4C(=O)c5ccccc5C4=O)c3)oc(=O)c2c1,0.37326662
*Cc1cccc(C[n+]2ccc(-c3cc[n+](*)cc3)cc2)c1,0.36232627
*Cc1ccccc1C[n+]1ccc(-c2cc[n+](*)cc2)cc1,0.35795758
*N/C(C=C)=C/C=C(\C)C1(c2ccc(N*)cc2)CCCCC1,0.35375342
*N/N=N\c1ccc(N*)c2c1C(=O)c1c(N)ccc(N)c1C2=O,0.34005083
*N=P(*)(Oc1ccc2ccccc2c1)Oc1ccc2ccccc2c1,0.3648512
*NC(=C)/C=C\C(=C/C)C1(c2ccc(N*)cc2)CCCCC1,0.3442031
*NC/C=C(/c1cccc(-c2ccccc2)c1)c1ccccc1-c1ccccc1-c1ccc(C2(c3ccccc3)c3ccccc3-c3ccccc32)cc1N*,0.37006353
*NC1=C(N)N[C@H](N*)NC1=O,0.28322193
*NC1=C(c2c(N*)ccc3ccccc23)c2ccccc2CC1,0.35718606
*NC1=C(c2ccccc2)[C@@](O)(N*)[C@H](N)C=C1,0.32796087
*NC1=CC=C(c2ccc(N*)cc2)C(C(C)(C)C)(C(C)(C)C)C1,0.34704926
*NC1=NC(=S)N=C(N)C1N*,0.30874827
*NCC1(C)CC(N*)CC(C)(C)C1,0.34576246
*NCC1CCC(CN*)CC1,0.33576539
*NCCCc1ccc2ccc3ccc(N*)cc3c2c1,0.33634808
*NCCc1ccc2ccc3ccc(N*)cc3c2c1,0.33458647
*NC[C@@H]1CCC[C@@H](CN*)C1,0.32836534
*NNC(=O)C=CC(=O)Nc1cccc(C=C2CCCC(=Cc3cccc(NC(=O)C=CC(*)=O)c3)C2=O)c1,0.3376016
*NNC(=O)c1cc(NC(=O)c2ccc3c(c2)C(=O)N(c2ccc(C)cn2)C3=O)cc(C(=O)NNC(=O)c2ccc(C(*)=O)cc2)c1,0.33692653
*NNC(=O)c1cc(NC(=O)c2ccc3c(c2)C(=O)N(c2ncccc2C)C3=O)cc(C(=O)NNC(=O)c2ccc(C(*)=O)cc2)c1,0.33234263
*NNC(=O)c1ccccc1C(=O)Nc1cccc(C=C2CCCC(=Cc3cccc(NC(=O)c4ccccc4C(*)=O)c3)C2=O)c1,0.35281266
*N[C@H]1C(=O)NC(=O)[C@@](N*)(n2c(C)nc3c(N)c(N)cc(N)c3c2=O)[C@H]1N,0.35197979
*Nc1c(C)cc(C(c2cc(C)c(N*)c(C)c2)C(c2ccc(N)c(C(C)(C)C)c2)c2ccc(N)c(C(C)(C)C)c2)cc1C,0.39156416
*Nc1c(C)cc(Cc2cc(C)c(N*)c(CC)c2)cc1CC,0.36832
*Nc1c(CC)cc(Cc2cc(CC)c(N*)c(CC)c2)cc1CC,0.37359767
*Nc1c(CC)cc(Cc2cc(CC)c(N*)c(CC)c2Cl)c(Cl)c1CC,0.37886544
*Nc1c(N)c2c(c(N*)c1NC)C(=O)c1cccc(N)c1C2=O,0.34496829
*Nc1c(N*)c(-c2ccccc2-c2ccccc2)c(-c2ccccc2)c(-c2ccccc2)c1-c1ccccc1,0.37334739
*Nc1cc(C(c2ccc(C)c(N*)c2)(C(F)(F)F)C(F)(F)F)ccc1C,0.34739951
*Nc1cc(C(c2ccc(O)c(N*)c2)(C(F)(F)F)C(F)(F)F)ccc1O,0.32241826
*Nc1cc(C)c(-c2c(C)cc(N*)cc2-c2ccccc2)c(-c2ccccc2)c1,0.36600387
*Nc1cc(C)c(C2(c3c(C)cc(N*)c4ccccc34)c3ccccc3-c3ccccc32)c2ccccc12,0.38470754
*Nc1cc(C)c(Cc2ccc(CCCCCCCCCCCCCc3ccc(Cc4c(C)cc(N*)cc4C)cc3)cc2)c(C)c1,0.35997455
*Nc1cc(C)c(Cc2ccc(CCCCCCCCCCCCc3ccc(Cc4c(C)cc(N*)cc4C)cc3)cc2)c(C)c1,0.35798452
*Nc1cc(C)c(Cc2ccc(CCCCCCCCCc3ccc(Cc4c(C)cc(N*)cc4C)cc3)cc2)c(C)c1,0.36147124
*Nc1cc(C)c(Cc2ccc(CCCCCCCCc3ccc(Cc4c(C)cc(N*)cc4C)cc3)cc2)c(C)c1,0.35897643
*Nc1cc(C)c(N*)c2c1C(=O)c1c(N)c(C)cc(N)c1C2=O,0.34660788
*Nc1cc(C2CCCCC2)c2ccc3c(N*)c(-c4ccc(C5(c6ccccc6)c6ccccc6-c6ccccc65)cc4)c(C4CCCCC4)c4ccc1c2c34,0.39403941
*Nc1cc(N)c(N)c(C)c1C(=O)Oc1cc(N)c(C(=O)Oc2cc(N)c(N)c(C)c2N)c(N)c1N*,0.32137021
*Nc1cc(N*)c(Cc2cc(C)c(N)cc2N)cc1C,0.35659354
*Nc1cc(N*)c2c(ccc3ccccc32)c1,0.33406138
*Nc1cc(N*)c2ccc3ccccc3c2c1,0.33145622
*Nc1cc(NC(=O)Nc2ccc(Cc3ccc(NC(*)=O)cc3)cc2)cc(C(=O)Nc2ccc3c(c2)C(=O)c2ccccc2C3=O)c1,0.33705899
*Nc1cc(NC(=O)Nc2ccc(NC(*)=O)cc2)cc(C(=O)Nc2cccc3c2C(=O)c2ccccc2C3=O)c1,0.32684433
*Nc1cc(NC(=O)c2cc(NC(=O)C(C(C)CC)N3C(=O)c4ccccc4C3=O)cc(C(*)=O)c2)ccc1C,0.34847575
*Nc1cc(NC(=O)c2cc(OCCN(C)c3ccc(C#N)cc3)cc(C(*)=O)c2)cc(C(=O)OCCN(C)c2ccc(C#N)cc2)c1,0.35581869
*Nc1cc(NC(=O)c2cc(OCCN(C)c3ccc(C#N)cc3)cc(C(*)=O)c2)cc(C(=O)OCCN(C)c2ccc(S(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc2)c1,0.34405633
*Nc1cc(NC(=O)c2cc(OCCN(C)c3ccc(S(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc3)cc(C(*)=O)c2)cc(C(=O)OCCN(C)c2ccc(C#N)cc2)c1,0.3482804
*Nc1cc(NC(=O)c2ccc(C(*)=O)cc2)cc(C(=O)Oc2cccc3ccccc23)c1,0.34233379
*Nc1cc(NC(=O)c2ccc3cc(C(*)=O)ccc3c2)cc(C(=O)OCCOc2ccc(C=CC(=O)c3ccccc3)cc2)c1,0.34387133
*Nc1cc2c(-c3ccc4c(c3)c3ccccc3c3c(N*)c(N)ccc43)cc3c4ccccc4ccc3c2cc1N,0.3459392
*Nc1cc2c(N*)cccc2c2ccccc12,0.333837
*Nc1cc2c(cc(N*)c3c(-c4ccc5ccccc5c4)c(-c4ccc5ccccc5c4)c(-c4ccc5ccccc5c4)c(-c4ccc5ccccc5c4)c32)c2ccccc12,0.37064494
*Nc1cc2c(cc1N*)C(=O)c1cc(N)c(N)cc1C2=O,0.34542857
*Nc1cc2c3cccc(-c4ccc(C)c(C)c4)c3c(N*)cc2c2ccccc12,0.35695006
*Nc1cc2cccc(N*)c2c2ccccc12,0.33765318
*Nc1cc2ccccc2c(N*)c1-c1ccccc1,0.3508717
*Nc1cc2ccccc2c2c(N*)cccc12,0.33679998
*Nc1cc2ccccc2c2c1ccc1ccc3c(N*)cc4ccccc4c3c12,0.36566777
*Nc1ccc(*)cc1OCCCCCCCCCCOc1ccc(C2CCC(CCCCC)CC2)cc1,0.3832015
*Nc1ccc(-c2c(-c3ccccc3)cc(-c3ccc(-c4cc(-c5ccccc5)c(-c5ccc(NC(=O)c6ccc(C(*)=O)cc6)cc5)c(-c5ccccc5)c4)cc3)cc2-c2ccccc2)cc1,0.38336728
*Nc1ccc(-c2c(C(C)C)cc(C)cc2C2(c3cc(C)cc(C(C)C)c3-c3ccc(N*)cc3)c3ccccc3-c3ccccc32)cc1,0.39756786
*Nc1ccc(-c2cc(-c3ccc(-c4ccccc4)cc3)cc(-c3ccc(-c4cc(-c5ccc(N*)cc5)cc(-c5ccc(-c6ccccc6)cc5)c4)cc3)c2)cc1,0.35431916
*Nc1ccc(-c2cc(-c3ccc(N*)cc3)c3ccc4c(-c5ccc(N)cc5)cc(-c5ccc(N)cc5)c5ccc2c3c54)cc1,0.36303147
*Nc1ccc(-c2ccc(-c3ccc(N*)c(-c4ccc5ccccc5c4)c3-c3ccc4ccccc4c3)c(-c3ccc4ccccc4c3)c2)c(-c2ccc3ccccc3c2)c1,0.36605712
*Nc1ccc(-c2ccc(-c3ccc(N*)cc3)c3c2CC2(CCCC2)C3)cc1,0.35946418
*Nc1ccc(-c2ccc(N*)c(-c3ccc(-c4ccccc4)cc3)c2-c2ccc(-c3ccccc3)cc2)cc1,0.35925281
*Nc1ccc(-c2ccc(N*)c(-c3cccc(C)c3-c3ccccc3)c2)cc1,0.35748308
*Nc1ccc(-c2ccc(N*)c(/C=C/c3ccccc3)c2)cc1,0.34484999
*Nc1ccc(-c2ccc(N*)c(Cc3ccccc3)c2Cc2ccccc2)c(Cc2ccccc2)c1Cc1ccccc1,0.35710982
*Nc1ccc(-c2ccc(N*)c(N)c2-c2ccccc2)c(-c2ccccc2)c1N,0.35339596
*Nc1ccc(-c2ccc(N*)cc2-c2cc(N)ccc2-c2ccc(N)cc2)cc1,0.36187072
*Nc1ccc(-c2ccc(N*)cc2-c2ccc(C)cc2)c(-c2ccc(C)cc2)c1,0.36702255
*Nc1ccc(-c2ccc(N*)cc2-c2ccc(C)cc2C)c(-c2ccc(C)cc2C)c1,0.37675264
*Nc1ccc(-c2ccc(N*)cc2-c2ccc(C=C)cc2)c(-c2ccc(C=C)cc2)c1,0.35833444
*Nc1ccc(-c2ccc(N*)cc2-c2ccc(CC)cc2)c(-c2ccc(CC)cc2)c1,0.36900944
*Nc1ccc(-c2ccc(N*)cc2-c2cccc(C)c2)c(-c2cccc(C)c2)c1,0.36515839
*Nc1ccc(-c2ccc(N*)cc2-c2cccc(CC)c2)c(-c2cccc(CC)c2)c1,0.36267578
*Nc1ccc(-c2ccc(N*)cc2-c2ccccc2)c(-c2ccccc2)c1,0.35575719
*Nc1ccc(-c2ccc(N*)cc2-c2ccccc2-c2ccc(C)cc2)cc1,0.35953405
*Nc1ccc(-c2ccc(N*)cc2-c2ccccc2-c2ccccc2)cc1,0.35741644
*Nc1ccc(-c2ccc(N*)cc2-c2ccccc2-c2ccccc2C)cc1,0.36316601
*Nc1ccc(-c2ccc(N*)cc2-c2ccccc2C(C)(C)C)c(-c2ccccc2C(C)(C)C)c1,0.37633983
*Nc1ccc(-c2ccc(N*)cc2-c2ccccc2C(C)C)c(-c2ccccc2C(C)C)c1,0.37695577
*Nc1ccc(-c2ccc(N*)cc2-c2ccccc2C)c(-c2ccccc2C)c1,0.36761088
*Nc1ccc(-c2ccc(N*)cc2-c2ccccc2CC)c(-c2ccccc2CC)c1,0.3676166
*Nc1ccc(-c2ccc(N*)cc2-c2ccccc2CC=C)c(-c2ccccc2CC=C)c1,0.35805994
*Nc1ccc(-c2ccc(N*)cc2-c2ccccc2CCC)c(-c2ccccc2CCC)c1,0.36819448
*Nc1ccc(-c2ccc(N*)cc2-c2ccccc2CCCC)c(-c2ccccc2CCCC)c1,0.36989526
*Nc1ccc(-c2ccc(N*)cc2-c2ccccc2[C@@H](C)CC)c(-c2ccccc2[C@@H](C)CC)c1,0.37601542
*Nc1ccc(-c2ccc(N*)cc2/C(N)=C/c2ccccc2)c(/C(N)=C/c2ccccc2)c1,0.35009849
*Nc1ccc(-c2ccccc2)c(C2CCCCC2)c1N*,0.37359962
*Nc1ccc(-c2ccccc2-c2ccccc2-c2ccc(N*)cc2)cc1,0.35490612
*Nc1ccc(/C(=C(/c2ccc3ccccc3c2)c2ccc(N*)c(-c3ccc4ccccc4c3)c2-c2ccc3ccccc3c2)c2ccc3ccccc3c2)cc1,0.37988811
*Nc1ccc(/C(C)=C/C(C)(C)c2ccc(N*)cc2)cc1,0.35500937
*Nc1ccc(/C=C/c2ccc(N*)cc2-c2ccccc2)c(-c2ccccc2)c1,0.35626419
*Nc1ccc(/C=C/c2ccc3ccccc3c2/C=C/c2ccc(N*)cc2)cc1,0.35372743
*Nc1ccc(C(=C)C(C)(C)C(C)(C)c2ccc(N*)cc2)cc1,0.34980902
*Nc1ccc(C(=C)CC(C)(C)c2ccc(N*)cc2)cc1,0.34510572
*Nc1ccc(C(=C2CCCCC2)c2ccc(N*)cc2)cc1,0.37173273
*Nc1ccc(C(C)(C)c2cc(C(C)(C)c3ccc(N)cc3)cc(C(C)(C)c3ccc(N*)cc3)c2)cc1,0.36915657
*Nc1ccc(C(C)(C)c2ccc(C(C)(C)c3ccc(N*)cc3)cc2)cc1,0.35733218
*Nc1ccc(C(C)(C)c2ccc(C(C)(C)c3cccc(N*)c3)cc2)cc1,0.35860249
*Nc1ccc(C(C)(C)c2ccc(N*)cc2)cc1,0.34514842
*Nc1ccc(C(C)(C=C)c2ccc(N*)cc2)cc1,0.34310285
*Nc1ccc(C(C)(CC(C)C)c2ccc(N*)cc2)cc1,0.35303022
*Nc1ccc(C(C)(CC)c2ccc(N*)cc2)cc1,0.35054839
*Nc1ccc(C(C)(CC=C)c2ccc(N*)cc2)cc1,0.34950176
*Nc1ccc(C(C)(CCC(C)C)c2ccc(N*)cc2)cc1,0.3593959
*Nc1ccc(C(C)(CCC)c2ccc(N*)cc2)cc1,0.35256265
*Nc1ccc(C(C)(CCCC)c2ccc(N*)cc2)cc1,0.35842613
*Nc1ccc(C(C)(CCCCC)c2ccc(N*)cc2)cc1,0.35858625
*Nc1ccc(C(C)(CCCCCC)c2ccc(N*)cc2)cc1,0.36233629
*Nc1ccc(C(C)(c2ccc(N*)cc2)C(C)C)cc1,0.35465545
*Nc1ccc(C(C)(c2ccc(N*)cc2)[C@H](C)C(=C)C)cc1,0.35652932
*Nc1ccc(C(CC(C)(C)c2ccc(N*)cc2)=C(C)C)cc1,0.35535977
*Nc1ccc(C(CC(C)C)(CC(C)C)c2ccc(N*)cc2)cc1,0.36637357
*Nc1ccc(C(CC)(C/C=C/[C@@H](C)CCC)c2ccc(N*)cc2)cc1,0.36522046
*Nc1ccc(C(CC)(CC)c2ccc(N*)cc2)cc1,0.3582996
*Nc1ccc(C(CC)(CCC(C)C)c2ccc(N*)cc2)cc1,0.36819424
*Nc1ccc(C(CC)(CCC)c2ccc(N*)cc2)cc1,0.35892089
*Nc1ccc(C(CC)(CCCC)c2ccc(N*)cc2)cc1,0.36309206
*Nc1ccc(C(CC)(CCCCC)c2ccc(N*)cc2)cc1,0.36534914
*Nc1ccc(C(CC)(CCCCCC)c2ccc(N*)cc2)cc1,0.36353044
*Nc1ccc(C(CC)(C[C@H](C)CC)c2ccc(N*)cc2)cc1,0.35671255
*Nc1ccc(C(CC)c2ccc(N*)c(Cc3ccccc3)c2Cc2ccccc2)c(Cc2ccccc2)c1Cc1ccccc1,0.36855173
*Nc1ccc(C(CC=C)(CC=C)c2ccc(N*)cc2)cc1,0.34731572
*Nc1ccc(C(CC=C)c2ccc(N*)cc2)cc1,0.34719504
*Nc1ccc(C(CCC)(CCC)c2ccc(N*)cc2)cc1,0.36239191
*Nc1ccc(C(CCCC)(CCCC)c2ccc(N*)cc2)cc1,0.3699291
*Nc1ccc(C(C[C@@H]2CCC[C@@H](C(c3ccc(N)cc3)c3ccc(N*)cc3)C2)c2ccc(N)cc2)cc1,0.36461546
*Nc1ccc(C(c2ccc(N*)c(C(C)(C)C)c2)C(c2ccc(N)c(C(C)(C)C)c2)c2ccc(N)c(C(C)(C)C)c2)cc1C(C)(C)C,0.3977767
*Nc1ccc(C(c2ccc(N*)c(C)c2)C(c2ccc(N)c(C)c2)c2ccc(N)c(C)c2)cc1C,0.38150109
*Nc1ccc(C(c2ccc(N*)cc2)(C(C)C)C(C)C)cc1,0.36562181
*Nc1ccc(C(c2ccc(N*)cc2)(C(F)(F)F)C(F)(F)F)cc1,0.34186008
*Nc1ccc(C(c2ccc(N*)cc2)([C@H](C)CC)[C@H](C)CC)cc1,0.37291553
*Nc1ccc(C(c2ccc(N*)cc2)C(C)(C)C)cc1,0.3599413
*Nc1ccc(C(c2ccc(N*)cc2)C(c2ccc(N)cc2)c2ccc(N)cc2)cc1,0.36559722
*Nc1ccc(C(c2ccc(N*)cc2)C(c2cccc(N)c2)c2cccc(N)c2)cc1,0.35897099
*Nc1ccc(C(c2ccc(N*)cc2)C(c2ccccc2N)(c2ccccc2N)[C@H](c2ccc(N)cc2)c2ccccc2N)cc1,0.36452149
*Nc1ccc(C(c2ccc(NC(=O)c3cc(NC(=O)CCCN4C(=O)c5ccccc5C4=O)cc(C(*)=O)c3)cc2)(C(F)(F)F)C(F)(F)F)cc1,0.33372764
*Nc1ccc(C2(c3ccc(N*)c(-c4ccc(C)cc4)c3-c3ccc(C)cc3)CCCCC2)c(-c2ccc(C)cc2)c1-c1ccc(C)cc1,0.39222234
*Nc1ccc(C2(c3ccc(N*)c(C)c3)c3ccccc3Cc3ccccc32)cc1C,0.37721583
*Nc1ccc(C2(c3ccc(N*)cc3)C=CC=C3C2=Cc2ccccc23)cc1,0.36753725
*Nc1ccc(C2(c3ccc(N*)cc3)CCCC2)cc1,0.34564076
*Nc1ccc(C2(c3ccc(N*)cc3)CCc3ccccc32)cc1,0.35743146
*Nc1ccc(C2(c3ccc(N*)cc3)[C@H]3C[C@@H]4C[C@@H](C[C@H]2C4)C3)cc1,0.36122631
*Nc1ccc(C2(c3ccc(N*)cc3)c3ccccc3Cc3ccccc32)cc1,0.36815633
*Nc1ccc(C2=CC=C(c3ccc(N*)cc3)C2(C)C)cc1,0.35499932
*Nc1ccc(C2=CC[C@](N*)(c3ccccc3-c3ccccc3C)C=C2)cc1,0.35437873
*Nc1ccc(C2=C[C@@H]3CC[C@H]2C=C3c2ccc(N*)cc2)cc1,0.35798661
*Nc1ccc(CC[C@@H](Cc2ccc(N*)cc2)[C@@H](Cc2ccc(N)cc2)c2ccccc2)cc1,0.35442602
*Nc1ccc(CCc2ccc(C3(c4ccc(CCc5ccc(N*)cc5)cc4)CCC(c4ccccc4)CC3)cc2)cc1,0.362202
*Nc1ccc(Cc2cc(Cc3ccc(N)cc3)cc(Cc3ccc(N*)cc3)c2)cc1,0.36046154
*Nc1ccc(Cc2ccc(Cc3ccc(N*)c(C)c3C)cc2)c(C)c1C,0.35499508
*Nc1ccc(Cc2ccc(Cc3ccc(N*)cc3)cc2)cc1,0.34845951
*Nc1ccc(Cc2ccc(N*)c(C)c2)c(CC)c1,0.3496197
*Nc1ccc(Cc2ccc(N*)c(C)c2)cc1C,0.35117058
*Nc1ccc(Cc2ccc(N*)c(C)c2C)c(C)c1,0.35276469
*Nc1ccc(Cc2ccc(N*)c(C)c2C)c(C)c1C,0.35444913
*Nc1ccc(Cc2ccc(N*)c(CC)c2CC)c(CC)c1CC,0.37391243
*Nc1ccc(Cc2ccc(N*)c(CC=C)c2CC=C)c(CC=C)c1CC=C,0.35378076
*Nc1ccc(Cc2ccc(N*)c(Cl)c2)cc1Cl,0.3514118
*Nc1ccc(Cc2ccc(N*)cc2)cc1,0.34181931
*Nc1ccc(Cc2ccc(NC(=O)CCCCCCCC(*)=O)cc2)cc1,0.34905905
*Nc1ccc(Cc2ccc(NC(=O)CCCCCCCCC(*)=O)cc2)cc1,0.35151514
*Nc1ccc(Cc2ccc(NC(=O)NCCCCCCCCCCCCCCCCCCNC(*)=O)cc2)cc1,0.37009241
*Nc1ccc(Cc2ccc(NC(=O)NCCCCCCCCCCCCNC(*)=O)cc2)cc1,0.35680733
*Nc1ccc(Cc2ccc(NC(=O)NCCCCCCCCCCNC(*)=O)cc2)cc1,0.35544532
*Nc1ccc(Cc2ccc(NC(=O)NCCCCCCCCCNC(*)=O)cc2)cc1,0.35319219
*Nc1ccc(Cc2ccc(NC(=O)NCCCCCCCCNC(*)=O)cc2)cc1,0.35239715
*Nc1ccc(Cc2ccc(NC(=O)NCCCCCCNC(*)=O)cc2)cc1,0.34117859
*Nc1ccc(Cc2ccc(NC(=O)Nc3ccccc3CCc3ccc(NC(*)=O)cc3)cc2)cc1,0.35103259
*Nc1ccc(Cc2ccc(NC(=O)c3cc(C(*)=O)cc(N4C(=O)C5C6C=CC(C6)C5C4=O)c3)cc2)cc1,0.35689692
*Nc1ccc(Cc2ccc(NC(=O)c3cc(NC(=O)CCCN4C(=O)c5ccccc5C4=O)cc(C(*)=O)c3)cc2)cc1,0.33667621
*Nc1ccc(Cc2ccc(NC(=O)c3cc(NC(=O)c4ccc(NC(=O)C(CC(C)C)N5C(=O)c6ccccc6C5=O)cc4)cc(C(*)=O)c3)cc2)cc1,0.34741181
*Nc1ccc(Cc2cccc(N*)c2)cc1,0.33815386
*Nc1ccc(N*)c(-c2ccc3ccccc3c2)c1,0.34047659
*Nc1ccc(N*)c(C(c2ccc3ccccc3c2)c2ccc3ccccc3c2)c1,0.36285479
*Nc1ccc(N*)c2c1ccc1ccccc12,0.33816871
*Nc1ccc(NC(=O)Cc2cc(C)c(CC(*)=O)cc2C)cc1,0.3373035
*Nc1ccc(NC(=O)Cc2cc(CC(*)=O)c(C)cc2C)cc1,0.33730529
*Nc1ccc(NC(=O)c2cc(C(*)=O)cc(S(=O)(=O)c3ccccc3)c2)cc1,0.34297424
*Nc1ccc(NC(=O)c2ccc(C(=O)c3ccc(C(*)=O)c(C(=O)O)c3)cc2C(=O)O)cc1,0.31794387
*Nc1ccc(NC(=O)c2ccc(C(=O)c3ccc(C(*)=O)c(C(=O)OCC)c3)cc2C(=O)OCC)cc1,0.34534308
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCCCCCCCC,0.35844067
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCC,0.35882755
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCCCC,0.3595526
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCCCCCC,0.36201115
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCCCCCCCC,0.36659861
*Nc1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(N*)cc4)cc3)cc2)cc1,0.34442362
*Nc1ccc(Oc2cccc(Oc3ccc(N*)cc3)c2)cc1,0.34111608
*Nc1ccc(SSc2ccc(N*)cc2)cc1,0.35710288
*Nc1ccc([C@@H](C)c2ccc(C(C)(C)c3ccc(N*)cc3)cc2)cc1,0.35607415
*Nc1ccc([C@@H](c2ccccc2)[C@H](c2ccccc2)c2ccc(N*)cc2)cc1,0.36377948
*Nc1ccc([C@@H]2CC(C)(C)c3cc(N*)ccc32)cc1,0.36237935
*Nc1ccc([C@@H]2CC(C)(C)c3ccc(N*)cc32)cc1,0.3624745
*Nc1ccc([C@H](CC)c2ccc([C@@H](CC)c3ccc(N*)cc3)cc2)cc1,0.3623964
*Nc1ccc([C@H](CCCC)c2ccc(C3(c4ccc([C@@H](CCCC)c5ccc(N*)cc5C)cc4)CCC(C)CC3)cc2)c(C)c1,0.38081893
*Nc1ccc([C@H](CCCCCC)c2ccc(C3(c4ccc([C@@H](CCCCCC)c5ccc(N*)cc5)cc4)CCC(CCCCC)CC3)cc2)cc1,0.37108303
*Nc1ccc([C@H](c2ccc(N*)c(C)c2)C(c2ccc(N)c(C)c2)c2ccc(N)c(C)c2)cc1,0.37242979
*Nc1ccc([C@H](c2cccc(N*)c2)C(c2cccc(N)c2)c2cccc(N)c2)cc1,0.35918801
*Nc1ccc([C@H](c2ccccc2N*)C(c2ccc(N)cc2)c2ccc(N)cc2)cc1,0.37002899
*Nc1ccc([C@H]2CC[C@H](c3ccc(N*)cc3)CC2)cc1,0.35770572
*Nc1ccc2c(N*)cc3ccccc3c2c1,0.334748
*Nc1ccc2c(c1)-c1ccc3c4ccc5c6c(ccc(c7ccc(c1c73)C2)c64)-c1cc(N*)ccc1C5,0.34349672
*Nc1ccc2c(c1)C(=O)c1ccc(N*)cc1C2=O,0.33028392
*Nc1ccc2c(c1)[C@]1(C)CC[C@@]2(C)c2cc(N*)ccc21,0.36507821
*Nc1ccc2c(c1)c(C)c(C)c1cc(N*)ccc12,0.33627798
*Nc1ccc2c(c1)c(N*)cc1ccccc12,0.33211721
*Nc1ccc2c(c1)cc(N*)c1ccccc12,0.33817655
*Nc1ccc2c(c1N*)CCc1ccccc1-2,0.341292
*Nc1ccc2c(ccc3c(N*)cccc32)c1,0.3358041
*Nc1ccc2c(ccc3cc(N*)ccc32)c1,0.3363309
*Nc1ccc2c(ccc3ccc(N*)cc32)c1,0.33807688
*Nc1ccc2c(ccc3ccc4ccc5ccc6cc(N*)ccc6c5c4c32)c1,0.3586391
*Nc1ccc2c(ccc3cccc(N*)c32)c1,0.3331587
*Nc1ccc2c(ccc3ccccc32)c1N*,0.3361781
*Nc1ccc2c3cc(-c4ccc(-c5ccccc5)cc4)c(-c4ccc(-c5ccccc5)cc4)c4c(N*)c(-c5ccc(-c6ccccc6)cc5)c(-c5ccc(-c6ccccc6)cc5)c(c5cccc1c25)c43,0.3619219
*Nc1ccc2cc(N*)c3ccccc3c2c1,0.33919037
*Nc1ccc2ccc3ccc(N*)cc3c2c1,0.33497194
*Nc1ccc2ccc3cccc(N*)c3c2c1,0.32965144
*Nc1ccc2ccc3ccccc3c2c1N*,0.33674811
*Nc1cccc(-c2cc(N*)ccc2-c2ccccc2-c2ccccc2)c1,0.35340744
*Nc1cccc(-c2ccccc2-c2cc(N*)ccc2-c2ccccc2)c1,0.35219454
*Nc1cccc(-c2ccccc2-c2ccccc2-c2cccc(N*)c2)c1,0.35392163
*Nc1cccc(-c2ccccc2-c2ccccc2-c2ccccc2)c1N*,0.35659206
*Nc1cccc(C(c2cccc(N*)c2)(C(F)(F)F)C(F)(F)F)c1,0.34059386
*Nc1cccc(Cc2cccc(N*)c2)c1,0.33697796
*Nc1cccc(NC(=C(C#N)C#N)c2cccc(C(*)=C(C#N)C#N)c2)c1,0.41003738
*Nc1cccc(NC(=O)Cc2cc(C)c(CC(*)=O)cc2C)c1,0.3367393
*Nc1cccc(NC(=O)Cc2cc(CC(*)=O)c(C)cc2C)c1,0.33294467
*Nc1cccc(NC(=O)c2cc(C(*)=O)cc(S(=O)(=O)c3ccccc3)c2)c1,0.34798034
*Nc1cccc(NC(=O)c2cc(NC(=O)C(CCSC)N3C(=O)c4ccccc4C3=O)cc(C(*)=O)c2)c1,0.33770588
*Nc1cccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4cccc(N*)c4)cc3)cc2)c1,0.34393632
*Nc1cccc(Oc2cccc(Oc3cccc(N*)c3)c2)c1,0.33787009
*Nc1cccc2c(NC(=O)c3ccc(C(*)=O)cc3)cccc12,0.34599686
*Nc1cccc2c(NC(=O)c3ccc(NC(=O)c4ccc([Si](C)(C)c5ccc(C(=O)Nc6ccc(C(*)=O)cc6)cc5)cc4)cc3)cccc12,0.3612299
*Nc1cccc2c1C(=O)c1cccc(N[Se]*)c1C2=O,0.3481788
*Nc1cccc2c1C1=C(C[C@](N*)(c3ccccc3-c3ccccc3)C=C1)C2(c1ccccc1)c1ccccc1,0.37540911
*Nc1cccc2c1c(N*)cc1ccccc12,0.33290552
*Nc1cccc2c1ccc1c(N*)cccc12,0.33410164
*Nc1cccc2c1ccc1cccc(N*)c12,0.33883964
*Nc1cccc2ccc3cccc(N*)c3c12,0.34248529
*Nc1ccccc1-c1cccc(-c2ccccc2-c2cccc(-c3ccccc3)c2)c1N*,0.35563081
*Nc1ccccc1-c1ccccc1-c1ccccc1-c1ccccc1N*,0.35892837
*Nc1ccccc1/C=C/c1ccc2ccccc2c1/C=C/c1ccccc1N*,0.35648517
*Nc1ccccc1CCc1ccccc1NC(=O)Nc1ccc(CCc2ccc(NC(*)=O)cc2)cc1,0.35411182
*Nc1ccccc1SSc1ccccc1N*,0.35017586
*Nc1nc(=O)n(C)c(N*)c1N,0.31039992
*OC(=O)C(Cc1ccccc1)NC(=O)CCCCCCC(=O)NC(Cc1ccccc1)C(=O)OC1COC2C(*)COC12,0.34161233
*OC(=O)C(Cc1ccccc1)NC(=O)CCCCCCCCC(=O)NC(Cc1ccccc1)C(=O)OC1COC2C(*)COC12,0.34522868
*OC(=O)C(Cc1ccccc1)NC(=O)CCCCCCCCCCC(=O)NC(Cc1ccccc1)C(=O)OC1COC2C(*)COC12,0.35207165
*OC(=O)Oc1ccc(C(=O)Oc2ccc(OC(=O)OC3COC4C(*)COC34)cc2)cc1,0.33078823
*OC(=O)c1ccc(C(=O)OC2COC3C(*)COC23)cc1,0.33842265
*OC(=O)c1ccc(Cc2ccc(C(*)=O)cc2)cc1,0.34410161
*OC(COC(=O)c1ccc2cc(C(*)=O)ccc2c1)COc1ccc(N=Nc2ccc(C#N)cc2)cc1,0.35895878
*OC1C(C)(C)C(OC(=O)C2CCC(C(*)=O)CC2)C1(C)C,0.35756125
*OS(=O)(=O)c1cccc(S(=O)(=O)Oc2ccc(C(C)(C)c3ccc(*)cc3)cc2)c1,0.36372411
*OS(=O)(=O)c1cccc(S(=O)(=O)Oc2ccc(C(C)(CC)c3ccc(*)cc3)cc2)c1,0.36537348
*OS(=O)(=O)c1cccc(S(=O)(=O)Oc2ccc(C3(c4ccc(*)cc4)CCCCC3)cc2)c1,0.36029922
*O[Si](*)(C)CCCOc1ccc(C(=O)Oc2ccc(C(=O)Oc3ccc(OC)cc3)cc2)cc1,0.34401737
*O[Si](C)(C)CCCC(=O)Oc1ccc(C=Nc2ccc(Cc3ccc(N=Cc4ccc(OC(=O)CCC[Si](*)(C)C)cc4)cc3)cc2)cc1,0.38299785
*O[Si](C)(C)CCCN=Cc1cc(Cc2ccc(O)c(C=NCCC[Si](*)(C)C)c2)ccc1O,0.39347596
*O[Si](C)(C)OC(CCl)COc1ccc(C(C)(C)c2ccc(OCC(*)CCl)cc2)cc1,0.3750933
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)Oc1c(*)c2ccccc2c2ccccc12,0.41772931
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)Oc1c(*)c2ccccc2c2ccccc12,0.41127913
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)Oc1c(*)c2ccccc2c2ccccc12,0.40298758
*O[Si](C)(C)Oc1ccc(C(C)(C)c2ccc(*)cc2)cc1,0.40918281
*O[Si](C)(C)c1cccc2c1ccc1c([Si](*)(C)C)cccc12,0.40173269
*O[Si](C)(Oc1ccc(*)cc1)c1ccccc1,0.40342541
*Oc1c(-c2ccccc2)cc(*)cc1-c1ccccc1-c1ccccc1,0.4050477
*Oc1c(-c2ccccc2)cc(Cc2cc(-c3ccccc3)c(OC(=O)CCCCC(*)=O)c(-c3ccccc3)c2)cc1-c1ccccc1,0.37195156
*Oc1c(-c2ccccc2)cc(Cc2cc(-c3ccccc3)c(OC(=O)CCCCCC(*)=O)c(-c3ccccc3)c2)cc1-c1ccccc1,0.37107521
*Oc1c(-c2ccccc2)cc(Cc2cc(-c3ccccc3)c(OC(=O)CCCCCCCCC(*)=O)c(-c3ccccc3)c2)cc1-c1ccccc1,0.3718338
*Oc1c(Br)cc(C(C)(C)c2cc(Br)c(OC(*)=O)c(Br)c2)cc1Br,0.4290399
*Oc1c(Br)cc(C(C)(C)c2cc(Br)c(OC(=O)c3ccc(OCCCCCCCCCCOc4ccc(C(*)=O)cc4)cc3)c(Br)c2)cc1Br,0.39126002
*Oc1c(Br)cc(C(c2cc(Br)c(OC(*)=O)c(Br)c2)(C(F)(F)F)C(F)(F)F)cc1Br,0.41928552
*Oc1c(C(C)C)cc(C(=O)c2cccc(C(=O)c3ccc(*)cc3)c2)cc1C(C)C,0.40376992
*Oc1c(C)cc(C(C)(C)c2cc(C)c(OC(*)=O)c(C)c2)cc1C,0.39805007
*Oc1c(C)cc(C(C)(C)c2cc(C)c(OC(*)=O)c(Cl)c2)cc1Cl,0.3979854
*Oc1c(C)cc(C(C)(C)c2cc(C)c(OC(=O)CCCCC(*)=O)c(C)c2)cc1C,0.37611741
*Oc1c(C)cc(C(C)(C)c2cc(C)c(OC(=O)CCCCCC(*)=O)c(C)c2)cc1C,0.37560672
*Oc1c(C)cc(C(C)(C)c2cc(C)c(OC(=O)CCCCCCCCC(*)=O)c(C)c2)cc1C,0.38134315
*Oc1c(C)cc(C(c2cc(C)c(OC(*)=O)c(C)c2)(C(F)(F)F)C(F)(F)F)cc1C,0.39598526
*Oc1c(Cl)cc(C(C)(C)c2cc(Cl)c(OC(*)=O)c(Cl)c2)cc1Cl,0.39482335
*Oc1c(Cl)cc(C(C)(C)c2cc(Cl)c(OC(=O)CCCCC(*)=O)c(Cl)c2)cc1Cl,0.37453429
*Oc1c(Cl)cc(C(C)(C)c2cc(Cl)c(OC(=O)c3ccc(OCCCCCCCCCCOc4ccc(C(*)=O)cc4)cc3)c(Cl)c2)cc1Cl,0.37760348
*Oc1c(Cl)cc(C(c2cc(Cl)c(OC(*)=O)c(Cl)c2)C(Cl)(Cl)Cl)cc1Cl,0.39461971
*Oc1c(Cl)cc(C2(c3cc(Cl)c(OC(*)=O)c(Cl)c3)CCCCC2)cc1Cl,0.39600978
*Oc1c(F)c(C#N)c(Oc2ccc(C(C)(C)c3cccc(C(C)(C)c4ccc(*)cc4)c3)cc2)c(F)c1C#N,0.38046147
*Oc1cc(Br)c(C(C)(C)c2c(Br)cc(OC(*)=O)cc2Br)c(Br)c1,0.3939151
*Oc1cc(Cl)c(C(C)(C)c2c(Cl)cc(OC(*)=O)cc2Cl)c(Cl)c1,0.37057341
*Oc1cc(OC(=O)c2ccc(C)cc2)c(OC(=O)CCCCCCCCCCCCCCC(*)=O)cc1OC(=O)c1ccc(C)cc1,0.36849104
*Oc1cc(OC(=O)c2ccc(OC)cc2)c(OC(=O)CCCC(*)=O)cc1OC(=O)c1ccc(OC)cc1,0.33574151
*Oc1cc(OC(=O)c2ccc(OC)cc2)c(OC(=O)CCCCCCCCCCCCCCC(*)=O)cc1OC(=O)c1ccc(OC)cc1,0.35345176
*Oc1cc(OC(=O)c2ccc(OCC(C)CC)cc2)c(OC(=O)CCCC(*)=O)cc1OC(=O)c1ccc(OCC(C)CC)cc1,0.35730159
*Oc1cc(OC(=O)c2ccc(OCC)cc2)c(OC(=O)CCCC(*)=O)cc1OC(=O)c1ccc(OCC)cc1,0.33981747
*Oc1cc(OC(=O)c2ccc(OCC)cc2)c(OC(=O)CCCCCCCCCCCCCCC(*)=O)cc1OC(=O)c1ccc(OCC)cc1,0.36137547
*Oc1cc(OC(=O)c2ccc(OCCC)cc2)c(OC(=O)CCCC(*)=O)cc1OC(=O)c1ccc(OCCC)cc1,0.34814804
*Oc1cc(OC(=O)c2ccc(OCCC)cc2)c(OC(=O)CCCCCCCCCCCCCCC(*)=O)cc1OC(=O)c1ccc(OCCC)cc1,0.36365256
*Oc1cc(OC(=O)c2ccc(OCCCC)cc2)c(OC(=O)C(C)(C)CCC(*)=O)cc1OC(=O)c1ccc(OCCCC)cc1,0.35709055
*Oc1cc(OC(=O)c2ccc(OCCCC)cc2)c(OC(=O)C(C)CCC(*)=O)cc1OC(=O)c1ccc(OCCCC)cc1,0.35328505
*Oc1cc(OC(=O)c2ccc(OCCCC)cc2)c(OC(=O)CCCC(*)=O)cc1OC(=O)c1ccc(OCCCC)cc1,0.35109057
*Oc1cc(OC(=O)c2ccc(OCCCC)cc2)c(OC(=O)CCCCCC(*)=O)cc1OC(=O)c1ccc(OCCCC)cc1,0.35336805
*Oc1cc(OC(=O)c2ccc(OCCCC)cc2)c(OC(=O)CCCCCCCC(*)=O)cc1OC(=O)c1ccc(OCCCC)cc1,0.35540563
*Oc1cc(OC(=O)c2ccc(OCCCCC)cc2)c(OC(=O)CCCC(*)=O)cc1OC(=O)c1ccc(OCCCCC)cc1,0.35445196
*Oc1cc(OC(=O)c2ccc(OCCCCCCCCCCCC)cc2)c(OC(=O)CCCC(*)=O)cc1OC(=O)c1ccc(OCCCCCCCCCCCC)cc1,0.37044637
*Oc1cc(OC(=O)c2ccc(Oc3ccc(C4(c5ccc(Oc6ccc(C(*)=O)cc6)cc5)CCC(C(C)(C)C)CC4)cc3)cc2)ccc1C12CC3CC(CC(C3)C1)C2,0.38278757
*Oc1ccc(-c2ccc(OC(=O)OC3C(C)(C)C(OC(*)=O)C3(C)C)cc2)cc1,0.35906343
*Oc1ccc(-c2ccc(OC3(F)C(*)(F)C(F)(F)C3(F)F)cc2)cc1,0.34511864
*Oc1ccc(-c2ccc(Oc3cccc(*)n3)cc2)cc1,0.34884407
*Oc1ccc(C(=O)NNC(=O)c2ccc(*)cc2)cc1,0.34717894
*Oc1ccc(C(=O)Nc2cc(NC(=O)c3ccc(*)cc3)cc(-c3nc4ccccc4[nH]3)c2)cc1,0.36445259
*Oc1ccc(C(=O)Nc2ccc(S(=O)(=O)c3ccc(NC(=O)c4ccc(Oc5ccc(C(=O)c6ccc(S(=O)(=O)c7ccc(C(=O)c8ccc(*)cc8)cc7)cc6)cc5)cc4)cc3)cc2)cc1,0.36195969
*Oc1ccc(C(=O)Nc2ccc(S(=O)(=O)c3ccc(NC(=O)c4ccc(Oc5nc(*)nc(Sc6ccccc6)n5)cc4)cc3)cc2)cc1,0.35315957
*Oc1ccc(C(=O)OCC(C)(C)COC(=O)c2ccc(*)cc2)cc1,0.35526052
*Oc1ccc(C(=O)OCCCCCOC(=O)c2ccc(*)cc2)cc1,0.34696395
*Oc1ccc(C(=O)OCCCOC(=O)c2ccc(*)cc2)cc1,0.34520298
*Oc1ccc(C(=O)c2ccc(C(=O)c3ccc(Oc4cccc(Cc5cccc(*)c5)c4)cc3)cc2)cc1,0.36639351
*Oc1ccc(C(=O)c2cccc(C(=O)c3ccc(*)c(C(C)C)c3)c2)cc1C,0.39318082
*Oc1ccc(C(=O)c2cccc(C(=O)c3ccc(*)c(C(C)C)c3)c2)cc1CC,0.39239857
*Oc1ccc(C(=O)c2cccc(C(=O)c3ccc(*)c(CC)c3)c2)cc1CC,0.38536105
*Oc1ccc(C(=O)c2cccc(C(=O)c3ccc(*)cc3)c2)cc1,0.36581437
*Oc1ccc(C(=O)c2cccc(C(=O)c3ccc(Oc4ccc(Cc5ccc(*)cc5)cc4)cc3)c2)cc1,0.37058113
*Oc1ccc(C(C)(C)c2cc(Cl)c(OC(*)=O)c(Cl)c2)cc1,0.37520677
*Oc1ccc(C(C)(C)c2ccc(C(C)(C)c3ccc(OC(*)=O)cc3)cc2)cc1,0.36640421
*Oc1ccc(C(C)(C)c2ccc(OC(*)(Oc3ccccc3)Oc3ccccc3)cc2)cc1,0.37316612
*Oc1ccc(C(C)(C)c2ccc(OC(*)=O)c(C(C)C)c2)cc1C(C)C,0.40309692
*Oc1ccc(C(C)(C)c2ccc(OC(*)=O)c(C)c2)cc1C,0.37267711
*Oc1ccc(C(C)(C)c2ccc(OC(*)=O)c(CC)c2)cc1CC,0.38097605
*Oc1ccc(C(C)(C)c2ccc(OC(*)=O)c(Cl)c2)cc1,0.3646685
*Oc1ccc(C(C)(C)c2ccc(OC(*)=O)c(Cl)c2)cc1C,0.37412442
*Oc1ccc(C(C)(C)c2ccc(OC(*)=O)c(Cl)c2)cc1Cl,0.37372084
*Oc1ccc(C(C)(C)c2ccc(OC(*)=S)cc2)cc1,0.35877547
*Oc1ccc(C(C)(C)c2ccc(OC(=O)CC(C)CCC(*)=O)cc2)cc1,0.35041329
*Oc1ccc(C(C)(C)c2ccc(OC(=O)CCCCC(*)=O)c(C)c2)cc1C,0.35992444
*Oc1ccc(C(C)(C)c2ccc(OC(=O)CCCCC(*)=O)cc2)cc1,0.34517826
*Oc1ccc(C(C)(C)c2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)cc1,0.35872609
*Oc1ccc(C(C)(C)c2ccc(OC(=O)OCCCCCOC(*)=O)cc2)cc1,0.34414164
*Oc1ccc(C(C)(C)c2ccc(OC(=O)OCCCCOC(*)=O)cc2)cc1,0.34176051
*Oc1ccc(C(C)(C)c2ccc(OC(=O)OCCCOC(*)=O)cc2)cc1,0.33934743
*Oc1ccc(C(C)(C)c2ccc(OC(=O)OCCN(CCOC(*)=O)c3ccc(OC)cc3)cc2)cc1,0.34363634
*Oc1ccc(C(C)(C)c2ccc(OC(=O)OCCN(CCOC(*)=O)c3ccccc3)cc2)cc1,0.34756362
*Oc1ccc(C(C)(C)c2ccc(OC(=O)SCCCCCCSC(*)=O)cc2)cc1,0.3614361
*Oc1ccc(C(C)(C)c2ccc(OC(=O)SCCCSC(*)=O)cc2)cc1,0.35558207
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(OCCCCCCCCCCOc4ccc(C(*)=O)cc4)cc3)cc2)cc1,0.35755108
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)C(=O)c4ccc(*)cc4)cc3)cc2)cc1,0.37690328
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)Nc4ccc(NC(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1,0.35980678
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4c(C(=O)c5ccc(*)cc5)c(-c5ccccc5)c(-c5ccc6ccccc6c5)c(-c5ccc6ccccc6c5)c4-c4ccccc4)cc3)cc2)cc1,0.39617794
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4cccc(C(=O)c5ccc(*)cc5)c4)cc3)cc2)cc1,0.37447472
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4ccccc4-c4ccccc4C(=O)c4ccc(*)cc4)cc3)cc2)cc1,0.37964806
*Oc1ccc(C(C)(CC(C)C)c2ccc(OC(*)=S)cc2)cc1,0.36650609
*Oc1ccc(C(C)(CC)c2ccc(OC(*)=O)c(C)c2)cc1C,0.37939809
*Oc1ccc(C(C)(CC)c2ccc(OC(*)=O)c(Cl)c2)cc1Cl,0.37781492
*Oc1ccc(C(C)(CC)c2ccc(OC(*)=O)cc2)cc1,0.3639544
*Oc1ccc(C(C)(CC)c2ccc(OC(*)=S)cc2)cc1,0.36451292
*Oc1ccc(C(C)(CCC#N)c2ccc(OC(*)=O)c(C)c2)cc1C,0.38107835
*Oc1ccc(C(C)(CCC#N)c2ccc(OC(*)=O)cc2)cc1,0.36182275
*Oc1ccc(C(C)(CCC)c2ccc(OC(*)=O)cc2)cc1,0.37045396
*Oc1ccc(C(C)(c2ccccc2)c2ccc(OC(*)=O)cc2)cc1,0.36147298
*Oc1ccc(C(C)(c2ccccc2)c2ccc(OC(=O)c3ccccc3-c3ccccc3C(*)=O)cc2)cc1,0.36060662
*Oc1ccc(C(C)c2ccc(OC(*)=O)cc2)cc1,0.35421995
*Oc1ccc(C(CC)(CC)c2ccc(OC(*)=S)cc2)cc1,0.36838966
*Oc1ccc(C(CC)c2ccc(OC(*)=O)cc2)cc1,0.36281879
*Oc1ccc(C(CC)c2ccc(OC(*)=S)cc2)cc1,0.36420995
*Oc1ccc(C(CCC)(CCC)c2ccc(OC(*)=O)c(C)c2)cc1C,0.39582423
*Oc1ccc(C(CCC)(CCC)c2ccc(OC(*)=O)cc2)cc1,0.38249055
*Oc1ccc(C(CCC)c2ccc(OC(*)=O)cc2)cc1,0.36627071
*Oc1ccc(C(CCC)c2ccc(OC(*)=S)cc2)cc1,0.3689351
*Oc1ccc(C(CCCC)(CCCC)c2ccc(OC(*)=O)cc2)cc1,0.38524755
*Oc1ccc(C(c2ccc(OC(*)=O)cc2)(C(F)(F)Cl)C(F)(F)Cl)cc1,0.36111109
*Oc1ccc(C(c2ccc(OC(*)=O)cc2)(C(F)(F)F)C(F)(F)F)cc1,0.3466164
*Oc1ccc(C(c2ccc(OC(*)=O)cc2)C(C)C)cc1,0.36947312
*Oc1ccc(C(c2ccc(OC(*)=O)cc2)C(Cl)(Cl)Cl)cc1,0.36652065
*Oc1ccc(C(c2ccc(OC(*)=O)cc2)C(Cl)Cl)cc1,0.36363286
*Oc1ccc(C(c2ccc(OC(*)=S)cc2)C(C)C)cc1,0.37198799
*Oc1ccc(C(c2ccc(OC(*)=S)cc2)C(CC)CC)cc1,0.37352636
*Oc1ccc(C(c2ccc(OC(=O)c3ccc(OCCCCCCCCCCOc4ccc(C(*)=O)cc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1,0.35492275
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)C(=O)c4ccc(*)cc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1,0.37168595
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)Nc4ccc(NC(=O)c5ccc(*)cc5)cc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1,0.35746728
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)c4c(C(=O)c5ccc(*)cc5)c(-c5ccccc5)c(-c5ccc6ccccc6c5)c(-c5ccc6ccccc6c5)c4-c4ccccc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1,0.39580075
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)c4cc(C(=O)c5ccc(*)cc5)cc(C(C)(C)C)c4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1,0.38577086
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)c4cccc(C(=O)c5ccc(*)cc5)c4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1,0.37070417
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)c4ccccc4-c4ccccc4C(=O)c4ccc(*)cc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1,0.37602202
*Oc1ccc(C(c2ccccc2)(c2ccccc2)c2ccc(OC(=O)OC3C(C)(C)C(OC(*)=O)C3(C)C)cc2)cc1,0.37906621
*Oc1ccc(C2(c3ccc(OC(*)=O)c(C)c3)CCCCC2)cc1C,0.38771377
*Oc1ccc(C2(c3ccc(OC(*)=O)c(Cl)c3)CCCCC2)cc1Cl,0.37361861
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)CCCC2)cc1,0.35187363
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)CCCCC2)cc1,0.35918735
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)CCc3ccccc32)cc1,0.36699475
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)c3ccccc3-c3ccccc32)cc1,0.38475968
*Oc1ccc(C2(c3ccc(OC(*)=S)cc3)CCCC2)cc1,0.35616229
*Oc1ccc(C2(c3ccc(OC(=O)CCCCCCCCC(*)=O)cc3)c3ccccc3Cc3ccccc32)cc1,0.36332436
*Oc1ccc(C2(c3ccc(OC(=O)OC4C(C)(C)C(OC(*)=O)C4(C)C)cc3)CC3CCC2C3)cc1,0.3705045
*Oc1ccc(C2(c3ccc(OC(=O)c4ccc(C(*)=O)cc4)cc3)c3ccccc3C(=O)c3ccccc32)cc1,0.36880796
*Oc1ccc(C2(c3ccc(OC(=O)c4ccc(Oc5ccc(C6(c7ccc(Oc8ccc(C(*)=O)cc8)cc7)CCC(C(C)(C)C)CC6)cc5)cc4)cc3)CC3CC2C2CCCC32)cc1,0.37986369
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5c(C(=O)c6ccc(*)cc6)c(-c6ccccc6)c(-c6ccc7ccccc7c6)c(-c6ccc7ccccc7c6)c5-c5ccccc5)cc4)cc3)c3ccccc3-c3ccccc32)cc1,0.40813978
*Oc1ccc(C2(c3ccc(Oc4ccc5c(=O)n6c7cc(Oc8ccc9c(c8)nc8c%10ccc(*)c%11cccc(c(=O)n98)c%11%10)ccc7nc6c6cccc4c56)cc3)c3ccccc3-c3ccccc32)cc1,0.396728
*Oc1ccc(C=C2CCCC(=Cc3ccc(OC(=O)CCCCCCCCC(*)=O)c(OC)c3)C2=O)cc1OC,0.35776264
*Oc1ccc(C=NN=Cc2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)cc2)cc1,0.3809024
*Oc1ccc(C=NN=Cc2ccc(Oc3ccc(C(c4ccc(*)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc2)cc1,0.37282962
*Oc1ccc(CCNC(=O)c2cccc(C(=O)NCCc3ccc(OC(=O)c4cccc(C(*)=O)c4)cc3)c2)cc1,0.33853882
*Oc1ccc(CNC(=O)CCCCC(=O)NCc2ccc(OC3COC4C(*)COC34)cc2)cc1,0.33099906
*Oc1ccc(Cc2ccc(Cc3ccc(OC(*)=O)cc3)cc2)cc1,0.36126118
*Oc1ccc(Cc2ccc(OC(*)=O)c(C)c2)cc1C,0.36953235
*Oc1ccc(Cc2ccc(OC(*)=O)cc2)cc1,0.34889783
*Oc1ccc(Cc2ccc(OC(*)=S)cc2)cc1,0.35541969
*Oc1ccc(Cc2ccc(OC(=O)CCCCC(*)=O)cc2)cc1,0.34469429
*Oc1ccc(NC(=O)CCCCCCC(=O)Nc2ccc(OC3COC4C(*)COC34)cc2)cc1,0.33802231
*Oc1ccc(NC(=O)NC2CC(C)(C)CC(C)(CNC(=O)Nc3ccc(*)cc3)C2)cc1,0.34918835
*Oc1ccc(NC(=O)c2cc(C(=O)Nc3ccc(Oc4ccc(C(C)(C)c5ccc(*)cc5)cc4)cc3)cc(C(C)(C)C)c2)cc1,0.36926959
*Oc1ccc(NC(=O)c2cc(C(=O)Nc3ccc(Oc4ccc(C(c5ccc(*)cc5)(C(F)(F)F)C(F)(F)F)cc4)cc3)cc(C(C)(C)C)c2)cc1,0.36861613
*Oc1ccc(NC(=O)c2cc(C(=O)Nc3ccc(Oc4ccc(C(c5ccc(*)cc5)(C(F)(F)F)C(F)(F)F)cc4)cc3)cc([N+](=O)[O-])c2)cc1,0.3558116
*Oc1ccc(NC(=O)c2cc(NC(=O)CCCCCCCCCCN3C(=O)c4ccccc4C3=O)cc(C(=O)Nc3ccc(*)cc3)c2)cc1,0.34139919
*Oc1ccc(NC(=O)c2cc(NC(=O)c3ccc(OC(C)=O)cc3)cc(C(=O)Nc3ccc(*)cc3)c2)cc1,0.33796697
*Oc1ccc(NC(=O)c2cc(NC(=O)c3ccccc3)cc(C(=O)Nc3ccc(*)cc3)c2)cc1,0.34264944
*Oc1ccc(NC(=O)c2ccc(NC(=O)c3ccc([Si](C)(C)c4ccc(C(=O)Nc5ccc(C(=O)Nc6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1,0.35582732
*Oc1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(Oc4ccc(C(c5ccc(*)cc5)(C(F)(F)F)C(F)(F)F)cc4)cc3)c2)cc1,0.35683545
*Oc1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(Oc4ccc(C5(c6ccc(*)cc6)C6CC7CC(C6)CC5C7)cc4)cc3)c2)cc1,0.36643837
*Oc1ccc(OC(=O)c2cc(OCCCc3ccccc3)c(C(*)=O)cc2OCCCc2ccccc2)cc1C,0.35359821
*Oc1ccc(OC(=O)c2ccc(-c3ccc(C(*)=O)cc3)cc2-c2ccccc2)cc1,0.35277091
*Oc1ccc(OC(=O)c2ccc(-c3ccc(C(*)=O)cc3)cc2-c2ccccc2)cc1C,0.36137424
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2)cc1C1CCCCCCC1,0.36680265
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Sc2ccc(C)cc2)cc1,0.36164456
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Sc2ccc3ccccc3c2)cc1,0.35895279
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Sc2ccccc2)cc1,0.35449521
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Sc2ccccc2)cc1C,0.36029743
*Oc1ccc(OC(=O)c2ccc(OCCCCCCCCCCCCOc3ccc(C(*)=O)cc3)cc2)cc1C=Cc1ccncc1,0.36385478
*Oc1ccc(OC(=O)c2ccc([Si](C)(C)c3ccc(C(*)=O)cc3)cc2)cc1,0.36637923
*Oc1ccc(OC(=O)c2cccc(Oc3cccc(C(*)=O)c3)c2)cc1,0.34914243
*Oc1ccc(Oc2ccc(C(=O)c3cccc(-c4cccc(C(=O)c5ccc(*)cc5)c4)c3)cc2)cc1,0.36870915
*Oc1ccc(Oc2ccc(C(=O)c3cccc(NC(=O)c4ccc(C(=O)Nc5cccc(C(=O)c6ccc(*)cc6)c5)cc4)c3)cc2)cc1,0.35637997
*Oc1ccc(Oc2ccc(NC(=C(C#N)C#N)c3cccc(C(Nc4ccc(*)cc4)=C(C#N)C#N)c3)cc2)cc1,0.39273786
*Oc1ccc(Oc2ccc(OC(=O)NC(=O)c3cc(C(=O)NC(*)=O)cc(C(C)(C)C)c3)cc2)cc1,0.33792975
*Oc1ccc(Oc2ccc(P(C)(=O)c3ccc(*)cc3)cc2)cc1,0.37537227
*Oc1ccc(Oc2ccc(S(=O)(=O)c3ccc(-c4ccc(-c5ccc(S(=O)(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1,0.3744368
*Oc1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1,0.37202073
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)cc1,0.37219019
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(OCCCCOc5ccc(C(C)(C)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1,0.36776278
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(*)c(C)c5)CCCCC4)cc3C)cc2)cc1,0.38723397
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(*)cc5)CC5CCC4C5)cc3)cc2)cc1,0.38350802
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(*)cc5)CCCCC4)cc3)cc2)cc1,0.37562068
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(*)cc5)CCCCCC4)cc3)cc2)cc1,0.37649846
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C=C4CCCCC(=Cc5ccc(*)cc5)C4=O)cc3)cc2)cc1,0.3771408
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(Sc4ccc(*)cc4)cc3)cc2)cc1,0.37748733
*Oc1ccc(SSc2ccc(*)cc2)cc1,0.3974058
*Oc1ccc2ccc(Oc3ccc(C(=O)Nc4ccc(C(C)(C)c5ccc(C(C)(C)c6ccc(NC(=O)c7ccc(*)cc7)cc6)cc5)cc4)cc3)cc2c1,0.36959735
*Oc1cccc(C(=O)Nc2ccc(-c3ccc(NC(=O)c4cccc(Oc5nc(*)nc(Sc6ccccc6)n5)c4)cc3)cc2)c1,0.35084083
*Oc1cccc(C(=O)OCC(F)(F)C(F)(F)C(F)(F)COC(=O)c2cccc(*)c2)c1,0.33298625
*Oc1cccc(NC(=O)c2cc(NC(=O)c3ccc(OC(C)=O)cc3)cc(C(=O)Nc3ccc(*)cc3)c2)c1,0.3340158
*Oc1cccc(NC(=O)c2ccc(C(=O)c3cccc(C(=O)Nc4ccc(*)cc4)c3)cc2)c1,0.35065699
*Oc1cccc(NC(=O)c2ccc(P(=O)(c3ccccc3)c3ccc(C(=O)Nc4cccc(Oc5ccc(P(=O)(c6ccccc6)c6ccc(*)cc6)cc5)c4)cc3)cc2)c1,0.36861977
*Oc1cccc(OC(=O)c2ccc([Si](C)(C)c3ccc(C(*)=O)cc3)cc2)c1,0.3731365
*Oc1cccc(Oc2ccc(NC(=O)c3ccc(Oc4cccc(Oc5ccc(C(=O)Nc6ccc(*)cc6)cc5)c4)cc3)cc2)c1,0.3505917
*Oc1cccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)c1,0.36939797
*Oc1cccc(Oc2ccc3c(=O)n4c5cc(-c6ccc7c(c6)nc6c8ccc(*)c9cccc(c(=O)n76)c98)ccc5nc4c4cccc2c34)c1,0.3689235
*Oc1cccc2ccc(Oc3ccc(NC(=O)c4ccc(Oc5cccc(Oc6ccc(C(=O)Nc7ccc(*)cc7)cc6)c5)cc4)cc3)cc12,0.35508677
*Oc1cccc2ccc(Oc3ccc(NC(=O)c4ccc(S(=O)(=O)c5ccc(C(=O)Nc6ccc(*)cc6)cc5)cc4)cc3)cc12,0.35780365
*Sc1ccc(Cc2ccc(SC(*)=O)cc2)cc1,0.37184413
*c1c(C)cc(-c2cc(C)c(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C)c2)cc1C,0.41438034
*c1c(C)cc(C(C)(C)c2cc(C)c(S(*)(=O)=O)c(C)c2)cc1C,0.39361578
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCC)c(C(=O)OCCCC)c1,0.40136201
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCCCC)c(C(=O)OCCCCCC)c1,0.40008637
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCCCCCC)c(C(=O)OCCCCCCCC)c1,0.39346173
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCCCCCCCC)c(C(=O)OCCCCCCCCCC)c1,0.39569707
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCCCCCCCCCC)c(C(=O)OCCCCCCCCCCCC)c1,0.39501882
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCCCCCCCCCCCC)c(C(=O)OCCCCCCCCCCCCCC)c1,0.39219199
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCCCCCCCCCCCCCC)c(C(=O)OCCCCCCCCCCCCCCCC)c1,0.38889387
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCCCCCCCCCCCCCCCC)c(C(=O)OCCCCCCCCCCCCCCCCCC)c1,0.39124104
*c1cc(C(C)(C)c2ccc(O)cc2)cc(*)c1O,0.36895404
*c1cc(C)c(-c2c(C)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C)c(C)c1,0.42456606
*c1cc(C)c(Cc2cc(C)c(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)cc2C)cc1C,0.38884791
*c1cc(CCCCCCOC(=O)Cc2ccccc2)c(*)s1,0.36920522
*c1cc(Oc2c(C)cc(-c3cc(C)c(Oc4cc(N5C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc(C(F)(F)F)c4)c(C)c3)cc2C)cc(C(F)(F)F)c1,0.3863168
*c1ccc(-c2cc(-c3ccc(OCCCC#N)cc3)cc(-c3ccc(N4C(=O)c5ccc(-c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)n2)cc1,0.38426678
*c1ccc(-c2cc(C(C)(C)C)c(Oc3ccc(N4C(=O)c5ccc(S(=O)(=O)c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)c(C(C)(C)C)c2)cc1,0.40835599
*c1ccc(-c2cc(C(C)(C)C)c(Oc3ccc(N4C(=O)c5ccc(S(=O)(=O)c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3C(F)(F)F)c(C(C)(C)C)c2)cc1,0.40507785
*c1ccc(-c2cc(CCCCCCBr)c(*)s2)s1,0.4247369
*c1ccc(-c2cc3c(ccc4ccccc43)cc2-c2ccc(N(*)c3ccc(C)cc3)cc2)cc1,0.39961477
*c1ccc(-c2ccc(-c3cc(-c4ccccc4)c4cc(-c5ccc6nc(*)cc(-c7ccccc7)c6c5)ccc4n3)cc2)cc1,0.41687752
*c1ccc(-c2ccc(-c3ccc(-c4ccc(-c5ccc(*)n5CCCCCCCCCCCC)s4)s3)s2)s1,0.43060128
*c1ccc(-c2ccc(-c3ccc(-c4ccc(-c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(-c8ccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cc8)cc7)cc5)C6=O)cc4)cc3)cc2)cc1,0.36472216
*c1ccc(-c2ccc(-c3ccc([Si](*)(C)C)s3)s2)s1,0.45230424
*c1ccc(-c2ccc(-c3ccc([Si](*)(CCCC)CCCC)s3)s2)s1,0.43066164
*c1ccc(-c2ccc(-c3ccc([Si](C)(C)[Si](*)(C)C)s3)s2)s1,0.45398953
*c1ccc(-c2ccc(-c3ccc([Si](CCCC)(CCCC)[Si](*)(CCCC)CCCC)s3)s2)s1,0.42687929
*c1ccc(-c2ccc(-c3nnc(-c4ccc([Si](c5ccccc5)(c5ccccc5)c5ccc(-c6nnc(*)o6)cc5)cc4)o3)cc2)cc1,0.46586821
*c1ccc(-c2ccc(C3(*)c4ccccc4-c4ccccc43)cc2)cc1,0.41877221
*c1ccc(-c2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C)c2)cc1C,0.39090779
*c1ccc(-c2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(F)(F)F)c(C(F)(F)F)c1,0.40447598
*c1ccc(-c2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)c(OC)c2)cc1OC,0.36448396
*c1ccc(-c2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)cc2)cc1,0.36285392
*c1ccc(-c2ccc(NC(=O)c3cccc(C(=O)Nc4ccc(-c5ccc(N6C(=O)c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)cc5)cc4)c3)cc2)cc1,0.35467611
*c1ccc(-c2ccc([Si](*)(C)C)s2)s1,0.43527879
*c1ccc(-c2ccc([Si](*)(CCCC)CCCC)s2)s1,0.4246241
*c1ccc(-c2ccc([Si](CCCC)(CCCC)[Si](*)(CCCC)CCCC)s2)s1,0.42578552
*c1ccc(-c2cnc3cc(-c4ccc5nc(*)cnc5c4)ccc3n2)cc1,0.43314968
*c1ccc(-c2cnc3ccc(-c4ccc5nc(*)cnc5c4)cc3n2)cc1,0.42108918
*c1ccc(-c2nc3cc(C(=O)c4ccc5nc(-c6ccccc6)c(-c6ccc(N7C(=O)C8OC9C(=O)N(*)C(=O)C9C8C7=O)cc6)nc5c4)ccc3nc2-c2ccccc2)cc1,0.41616021
*c1ccc(-c2nc3cc(Oc4ccc5nc(-c6ccccc6)c(*)nc5c4)ccc3nc2-c2ccccc2)cc1,0.4245693
*c1ccc(/C=C(\C#N)C(=O)NC2CCCCC2NC(=O)/C(C#N)=C/c2ccc(N(c3ccccc3)c3ccc(N(*)c4ccccc4)cc3)cc2)cc1,0.39541341
*c1ccc(C(=O)NCCCCCCCCNC(=O)c2ccc(-c3nc4ccccc4nc3*)cc2)cc1,0.36159736
*c1ccc(C(=O)OCCCCCCCCCCOc2ccc(C=C3CCC(=Cc4ccc(OCCCCCCCCCCOC(=O)c5ccc(-c6nnc(*)o6)cc5)cc4)C3=O)cc2)cc1,0.36684911
*c1ccc(C(=O)OCCCCCCOc2ccc(C=C3CCCCC(=Cc4ccc(OCCCCCCOC(=O)c5ccc(-c6nnc(*)o6)cc5)cc4)C3=O)cc2)cc1,0.36570631
*c1ccc(C(=O)Oc2ccc3ccc(OC(=O)c4ccc(N5C(=O)CC(SCCOCCSC6CC(=O)N(*)C6=O)C5=O)cc4)cc3c2)cc1,0.33882496
*c1ccc(C(=O)c2cccc(C(=O)c3ccc(S(*)(=O)=O)cc3)c2)cc1,0.3680485
*c1ccc(C(C)(C)c2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)cc2)cc1,0.38365681
*c1ccc(C(Cl)(Cl)C(*)Cl)cc1,0.39641701
*c1ccc(C(F)(F)C(*)(F)F)cc1,0.37735155
*c1ccc(C(c2ccccc2)(c2ccc(-c3nc4ccc(-c5ccc6nc(*)oc6c5)cc4o3)cc2)C(F)(F)F)cc1,0.42450788
*c1ccc(C(c2ccccc2)c2ccc(N(c3ccc(C)cc3)c3ccc(-c4ccc(N(*)c5ccc(C)cc5)cc4)cc3)cc2)cc1,0.43608862
*c1ccc(C2(c3ccc(-c4cc(-c5ccccc5)c5cc(Oc6ccc7nc(*)cc(-c8ccccc8)c7c6)ccc5n4)cc3)c3ccccc3-c3ccccc32)cc1,0.42387068
*c1ccc(C2C3C(=O)N(c4ccc(N5C(=O)C6ON(C)C(*)C6C5=O)cc4)C(=O)C3ON2C)cc1,0.38700602
*c1ccc(Cc2ccc(N3C(=O)CC(Nc4ccc(N(c5ccc(NC6CC(=O)N(*)C6=O)cc5)c5ccc([N+](=O)[O-])cc5)cc4)C3=O)cc2)cc1,0.37849239
*c1ccc(Cc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1,0.38180524
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)c(C)c2)cc1C,0.37895775
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)c(C)c2C)c(C)c1C,0.38199636
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)c(OC)c2)cc1OC,0.37197016
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)cc2)cc1,0.36838096
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)cc2C)c(C)c1,0.37649357
*c1ccc(Cc2ccc(N3C(=O)c4ccc(S(=O)(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C(C)(C)C)c2)cc1C(C)(C)C,0.40289103
*c1ccc(Cc2ccc(N3C(=O)c4ccc(S(=O)(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1,0.38388245
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Sc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C(C)(C)C)c2)cc1C(C)(C)C,0.40667097
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Sc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1,0.37611089
*c1ccc(Cc2ccc(NC(=O)c3ccc(Oc4cccc5c4C(=O)N(*)C5=O)cc3)cc2)cc1,0.36075451
*c1ccc(Cc2ccc(S(*)(=O)=O)cc2)cc1,0.37585986
*c1ccc(N2C(=O)c3ccc(C(c4ccc5c(c4)C(=O)N(c4ccc(N6C(=O)c7ccc(-c8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)cc4)C5=O)(C(F)(F)F)C(F)(F)F)cc3C2=O)cc1,0.39383584
*c1ccc(N2C(=O)c3ccc(Oc4ccc(Sc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4)cc3C2=O)cc1,0.36725031
*c1ccc(NC(=O)Cc2ccc(-c3sc(-c4ccc(CC(=O)Nc5ccc(-c6sc(*)c(-c7ccccc7)c6-c6ccccc6)cc5)cc4)c(-c4ccccc4)c3-c3ccccc3)cc2)cc1,0.40560913
*c1ccc(NC(=O)c2ccc(NC(=O)c3ccc([Si](C)(C)c4ccc(C(=O)Nc5ccc(C(=O)Nc6ccc(-c7sc(*)c(-c8ccccc8)c7-c7ccccc7)cc6)cc5)cc4)cc3)cc2)cc1,0.38057891
*c1ccc(NC(=O)c2ccc(NC(=O)c3ccc([Si](C)(C)c4ccc(C(=O)Nc5ccc(C(=O)Nc6ccc(S(*)(=O)=O)cc6)cc5)cc4)cc3)cc2)cc1,0.3556199
*c1ccc(NC(=O)c2ccc(OCCOCCOc3ccc(C(=O)Nc4ccc5[nH]c(*)nc5c4)cc3)cc2)cc1,0.34897445
*c1ccc(OC(=O)CCCCCCCCCCCCCCCCCCCCC(=O)Oc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1,0.35924634
*c1ccc(OC(=O)c2cccc(C(=O)Oc3ccc(C4(*)NC(=O)c5ccccc54)cc3)c2)cc1,0.35684437
*c1ccc(OC(=O)c2cccc(C(=O)Oc3ccc(C4(*)c5ccccc5C(=O)N4C)cc3)c2)cc1,0.36854686
*c1ccc(OCCCCOc2ccc(-c3nc4ccc(-c5ccc6nc(*)c(-c7ccccc7)nc6c5)cc4nc3-c3ccccc3)cc2)cc1,0.38981232
*c1ccc(OCCN(CC)c2ccc(-c3ccc(C(=C(C#N)C#N)c4ccc(-c5ccc(N(CC)CC)cc5)s4)s3)cc2)c(-c2cc(-c3ccccc3)c3cc(Oc4ccc5nc(*)cc(-c6ccccc6)c5c4)ccc3n2)c1,0.40222587
*c1ccc(Oc2cc3ccccc3cc2Oc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1,0.36809048
*c1ccc(Oc2ccc(-c3c(-c4ccccc4)c(-c4ccc(-c5nc6ccccc6c(*)c5-c5ccccc5)cc4)nc4ccccc34)cc2)cc1,0.40153318
*c1ccc(Oc2ccc(-c3cc(-c4ccccc4)c4cc5c(-c6ccccc6)cc(*)nc5cc4n3)cc2)cc1,0.41795495
*c1ccc(Oc2ccc(-c3cc(OCCCCCC)c(-c4ccc(Oc5ccc(-c6nnc(*)o6)cc5)cc4)cc3OCCCCCC)cc2)cc1,0.39000442
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(-c5cnc6cc(-c7ccc8nc(*)cnc8c7)ccc6n5)cc4)cc3)cc2)cc1,0.38709806
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(-c5nc(-c6ccccn6)nnc5*)cc4)cc3)cc2)cc1,0.39038278
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(C8(c9ccc(Oc%10ccc%11c(c%10)C(=O)N(*)C%11=O)cc9)CCC(c9ccccc9)CC8)cc7)cc6C5=O)cc4)cc3C)c(C)c2)cc1,0.38011618
*c1ccc(Oc2ccc(-c3nc4cc(Oc5ccc6nc(-c7ccccc7)c(*)nc6c5)ccc4nc3-c3ccccc3)cc2)cc1,0.41406386
*c1ccc(Oc2ccc(-n3c(=O)c4cc5c(=O)n(-c6ncc(*)s6)c(=O)c5cc4c3=O)cc2)cc1,0.39753647
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(C)C6=O)cc4)cc3)cc2)cc1,0.37568068
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(CCCCCCCCCCCC)C6=O)cc4)cc3)cc2)cc1,0.37658949
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5cc(*)n(-c6ccccc6)n5)cc4)cc3)cc2)cc1,0.39170081
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5nnc(*)c6c(-c7ccc(F)cc7)c(-c7ccc(F)cc7)c(-c7ccc(F)cc7)c(-c7ccc(F)cc7)c56)cc4)cc3)cc2)cc1,0.4007497
*c1ccc(Oc2ccc(C(=O)c3ccc4cc(C(=O)c5ccc(Oc6ccc(-c7nc(-c8ccccc8)[nH]c7*)cc6)cc5)ccc4c3)cc2)cc1,0.38999587
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(-c6cc(*)n(-c7ccccc7)n6)cc5)cc4)c3)cc2)cc1,0.38437007
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(-c5nnc(*)c6c(-c7ccccc7)c(-c7ccc8ccccc8c7)c(-c7ccc8ccccc8c7)c(-c7ccccc7)c56)cc4)cc3)cc2)cc1,0.40019815
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(N5C(=O)C6CCC7C(=O)N(*)C(=O)C7C6C5=O)cc4)cc3)cc2)cc1,0.37215765
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1,0.37435414
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-c5nnc(*)c6c(-c7ccccc7)c(-c7ccc8ccccc8c7)c(-c7ccc8ccccc8c7)c(-c7ccccc7)c56)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1,0.39876705
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(N5C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1,0.37203901
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1,0.37553925
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccccc8Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)cc5C(F)(F)F)cc4)c4ccccc4-c4ccccc43)cc2)c(C(F)(F)F)c1,0.3879662
*c1ccc(Oc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1,0.37648982
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5cc6ccccc6cc5Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1,0.36464157
*c1ccc(Oc2ccc(N3C(=O)c4ccc(P(=O)(c5ccccc5)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1,0.39239064
*c1ccc(Oc2ccc(N3C(=O)c4ccc([Si](C)(C)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1,0.38889031
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5c(C)cc(-c6cc(C)c(Oc7cccc8c7C(=O)N(*)C8=O)c(C)c6)cc5C)c4C3=O)cc2)cc1,0.38403589
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5c(C)cc(Cc6cc(C)c(Oc7cccc8c7C(=O)N(*)C8=O)c(C)c6)cc5C)c4C3=O)cc2)cc1,0.38048862
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5ccc(-c6ccc(Oc7cccc8c7C(=O)N(*)C8=O)cc6)cc5)c4C3=O)cc2)cc1,0.36710709
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5ccc(C(C)(C)c6ccc(Oc7cccc8c7C(=O)N(*)C8=O)cc6)cc5)c4C3=O)cc2)cc1,0.37037622
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5ccc(Sc6ccc(Oc7cccc8c7C(=O)N(*)C8=O)cc6)cc5)c4C3=O)cc2)cc1,0.36952098
*c1ccc(Oc2ccc(NC(=O)CN3C(=O)c4ccc(C(c5ccc6c(c5)C(=O)N(CC(=O)Nc5ccc(Oc7ccc(-c8nnc(*)o8)cc7)cc5)C6=O)(C(F)(F)F)C(F)(F)F)cc4C3=O)cc2)cc1,0.3576037
*c1ccc(Oc2ccc(NC(=O)c3cc(NC(=O)c4ccc(OC(C)=O)cc4)cc(C(=O)Nc4ccc(Oc5ccc(-c6nnc(*)o6)cc5)cc4)c3)cc2)cc1,0.35432779
*c1ccc(Oc2ccc(NC(=O)c3cccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)c3)cc2)cc1,0.35195763
*c1ccc(Oc2ccc(Oc3ccc(-c4nnc(*)c5c(-c6ccc(F)cc6)c(-c6ccc(F)cc6)c(-c6ccc(F)cc6)c(-c6ccc(F)cc6)c45)cc3)cc2)cc1,0.40913388
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(Oc7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)cc2)cc1,0.36392805
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5cccc(Oc6c(C)cc(Cc7cc(C)c(Oc8cccc9c8C(=O)N(*)C9=O)c(C)c7)cc6C)c5C4=O)c(C)c3)cc2)cc1C,0.38135415
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5cccc(Oc6ccc(C(C)(C)c7ccc(Oc8cccc9c8C(=O)N(*)C9=O)cc7)cc6)c5C4=O)c(C)c3)cc2)cc1C,0.37551052
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5nc(-c6ccc(-c7nc(*)c(-c8ccccc8)[nH]7)cc6)[nH]c5-c5ccccc5)cc4)cc3)cc2)cc1,0.40109727
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5nc6ccccc6n5-c5ccc(-n6c(*)nc7ccccc76)cc5)cc4)cc3)cc2)cc1,0.37966679
*c1ccc(Oc2ccc(Sc3ccc(Oc4ccc(-c5nc(*)nc(-c6ccccc6)n5)cc4)cc3)cc2)cc1,0.39963304
*c1ccc(Oc2ccc3c(c2)C(=O)N(*)C3=O)cc1,0.37183519
*c1ccc(Oc2ccc3ccccc3c2-c2c(Oc3ccc(N4C(=O)c5ccc(NC(=O)Nc6cccc7c(NC(=O)Nc8ccc9c(c8)C(=O)N(*)C9=O)cccc67)cc5C4=O)cc3)ccc3ccccc23)cc1,0.36092398
*c1ccc(Oc2cccc(N3C(=O)c4ccc(Oc5cccc6c(Oc7ccc8c(c7)C(=O)N(*)C8=O)cccc56)cc4C3=O)c2)cc1,0.37487112
*c1ccc(Oc2cccc(NC(=O)c3ccc(C(=O)Nc4cccc(Oc5ccc(-c6nnc(*)o6)cc5)c4)c(Oc4ccccc4)c3)c2)cc1,0.36258207
*c1ccc(Oc2cccc3c(NC(=O)c4ccc([Si](c5ccccc5)(c5ccccc5)c5ccc(C(=O)Nc6cccc7c(Oc8ccc(-c9nnc(*)o9)cc8)cccc67)cc5)cc4)cccc23)cc1,0.37704837
*c1ccc(Oc2ccccc2Oc2ccc(N3C(=O)c4ccc(Oc5ccccc5Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1,0.35809599
*c1ccc(Oc2ccccc2Oc2ccc(N3C(=O)c4ccc(Oc5ccccc5Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(F)(F)F)c(C(F)(F)F)c1,0.36055432
*c1ccc(S(=O)(=O)c2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1,0.37525452
*c1ccc(Sc2ccc(Oc3ccc(Sc4ccc(N5C(=O)c6ccc(Oc7ccc(C(C)(C)c8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)cc7)cc6C5=O)cc4)cc3)cc2)cc1,0.37448846
*c1ccc(Sc2ccc(Sc3ccc(N4C(=O)c5ccc(Sc6ccc(Sc7ccc(Sc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)cc2)cc1,0.37717954
*c1ccc(Sc2ccc(Sc3ccc(Sc4ccc(N5C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1,0.37722426
*c1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(-c3nnc(-c4ccc(-c5nnc(*)o5)nc4)o3)cc2)cc1,0.46577218
*c1ccc2c(c1)C(=O)N(C1CCC(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)CC1)C2=O,0.37690953
*c1ccc2c(c1)C(=O)N(c1c(C)c(C)c(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)c(C)c1C)C2=O,0.41737364
*c1ccc2c(c1)C(=O)N(c1c(C)cc(-c3cc(C)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c4ccccc34)c3ccccc13)C2=O,0.41291503
*c1ccc2c(c1)C(=O)N(c1cc(-c3ccc(O)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)ccc1O)C2=O,0.37301864
*c1ccc2c(c1)C(=O)N(c1cc(C(=O)Nc3ccc(C(c4ccc(NC(=O)c5ccc(C)c(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)cc4)(C(F)(F)F)C(F)(F)F)cc3)ccc1C)C2=O,0.36683082
*c1ccc2c(c1)C(=O)N(c1cc(C)c(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)cc1C)C2=O,0.40933634
*c1ccc2c(c1)C(=O)N(c1cc(OCCN(CC)c3ccc(N=Nc4ccc([N+](=O)[O-])cc4)cc3)cc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)c1)C2=O,0.37866227
*c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(OCCOc4ccc5c(C)cc(=O)oc5c4)c3)cc1OCCOc1ccc3c(C)cc(=O)oc3c1)C2=O,0.34983336
*c1ccc2c(c1)C(=O)N(c1ccc(C(=O)Nc3ccc(Oc4cccc(Oc5ccc(NC(=O)c6ccc(N7C(=O)c8ccc(C(*)(C(F)(F)F)C(F)(F)F)cc8C7=O)cc6)cc5)c4)cc3)cc1)C2=O,0.35654385
*c1ccc2c(c1)C(=O)N(c1ccc(C(c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)(C(F)(F)F)C(F)(F)F)cc1)C2=O,0.39524704
*c1ccc2c(c1)C(=O)N(c1ccc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)cc1)C2=O,0.39320221
*c1ccc2c(c1)C(=O)N(c1ccc(NC(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(=O)Nc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O,0.34222618
*c1ccc2c(c1)C(=O)N(c1ccc(OCCOCCOCCOCCOc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O,0.34187588
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(c4ccc(Oc5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5C(F)(F)F)cc4)(C(F)(F)F)C(F)(F)F)cc3)c(C(F)(F)F)c1)C2=O,0.38326805
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O,0.38674054
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(S(=O)(=O)c4ccc(Oc5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)cc4)cc3)cc1)C2=O,0.37337767
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccccc3-c3ccccc3Oc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O,0.37740671
*c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(C(c4ccc(NC(=O)c5cccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)cc4)(C(F)(F)F)C(F)(F)F)cc3)c1)C2=O,0.36118518
*c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(C(c4ccc(NC(=O)c5cccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5C)cc4)(C(F)(F)F)C(F)(F)F)cc3)c1C)C2=O,0.36895419
*c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(Oc4ccc(NC(=O)c5cccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)cc4)cc3)c1)C2=O,0.35705395
*c1ccc2c(c1)C(=O)N(c1cccc(C(c3cccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)(C(F)(F)F)C(F)(F)F)c1)C2=O,0.38091589
*c1ccc2c(c1)C(=O)N(c1cccc(Oc3cc(Oc4cccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)c4)ccc3P(=O)(c3ccccc3)c3ccccc3)c1)C2=O,0.37529286
*c1ccc2c(c1)C(=O)N(c1cccc(Oc3ccc(N4C(=O)c5ccc(-c6c(-c7ccccc7)c(-c7ccccc7)c(-c7ccc(Sc8ccc(-c9c(-c%10ccccc%10)c(-c%10ccccc%10)c(*)c(-c%10ccccc%10)c9-c9ccccc9)cc8)cc7)c(-c7ccccc7)c6-c6ccccc6)cc5C4=O)cc3)c1)C2=O,0.4011794
*c1ccc2c(c1)C(=O)N(c1cccc(Oc3ccc(Oc4cccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)c4)cc3)c1)C2=O,0.37037326
*c1ccc2c(c1)C(=O)N(c1cccc(Oc3ccc(P(=O)(c4ccccc4)c4ccc(Oc5cccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)cc4)cc3)c1)C2=O,0.37783134
*c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(-c3ccc4c(c3)C(CC3=NC(Cc5ccccc5)CO3)(CC3=NC(Cc5ccccc5)CO3)c3cc(*)ccc3-4)ccc1-2,0.40982823
*c1ccc2c(c1)Sc1cc(-c3sc(C=CC4=CC(=C(C#N)C#N)C=C(C=Cc5sc(*)c(CCCCCC)c5CCCCCC)O4)c(CCCCCC)c3CCCCCC)ccc1N2CCCCCC,0.41634717
*c1ccc2c(c1)Sc1cc(-c3sc4cc(*)sc4c3CCCCC)ccc1N2CCCCCC,0.52516398
*c1ccc2c(c1)Sc1cc(-c3sc4cc(*)sc4c3CCCCC)ccc1N2CCCCCCCCCC,0.50803301
*c1ccc2c(c1)Sc1cc(-c3sc4cc(*)sc4c3CCCCC)ccc1N2CCCCCCCCCCCC,0.4916283
*c1ccc2c3ccc(-c4c5ccc(C=Cc6ccc(N(CCCCCC)CCCCCC)cc6)cc5c(*)c5ccc(C=Cc6ccc(N(CCCCCC)CCCCCC)cc6)cc45)cc3n(CCCCCCCC)c2c1,0.40341568
*c1ccc2oc(-c3cccc(-c4nc5cc(C(*)(C(F)(F)F)C(F)(F)F)ccc5o4)n3)nc2c1,0.45901468
*c1ccc2oc(C3CCC(c4nc5cc(C(*)(C(F)(F)F)C(F)(F)F)ccc5o4)CC3)nc2c1,0.38067376
*c1cccc(-c2nc3cc(Oc4ccc5nc(-c6ccccc6)c(*)nc5c4)ccc3nc2-c2ccccc2)c1,0.41748276
*c1cccc(C(=O)Nc2ccc(Cc3ccc(N4C(=O)c5ccc(S(=O)(=O)c6ccc7c(c6)C(=O)N(c6ccc(Cc8ccc(NC(=O)c9cccc(-c%10nc%11cc(-c%12ccc%13[nH]c(*)nc%13c%12)ccc%11[nH]%10)c9)cc8)cc6)C7=O)cc5C4=O)cc3)cc2)c1,0.37929385
*c1cccc(C(=O)Nc2ccc(NC(=O)c3cccc(N4C(=O)c5ccc(-c6cccc7c6C(=O)N(*)C7=O)cc5C4=O)c3)cc2)c1,0.34929698
*c1cccc(C(=O)Nc2ccc(Oc3ccc(-c4ccc(Oc5ccc(NC(=O)c6cccc(N7C(=O)c8ccc(-c9cccc%10c9C(=O)N(*)C%10=O)cc8C7=O)c6)cc5)cc4)cc3)cc2)c1,0.35563189
*c1cccc(C(=O)Nc2ccc(Oc3ccc(Oc4ccc(NC(=O)c5cccc(N6C(=O)c7ccc(-c8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)c5)cc4)cc3)cc2)c1,0.35757013
*c1cccc(C(=O)Nc2ccc(Oc3ccc(Oc4ccc(Oc5ccc(NC(=O)c6cccc(N7C(=O)c8ccc(-c9ccc%10c(c9)C(=O)N(*)C%10=O)cc8C7=O)c6)cc5)cc4)cc3)cc2)c1,0.35608701
*c1cccc(C(=O)Nc2cccc(S(=O)(=O)c3cccc(NC(=O)c4cccc(N5C(=O)c6ccc(-c7cccc8c7C(=O)N(*)C8=O)cc6C5=O)c4)c3)c2)c1,0.35520964
*c1cccc(Cc2cccc(N3C(=O)c4ccc([Si](C)(C)O[Si](C)(C)O[Si](C)(C)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1,0.38675185
*c1cccc(N2C(=O)c3ccc(Oc4ccc(Sc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4)cc3C2=O)c1,0.36600422
*c1cccc(NC(=O)c2ccc(-c3ccc(C(=O)Nc4cccc(S(*)(=O)=O)c4)c(C)c3)cc2C)c1,0.3574533
*c1cccc(NC(=O)c2ccc(OCCOCCOc3ccc(C(=O)Nc4ccc5[nH]c(*)nc5c4)cc3)cc2)c1,0.34893481
*c1cccc(OCCCCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)c2)c1,0.35046399
*c1cccc(OCCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)c2)c1,0.34909456
*c1cccc(OCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)c2)c1,0.35089152
*c1cccc(OCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)c2)c1,0.34538642
*c1cccc(Oc2cccc(Oc3cccc(N4C(=O)c5ccc(Oc6ccc(Sc7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)c3)c2)c1,0.36222427
*c1cccc(P(C)(=O)c2cccc(N3C(=O)c4ccc(Oc5ccc(C(C)(C)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)c2)c1,0.36957365

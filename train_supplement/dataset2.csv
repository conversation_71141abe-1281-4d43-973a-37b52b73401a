SMILES
*/C(=C(/*)c1ccc(C(C)(C)C)cc1)c1ccccc1
*/C(=C(/*)c1ccc(CCCC)cc1)c1ccccc1
*/C(=C(/*)c1ccc(Oc2ccccc2)cc1)c1ccccc1
*/C(=C(/*)c1ccc([Si](C(C)C)(C(C)C)C(C)C)cc1)c1ccccc1
*/C(=C(/*)c1ccc([Si](C)(C)C)cc1)c1ccccc1
*/C(=C(/*)c1cccc([Ge](C)(C)C)c1)c1ccccc1
*/C(=C(/*)c1cccc([Si](C)(C)C)c1)c1ccccc1
*/C(=C(/c1ccc(*)cc1)c1ccc(Oc2ccccc2)cc1)c1ccc(Oc2ccccc2)cc1
*/C(=C(\C#N)c1ccc(C(=O)OC2CCN(*)CC2)cc1)c1ccc(OC)cc1
*/C(=C(\[2H])C([2H])([2H])C(*)([2H])[2H])C([2H])([2H])[2H]
*/C(=C/c1cc(OC)c(/C=C(/c2ccc(*)cc2)c2ccc(C(F)(F)F)cc2)cc1OC)c1ccc(C(F)(F)F)cc1
*/C(=C/c1cc(OC)c(/C=C(/c2ccc(*)cc2)c2ccc(F)cc2)cc1OC)c1ccc(F)cc1
*/C(=C/c1cc(OC)c(/C=C(/c2ccc(*)cc2)c2ccc(OC)cc2)cc1OC)c1ccc(OC)cc1
*/C(=C/c1cc(OC)c(/C=C(\c2ccc(F)cc2)c2ccc(-c3ccc(*)cc3)cc2)cc1OC)c1ccc(F)cc1
*/C(=C/c1cc(OC)c(/C=C(\c2ccc(F)cc2)c2ccc3cc(*)ccc3c2)cc1OC)c1ccc(F)cc1
*/C(=C/c1cc(OC)c(/C=C(\c2ccccc2)c2ccc(-c3ccc(*)cc3)cc2)cc1OC)c1ccccc1
*/C(=C/c1cc(OC)c(/C=C(\c2ccccc2)c2ccc3cc(*)ccc3c2)cc1OC)c1ccccc1
*/C(=C/c1cc(OCCCCCCCC)c(/C=C(\c2ccccc2)c2ccc3c(c2)Sc2ccc(*)cc2S3)cc1OCCCCCCCC)c1ccccc1
*/C(=C/c1ccc(/C=C(\c2ccccc2)c2ccc(-c3ccc(*)cc3)cc2)cc1)c1ccccc1
*/C(=N\c1ccccc1)c1ccc(-c2ccc(*)cc2)cc1
*/C(C#N)=C(\C#N)N1C(=O)c2ccc(C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2C1=O
*/C(C#N)=C(\C#N)n1c(=O)c2cc3c(=O)n(*)c(=O)c3cc2c1=O
*/C(C#N)=C/c1cc(/C=C(\C#N)c2ccc3c(c2)C(CCCCCCCC)(CCCCCCCC)c2cc(*)ccc2-3)cc(N(c2ccccc2)c2ccccc2)c1
*/C(C#N)=C/c1cc(OCC(CC)CCCC)c(/C=C(\C#N)c2ccc3c4ccc(*)cc4n(CC(CC)CCCC)c3c2)cc1OCC(CC)CCCC
*/C(C#N)=C/c1cc(OCCCCCCCC)c(/C=C(\C#N)c2cc(OCCCCCCCC)c(*)cc2OC)cc1OC
*/C(C#N)=C/c1cc(OCCCCCCCCCC)c(/C=C(\C#N)c2cc(OCCCCCCCCCC)c(*)cc2OC)cc1OC
*/C(C#N)=C/c1cc(OCCCCCCCCCCCC)c(/C=C(\C#N)c2cc(OCCCCCCCCCCCC)c(*)cc2OC)cc1OC
*/C(C#N)=C/c1cc(OCCCCCCCCCCCCCCCC)c(/C=C(\C#N)c2cc(OCCCCCCCCCCCCCCCC)c(*)cc2OC)cc1OC
*/C(C#N)=C/c1ccc(N(CC)Cc2ccccc2CN(CC)c2ccc(/C=C(\C#N)S(*)(=O)=O)cc2)cc1
*/C(C)=C(/*)CCC
*/C(C)=C(/*)CCCCC
*/C(C)=C(/*)CCCCCCC
*/C(C)=C(/*)SCC
*/C(C)=C(/*)SCCCCCC
*/C(C)=C(/*)SCCCCCCCCCC
*/C(C)=C(/*)[Ge](C)(C)C
*/C(C)=C(/*)[Si](C)(C)C
*/C(C)=C(/*)[Si](C)(C)CCCCCC
*/C(C)=C(/*)[Si](C)(C)CC[Si](C)(C)C
*/C(C)=C(/*)[Si](C)(C)C[Si](C)(C)C
*/C(C)=C(/*)c1ccc([Si](C)(C)C)cc1
*/C(CC)=C(/*)c1ccccc1
*/C(CCCCCC)=C(/*)c1ccccc1
*/C(Cl)=C(/*)CCCC
*/C(Cl)=C(/*)CCCCCC
*/C(Cl)=C(/*)CCCCCCCC
*/C(Cl)=C(/*)c1ccccc1
*/C(F)=C(\F)C(F)(C(*)(F)F)C(F)(F)F
*/C(OC)=C(\OC)c1ccc(*)s1
*/C=C(/*)C(C)(C)C
*/C=C(/*)C(CCC)[Si](C)(C)CCCCCC
*/C=C(/*)C(CCC)[Si](C)(C)c1ccccc1
*/C=C(/*)C(CCCCC)[Si](C)(C)C
*/C=C(/*)C(CCCCCCC)[Si](C)(C)C
*/C=C(/*)CCCOC(=O)c1cc(-c2ccc(OC)cc2)ccc1-c1ccc(OC)cc1
*/C=C(/*)CCCOc1ccc(OC(=O)c2ccc(OC)cc2)cc1
*/C=C(/*)CCCOc1ccc(OC(=O)c2ccc(OCCCC)cc2)cc1
*/C=C(/*)CCCOc1ccc(OC(=O)c2ccc(OCCCCCCCC)cc2)cc1
*/C=C(/*)c1c(C)cc(C(C)(C)C)cc1C
*/C=C(/*)c1c(F)c(F)c(CCCC)c(F)c1F
*/C=C(/*)c1ccc(-n2c(=O)c3cc4c(=O)n(-c5ccc(C#C)cc5)c(=O)c4cc3c2=O)cc1
*/C=C(/*)c1ccccc1C(F)(F)F
*/C=C(/*)c1ccccc1C[Si](C)(C)C
*/C=C(/*)c1ccccc1[Ge](C)(C)C
*/C=C(/*)c1ccccc1[Si](C)(C)C
*/C=C(/c1ccc(*)cc1)c1ccc(C(F)(F)F)cc1
*/C=C(\C#N)C(=O)NC1CCCCC1NC(=O)/C(C#N)=C/c1ccc(/C=C/c2ccc(N(CC)Cc3cccc(CN(CC)c4ccc(/C=C/c5ccc(*)s5)cc4)c3)cc2)s1
*/C=C(\C#N)C(=O)NC1CCCCC1NC(=O)/C(C#N)=C/c1ccc(/C=C/c2ccc(N(CC)Cc3ccccc3CN(CC)c3ccc(/C=C/c4ccc(*)s4)cc3)cc2)s1
*/C=C(\C#N)C(=O)NC1CCCCC1NC(=O)/C(C#N)=C/c1ccc(/C=C/c2ccc(N(c3ccccc3)c3ccc(N(c4ccccc4)c4ccc(/C=C/c5ccc(*)s5)cc4)cc3)cc2)s1
*/C=C(\C#N)C(=O)Nc1cc(-c2ccc(O)c(NC(=O)/C(C#N)=C/c3ccc(/C=C/c4ccc(N(c5ccccc5)c5ccc(N(c6ccccc6)c6ccc(/C=C/c7ccc(*)s7)cc6)cc5)cc4)s3)c2)ccc1O
*/C=C(\C#N)C(=O)OCCCCCCCCCCOC(=O)/C(C#N)=C/c1ccc2c(c1)c1cc(*)ccc1n2CCCCCCC
*/C=C(\C#N)C(=O)OCCCCCCCCCCOC(=O)/C(C#N)=C/c1ccc2c(c1)c1cc(*)ccc1n2CCCCCCCCCCCCCC
*/C=C(\C#N)C(=O)OCCCCCCOC(=O)/C(C#N)=C/c1ccc2c(c1)c1cc(*)ccc1n2CCCCCCC
*/C=C(\C#N)C(=O)OCCCCCCOC(=O)/C(C#N)=C/c1ccc2c(c1)c1cc(*)ccc1n2CCCCCCCCCCCCCC
*/C=C(\C#N)C(=O)OCCCCCCOC(=O)/C(C#N)=C/c1ccc2c(c1)c1cc(*)ccc1n2CCCCCCCCCCCOc1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1
*/C=C(\C#N)C(=O)OCCCCCCOC(=O)/C(C#N)=C/c1ccc2c(c1)c1cc(*)ccc1n2CCCCCCCCCCCn1c2ccccc2c2ccccc21
*/C=C(\C#N)c1cccc(/C(C#N)=C/c2ccc(/C=C/c3ccc(N(CC)Cc4ccccc4CN(CC)c4ccc(/C=C/c5ccc(*)s5)cc4)cc3)s2)c1
*/C=C(\C#N)c1cccc(/C(C#N)=C/c2ccc(/C=C/c3ccc(N(c4ccccc4)c4ccc(N(c5ccccc5)c5ccc(/C=C/c6ccc(*)s6)cc5)cc4)cc3)s2)c1
*/C=C(\F)C(F)(F)C*
*/C=C/*
*/C=C/C(*)(C)C
*/C=C/C(*)(C)c1ccccc1
*/C=C/C(C(=O)OCC)C(*)C(=O)OCC
*/C=C/C(C(=O)OCc1ccccc1)C(*)C(=O)OCc1ccccc1
*/C=C/C(C)(C(=O)OC)C(*)C
*/C=C/C(C)C(*)C(=O)OC
*/C=C/C(C)C(*)C(=O)OC12CC3CC(CC(C3)C1)C2
*/C=C/C(C)C(*)C(=O)OCCCCCCN(C)c1ccc(N(C)C)cc1
*/C=C/C(C)C(*)C(=O)OCCCN(C)c1ccc(N(C)C)cc1
*/C=C/C(C)C(*)C(=O)OCCN(C)c1ccc(N(C)C)cc1
*/C=C/C(C)C*
*/C=C/C(C)C*
*/C=C/C(C*)COCC(=O)OCCCCCCCCOc1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1
*/C=C/C(C*)OC
*/C=C/C(COCc1ccccc1)C(*)COCc1ccccc1
*/C=C/C1=C(*)CC(C(=O)OCC)(C(=O)OCC)C1
*/C=C/C1C/C(=C\C)C(*)C1
*/C=C/C1CC(*)C(C#N)(CC)C1
*/C=C/C1CC(*)C(C#N)(CCC)C1
*/C=C/C1CC(*)C(C#N)(CCCC)C1
*/C=C/C1CC(*)C(C#N)(C[Si](C)(C)C)C1
*/C=C/C1CC(*)C(C#N)(C[Si](C)(C)O[Si](C)(C)C)C1
*/C=C/C1CC(*)C(C#N)([Si](C)(C)C)C1
*/C=C/C1CC(*)C(C#N)([Si](C)(C)O[Si](C)(C)C)C1
*/C=C/C1CC(*)C(C#N)([Si](C)(C)O[Si](C)(C)O[Si](C)(C)C)C1
*/C=C/C1CC(*)C(C#N)([Si](C)(C)[Si](C)(C)C)C1
*/C=C/C1CC(*)C(C#N)C1
*/C=C/C1CC(*)C(C(=O)OC(C)(C)C)C1C(=O)OC(C)(C)C
*/C=C/C1CC(*)C(C(=O)OC)C1
*/C=C/C1CC(*)C(C(=O)OC)C1C(=O)OC
*/C=C/C1CC(*)C(C(=O)OCCCCCCCCCCCOc2ccc(-c3ccc(C#N)cc3)cc2)C1C(=O)OCCCCCCCCCCCOc1ccc(-c2ccc(C#N)cc2)cc1
*/C=C/C1CC(*)C(C(=O)OCCCCCCCCOc2ccc(/C=C/c3ccc([N+](=O)[O-])cc3)cc2)C1
*/C=C/C1CC(*)C(C(=O)OCCCCCCOc2ccc(-c3ccc(C#N)cc3)cc2)C1C(=O)OCCCCCCOc1ccc(-c2ccc(C#N)cc2)cc1
*/C=C/C1CC(*)C(C(=O)OCCNC(C)(C)C)C1
*/C=C/C1CC(*)C(C(=O)Oc2ccccc2)C1
*/C=C/C1CC(*)C(C(=O)Oc2ccccc2)C1C(=O)Oc1ccccc1
*/C=C/C1CC(*)C(C2CC=CCC2)C1
*/C=C/C1CC(*)C(F)(F)C1(F)OC(F)(F)C(F)(F)C(F)(F)F
*/C=C/C1CC(*)C([Si](C)(C)C)C1
*/C=C/C1CC(*)C([Si](C)(C)C[Si](C)(C)C)C1
*/C=C/C1CC(*)C2C(=O)N(CCC)C(=O)C12
*/C=C/C1CC(*)C2C(=O)N(CCCC)C(=O)C12
*/C=C/C1CC(*)C2C(=O)N(CCCCC)C(=O)C12
*/C=C/C1CC(*)C2C(=O)N(CCCCCC)C(=O)C12
*/C=C/C1CC(*)C2C(=O)N(CCCCCCC)C(=O)C12
*/C=C/C1CC(*)C2C(=O)N(CCCCCCCC)C(=O)C12
*/C=C/C1CC(*)C2C(=O)N(CCCCCCCCC)C(=O)C12
*/C=C/C1CC(*)C2C(=O)N(CCCCCCCCCC)C(=O)C12
*/C=C/C1CC(*)C2C(=O)N(CCCCCCCCCCCC)C(=O)C12
*/C=C/C1CC(*)C2C(=O)N(c3cc(C(F)(F)F)cc(C(F)(F)F)c3)C(=O)C12
*/C=C/C1CC(*)C2C(=O)N(c3ccc(C)cc3)C(=O)C12
*/C=C/C1CC(*)C2C(=O)N(c3cccc(C)c3)C(=O)C12
*/C=C/C1CC(*)C2C(=O)N(c3ccccc3)C(=O)C12
*/C=C/C1CC(*)C2C(=O)N(c3ccccc3C)C(=O)C12
*/C=C/C1CC(*)C2C3CCC(C3)C12
*/C=C/C1CC(*)C2CC=CC12
*/C=C/C1CC(*)C2CC=CC12
*/C=C/C1CCC(*)C1
*/C=C/CC(C*)(C(=O)OC12CC3CC(CC(C3)C1)C2)C(=O)OC12CC3CC(CC(C3)C1)C2
*/C=C/CC(C*)c1ccccc1
*/C=C/CC*
*/C=C/CC*
*/C=C/CC*
*/C=C/CCC*
*/C=C/CCC*
*/C=C/CCC*
*/C=C/CCCC(Cl)CCC*
*/C=C/CCCCCC*
*/C=C/CCCCCCC(Cl)CCCCCC*
*/C=C/CCCCCCCCCC(CCCCCCCCC*)COCCOCCOCCOCCOCCCCCC
*/C=C/CCCCCCCCCC(CCCCCCCCC*)COCCOCCOCCOCCOCc1ccc2ccc3cccc4ccc1c2c34
*/C=C/CCCCCCCCCC(Cl)CCCCCCCCC*
*/C=C/CCCCCCCCCC*
*/C=C/[Ge](*)(c1ccccc1)c1ccccc1
*/C=C/[Ge](/C=C/[Si](*)(c1ccccc1)c1ccccc1)(c1ccccc1)c1ccccc1
*/C=C/[Si](*)(c1ccccc1)c1ccccc1
*/C=C/c1c(OC(=O)c2ccc(C(=O)Oc3ccc4ccccc4c3/C=C/c3cccc(*)n3)cc2)ccc2ccccc12
*/C=C/c1c(OC(=O)c2ccc(C(=O)Oc3cccc4ccc(*)nc34)cc2)ccc2ccccc12
*/C=C/c1cc(CCCCCCCCCC)c(/C=C/c2ccc(-c3ccc(*)nc3)cn2)cc1CCCCCCCCCC
*/C=C/c1cc(CCCCCCCCCCCCCCCC)c(/C=C/c2nc(*)c(C#N)c(/C=C/c3ccc(N(c4ccccc4)c4ccccc4)cc3)c2C#N)cc1CCCCCCCCCCCCCCCC
*/C=C/c1cc(OC)c(/C=C/c2ccc(/C=C(\Oc3ccccc3)c3ccc(/C(=C/c4ccc(*)cc4)c4ccc(Oc5ccccc5)cc4)cc3)cc2)cc1OC
*/C=C/c1cc(OCC(CC)CCCC)c(*)cc1-c1ccc(F)c(C(F)(F)F)c1
*/C=C/c1cc(OCC(CC)CCCC)c(*)cc1-c1ccc(N(c2ccc(OC)cc2)c2ccc(OC)cc2)cc1
*/C=C/c1cc(OCC(CC)CCCC)c(*)cc1OC
*/C=C/c1cc(OCC(CC)CCCC)c(/C=C/c2cc(OC)c(*)cc2OC)cc1OC
*/C=C/c1cc(OCC(CC)CCCC)c(/C=C/c2ccc3c4ccc(*)cc4n(CC(CC)CCCC)c3c2)cc1OCC(CC)CCCC
*/C=C/c1cc(OCC2CC3CC2C2CCCC32)c(*)cc1OC
*/C=C/c1cc(OCCC(C)CCCC(C)C)c(*)cc1OC
*/C=C/c1cc(OCCCC)c(*)cc1OC
*/C=C/c1cc(OCCCCCC)c(*)cc1OC
*/C=C/c1cc(OCCCCCC)c(*)cc1OC
*/C=C/c1cc(OCCCCCCC)c(/C=C/c2ccc(*)cc2)cc1OCCCCCCC
*/C=C/c1cc(OCCCCCCCC)c(*)cc1OC
*/C=C/c1cc(OCCCCCCCC)c(/C=C/c2ccc(*)c3nsnc23)cc1OCCCCCCCC
*/C=C/c1cc(OCCCCCCCCC)c(/C=C/c2ccc(*)cc2)cc1OCCCCCCCCC
*/C=C/c1cc(OCCCCCCCCCCCC)c(*)cc1OC
*/C=C/c1cc(OCCCCCCCCCCCC)c(/C=C/c2ccc(*)cc2)cc1OCCCCCCCCCCCC
*/C=C/c1cc(OCCCCCCCCCCCCCCCC)c(/C=C/c2ccc(*)cc2)cc1OCCCCCCCCCCCCCCCC
*/C=C/c1cc(OCCc2ccccc2)c(*)cc1OC
*/C=C/c1ccc(*)c(-c2c(OCC(CC)CCCC)ccc3cc(-c4ccccc4)ccc23)c1
*/C=C/c1ccc(*)c(-c2c(OCC(CC)CCCC)ccc3ccccc23)c1
*/C=C/c1ccc(*)c(-c2cc(OCCC(C)C)c(OCCC(C)C)cc2-c2ccccc2)c1
*/C=C/c1ccc(*)c(-c2ccc(OCC(CC)CCCC)c3ccccc23)c1
*/C=C/c1ccc(*)c(OCc2cc(OCCC(C)CCCC(C)C)cc(OCC(CC)CCCC)c2)c1
*/C=C/c1ccc(*)c(OCc2cc(OCc3cc(OCCC(C)CCCC(C)C)cc(OCCC(C)CCCC(C)C)c3)cc(OCc3cc(OCC(CC)CCCC)cc(OCC(CC)CCCC)c3)c2)c1
*/C=C/c1ccc(*)cc1
*/C=C/c1ccc(-c2nnc(-c3ccc(/C=C/c4ccc5c(c4)c4cc(*)ccc4n5CCCCCCCC)cc3)n2-c2ccccc2)cc1
*/C=C/c1ccc(-c2nnc(-c3ccc(/C=C/c4ccc5c(c4)c4cc(-c6ccc7c(c6)c6cc(*)ccc6n7CCCCCCCC)ccc4n5CCCCCCCC)cc3)n2-c2ccccc2)cc1
*/C=C/c1ccc(/C=C/[Si](C)(C)c2ccc([Si](*)(C)C)cc2)cc1
*/C=C/c1ccc(/C=C/[Si](C)(C)c2ccc([Si](*)(C)C)cc2)cc1
*/C=C/c1ccc(/C=C/[Si](C)(C)c2cccc([Si](*)(C)C)c2)cc1
*/C=C/c1ccc(/C=C/[Si](C)(C)c2cccc([Si](*)(C)C)c2)cc1
*/C=C/c1ccc(/C=C/[Si](C)(CCC(F)(F)F)c2ccc([Si](*)(C)CCC(F)(F)F)cc2)cc1
*/C=C/c1ccc(/C=C/[Si](C)(CCC(F)(F)F)c2ccc([Si](*)(C)CCC(F)(F)F)cc2)cc1
*/C=C/c1ccc(/C=C/[Si](C)(CCC(F)(F)F)c2cccc([Si](*)(C)CCC(F)(F)F)c2)cc1
*/C=C/c1ccc(/C=C/[Si](C)(CCC(F)(F)F)c2cccc([Si](*)(C)CCC(F)(F)F)c2)cc1
*/C=C/c1ccc(/C=C/c2ccc3c(c2)C(CCCCCCOc2ccc4ccc(=O)oc4c2)(CCCCCCOc2ccc4ccc(=O)oc4c2)c2cc(*)ccc2-3)cc1
*/C=C/c1ccc(/C=C/c2ccc3c(c2)Sc2cc(*)ccc2N3c2ccc(OCCCCCCCCCCCC)cc2)cc1
*/C=C/c1ccc(/C=C/c2ccc3c(c2)Sc2cc(*)ccc2N3c2ccc(OCCCCCCCCCCCC)cc2)s1
*/C=C/c1ccc(/N=N/c2ccc(N(C)CCOC(=O)Nc3ccc(C)c(NC(=O)OCCN(C)c4ccc(/N=N/c5ccc(/C=C/C6=CC(=C(C#N)C#N)C=C(*)O6)cc5)cc4)c3)cc2)cc1
*/C=C/c1ccc(N(C)CCOC(=O)Nc2ccc(C)c(NC(=O)OCCN(C)c3ccc(/C=C/C4=CC(=C(C#N)C#N)C=C(*)O4)s3)c2)s1
*/C=C/c1ccc(N(CC)Cc2ccccc2CN(CC)c2ccc(/C=C/c3ccc(/C=C(\C#N)S(=O)(=O)/C(C#N)=C/c4ccc(*)s4)s3)cc2)cc1
*/C=C/c1ccc(N(c2ccc(/C=C/c3ccc4c5ccc(*)cc5n(CC(CC)CCCC)c4c3)cc2)c2ccc(OCC(CC)CCCC)cc2)cc1
*/C=C/c1ccc(OC(=O)CCCCCCCCC(=O)Oc2ccc(-c3nnc(*)s3)cc2)cc1
*/C=C/c1ccc(OC(=O)CCCCCCCCC(=O)Oc2cccc(-c3nnc(*)s3)c2)cc1
*/C=C/c1ccc(OC(=O)c2ccc(C(=O)Oc3cccc4ccc(*)nc34)cc2)cc1
*/C=C/c1ccc2c(c1)C(CC(CC)CCCC)(CC(CC)CCCC)c1cc(*)ccc1-2
*/C=C/c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(/C=C/c3nc(*)c(C#N)c(/C=C/c4ccc(N(c5ccccc5)c5ccccc5)cc4)c3C#N)ccc1-2
*/C=C/c1ccc2c(c1)C(CCCCCCOc1ccc3ccc(=O)oc3c1)(CCCCCCOc1ccc3ccc(=O)oc3c1)c1cc(/C=C/c3ccc4c(c3)C(CCCCCC)(CCCCCC)c3cc(*)ccc3-4)ccc1-2
*/C=C/c1ccc2c(c1)Sc1cc(*)ccc1N2c1ccc(OCCCCCCCCCCCC)cc1
*/C=C/c1ccc2c3ccc(*)cc3n(-c3ccc(OCCCCCCCCCC)c(OCCCCCCCCCC)c3)c2c1
*/C=C/c1cccc(/C=C/[Si](C)(C)c2ccc([Si](*)(C)C)cc2)c1
*/C=C/c1cccc(/C=C/[Si](C)(C)c2ccc([Si](*)(C)C)cc2)c1
*/C=C/c1cccc(/C=C/[Si](C)(C)c2cccc([Si](*)(C)C)c2)c1
*/C=C/c1cccc(/C=C/[Si](C)(CCC(F)(F)F)c2ccc([Si](*)(C)CCC(F)(F)F)cc2)c1
*/C=C/c1cccc(/C=C/[Si](C)(CCC(F)(F)F)c2ccc([Si](*)(C)CCC(F)(F)F)cc2)c1
*/C=C/c1cccc(/C=C/[Si](C)(CCC(F)(F)F)c2cccc([Si](*)(C)CCC(F)(F)F)c2)c1
*/C=C/c1cccc(OC(=O)CCCCCCCCC(=O)Oc2ccc(-c3nnc(*)s3)cc2)c1
*/C=C/c1cccc(OC(=O)CCCCCCCCC(=O)Oc2cccc(-c3nnc(*)s3)c2)c1
*/C=C/c1nc(/C=C/c2ccc3c(c2)c2cc(*)ccc2n3CCCCCC)c(C#N)c(/C=C/c2ccc(N(c3ccccc3)c3ccccc3)cc2)c1C#N
*/C=C/c1sc(-c2ccc(-c3ccc(-c4ccc(-c5sc(/C=C/C6=CC(=C(C#N)C#N)C=C(*)O6)c(CCCCCC)c5CCCCCC)s4)s3)s2)c(CCCCCC)c1CCCCCC
*/C=C/c1sc(-c2ccc(-c3ccc(-c4sc(/C=C/C5=CC(=C(C#N)C#N)C=C(*)O5)c(CCCCCC)c4CCCCCC)s3)s2)c(CCCCCC)c1CCCCCC
*/C=C/c1sc(-c2ccc(-c3sc(/C=C/C4=CC(=C(C#N)C#N)C=C(*)O4)c(CCCCCC)c3CCCCCC)s2)c(CCCCCC)c1CCCCCC
*/C=C/c1sc(-c2ccc3c(c2)C(CCCCCC)(CCCCCC)c2cc(-c4sc(/C=C/C5=CC(=C(C#N)C#N)C=C(*)O5)c(CCCCCC)c4CCCCCC)ccc2-3)c(CCCCCC)c1CCCCCC
*/C=C/c1sc(/C=C/c2ccc3c4ccc(*)cc4n(CC(CC)CCCC)c3c2)c(CC(CC)CCCC)c1CC(CC)CCCC
*/C=N/c1ccc(C(=O)OCCCCOCCCCOCCCCOC(=O)c2ccc(/N=C/c3ccc(*)s3)cc2)cc1
*/C=N/c1ccc(C(=O)OCCCCOCCCCOCCCCOC(=O)c2ccc(/N=C/c3ccc4c(c3)c3cc(*)ccc3n4CC(CC)CCCC)cc2)cc1
*/N=N/c1ccc(C(=O)NCCCCNC(=O)c2ccc(*)cc2)cc1
*/N=N/c1ccc(NC(=O)CCC(=O)Nc2ccc(*)cc2)cc1
*/N=N/c1ccc(Nc2ccc(C(=O)c3ccc(C(=O)c4ccc(Nc5ccc(*)cc5)cc4)cc3)cc2)cc1
*/N=N/c1ccc(Nc2ccc(C(=O)c3ccc(Nc4ccc(*)cc4)cc3)cc2)cc1
*/N=N/c1ccc([Te]c2ccc(*)cc2)cc1
*=CNc1ccc(CCc2ccc(N=*)cc2)cc1
*=CNc1ccc(Cc2ccc(N=*)cc2)cc1
*=Cc1cc(OCCCCCCCC)c(/C=N/c2ccc(N=*)cc2)cc1OCCCCCCCC
*=Cc1ccc(/C=N/c2c(-c3ccccc3)cc(-c3ccc(-c4cc(-c5ccccc5)c(N=*)c(-c5ccccc5)c4)cc3)cc2-c2ccccc2)cc1
*=NP(=NP(=NC(=*)Oc1ccc(Br)cc1)(Oc1ccc(Br)cc1)Oc1ccc(Br)cc1)(Oc1ccc(Br)cc1)Oc1ccc(Br)cc1
*=NP(=NP(=NC(=*)Oc1ccc(C(=O)OC)cc1)(Oc1ccc(C(=O)OC)cc1)Oc1ccc(C(=O)OC)cc1)(Oc1ccc(C(=O)OC)cc1)Oc1ccc(C(=O)OC)cc1
*=NP(=NP(=NC(=*)Oc1ccc(C(C)(C)C)cc1)(Oc1ccc(C(C)(C)C)cc1)Oc1ccc(C(C)(C)C)cc1)(Oc1ccc(C(C)(C)C)cc1)Oc1ccc(C(C)(C)C)cc1
*=NP(=NP(=NC(=*)Oc1ccc(C(C)(C)c2ccccc2)cc1)(Oc1ccc(C(C)(C)c2ccccc2)cc1)Oc1ccc(C(C)(C)c2ccccc2)cc1)(Oc1ccc(C(C)(C)c2ccccc2)cc1)Oc1ccc(C(C)(C)c2ccccc2)cc1
*=NP(=NP(=NC(=*)Oc1ccc(C(F)(F)F)cc1)(Oc1ccc(C(F)(F)F)cc1)Oc1ccc(C(F)(F)F)cc1)(Oc1ccc(C(F)(F)F)cc1)Oc1ccc(C(F)(F)F)cc1
*=NP(=NP(=NC(=*)Oc1ccc(OC)cc1)(Oc1ccc(OC)cc1)Oc1ccc(OC)cc1)(Oc1ccc(OC)cc1)Oc1ccc(OC)cc1
*=NP(=NP(=NC(=*)Oc1ccccc1)(Oc1ccccc1)Oc1ccccc1)(Oc1ccccc1)Oc1ccccc1
*=NP(=NP(=NC(=*)Oc1ccccc1C(F)(F)F)(Oc1ccccc1C(F)(F)F)Oc1ccccc1C(F)(F)F)(Oc1ccccc1C(F)(F)F)Oc1ccccc1C(F)(F)F
*=NP(Cl)(Cl)=NP(Cl)(Cl)=NC(=*)Cl
*=Nc1ccc(/N=C(/C=C/C(=*)c2ccccc2)c2ccccc2)cc1
*=Nc1ccc(/N=C(\c2ccccc2)c2ccc(C(=*)c3ccccc3)cc2)cc1
*=Nc1ccc(C(C)(C)c2ccc(C(C)(C)c3ccc(/N=C4/OC(=O)c5ccc(Oc6ccc7c(c6)C(=O)OC7=*)cc54)cc3)cc2)cc1
*=Nc1ccc(Cc2ccc(NC(=*)C)cc2)cc1
*=Nc1ccc(Cc2ccc(NC(=*)CC)cc2)cc1
*=Nc1ccc(Cc2ccc(NC(=*)CCC)cc2)cc1
*=Nc1ccc(Cc2ccc(NC(=*)CCCC)cc2)cc1
*=Nc1ccc(Cc2ccc(NC(=*)c3ccccc3)cc2)cc1
*=Nc1ccc(Oc2ccc(C(c3ccc(Oc4ccc(/N=C5/OC(=O)c6cc7c(cc65)C(=*)OC7=O)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*=Nc1cccc(/N=C(/C=C/C(=*)c2ccccc2)c2ccccc2)c1
*=Nc1cccc(/N=C(\c2ccccc2)c2ccc(C(=*)c3ccccc3)cc2)c1
*=Nc1cccc(S(=O)(=O)c2cccc(/N=C3/OC(=O)c4ccc(Oc5ccc6c(c5)C(=O)OC6=*)cc43)c2)c1
*C#C/C(COS(=O)(=O)c1ccc(C)cc1)=C(/*)COS(=O)(=O)c1ccc(C)cc1
*C#CC#Cc1cc(C(C)(C)C)cc2c3cc(C(C)(C)C)cc(*)c3n(CCCCCCCCCCCCCCCC)c12
*C#Cc1cc(C(C)(C)C)cc2c3cc(C(C)(C)C)cc(*)c3n(CCCCCCCCCCCCCCCC)c12
*C#Cc1cccc(C#C[SiH2]*)c1
*C#Cc1cccc(C#C[SiH](*)C)c1
*C#Cc1cccc(C#C[SiH](*)c2ccccc2)c1
*C#Cc1cccc(C#C[Si](*)(C)C)c1
*C#Cc1ccccc1C#C[SiH](*)c1ccccc1
*C(*)(F)Cl
*C(*)(F)F
*C(*)C(=O)OC
*C(*)C(=O)OC(C)C
*C(*)C(=O)OC1CCCCC1
*C(*)C(=O)OCC
*C(*)C(=O)OCCC
*C(*)F
*C(*)O
*C(=O)CCC1(CCC(=O)N2CCN(*)CC2)c2ccccc2-c2ccccc21
*C(=O)CCCCCCCCC(=O)N1CC(C)N(*)CC1C
*C(=O)CCCCCCCCC(=O)N1CCN(*)CC1
*C(=O)CCCCCCCCC(=O)NC1CCCCN(*)C1
*C(=O)N1C(=O)N(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)C(C)(C)C1=O
*C(=O)N1C(=O)N(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)C(CC)(CC)C1=O
*C(=O)N1C(=S)N(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)C(C)=C(C(=O)OCC)C1c1ccc(OC)cc1OC
*C(=O)N1C(=S)N(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)C(C)=C(C(=O)OCC)C1c1ccccc1
*C(=O)NC1CCC(CC2CCC(NC(=O)c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)CC2)CC1
*C(=O)NC1CCCC(*)O1
*C(=O)NCC(C)(C)CNC(=O)c1ncn(C)c1*
*C(=O)NCCCCCCNC(=O)c1ccc(*)nc1
*C(=O)NCCCCCCNC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)NCCCCCCNC(=O)c1ncn(C)c1*
*C(=O)NCCN(CCNC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1)c1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1
*C(=O)NCCN(CCNC(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O)c1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1
*C(=O)NCCN(CCNC(=O)c1cccc(N2C(=O)c3ccc(*)cc3C2=O)c1)c1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1
*C(=O)NCCNC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)NCCNC(=O)c1ncn(C)c1*
*C(=O)NNC(=O)c1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(C(=O)NNC(=O)c3ccc(*)nc3)cc2)cc1
*C(=O)NNC(=O)c1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(C(=O)NNC(=O)c3ccc(*)o3)cc2)cc1
*C(=O)NNC(=O)c1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(C(=O)NNC(=O)c3cncc(*)c3)cc2)cc1
*C(=O)Nc1c(C)cc(C)c(NC(=O)c2ccc3c(c2)C(=O)N(c2c(C)cc(C)c(N4C(=O)c5ccc(*)cc5C4=O)c2C)C3=O)c1C
*C(=O)Nc1c(C)cc(Cc2cc(C)c(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(/N=N/c5ccc(F)cc5)c(N5C(=O)c6ccc(*)cc6C5=O)c3)C4=O)c(C)c2)cc1C
*C(=O)Nc1c(C)cc(Cc2cc(C)c(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(/N=N/c5ccc(OC(F)(F)F)cc5)c(N5C(=O)c6ccc(*)cc6C5=O)c3)C4=O)c(C)c2)cc1C
*C(=O)Nc1c(C)cc(Cc2cc(C)c(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(/N=N/c5ccc([N+](=O)[O-])cc5)c(N5C(=O)c6ccc(*)cc6C5=O)c3)C4=O)c(C)c2)cc1C
*C(=O)Nc1c(CC)cc(Cc2cc(CC)c(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(/N=N/c5ccc(C)cc5)c(N5C(=O)c6ccc(*)cc6C5=O)c3)C4=O)c(CC)c2)cc1CC
*C(=O)Nc1cc(C(F)(F)F)ccc1Oc1cccc2c(Oc3ccc(C(F)(F)F)cc3NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5-c5ccccc5)c(-c5ccccc5)c3)C4=O)cccc12
*C(=O)Nc1cc(C(c2cc(C)c(C)c(NC(=O)c3ccc4c(c3)C(=O)N(c3cccc5c(N6C(=O)c7ccc(*)cc7C6=O)cccc35)C4=O)c2)(C(F)(F)F)C(F)(F)F)cc(C)c1C
*C(=O)Nc1cc(NC(=O)c2ccc3[nH]c(-c4cc(-c5nc6cc(*)ccc6[nH]5)cc(N5C(=O)c6ccccc6C5=O)c4)nc3c2)cc(-c2nc3ccccc3[nH]2)c1
*C(=O)Nc1cc(NC(=O)c2ccc3[nH]c(-c4cccc(-c5nc6cc(*)ccc6[nH]5)c4)nc3c2)cc(-c2nc3ccccc3[nH]2)c1
*C(=O)Nc1cc(NC(=O)c2ccc3c(c2)C(=O)N(c2cc(-c4nc5ccccc5o4)cc(N4C(=O)c5ccc(*)cc5C4=O)c2)C3=O)cc(-c2nc3ccccc3[nH]2)c1
*C(=O)Nc1cc(NC(=O)c2ccc3c(c2)C(=O)N(c2cc(-c4nc5ccccc5s4)cc(N4C(=O)c5ccc(*)cc5C4=O)c2)C3=O)cc(-c2nc3ccccc3[nH]2)c1
*C(=O)Nc1cc(NC(=O)c2ccc3c(c2)C(=O)N(c2cc(-c4nnc(-c5ccccn5)o4)cc(N4C(=O)c5ccc(*)cc5C4=O)c2)C3=O)cc(-c2nnc(-c3ccccn3)o2)c1
*C(=O)Nc1cc(NC(=O)c2ccc3c(c2)C(=O)N(c2cccc(N4C(=O)c5ccc(*)cc5C4=O)c2)C3=O)cc(-c2nc3ccccc3[nH]2)c1
*C(=O)Nc1cc(NC(=O)c2cccc(*)n2)cc(-c2nnc(-c3ccccn3)o2)c1
*C(=O)Nc1cc(NC(=O)c2cncc(*)c2)cc(-c2nnc(-c3ccccn3)o2)c1
*C(=O)Nc1ccc(-c2cc(-c3ccccc3)cc(-c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(NC(=O)Nc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc4)C5=O)cc3)n2)cc1
*C(=O)Nc1ccc(-c2cc(-c3ccccc3)cc(-c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6cccc7c(N8C(=O)c9ccc(*)cc9C8=O)cccc67)cc4)C5=O)cc3)n2)cc1
*C(=O)Nc1ccc(-c2cc(-c3ccccc3)cc(-c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4cccc(P(=O)(c6ccccc6)c6cccc(N7C(=O)c8ccc(*)cc8C7=O)c6)c4)C5=O)cc3)n2)cc1
*C(=O)Nc1ccc(-c2ccc(N3C(=O)CC(N4C(=O)c5ccc(*)cc5C4=O)C3=O)cc2)cc1
*C(=O)Nc1ccc(-c2ccc(NC(=O)N3CC(C)N(*)CC3C)cc2)cc1
*C(=O)Nc1ccc(-c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3c(C)cc(C)c(N5C(=O)c6ccc(*)cc6C5=O)c3C)C4=O)cc2C(F)(F)F)c(C(F)(F)F)c1
*C(=O)Nc1ccc(-c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3c(C)cc(C)c(N5C(=O)c6ccc(*)cc6C5=O)c3C)C4=O)cc2C)c(C)c1
*C(=O)Nc1ccc(-c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(NC(=O)Nc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(-c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(-c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5-c5ccccc5)c(-c5ccccc5)c3)C4=O)cc2)cc1
*C(=O)Nc1ccc(-c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5cccc6c(N7C(=O)c8ccc(*)cc8C7=O)cccc56)cc3)C4=O)c(OC)c2)cc1OC
*C(=O)Nc1ccc(-c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5cccc6c(N7C(=O)c8ccc(*)cc8C7=O)cccc56)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(-c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(S(=O)(=O)c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(-c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3cccc(P(=O)(c5ccccc5)c5cccc(N6C(=O)c7ccc(*)cc7C6=O)c5)c3)C4=O)cc2)cc1
*C(=O)Nc1ccc(-c2sc(-c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(-c6sc(-c7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)c(-c7ccccc7)c6-c6ccccc6)cc4)C5=O)cc3)c(-c3ccccc3)c2-c2ccccc2)cc1
*C(=O)Nc1ccc(-c2sc(-c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6ccc7ccc(Oc8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)cc7c6)cc4)C5=O)cc3)c(-c3ccccc3)c2-c2ccccc2)cc1
*C(=O)Nc1ccc(-c2sc(-c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6cccc7c(Oc8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)cccc67)cc4)C5=O)cc3)c(-c3ccccc3)c2-c2ccccc2)cc1
*C(=O)Nc1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)c(NC(=O)c2ccc3c(c2)C(=O)N(c2c(C)cc(-c4cc(C)c(N5C(=O)c6ccc(*)cc6C5=O)c(C)c4)cc2C)C3=O)c1
*C(=O)Nc1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)c(NC(=O)c2ccc3c(c2)C(=O)N(c2c(C)cc(Cc4cc(C)c(N5C(=O)c6ccc(*)cc6C5=O)c(C)c4)cc2C)C3=O)c1
*C(=O)Nc1ccc(C(=O)Nc2ccc(NC(=O)c3ccc4[nH]c(-c5ccc(-c6nc7cc(*)ccc7[nH]6)cc5)nc4c3)cc2)cc1
*C(=O)Nc1ccc(C(=O)Nc2ccc(NC(=O)c3ccc4[nH]c(-c5cccc(-c6nc7cc(*)ccc7[nH]6)c5)nc4c3)cc2)cc1
*C(=O)Nc1ccc(C(=O)c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(NC(=O)Nc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(C(=O)c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3cccc(P(=O)(c5ccccc5)c5cccc(N6C(=O)c7ccc(*)cc7C6=O)c5)c3)C4=O)cc2)cc1
*C(=O)Nc1ccc(C(c2ccc(NC(=O)c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3C)cc2)(C(F)(F)F)C(F)(F)F)cc1
*C(=O)Nc1ccc(C(c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3c(C)cc(-c5cc(C)c(N6C(=O)c7ccc(*)cc7C6=O)c(C)c5)cc3C)C4=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*C(=O)Nc1ccc(C(c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3c(C)cc(-c5cc(C)c(N6C(=O)c7ccc(*)cc7C6=O)c6ccccc56)c5ccccc35)C4=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*C(=O)Nc1ccc(C(c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3c(C)cc(C)c(N5C(=O)c6ccc(*)cc6C5=O)c3C)C4=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*C(=O)Nc1ccc(C(c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(-c5ccc(N6C(=O)c7ccc(*)cc7C6=O)c(OC)c5)cc3OC)C4=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*C(=O)Nc1ccc(C(c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc6ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc6c5)cc3)C4=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*C(=O)Nc1ccc(C(c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5cccc6c(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cccc56)cc3)C4=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*C(=O)Nc1ccc(C(c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3cccc5c(N6C(=O)c7ccc(*)cc7C6=O)cccc35)C4=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*C(=O)Nc1ccc(C)cc1N1C(=O)CC(N2C(=O)c3ccc(*)cc3C2=O)C1=O
*C(=O)Nc1ccc(C2(c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)CC(C)CC(C)(C)C2)cc1
*C(=O)Nc1ccc(C2(c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)CCC(C(C)(C)C)CC2)cc1
*C(=O)Nc1ccc(C2(c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)CCC(C(C)(C)CC)CC2)cc1
*C(=O)Nc1ccc(C2(c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)CCC(C)CC2)cc1
*C(=O)Nc1ccc(C2(c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)CCC(CC)CC2)cc1
*C(=O)Nc1ccc(C2(c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)CCCCC2)cc1
*C(=O)Nc1ccc(C2(c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(-c6ccc(N7C(=O)c8ccc(*)cc8C7=O)c(OC)c6)cc4OC)C5=O)cc3)c3ccccc3-c3ccccc32)cc1
*C(=O)Nc1ccc(C2(c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc4)C5=O)cc3)c3ccccc3-c3ccccc32)cc1
*C(=O)Nc1ccc(C2(c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6cccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)c6)cc4)C5=O)cc3)c3ccccc3-c3ccccc32)cc1
*C(=O)Nc1ccc(C2(c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Sc6ccc(Oc7ccc(Sc8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)cc7)cc6)cc4)C5=O)cc3)c3ccccc3-c3ccccc32)cc1
*C(=O)Nc1ccc(C2(c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4cccc(N6C(=O)c7ccc(*)cc7C6=O)c4)C5=O)cc3)c3ccccc3-c3ccccc32)cc1
*C(=O)Nc1ccc(Cc2ccc(N3C(=O)CC(N4C(=O)c5ccc(*)cc5C4=O)C3=O)cc2)cc1
*C(=O)Nc1ccc(Cc2ccc(N3C(=O)c4ccc(*)cc4C3=O)cc2)cc1
*C(=O)Nc1ccc(Cc2ccc(NC(=O)c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc2)cc1
*C(=O)Nc1ccc(Cc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3c(C)cc(-c5cc(C)c(N6C(=O)c7ccc(*)cc7C6=O)c(C)c5)cc3C)C4=O)c(C)c2)cc1C
*C(=O)Nc1ccc(Cc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(-c5sc(-c6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)c(-c6ccccc6)c5-c5ccccc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Cc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(/N=N/c5ccc(C)cc5)c(N5C(=O)c6ccc(*)cc6C5=O)c3)C4=O)c(C)c2)cc1C
*C(=O)Nc1ccc(Cc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(/N=N/c5ccc(C)cc5)c(N5C(=O)c6ccc(*)cc6C5=O)c3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Cc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(NC(=O)Nc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Cc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Cc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc6ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc6c5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Cc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(S(=O)(=O)c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Cc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3cccc(P(=O)(c5ccccc5)c5cccc(N6C(=O)c7ccc(*)cc7C6=O)c5)c3)C4=O)cc2)cc1
*C(=O)Nc1ccc(N2C(=O)CC(N3C(=O)c4ccc(*)cc4C3=O)C2=O)cc1
*C(=O)Nc1ccc(NC(=O)Nc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(NC(=O)Nc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(NC(=O)c2ccc(N3C(=O)c4ccc(*)cc4C3=O)cc2)cc1
*C(=O)Nc1ccc(NC(=O)c2ccc(NC(=O)c3ccc(-c4nc5cc(*)ccc5[nH]4)cc3)cc2)cc1
*C(=O)Nc1ccc(NC(=O)c2ccc3[nH]c(-c4cc(-c5nc6cc(*)ccc6[nH]5)cc(N5C(=O)c6ccccc6C5=O)c4)nc3c2)cc1
*C(=O)Nc1ccc(NC(=O)c2ccc3c(c2)C(=O)N(c2c(C)cc(C)c(N4C(=O)c5ccc(*)cc5C4=O)c2C)C3=O)cc1
*C(=O)Nc1ccc(NC(=O)c2ccc3c(c2)C(=O)N(c2cc(-c4nc5ccccc5s4)cc(N4C(=O)c5ccc(*)cc5C4=O)c2)C3=O)cc1
*C(=O)Nc1ccc(NC(=O)c2ccc3c(c2)C(=O)N(c2ccc(-c4sc(-c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)c(-c5ccccc5)c4-c4ccccc4)cc2)C3=O)cc1
*C(=O)Nc1ccc(NC(=O)c2ccc3c(c2)C(=O)N(c2ccc(NC(=O)Nc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)cc1
*C(=O)Nc1ccc(NC(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)c(OCCCCCCS(=O)(=O)c2ccc(/C=C/c3ccc(N(C)C)cc3)cc2)c1
*C(=O)Nc1ccc(NC(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4cccc5c(N6C(=O)c7ccc(*)cc7C6=O)cccc45)cc2)C3=O)cc1
*C(=O)Nc1ccc(NC(=O)c2ccc3c(c2)C(=O)N(c2ccc(S(=O)(=O)c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)cc1
*C(=O)Nc1ccc(NC(=O)c2ccc3c(c2)C(=O)N(c2cccc(N4C(=O)c5ccc(*)cc5C4=O)c2)C3=O)cc1
*C(=O)Nc1ccc(NC(=O)c2ccc3c(c2)C(=O)N(c2cccc(P(=O)(c4ccccc4)c4cccc(N5C(=O)c6ccc(*)cc6C5=O)c4)c2)C3=O)cc1
*C(=O)Nc1ccc(Oc2c(C)cc(C(C)(C)c3cc(C)c(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(C8(c9ccc(Oc%10ccc(N%11C(=O)c%12ccc(*)cc%12C%11=O)cc%10)cc9)CC9CC8C8CCCC98)cc7)cc5)C6=O)cc4)c(C)c3)cc2C)cc1
*C(=O)Nc1ccc(Oc2cc(C(C)(C)C)c(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6ccc(C7(c8ccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cc8)CC8CC7C7CCCC87)cc6)cc4)C5=O)cc3)cc2C(C)(C)C)cc1
*C(=O)Nc1ccc(Oc2ccc(-c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(C)c(N7C(=O)c8ccc(*)cc8C7=O)c5)C6=O)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(-c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(C(C)(C)c8ccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cc8)cc7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(-c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(C8(c9ccc(Oc%10ccc(N%11C(=O)c%12ccc(*)cc%12C%11=O)cc%10)cc9)CC9CC8C8CCCC98)cc7)cc5)C6=O)cc4)cc3C)c(C)c2)cc1
*C(=O)Nc1ccc(Oc2ccc(-c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(-c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(-c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(S(=O)(=O)c8ccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cc8)cc7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(-c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7cccc8c(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cccc78)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(-c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccccc7-c7ccccc7Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(-c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5cccc(N7C(=O)c8ccc(*)cc8C7=O)c5)C6=O)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(NC(=O)c5ccc(*)nc5)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(NC(=O)c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(-c7sc(-c8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)c(-c8ccccc8)c7-c7ccccc7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc8ccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cc8c7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7cccc(N8C(=O)c9ccc(*)cc9C8=O)c7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7cccc8c(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cccc78)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C(C)(C)c3cccc(C(C)(C)c4ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc4)c3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C(c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(C8(c9ccc(Oc%10ccc(N%11C(=O)c%12ccc(*)cc%12C%11=O)cc%10)cc9)CC9CC8C8CCCC98)cc7)cc5)C6=O)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C(c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc8ccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cc8c7)cc5)C6=O)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C(c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7cccc8c(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cccc78)cc5)C6=O)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(NC(=O)c6ccc7c(c6)C(=O)N(c6ccc(C)c(N8C(=O)c9ccc(*)cc9C8=O)c6)C7=O)cc5)cc4)NC(=O)c4ccccc43)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(NC(=O)c6ccc7c(c6)C(=O)N(c6ccc(N8C(=O)c9ccc(*)cc9C8=O)cc6)C7=O)cc5)cc4)NC(=O)c4ccccc43)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(NC(=O)c6ccc7c(c6)C(=O)N(c6ccc(Oc8ccc(C9(c%10ccc(Oc%11ccc(N%12C(=O)c%13ccc(*)cc%13C%12=O)cc%11)cc%10)NC(=O)c%10ccccc%109)cc8)cc6)C7=O)cc5)cc4)NC(=O)c4ccccc43)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(NC(=O)c6ccc7c(c6)C(=O)N(c6ccc(Oc8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)cc6)C7=O)cc5)cc4)NC(=O)c4ccccc43)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(NC(=O)c6ccc7c(c6)C(=O)N(c6ccc(Oc8ccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)c(C)c8)cc6)C7=O)cc5)cc4)NC(=O)c4ccccc43)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(NC(=O)c6ccc7c(c6)C(=O)N(c6ccc(Oc8ccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cc8)cc6)C7=O)cc5)cc4)NC(=O)c4ccccc43)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(NC(=O)c6ccc7c(c6)C(=O)N(c6ccc(Oc8ccc(S(=O)(=O)c9ccc(Oc%10ccc(N%11C(=O)c%12ccc(*)cc%12C%11=O)cc%10)cc9)cc8)cc6)C7=O)cc5)cc4)NC(=O)c4ccccc43)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(NC(=O)c6ccc7c(c6)C(=O)N(c6ccc(Oc8cccc(N9C(=O)c%10ccc(*)cc%10C9=O)c8)cc6)C7=O)cc5)cc4)NC(=O)c4ccccc43)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(NC(=O)c6ccc7c(c6)C(=O)N(c6ccc(Oc8cccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)c8)cc6)C7=O)cc5)cc4)NC(=O)c4ccccc43)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(NC(=O)c6ccc7c(c6)C(=O)N(c6cccc(N8C(=O)c9ccc(*)cc9C8=O)c6)C7=O)cc5)cc4)NC(=O)c4ccccc43)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(N3C(=O)CC(N4C(=O)c5ccc(*)cc5C4=O)C3=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(N3C(=O)c4ccc(*)cc4C3=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(NC(=O)c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(C3CCC(CC5CCC(N6C(=O)c7ccc(*)cc7C6=O)CC5)CC3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(-c5sc(-c6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)c(-c6ccccc6)c5-c5ccccc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Cc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(NC(=O)Nc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(C6(c7ccc(Oc8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)cc7)CC7CC6C6CCCC76)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(C6(c7ccc(Oc8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)cc7)NC(=O)c7ccccc76)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5-c5ccccc5)c(-c5ccccc5)c3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5-c5ccccc5)c(-c5ccccc5)c3)C4=O)cc2-c2ccccc2)c(-c2ccccc2)c1
*C(=O)Nc1ccc(Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5cccc6c(N7C(=O)c8ccc(*)cc8C7=O)cccc56)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(S(=O)(=O)c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3cccc(P(=O)(c5ccccc5)c5cccc(N6C(=O)c7ccc(*)cc7C6=O)c5)c3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(-c6sc(-c7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)c(-c7ccccc7)c6-c6ccccc6)cc4)C5=O)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6ccc(C7(c8ccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cc8)CC8CC7C7CCCC87)cc6)cc4)C5=O)cc3)c(C(C)(C)C)c2)cc1
*C(=O)Nc1ccc(Oc2ccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6ccc(C7(c8ccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cc8)CC8CC7C7CCCC87)cc6)cc4)C5=O)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6ccc(C7(c8ccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cc8)NC(=O)c8ccccc87)cc6)cc4)C5=O)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc4)C5=O)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6cccc7c(Oc8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)cccc67)cc4)C5=O)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(S(=O)(=O)c6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc4)C5=O)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(C8(c9ccc(Oc%10ccc(N%11C(=O)c%12ccc(*)cc%12C%11=O)cc%10)cc9)NC(=O)c9ccccc98)cc7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc8ccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cc8c7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7cccc8c(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cccc78)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)Nc1ccc(Oc2cccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(-c5sc(-c6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)c(-c6ccccc6)c5-c5ccccc5)cc3)C4=O)c2)cc1
*C(=O)Nc1ccc(Oc2cccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(C6(c7ccc(Oc8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)cc7)NC(=O)c7ccccc76)cc5)cc3)C4=O)c2)cc1
*C(=O)Nc1ccc(Oc2cccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)c2)cc1
*C(=O)Nc1ccc(Oc2cccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc6ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc6c5)cc3)C4=O)c2)cc1
*C(=O)Nc1ccc(Oc2cccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5cccc(N6C(=O)c7ccc(*)cc7C6=O)c5)cc3)C4=O)c2)cc1
*C(=O)Nc1ccc(Oc2cccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6ccc(C7(c8ccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cc8)NC(=O)c8ccccc87)cc6)cc4)C5=O)cc3)c2)cc1
*C(=O)Nc1ccc(Oc2cccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6cccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)c6C#N)cc4)C5=O)cc3)c2)cc1
*C(=O)Nc1ccc(Oc2cccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6cccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)c6C#N)cc4)C5=O)cc3)c2C#N)cc1
*C(=O)Nc1ccc(Oc2cccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Sc6ccc(Oc7ccc(Sc8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)cc7)cc6)cc4)C5=O)cc3)c2)cc1
*C(=O)Nc1ccc(Oc2cccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(c4cccc(Oc6cccc(Oc7cccc(N8C(=O)c9ccc(*)cc9C8=O)c7)c6C#N)c4)C5=O)cc3)c2)cc1
*C(=O)Nc1ccc(Oc2cccc3c(NC(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6cccc7c(N8C(=O)c9ccc(*)cc9C8=O)cccc67)cc4)C5=O)cccc23)cc1
*C(=O)Nc1ccc(Oc2ccccc2-c2ccccc2Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(C)c(N5C(=O)c6ccc(*)cc6C5=O)c3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccccc2-c2ccccc2Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Cc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccccc2-c2ccccc2Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(N5C(=O)c6ccc(*)cc6C5=O)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccccc2-c2ccccc2Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(-c6ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc6)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccccc2-c2ccccc2Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(C(C)(C)c6ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc6)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccccc2-c2ccccc2Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccccc2-c2ccccc2Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccccc2-c2ccccc2Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccccc2-c2ccccc2Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(S(=O)(=O)c6ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc6)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccccc2-c2ccccc2Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5cccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)c5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccccc2-c2ccccc2Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccccc5-c5ccccc5Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Oc2ccccc2-c2ccccc2Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3cccc(N5C(=O)c6ccc(*)cc6C5=O)c3)C4=O)cc2)cc1
*C(=O)Nc1ccc(S(=O)(=O)c2ccc(N3C(=O)CC(N4C(=O)c5ccc(*)cc5C4=O)C3=O)cc2)cc1
*C(=O)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc2)cc1
*C(=O)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)c3ccc4[nH]c(-c5cc(-c6nc7cc(*)ccc7[nH]6)cc(N6C(=O)c7ccccc7C6=O)c5)nc4c3)cc2)cc1
*C(=O)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)c3ccc4[nH]c(-c5cccc(-c6nc7cc(*)ccc7[nH]6)c5)nc4c3)cc2)cc1
*C(=O)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3cc(-c5nc6ccccc6o5)cc(N5C(=O)c6ccc(*)cc6C5=O)c3)C4=O)cc2)cc1
*C(=O)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3cc(-c5nc6ccccc6s5)cc(N5C(=O)c6ccc(*)cc6C5=O)c3)C4=O)cc2)cc1
*C(=O)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(-c5sc(-c6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)c(-c6ccccc6)c5-c5ccccc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(NC(=O)Nc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(S(=O)(=O)c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3cccc(N5C(=O)c6ccc(*)cc6C5=O)c3)C4=O)cc2)cc1
*C(=O)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3cccc(P(=O)(c5ccccc5)c5cccc(N6C(=O)c7ccc(*)cc7C6=O)c5)c3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Sc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(C6(c7ccc(Oc8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)cc7)NC(=O)c7ccccc76)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc(Sc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Nc1ccc2c(c1)-c1cc(NC(=O)c3ccc4c(c3)C(=O)N(c3cccc(P(=O)(c5ccccc5)c5cccc(N6C(=O)c7ccc(*)cc7C6=O)c5)c3)C4=O)ccc1C2
*C(=O)Nc1ccc2c(c1)Cc1cc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(NC(=O)Nc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)ccc1-2
*C(=O)Nc1cccc(C(=O)Nc2cccc(NC(=O)c3ccc4[nH]c(-c5ccc(-c6nc7cc(*)ccc7[nH]6)cc5)nc4c3)c2)c1
*C(=O)Nc1cccc(C(=O)Nc2cccc(NC(=O)c3ccc4[nH]c(-c5cccc(-c6nc7cc(*)ccc7[nH]6)c5)nc4c3)c2)c1
*C(=O)Nc1cccc(C(=O)c2cccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(-c5sc(-c6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)c(-c6ccccc6)c5-c5ccccc5)cc3)C4=O)c2)c1
*C(=O)Nc1cccc(N2C(=O)CC(N3C(=O)c4ccc(*)cc4C3=O)C2=O)c1
*C(=O)Nc1cccc(NC(=O)/C=C/c2cccc(N3C(=O)c4ccc(*)cc4C3=O)c2)c1
*C(=O)Nc1cccc(NC(=O)c2ccc(-c3nc4cc(*)ccc4[nH]3)cc2)c1
*C(=O)Nc1cccc(NC(=O)c2ccc(N3C(=O)c4ccc(*)cc4C3=O)cc2)c1
*C(=O)Nc1cccc(NC(=O)c2ccc3[nH]c(-c4cc(-c5nc6cc(*)ccc6[nH]5)cc(N5C(=O)c6ccccc6C5=O)c4)nc3c2)c1
*C(=O)Nc1cccc(NC(=O)c2ccc3[nH]c(-c4cc(-c5nc6cc(*)ccc6[nH]5)cc(N5C(=O)c6ccccc6C5=O)c4)nc3c2)n1
*C(=O)Nc1cccc(NC(=O)c2ccc3[nH]c(-c4ccc(-c5nc6cc(*)ccc6[nH]5)cc4)nc3c2)c1
*C(=O)Nc1cccc(NC(=O)c2ccc3[nH]c(-c4cccc(-c5nc6cc(*)ccc6[nH]5)c4)nc3c2)c1
*C(=O)Nc1cccc(NC(=O)c2ccc3[nH]c(-c4cccc(-c5nc6cc(*)ccc6[nH]5)c4)nc3c2)n1
*C(=O)Nc1cccc(NC(=O)c2ccc3c(c2)C(=O)N(c2cc(-c4nc5ccccc5o4)cc(N4C(=O)c5ccc(*)cc5C4=O)c2)C3=O)n1
*C(=O)Nc1cccc(NC(=O)c2ccc3c(c2)C(=O)N(c2cc(-c4nc5ccccc5s4)cc(N4C(=O)c5ccc(*)cc5C4=O)c2)C3=O)n1
*C(=O)Nc1cccc(NC(=O)c2ccc3c(c2)C(=O)N(c2ccc(-c4sc(-c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)c(-c5ccccc5)c4-c4ccccc4)cc2)C3=O)c1
*C(=O)Nc1cccc(NC(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(C5(c6ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc6)NC(=O)c6ccccc65)cc4)cc2)C3=O)c1
*C(=O)Nc1cccc(NC(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc5ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc5c4)cc2)C3=O)c1
*C(=O)Nc1cccc(NC(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4cccc5c(N6C(=O)c7ccc(*)cc7C6=O)cccc45)cc2)C3=O)n1
*C(=O)Nc1cccc(NC(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4cccc5c(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cccc45)cc2)C3=O)c1
*C(=O)Nc1cccc(NC(=O)c2ccc3c(c2)C(=O)N(c2ccc(S(=O)(=O)c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)c1
*C(=O)Nc1cccc(NC(=O)c2ccc3c(c2)C(=O)N(c2cccc(N4C(=O)c5ccc(*)cc5C4=O)c2)C3=O)n1
*C(=O)Nc1cccc(NC(=O)c2ccc3c(c2)C(=O)N(c2cccc(P(=O)(c4ccccc4)c4cccc(N5C(=O)c6ccc(*)cc6C5=O)c4)c2)C3=O)n1
*C(=O)Nc1cccc(NC(=O)c2cccc(NC(=O)c3ccc(-c4nc5cc(*)ccc5[nH]4)cc3)c2)c1
*C(=O)Nc1cccc(Oc2ccc(C(=O)c3ccc(Oc4cccc(NC(=O)c5ccc(*)nc5)c4)cc3)cc2)c1
*C(=O)Nc1cccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4cccc(NC(=O)c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)c4)cc3)cc2)c1
*C(=O)Nc1cccc(Oc2cccc(Oc3cccc(NC(=O)c4ccc5c(c4)C(=O)N(c4cccc(Oc6cccc(Oc7cccc(N8C(=O)c9ccc(*)cc9C8=O)c7)c6C#N)c4)C5=O)c3)c2C#N)c1
*C(=O)Nc1cccc(Oc2cccc(Oc3cccc(Oc4cccc(Oc5cccc(NC(=O)c6ccc7c(c6)C(=O)N(c6ccc(Oc8cccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)c8C#N)cc6)C7=O)c5)c4C#N)c3)c2C#N)c1
*C(=O)Nc1cccc(Oc2cccc(Oc3cccc(Oc4cccc(Oc5cccc(NC(=O)c6ccc7c(c6)C(=O)N(c6cccc(Oc8cccc(Oc9cccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)c9)c8C#N)c6)C7=O)c5)c4C#N)c3)c2C#N)c1
*C(=O)Nc1cccc(P(=O)(c2ccccc2)c2cccc(NC(=O)c3ccc4c(c3)C(=O)N(c3cccc(P(=O)(c5ccccc5)c5cccc(N6C(=O)c7ccc(*)cc7C6=O)c5)c3)C4=O)c2)c1
*C(=O)Nc1cccc(S(=O)(=O)c2cccc(NC(=O)c3ccc4c(c3)C(=O)N(c3cc(-c5nnc(-c6ccccn6)o5)cc(N5C(=O)c6ccc(*)cc6C5=O)c3)C4=O)c2)c1
*C(=O)Nc1cccc(S(=O)(=O)c2cccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(-c5sc(-c6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)c(-c6ccccc6)c5-c5ccccc5)cc3)C4=O)c2)c1
*C(=O)Nc1cccc2c(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5cccc6c(N7C(=O)c8ccc(*)cc8C7=O)cccc56)cc3)C4=O)cccc12
*C(=O)Nc1cccc2cccc(N3C(=O)CC(N4C(=O)c5ccc(*)cc5C4=O)C3=O)c12
*C(=O)Nc1ccccc1NC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)Nc1nc(NC(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4cccc5c(N6C(=O)c7ccc(*)cc7C6=O)cccc45)cc2)C3=O)nc(-c2ccccc2)n1
*C(=O)OC1CC(*)N(C(=O)OCc2ccccc2)C1
*C(=O)OC1CC2CC(OC(=O)N3CCN(*)CC3)CC(C1)O2
*C(=O)OCC(C)(C)COC(=O)N1CCN(*)CC1
*C(=O)OCC(C)(C)CS(=O)(=O)CC(C)(C)COC(=O)N1CC(C)N(*)CC1C
*C(=O)OCC(C)(C)CSCC(C)(C)COC(=O)C1(C)CNC(*)(C)CN1
*C(=O)OCC(COc1ccc(/N=N/c2ccc(C#N)cc2)cc1)OC(=O)c1ccc(*)s1
*C(=O)OCC(COc1ccc(/N=N/c2ccc(C#N)cc2)cc1)OC(=O)c1ncccc1*
*C(=O)OCCCCCCCCCCCCCCCCCCCCCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)OCCCCCCCCCCCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)OCCCCCCCCCCCCOC(=O)c1ccc(NC(=O)c2ccc(N3C(=O)c4ccc(*)cc4C3=O)cc2)cc1
*C(=O)OCCCCCCCCCCCCOC(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)OCCCCCCCCCCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)OCCCCCCCCCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)OCCCCCCCCCCOC(=O)c1ccc(NC(=O)c2ccc(N3C(=O)c4ccc(*)cc4C3=O)cc2)cc1
*C(=O)OCCCCCCCCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)OCCCCCCCCCOC(=O)c1ccc(NC(=O)c2ccc(N3C(=O)c4ccc(*)cc4C3=O)cc2)cc1
*C(=O)OCCCCCCCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)OCCCCCCCCOC(=O)c1ccc(NC(=O)c2ccc(N3C(=O)c4ccc(*)cc4C3=O)cc2)cc1
*C(=O)OCCCCCCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)OCCCCCCCOC(=O)c1ccc(NC(=O)c2ccc(N3C(=O)c4ccc(*)cc4C3=O)cc2)cc1
*C(=O)OCCCCCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)OCCCCCCOC(=O)c1ccc(NC(=O)c2ccc(N3C(=O)c4ccc(*)cc4C3=O)cc2)cc1
*C(=O)OCCCCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)OCCCCCOC(=O)c1ccc(NC(=O)c2ccc(N3C(=O)c4ccc(*)cc4C3=O)cc2)cc1
*C(=O)OCCCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)OCCCCOC(=O)c1ccc(NC(=O)c2ccc(N3C(=O)c4ccc(*)cc4C3=O)cc2)cc1
*C(=O)OCCCSCCCCCCSCCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)OCCCSCCCCSCCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)OCCN(CCOC(=O)c1ccc2c(c1)C(=O)N(c1c(C)cc(Cc3cc(C)c(N4C(=O)c5ccc(*)cc5C4=O)c(C)c3)cc1C)C2=O)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*C(=O)OCCN(CCOC(=O)c1ccc2c(c1)C(=O)N(c1c(C)cc(Cc3cc(C)c(N4C(=O)c5ccc(*)cc5C4=O)c(C)c3)cc1C)C2=O)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2C#N)cc1
*C(=O)OCCN(CCOC(=O)c1ccc2c(c1)C(=O)N(c1c(C)cc(Cc3cc(C)c(N4C(=O)c5ccc(*)cc5C4=O)c(C)c3)cc1C)C2=O)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2[N+](=O)[O-])cc1
*C(=O)OCCN(CCOC(=O)c1ccc2c(c1)C(=O)N(c1c(C)cc(Cc3cc(C)c(N4C(=O)c5ccc(*)cc5C4=O)c(C)c3)cc1C)C2=O)c1ccccc1
*C(=O)OCCN(CCOC(=O)c1ccc2c(c1)C(=O)N(c1cccc(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O)c1ccc(/N=N/c2ccc(C)cc2)cc1
*C(=O)OCCN(CCOC(=O)c1ccc2c(c1)C(=O)N(c1cccc(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O)c1ccc(/N=N/c2ccc(F)cc2)cc1
*C(=O)OCCN(CCOC(=O)c1ccc2c(c1)C(=O)N(c1cccc(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*C(=O)OCCOC(=O)N1CCN(*)CC1
*C(=O)OCCOC(=O)c1ccc(*)o1
*C(=O)OCCOC(=O)c1ccc(C(=O)OCCOC(=O)N2CCN(*)CC2)cc1
*C(=O)OCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)OCCOC(=O)c1cccc(N2C(=O)c3ccc(*)cc3C2=O)c1
*C(=O)OCCOCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)OCCOCCOCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)OCCOCCOCCOCCOC(=O)c1ccc(N2C(=O)c3ccc(*)cc3C2=O)cc1
*C(=O)Oc1c(C)cc(C(C)(C)c2cc(C)c(OC(=O)c3ccc4c(c3)C(=O)N(c3ccc(-c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5C)c(C)c3)C4=O)c(C)c2)cc1C
*C(=O)Oc1c(C)cc(C(c2cc(C)c(OC(=O)c3ccc4c(c3)C(=O)N(c3c(C)cc(-c5cc(C)c(N6C(=O)c7ccc(*)cc7C6=O)c(C)c5)cc3C)C4=O)c(C)c2)(C(F)(F)F)C(F)(F)F)cc1C
*C(=O)Oc1ccc(-c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(c3ccc(-c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5C)c(C)c3)C4=O)cc2C)c(C)c1
*C(=O)Oc1ccc(-c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Oc1ccc(C(=O)Oc2cccc(OC(=O)c3ccc(OC(=O)c4ccc(*)nc4)cc3)c2)cc1
*C(=O)Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(c3ccc(-c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5C)c(C)c3)C4=O)cc2)cc1
*C(=O)Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(c3ccc(O)c(N5C(=O)c6ccc(*)cc6C5=O)c3)C4=O)cc2)cc1
*C(=O)Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(c3cccc(N5C(=O)c6ccc(*)cc6C5=O)c3)C4=O)cc2)cc1
*C(=O)Oc1ccc(C(c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(c3c(C)cc(-c5cc(C)c(N6C(=O)c7ccc(*)cc7C6=O)c(C)c5)cc3C)C4=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*C(=O)Oc1ccc(C2(c3ccc(OC(=O)c4ccc5c(c4)C(=O)N(c4ccc(-c6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6C)c(C)c4)C5=O)cc3)C3CC4CC(C3)CC2C4)cc1
*C(=O)Oc1ccc(C2(c3ccc(OC(=O)c4ccc5c(c4)C(=O)N(c4ccc(-c6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6C)c(C)c4)C5=O)cc3)CC3CC2C2CCCC32)cc1
*C(=O)Oc1ccc(C2(c3ccc(OC(=O)c4ccc5c(c4)C(=O)N(c4ccc(-c6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6C)c(C)c4)C5=O)cc3)CCC(C(C)(C)C)CC2)cc1
*C(=O)Oc1ccc(C2(c3ccc(OC(=O)c4ccc5c(c4)C(=O)N(c4ccc(-c6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6C)c(C)c4)C5=O)cc3)CCCCCCCCCCC2)cc1
*C(=O)Oc1ccc(C23CC4CC(CC(C4)C2)C3)c(OC(=O)c2ccc3c(c2)C(=O)N(c2ccc(-c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4C)c(C)c2)C3=O)c1
*C(=O)Oc1ccc(OC(=O)c2ccc3c(c2)C(=O)N(C2CCC(N4C(=O)c5ccc(*)cc5C4=O)CC2)C3=O)cc1
*C(=O)Oc1ccc(OC(=O)c2ccc3c(c2)C(=O)N(c2ccc(Cc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)c(C(C)(C)C)c1
*C(=O)Oc1ccc(OC(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)c(-c2ccccc2)c1
*C(=O)Oc1ccc(OC(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)c(C(C)(C)C)c1
*C(=O)Oc1ccc(OC(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)cc1
*C(=O)Oc1ccc(OC(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc4)cc2)C3=O)cc1
*C(=O)Oc1ccc(OC(=O)c2ccc3c(c2)C(=O)N(c2ccc(S(=O)(=O)c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)c(C(C)(C)C)c1
*C(=O)Oc1ccc(OCCCCCCCCCCCCOc2ccc(OC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Cc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Oc1ccc(OCCCCCCCCCCCCOc2ccc(OC(=O)c3ccc4c(c3)C(=O)N(c3ccc(S(=O)(=O)c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*C(=O)Oc1ccc2ccc(OC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc3)C4=O)cc2c1
*C(=O)c1cc(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(C(c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)(C(F)(F)F)C(F)(F)F)cc2)C3=O)cc(-c2ccccc2)c1
*C(=O)c1cc(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(C(c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)(C(F)(F)F)C(F)(F)F)cc2)C3=O)cc(C(C)(C)C)c1
*C(=O)c1cc(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)cc(-c2ccccc2)c1
*C(=O)c1cc(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)cc(C(C)(C)C)c1
*C(=O)c1cc(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(S(=O)(=O)c5ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc5)cc4)cc2)C3=O)cc(-c2ccccc2)c1
*C(=O)c1cc(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(S(=O)(=O)c5ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc5)cc4)cc2)C3=O)cc(C(C)(C)C)c1
*C(=O)c1cc(C(=O)c2ccc3c(c2)C(=O)N(c2cccc(N4C(=O)c5ccc(*)cc5C4=O)c2)C3=O)cc(-c2ccccc2)c1
*C(=O)c1cc(C(=O)c2ccc3c(c2)C(=O)N(c2cccc(N4C(=O)c5ccc(*)cc5C4=O)c2)C3=O)cc(C(C)(C)C)c1
*C(=O)c1cc2c(cc1Cl)C(=O)N(c1c(C)cc(C)c(N3C(=O)c4cc(*)c(Cl)cc4C3=O)c1C)C2=O
*C(=O)c1cc2c(cc1Cl)C(=O)N(c1cc(C(=O)O)cc(N3C(=O)c4cc(*)c(Cl)cc4C3=O)c1)C2=O
*C(=O)c1cc2c(cc1Cl)C(=O)N(c1cc(C(=O)OC)cc(N3C(=O)c4cc(*)c(Cl)cc4C3=O)c1)C2=O
*C(=O)c1cc2c(cc1Cl)C(=O)N(c1cc(Cl)c(N3C(=O)c4cc(*)c(Cl)cc4C3=O)cc1Cl)C2=O
*C(=O)c1cc2c(cc1Cl)C(=O)N(c1cc(Cl)cc(N3C(=O)c4cc(*)c(Cl)cc4C3=O)c1)C2=O
*C(=O)c1cc2c(cc1Cl)C(=O)N(c1cc(N3C(=O)c4cc(*)c(Cl)cc4C3=O)cc(C(C)(C)C)c1)C2=O
*C(=O)c1cc2c(cc1Cl)C(=O)N(c1cc(N3C(=O)c4cc(*)c(Cl)cc4C3=O)cc(C(F)(F)F)c1)C2=O
*C(=O)c1cc2c(cc1Cl)C(=O)N(c1cccc(N3C(=O)c4cc(*)c(Cl)cc4C3=O)c1)C2=O
*C(=O)c1ccc(-c2ccc(C(C)(C)c3ccc(-c4ccc(C(=O)c5ccc6c(c5)C(=O)N(c5ccc(Cc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)c1ccc(C(=O)N(*)c2ccccc2)c(Sc2ccccc2)c1
*C(=O)c1ccc(C(=O)N(*)c2ccccc2)cc1
*C(=O)c1ccc(C(=O)N2CC(C)N(*)CC2C)cc1
*C(=O)c1ccc(C(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(Oc5ccc(*)s5)cc4)cc3)s2)cc1
*C(=O)c1ccc(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(C(=O)c4ccc(C(=O)c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc4)cc2)C3=O)cc1
*C(=O)c1ccc(C(C)(CC)c2ccc(C(=O)N3CCN(*)CC3)cc2)cc1
*C(=O)c1ccc(N(*)C)cc1
*C(=O)c1ccc(N(*)CCC)cc1
*C(=O)c1ccc(N(*)CCCC)cc1
*C(=O)c1ccc(N(*)CCCCC)cc1
*C(=O)c1ccc(N(*)CCCCCCC)cc1
*C(=O)c1ccc(N(*)CCCCCCCC)cc1
*C(=O)c1ccc(Oc2c(C)cc(-c3cc(C)c(Oc4ccc(C(=O)c5ccc6nc(*)ccc6c5)cc4)c(C)c3)cc2C)cc1
*C(=O)c1ccc(Oc2c(C)cc(-c3cc(C)c(Oc4ccc(C(=O)c5ccc6nc(*)ccc6c5)cc4)c(C)c3C)c(C)c2C)cc1
*C(=O)c1ccc(Oc2ccc(-c3ccc(Oc4ccc(C(=O)c5ccc6c(c5)C(=O)N(c5ccc(Cc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)c1ccc(Oc2ccc(-c3ccc(Oc4ccc(C(=O)c5ccc6c(c5)C(=O)N(c5ccc(N7C(=O)c8ccc(*)cc8C7=O)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)c1ccc(Oc2ccc(-c3ccc(Oc4ccc(C(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)c1ccc(Oc2ccc(-c3ccc(Oc4ccc(C(=O)c5ccc6nc(*)ccc6c5)cc4)cc3)cc2)cc1
*C(=O)c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(*)s4)cc3)cc2)cc1
*C(=O)c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(*)s4)cc3)cc2)s1
*C(=O)c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(C(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(*)s5)cc4)c4ccccc4-c4ccccc43)cc2)cc1
*C(=O)c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(C(=O)c6ccc7c(c6)C(=O)N(c6ccc(Cc8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)cc6)C7=O)cc5)cc4)OC(=O)c4ccccc43)cc2)cc1
*C(=O)c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(C(=O)c6ccc7c(c6)C(=O)N(c6ccc(Oc8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)cc6)C7=O)cc5)cc4)OC(=O)c4ccccc43)cc2)cc1
*C(=O)c1ccc(Oc2ccc(N3C(=O)c4ccc(*)cc4C3=O)cc2)cc1
*C(=O)c1ccc(Oc2ccc(Oc3ccc(C(=O)c4ccc5c(c4)C(=O)N(c4ccc(Cc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc4)C5=O)cc3)cc2)cc1
*C(=O)c1ccc(Oc2ccc(Oc3ccc(C(=O)c4ccc5c(c4)C(=O)N(c4ccc(N6C(=O)c7ccc(*)cc7C6=O)cc4)C5=O)cc3)cc2)cc1
*C(=O)c1ccc(Oc2ccc(Oc3ccc(C(=O)c4ccc5c(c4)C(=O)N(c4ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc4)C5=O)cc3)cc2)cc1
*C(=O)c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(C(=O)c5ccc6c(c5)C(=O)N(c5ccc(Cc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(C(=O)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc5)C6=O)cc4)cc3)cc2)cc1
*C(=O)c1ccc2c(c1)/C(=N\c1ccc(Oc3ccc(C(c4ccc(Oc5ccc(/N=C6/OC(=O)c7ccc(*)cc76)cc5)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc1)OC2=O
*C(=O)c1ccc2c(c1)/C(=N\c1ccc(Oc3cccc(/N=C4/OC(=O)c5ccc(*)cc54)c3)cc1)OC2=O
*C(=O)c1ccc2c(c1)/C(=N\c1cccc(Oc3cccc(Oc4cccc(/N=C5/OC(=O)c6ccc(*)cc65)c4)c3)c1)OC2=O
*C(=O)c1ccc2c(c1)C(=O)N(C1CCC(CC3CCC(N4C(=O)c5ccc(*)cc5C4=O)C(C)C3)CC1C)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(C1CCC(CC3CCC(N4C(=O)c5ccc(*)cc5C4=O)CC3)CC1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(CC1(C)CC(N3C(=O)c4ccc(*)cc4C3=O)CC(C)(C)C1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1c(-c3ccccc3)cc(-c3ccc(-c4cc(-c5ccccc5)c(N5C(=O)c6ccc(*)cc6C5=O)c(-c5ccccc5)c4)cc3)cc1-c1ccccc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1c(C(C)C)cc(Cc3cc(C(C)C)c(N4C(=O)c5ccc(*)cc5C4=O)c(C(C)C)c3)cc1C(C)C)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1c(C)cc(-c3cc(C)c(N4C(=O)c5ccc(*)cc5C4=O)c(C)c3)cc1C)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1c(C)cc(C(c3cc(C(F)(F)F)cc(C(F)(F)F)c3)c3cc(C)c(N4C(=O)c5ccc(*)cc5C4=O)c(C)c3)cc1C)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1c(C)cc(C(c3cc(C)c(N4C(=O)c5ccc(*)cc5C4=O)c(C)c3)c3cccc4ccccc34)cc1C)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1c(C)cc(C(c3cccc(C(F)(F)F)c3)c3cc(C)c(N4C(=O)c5ccc(*)cc5C4=O)c(C)c3)cc1C)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1c(C)cc(C)c(N3C(=O)c4ccc(*)cc4C3=O)c1C)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1c(C)cc(Cc3cc(C)c(N4C(=O)c5ccc(*)cc5C4=O)c(C)c3)cc1C)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1c(CC)cc(Cc3cc(CC)c(N4C(=O)c5ccc(*)cc5C4=O)c(CC)c3)cc1CC)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cc(-c3nc4ccccc4[nH]3)cc(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cc(-c3nc4ccccc4oc3=O)cc(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cc(/C=N/C(=S)Nc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)ccc1Cl)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cc(Br)c(Oc3c(Br)cc(N4C(=O)c5ccc(*)cc5C4=O)cc3Br)c(Br)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cc(C(=O)O)cc(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cc(C(=O)c3ccccc3)cc(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cc(C(c3ccc(O)c(N4C(=O)c5ccc(*)cc5C4=O)c3)(C(F)(F)F)C(F)(F)F)ccc1O)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cc(N3C(=O)c4ccc(*)cc4C3=O)c(C)cc1C)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cc(N3C(=O)c4ccc(*)cc4C3=O)c(O)cc1O)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cc(N3C(=O)c4ccc(*)cc4C3=O)cc(C(F)(F)F)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cc(OCCCC)cc(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cc(OCCCCCCCC)cc(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cc(OCCCCCCCCCCCC)cc(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cc(OCCCCCCCCCCCCCCCC)cc(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cc(Oc3c(C)cc(-c4cc(C)c(Oc5cc(N6C(=O)c7ccc(*)cc7C6=O)cc(C(F)(F)F)c5)c(C)c4)cc3C)cc(C(F)(F)F)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(-c3c(-c4ccc(-c5ccccc5)cc4)cc(-c4ccc(OCCCCCCCCCCOc5ccc(-c6cc(-c7ccc(-c8ccccc8)cc7)c(-c7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)c(-c7ccc(-c8ccccc8)cc7)c6)cc5)cc4)cc3-c3ccc(-c4ccccc4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(-c3c(-c4ccc(-c5ccccc5)cc4)cc(-c4ccc(OCCCCCCOc5ccc(-c6cc(-c7ccc(-c8ccccc8)cc7)c(-c7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)c(-c7ccc(-c8ccccc8)cc7)c6)cc5)cc4)cc3-c3ccc(-c4ccccc4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(-c3cc(-c4ccc(OC)cc4)cc(-c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)n3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(-c3cc(-c4ccc(OCCCC#N)cc4)cc(-c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)n3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(-c3cc(C(C)(C)C)c(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)c(C(C)(C)C)c3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(-c3cc(C(C)(C)C)c(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4C(F)(F)F)c(C(C)(C)C)c3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(N4C(=O)c5ccc(*)cc5C4=O)c(C)c3)cc1C)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(N4C(=O)c5ccc(*)cc5C4=O)c(OCCCCCCOc4ccc(/C=C/c5ccc(F)cc5)cc4)c3)cc1OCCCCCCOc1ccc(/C=C/c3ccc(F)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3C)c(C)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(NC(=O)c4cccc(C(=O)Nc5ccc(-c6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc5)c4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(Oc4ccc(C5(C)CC(C)(C)c6ccc(Oc7ccc(-c8ccc(N9C(=O)c%10ccc(*)cc%10C9=O)cc8)cc7C(F)(F)F)cc65)cc4)c(C(F)(F)F)c3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(-c3ccccc3N3C(=O)c4ccc(*)cc4C3=O)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(-c3nc(-c4ccccc4)nc(-c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)n3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(/C=C/c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(/C=C/c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3S(=O)(=O)O[Na])c(S(=O)(=O)O[Na])c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C(=O)N(C)c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C(=O)Nc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C(=O)Nc3ccc(Oc4ccc(NC(=O)c5ccc(NC(=O)Nc6ccc(Cc7ccc(NC(=O)Nc8ccc(C(=O)Nc9ccc(Oc%10ccc(NC(=O)c%11ccc(N%12C(=O)c%13ccc(*)cc%13C%12=O)cc%11)cc%10)cc9)cc8)cc7)cc6)cc5)cc4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C(=O)Nc3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C(=O)c3ccc(C(=O)c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C(=O)c3ccc(Cc4ccc(C(=O)c5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C(=O)c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C(=O)c3cccc(C(=O)c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)c3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C(=O)c3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C(=O)c3ccccc3N3C(=O)c4ccc(*)cc4C3=O)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C(C)(C)c3ccc(C(C)(C)c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C(C)(C)c3cccc(C(C)(C)c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)c3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C(c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)(C(F)(F)F)C(F)(F)F)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C(c3ccccc3)(c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)C(F)(F)F)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C(c3ccccc3)c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C)c(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C3(C)CC(C)(C)c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc43)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(C3(c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)c4ccccc4-c4ccccc43)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(CP(=O)(OCC)OCC)c(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(CP(=O)(OCCCl)OCCCl)c(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Cc3ccc(C(=O)c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Cc3ccc(C(=O)c4cccc(N5C(=O)c6ccc(*)cc6C5=O)c4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Cc3ccc(Cc4ccc(Cc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Cc3ccc(Cc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Cc3ccc(Cc4cccc(N5C(=O)c6ccc(*)cc6C5=O)c4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Cc3ccc(N4C(=O)c5ccc(*)cc5C4=O)c(C(C)(C)C)c3)cc1C(C)(C)C)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Cc3ccc(N4C(=O)c5ccc(*)cc5C4=O)c(C)c3)cc1C)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Cc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Cc3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Cc3ccccc3N3C(=O)c4ccc(*)cc4C3=O)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Cl)c(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(N(c3ccc(C#N)cc3)c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(N(c3ccccc3)c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(N3C(=O)c4ccc(*)cc4C3=O)c(S(=O)(=O)O[Na])c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(N3C(=O)c4ccc(*)cc4C3=O)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(NC(=O)c3cccc(C(=O)Nc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)c3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(OCC(C)(C)COc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(OCCOCCOCCOc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(OCCOCCOc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(OCCOc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3c(C)cc(C(c4cc(C)c(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)c(C)c4)c4cccc5ccccc45)cc3C)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3c(C)cc(Cc4cc(C)c(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)c(C)c4)cc3C)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3cc(C(C)(C)C)c(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc3C(C)(C)C)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3cc4ccccc4cc3Oc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4ccc(C(=O)c5ccc(C(=O)c6ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc6)cc5)cc4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4ccc(C(=O)c5ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc5)cc4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4ccc(Oc5ccc(C(=O)c6ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc6)cc5)cc4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4ccc5cc(C(=O)c6ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc6)ccc5c4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4cccc(C(=O)c5ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc5)c4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4cccc(C(=O)c5ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc5)n4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4cccc(C(=O)c5ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6C(F)(F)F)cc5)n4)cc3)c(C(F)(F)F)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(C)(C)c4ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(C)(C)c4ccccc4)cc3)c(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(c4ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(c4ccc(Oc5ccc(N6C(=O)c7ccc(*)cc7C6=O)cc5C(F)(F)F)cc4)(C(F)(F)F)C(F)(F)F)cc3)c(C(F)(F)F)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3Br)c(Br)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(NC(=O)c4cccc(C(=O)Nc5ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc5)c4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4C(F)(F)F)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(S(=O)(=O)c4ccc(C)cc4)cc3)c(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3cccc(-c4cc(-c5ccccc5)cc(-c5cccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)c5)n4)c3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3cccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)c3C#N)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccccc3N3C(=O)c4ccc(*)cc4C3=O)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccccc3Oc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3C(F)(F)F)c(C(F)(F)F)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(S(=O)(=O)c3ccc(NC(=O)c4cccc(C(=O)Nc5ccc(S(=O)(=O)c6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc5)c4)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Sc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Sc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cn3)nc1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc3c(c1)C(=O)c1cc(N4C(=O)c5ccc(*)cc5C4=O)ccc1-3)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccc3c(c1)Cc1cc(N4C(=O)c5ccc(*)cc5C4=O)ccc1-3)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(C#Cc3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(C(=O)N(C)c3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(N4C(=O)c5ccc(*)cc5C4=O)cc3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(C(=O)c3c(C)cc(C)c(N4C(=O)c5ccc(*)cc5C4=O)c3C)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(C(=O)c3ccc(C(=O)c4cccc(N5C(=O)c6ccc(*)cc6C5=O)c4)cc3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(C(=O)c3ccc(Cc4ccc(C(=O)c5cccc(N6C(=O)c7ccc(*)cc7C6=O)c5)cc4)cc3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(C(=O)c3ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(C(=O)c3ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4C(F)(F)F)cc3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(C(=O)c3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(C(=O)c3ccccc3N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(C(O)c3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(C(O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)C)c3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(C(c3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)(C(F)(F)F)C(F)(F)F)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(C(c3ccccc3)(c3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)C(F)(F)F)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(Cc3ccc(C(=O)c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(Cc3ccc(C(=O)c4cccc(N5C(=O)c6ccc(*)cc6C5=O)c4)cc3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(Cc3ccc(Cc4ccc(Cc5cccc(N6C(=O)c7ccc(*)cc7C6=O)c5)cc4)cc3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(Cc3ccc(Cc4cccc(N5C(=O)c6ccc(*)cc6C5=O)c4)cc3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(Cc3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(Cc3ccccc3N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(N3C(=O)c4ccc(*)cc4C3=O)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(NC(=O)c3cccc(C(=O)Nc4cccc(N5C(=O)c6ccc(*)cc6C5=O)c4)c3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(Nc3nc(Nc4cccc(N5C(=O)c6ccc(*)cc6C5=O)c4)nc(-c4ccccc4)n3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(Nc3nc(Nc4cccc(N5C(=O)c6ccc(*)cc6C5=O)c4)nc(N(C)C)n3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(Nc3nc(Nc4cccc(N5C(=O)c6ccc(*)cc6C5=O)c4)nc(N(c4ccccc4)c4ccccc4)n3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(Nc3ncnc(Nc4cccc(N5C(=O)c6ccc(*)cc6C5=O)c4)n3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(OCCOCCOc3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(OCCOc3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(OP(=O)(Oc3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)c3ccccc3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(Oc3cccc(Oc4cccc(N5C(=O)c6ccc(*)cc6C5=O)c4)c3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(P(=O)(c3ccc(C(F)(F)F)cc3)c3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(P(=O)(c3ccc(Oc4ccc(C56CC7CC(CC(C7)C5)C6)cc4)cc3)c3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(P(=O)(c3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)c3cc(C(F)(F)F)cc(C(F)(F)F)c3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(P(=O)(c3ccccc3)c3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1cccc(S(=O)(=O)c3cccc(N4C(=O)c5ccc(*)cc5C4=O)c3)c1)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccccc1C(=O)c1ccccc1N1C(=O)c3ccc(*)cc3C1=O)C2=O
*C(=O)c1ccc2c(c1)C(=O)N(c1ccccc1Cc1ccccc1N1C(=O)c3ccc(*)cc3C1=O)C2=O
*C(=O)c1ccc2c(c1OCc1ccccc1[N+](=O)[O-])C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(*)c(OCc6ccccc6[N+](=O)[O-])c5C4=O)cc3)cc1)C2=O
*C(=O)c1ccc2nc(-c3ccc(-c4cnc5cc(*)ccc5n4)cc3)cnc2c1
*C(=O)c1ccc2nc(-c3ccc(-c4nc5ccc(*)cc5nc4-c4ccccc4)cc3)c(-c3ccccc3)nc2c1
*C(=O)c1ccc2nc(-c3ccccc3)c(-c3ccc(-c4nc5cc(*)ccc5nc4-c4ccccc4)cc3)nc2c1
*C(=O)c1ccc2nc(-c3ccccc3)c(-c3ccc(-c4nc5ccc(*)cc5nc4-c4ccccc4)cc3)nc2c1
*C(=O)c1ccc2nc(-c3ccccc3)c(-c3ccc(N4C(=O)c5ccc(C(=O)c6ccc7c(c6)C(=O)N(c6ccc(-c8nc9cc(*)ccc9nc8-c8ccccc8)cc6)C7=O)cc5C4=O)cc3)nc2c1
*C(=O)c1ccc2nc(-c3ccccc3)c(-c3ccc(Oc4ccc(-c5nc6cc(*)ccc6nc5-c5ccccc5)cc4)cc3)nc2c1
*C(=O)c1ccc2ncc(-c3ccc(-c4cnc5ccc(*)cc5n4)cc3)nc2c1
*C(=O)c1cccc(C(=O)N(*)c2ccccc2)c1
*C(=O)c1cccc(C(=O)N2CCN(*)CC2)c1
*C(=O)c1cccc(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(C(c4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)(C(F)(F)F)C(F)(F)F)cc2)C3=O)c1
*C(=O)c1cccc(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(N5C(=O)c6ccc(*)cc6C5=O)cc4)cc2)C3=O)c1
*C(=O)c1cccc(C(=O)c2ccc3c(c2)C(=O)N(c2ccc(Oc4ccc(S(=O)(=O)c5ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc5)cc4)cc2)C3=O)c1
*C(=O)c1cccc(C(=O)c2ccc3c(c2)C(=O)N(c2cccc(N4C(=O)c5ccc(*)cc5C4=O)c2)C3=O)c1
*C(=O)c1ccccc1-c1ccccc1C(=O)N1CCN(*)CC1
*C(=S)Nc1ccc(Oc2ccc(NC(=S)N3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*C(=S)Nc1ccc(Oc2ccc(NC(=S)N3C(=O)c4ccc(C(c5ccc6c(c5)C(=O)N(*)C6=O)(C(F)(F)F)C(F)(F)F)cc4C3=O)cc2)cc1
*C(=S)Nc1ccc(Oc2ccc(NC(=S)n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2)cc1
*C(C(=O)OC)C(*)C(=O)OC(C)(C)C
*C(C)C(*)C(=O)OC(C)C
*C(C)C(*)C(=O)OC12CC3CC(C)(CC(C)(C3)C1)C2
*C(C)C(*)C(=O)OC12CC3CC(CC(C3)C1)C2
*C(C)C(*)C(=O)OCC
*C(C)C(*)C(=O)OCCC
*C(C)C(*)C(=O)OCCCC
*C(CC(C)C)C(=O)Nc1ccc(Oc2ccc(NC(=O)NCC3(C)CC(NC(=O)Nc4ccc(Oc5ccc(NC(=O)C(CC(C)C)n6c(=O)c7cc8c(=O)n(*)c(=O)c8cc7c6=O)cc5)cc4)CC(C)(C)C3)cc2)cc1
*C(CC(C)C)C(=O)Nc1ccc(Oc2ccc(NC(=O)Nc3ccc(Cc4ccc(NC(=O)Nc5ccc(Oc6ccc(NC(=O)C(CC(C)C)n7c(=O)c8cc9c(=O)n(*)c(=O)c9cc8c7=O)cc6)cc5)cc4)cc3)cc2)cc1
*C(CC)COC(=O)NCCCCCCNC(=O)OCC(CC)n1c(=O)c2cc3c(=O)n(*)c(=O)c3cc2c1=O
*C(F)(F)C(*)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*C(F)(F)C(*)(F)C(F)(F)F
*C(F)(F)C(*)(F)Cl
*C(F)(F)C(*)(F)OC(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*C(F)(F)C(*)(F)OC(F)(F)C(F)(F)F
*C(F)(F)C(*)(F)OC(F)(F)F
*C(F)(F)C(*)(F)OC(F)C(F)F
*C(F)(F)C(*)(F)c1c(F)c(F)c(F)c(F)c1F
*C(F)(F)C(*)(F)c1ccccc1
*C(F)(F)C(*)(OC(F)(F)F)OC(F)(F)F
*C(F)(F)C(=O)NCCCCCCNS(*)(=O)=O
*C(F)(F)C1(*)OC(F)(C(F)(F)F)C(F)(C(F)(F)F)O1
*C(F)(F)C1(*)OC(F)(F)C(F)(C(F)(F)F)O1
*C(F)(F)C1(*)OC(F)(F)C(F)(C(F)(F)F)O1
*C(F)(F)C1(*)OC(F)(F)C(F)(F)O1
*C(F)(F)C1(F)C(F)(F)C(*)(F)C(F)(F)C(F)(Cl)C1(F)F
*C(F)(F)C1(F)C(F)(F)C(*)(F)C(F)(F)C(F)(F)C(F)(F)C1(F)F
*C(F)(F)C1(F)C(F)(F)C(*)(F)C(F)(F)C(F)(F)C1(F)F
*C(F)(F)C1(F)C(F)(F)C(*)(F)C(F)(F)C1(F)F
*C(F)(F)C1(F)C(F)(F)C(*)(F)C1(F)F
*C(F)C(*)(F)F
*C(F)C(*)(F)OC(F)(F)F
*C(F)C(*)C(F)(F)F
*C(OC(F)(F)F)C(*)(F)F
*C([2H])([2H])C(*)(C(=O)OC([2H])([2H])C([2H])([2H])[2H])C([2H])([2H])[2H]
*C([2H])([2H])C(*)(C(=O)OC([2H])([2H])[2H])C([2H])([2H])[2H]
*C([2H])([2H])C(*)([2H])C([2H])([2H])[2H]
*C([2H])([2H])C(*)([2H])C([2H])=C([2H])[2H]
*C([2H])([2H])C(*)([2H])Cl
*C([2H])([2H])C(*)([2H])c1c([2H])c([2H])c([2H])c([2H])c1[2H]
*C(c1ccc(N(C)C)cc1)C(*)c1ccc([N+](=O)[O-])cc1
*C*
*C/C(C)=C/C(C)S(*)(=O)=O
*C/C=C(\C)C(C)S(*)(=O)=O
*C/C=C(\C)CS(*)(=O)=O
*C/C=C/C(C)S(*)(=O)=O
*C/C=C/C(CC)S(*)(=O)=O
*C/C=C/COC(=O)C(Cc1ccccc1)NC(=O)/C=C/C(=O)NC(Cc1ccccc1)C(=O)O*
*C/C=C/COC(=O)C(Cc1ccccc1)NC(=O)CCCCC(=O)NC(Cc1ccccc1)C(=O)O*
*C/C=C/COC(=O)C(Cc1ccccc1)NC(=O)CCCCCCCCC(=O)NC(Cc1ccccc1)C(=O)O*
*C/C=C/COC(=O)CCCCCCCCC(=O)O*
*C/C=C/COC(=O)CCCCCCCCC(=O)O*
*C/C=C/COC(=O)NCCCCCCNC(=O)O*
*C/C=C/COC(=O)NCCCCCCNC(=O)O*
*C/C=C/CS(*)(=O)=O
*C/C=C/C[Si](*)(C)C
*C/C=C/C[Si](*)(C)CCCOc1ccc(-c2ccccc2)cc1
*C/C=C/C[Si](*)(C)CCCOc1ccc2ccccc2c1
*C/C=C/C[Si](*)(C)CCCOc1ccccc1
*C/C=C/C[Si](*)(C)c1ccccc1
*C/C=C/C[Si](*)(C=C)C=C
*C/C=C/C[Si](*)(CCCOc1ccccc1)c1ccccc1
*C/C=C/C[Si](*)(c1ccccc1)c1ccccc1
*C/C=C/N(/C=C/CC1CC(=O)N(c2ccc(Cc3ccc(N4C(=O)CC(*)C4=O)cc3)cc2)C1=O)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*C/C=C/c1cc(C(C)(C)c2ccc(O)c(/C=C/CC3CC(=O)N(c4ccc(Cc5ccc(N6C(=O)CC(*)C6=O)cc5)cc4)C3=O)c2)ccc1O
*C1(C)CC(*)(C)C(=O)N(C)C1=O
*C1(F)OC(C(F)(F)F)(C(F)(F)Cl)OC1(*)F
*C1=C(c2ccc(-c3ccc4c(c3)C(CCCCCC)(CCCCCC)c3cc(-c5ccc(*)cc5)ccc3-4)cc2)C(=O)N(CCCC)C1=O
*C1=C(c2ccc(-c3ccc4c(c3)C(CCCCCC)(CCCCCC)c3cc(-c5ccc(*)cc5)ccc3-4)cc2)C(=O)N(CCCCCCCC)C1=O
*C1=C(c2ccc(-c3ccc4c(c3)C(CCCCCC)(CCCCCC)c3cc(-c5ccc(*)cc5)ccc3-4)cc2)C(=O)N(CCCCCCCCCCCC)C1=O
*C1C(*)C2CC1C(C(=O)OCC13CC4CC(CC(C4)C1)C3)C2C(=O)OCC12CC3CC(CC(C3)C1)C2
*C1C(*)C2CC1C(C(=O)OCC1CC3CCC1C3)C2C(=O)OCC1CC2CCC1C2
*C1C(*)C2CC1C(C(=O)OCCC13CC4CC(CC(C4)C1)C3)C2C(=O)OCCC12CC3CC(CC(C3)C1)C2
*C1C(*)C2CC1C(C(=O)OCCC1CC3CCC1C3)C2C(=O)OCCC1CC2CCC1C2
*C1C(*)C2CC1C(C(=O)OCCCc1ccccc1)C2C(=O)OCCCc1ccccc1
*C1C(*)C2CC1C(C(=O)OCCc1ccccc1)C2C(=O)OCCc1ccccc1
*C1C(*)C2CC1C(C(=O)OCc1ccccc1)C2C(=O)OCc1ccccc1
*C1C(*)C2CC1C(COCc1ccccc1)C2COCc1ccccc1
*C1C(*)C2CC1C1C=CCC12
*C1C(=O)N(C(C)(C)C)C(=O)C1*
*C1C(=O)N(C(C)CC)C(=O)C1*
*C1C(=O)N(C2CCCCC2)C(=O)C1*
*C1C(=O)N(CCCCCCCCOc2ccc(-c3ccc(C#N)cc3)cc2)C(=O)C1*
*C1C(=O)N(CCCCCCCOc2ccc(-c3ccc(C#N)cc3)cc2)C(=O)C1*
*C1C(=O)N(CCCCCCOc2ccc(-c3ccc(C#N)cc3)cc2)C(=O)C1*
*C1C(=O)N(CCCCCOc2ccc(-c3ccc(C#N)cc3)cc2)C(=O)C1*
*C1C(=O)N(CCCCOc2ccc(-c3ccc(C#N)cc3)cc2)C(=O)C1*
*C1C(=O)N(CCCOc2ccc(-c3ccc(C#N)cc3)cc2)C(=O)C1*
*C1C(=O)N(CCOc2ccc(-c3ccc(C#N)cc3)cc2)C(=O)C1*
*C1C(=O)N(c2c(CC)cccc2CC)C(=O)C1*
*C1C(=O)N(c2cc(Br)c(O[Si](C)(C)C(C)(C)C)c(Br)c2)C(=O)C1*
*C1C(=O)N(c2cc(Br)c(O[Si](C)(C)C)c(Br)c2)C(=O)C1*
*C1C(=O)N(c2cc(Br)c(O[Si](CC)(CC)CC)c(Br)c2)C(=O)C1*
*C1C(=O)N(c2cc(Br)c(O[Si](c3ccccc3)(c3ccccc3)c3ccccc3)c(Br)c2)C(=O)C1*
*C1C(=O)N(c2ccc(O[Si](C)(C)C(C)(C)C)cc2)C(=O)C1*
*C1C(=O)N(c2ccc(O[Si](C)(C)C)cc2)C(=O)C1*
*C1C(=O)N(c2ccc(O[Si](CC)(CC)CC)cc2)C(=O)C1*
*C1C(=O)N(c2ccc(O[Si](c3ccccc3)(c3ccccc3)c3ccccc3)cc2)C(=O)C1*
*C1C(=O)N(c2ccccc2C(=O)OC)C(=O)C1*
*C1C(=O)N(c2ccccc2C(=O)OCC)C(=O)C1*
*C1C(=O)N(c2ccccc2C(=O)OCCCC)C(=O)C1*
*C1C(=O)N(c2ccccc2C)C(=O)C1*
*C1C(=O)OC(=O)C1C1C2CC(C(=O)OC(C)CC)C(C2)C1*
*C1C(=O)OC(=O)C1C1C2CC(C(=O)OC3CCCCC3)C(C2)C1*
*C1C(=O)OC(=O)C1C1C2CC(C(=O)OCCOCC)C(C2)C1*
*C1C(=O)OC(=O)C1C1C2CC(COCCC)C(C2)C1*
*C1C2C/C(=C\C)C(C2)C1*
*C1C2CC(C(=O)OCCc3ccc(N(c4ccccc4)c4cccc(C)c4)cc3)C(C2)C1*
*C1C2CC(C(=O)OCCc3ccc(N(c4ccccc4)c4cccc5ccccc45)cc3)C(C2)C1*
*C1C2CC(C(=O)OCCc3ccc(N(c4ccccc4)c4ccccc4)cc3)C(C2)C1*
*C1C2CC(C)C(C2)C1*
*C1C2CC(C1*)C([Si](C)(C)C)C2
*C1C2CC(CCCCCCCCC(=O)OCC)C(C2)C1*
*C1C2CC(CCCCc3ccccc3)C(C2)C1*
*C1C2CCC(C2)C1*
*C1C2CCC(C2)C1S(*)(=O)=O
*C1C=C(CCCCCC)C(*)S1
*C1C=CC(*)CC1
*C1CC(*)(C#N)C1
*C1CC2CC1C(*)C2CCCC
*C1CC2CC1C(*)C2CCCCCC
*C1CCC(*)C1
*C1CCC(CC2CCC(N3C(=O)C4C5C=CC(C6C(=O)N(*)C(=O)C56)C4C3=O)C(C)C2)CC1C
*C1CCC(CC2CCC(N3C(=O)C4C5C=CC(C6C(=O)N(*)C(=O)C56)C4C3=O)CC2)CC1
*C1CCC(CC2CCC(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)C(C)C2)CC1C
*C1CCC(CC2CCC(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)CC2)CC1
*C1CCC(CC2CCC(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)C(C)C2)CC1C
*C1CCC(CC2CCC(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)CC2)CC1
*C1CCC(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)CC1
*C1CCC(N2C(=O)c3ccc(S(=O)(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)CC1
*C1CCCC(S(*)(=O)=O)C1
*C1CCCCC1S(*)(=O)=O
*C1CCOC1*
*C1Cc2ccccc2C1*
*C1OC(C(F)(F)F)(C(F)(F)F)OC1(*)F
*C1OCCCC1C1C(=O)OC(=O)C1*
*C1OCOC(C(*)(F)F)C1(F)F
*C1Oc2ccccc2C1*
*C1c2cccc3cccc(c23)C1*
*CC#C/C(C#CCOC(=O)CCCCCCCCC(=O)O*)=C\c1ccc(C#Cc2ccc(C=C(C#Cc3ccccc3)C#Cc3ccccc3)cc2)cc1
*CC#C/C(C#CCOC(=O)CCCCCCCCC(=O)O*)=C\c1ccc(C#Cc2ccc([N+](=O)[O-])cc2)cc1
*CC#C/C(C#CCOC(=O)CCCCCCCCC(=O)O*)=C\c1ccc(C#Cc2ccccc2)cc1
*CC#CC#CCOC(=O)c1ccc(C(=O)O*)cc1
*CC#CC#CCOc1ccc(C(=O)OCCN(CCOC(=O)c2ccc(O*)cc2)c2ccc(/N=N/c3ccc(/N=N/c4cc(C)c(C#N)c(C)c4)cc3)cc2)cc1
*CC#CC#CCOc1ccc(C(=O)OCCN(CCOC(=O)c2ccc(O*)cc2)c2ccc(/N=N/c3ccc(/N=N/c4cc(C)c([N+](=O)[O-])c(C)c4)cc3)cc2)cc1
*CC#CC#CCOc1ccc(C(=O)OCCN(CCOC(=O)c2ccc(O*)cc2)c2ccc(/N=N/c3ccc(C#N)cc3)cc2)cc1
*CC#CC#CCOc1ccc(C(=O)OCCN(CCOC(=O)c2ccc(O*)cc2)c2ccc(/N=N/c3ccc([N+](=O)[O-])cc3)cc2)cc1
*CC#CC#CCOc1ccc(C(=O)OCCN(CCOC(=O)c2ccc(O*)cc2)c2ccc([N+](=O)[O-])cc2)cc1
*CC#CC#CCOc1cccc(C(=O)OCCN(CCOC(=O)c2cccc(O*)c2)c2ccc(/N=N/c3ccc(C#N)cc3)cc2)c1
*CC#CCOC(=O)CCCCCCC(=O)O*
*CC#CCOC(=O)CCCCCCCCC(=O)O*
*CC#CCOC(=O)CCCCCCCCCCC(=O)O*
*CC#CCOC(=O)NCCCCCCNC(=O)O*
*CC(*)(C#N)C(=O)OC
*CC(*)(C#N)C(=O)OC(C)C
*CC(*)(C#N)C(=O)OCC
*CC(*)(C#N)C(=O)OCC(C)C
*CC(*)(C#N)C(=O)OCCCC
*CC(*)(C#N)C(=O)OCCCCCC
*CC(*)(C#N)C(=O)OCCCCCCC
*CC(*)(C(=O)OC)c1ccccc1
*CC(*)(C)C
*CC(*)(C)C#N
*CC(*)(C)C(=O)NC(=O)OC(C)COc1c(Br)cc(S(=O)(=O)c2cc(Br)c(OCC(C)O)c(Br)c2)cc1Br
*CC(*)(C)C(=O)NC(=O)OC(C)COc1ccc(S(=O)(=O)c2ccc(OCC(C)O)cc2)cc1
*CC(*)(C)C(=O)NC(=O)OCCOc1c(Br)cc(S(=O)(=O)c2cc(Br)c(OCCO)c(Br)c2)cc1Br
*CC(*)(C)C(=O)NC(=O)OCCOc1ccc(S(=O)(=O)c2ccc(OCCO)cc2)cc1
*CC(*)(C)C(=O)NC(=O)Oc1c(Br)cc(S(=O)(=O)c2cc(Br)c(O)c(Br)c2)cc1Br
*CC(*)(C)C(=O)NC(=O)Oc1ccc(S(=O)(=O)c2ccc(O)cc2)cc1
*CC(*)(C)C(=O)NC(C)c1cccc2ccccc12
*CC(*)(C)C(=O)NC(C)c1ccccc1
*CC(*)(C)C(=O)NC1C(O)OC(CO)C(O)C1O
*CC(*)(C)C(=O)Nc1ccc(C(=O)/C=C/c2ccc(OC)cc2)cc1
*CC(*)(C)C(=O)Nc1cccc(-c2nnc(-c3ccccc3)o2)c1
*CC(*)(C)C(=O)O
*CC(*)(C)C(=O)OC
*CC(*)(C)C(=O)OC(C(F)(F)F)C(F)(F)F
*CC(*)(C)C(=O)OC(C)(C)C
*CC(*)(C)C(=O)OC(C)C
*CC(*)(C)C(=O)OC(C)C(C)(C)C
*CC(*)(C)C(=O)OC(C)CC
*CC(*)(C)C(=O)OC(COc1cc2ccccc2c2ccccc12)COc1cc2ccccc2c2ccccc12
*CC(*)(C)C(=O)OC(COc1ccc(-c2ccccc2)cc1)COc1ccc(-c2ccccc2)cc1
*CC(*)(C)C(=O)OC(COc1cccc2ccccc12)COc1cccc2ccccc12
*CC(*)(C)C(=O)OC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)(C)C(=O)OC(OCC)C(F)(F)F
*CC(*)(C)C(=O)OC1(C)C2CC3CC(C2)CC1C3
*CC(*)(C)C(=O)OC1(C)CCCN(c2ccc(/N=N/c3ccc(/C=C(\C#N)S(C)(=O)=O)cc3)cc2)C1
*CC(*)(C)C(=O)OC1(C)CCOC(=O)C1
*CC(*)(C)C(=O)OC12CC3CC(C)(CC(C)(C3)C1)C2
*CC(*)(C)C(=O)OC12CC3CC(CC(C3)C1)C2
*CC(*)(C)C(=O)OC1CC(C)CC(C)(C)C1
*CC(*)(C)C(=O)OC1CC(C)CCC1C(C)C
*CC(*)(C)C(=O)OC1CC2CC1C1C3CCC(C3)C21
*CC(*)(C)C(=O)OC1CC2CCC1(C)C2(C)C
*CC(*)(C)C(=O)OC1CCC(C(C)(C)C)CC1
*CC(*)(C)C(=O)OC1CCC(C)CC1
*CC(*)(C)C(=O)OC1CCC1
*CC(*)(C)C(=O)OC1CCC2CCCCC2C1
*CC(*)(C)C(=O)OC1CCCC(C)C1
*CC(*)(C)C(=O)OC1CCCC1
*CC(*)(C)C(=O)OC1CCCCC1
*CC(*)(C)C(=O)OC1CCCCC1C
*CC(*)(C)C(=O)OC1CCCCC1C(C)(C)C
*CC(*)(C)C(=O)OC1CCOCC1
*CC(*)(C)C(=O)OC1COC(CP(=O)(OCC)OCC)OC1
*CC(*)(C)C(=O)OCC
*CC(*)(C)C(=O)OCC#C
*CC(*)(C)C(=O)OCC#CO[Si](C)(C)C
*CC(*)(C)C(=O)OCC#N
*CC(*)(C)C(=O)OCC(=O)N1CCCC1
*CC(*)(C)C(=O)OCC(=O)N1CCOCC1
*CC(*)(C)C(=O)OCC(=O)Nc1cc(C)on1
*CC(*)(C)C(=O)OCC(=O)c1ccccc1
*CC(*)(C)C(=O)OCC(C)(C)C
*CC(*)(C)C(=O)OCC(C)(C)C1OCC(C)(C)CO1
*CC(*)(C)C(=O)OCC(C)O
*CC(*)(C)C(=O)OCC(C)Oc1ccc(-c2ccc(-c3ccc(C#N)cc3)cc2)cc1
*CC(*)(C)C(=O)OCC(CC)CCCC
*CC(*)(C)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)F
*CC(*)(C)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)F
*CC(*)(C)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)F
*CC(*)(C)C(=O)OCC(F)(F)C(F)(F)F
*CC(*)(C)C(=O)OCC(F)(F)C(F)F
*CC(*)(C)C(=O)OCC(F)(F)F
*CC(*)(C)C(=O)OCC(O)C1CC(C)(c2ccccc2)C1
*CC(*)(C)C(=O)OCC(O)CO
*CC(*)(C)C(=O)OCC1(C)COC(C)(C)OC1
*CC(*)(C)C(=O)OCC1(C)COCOC1
*CC(*)(C)C(=O)OCC1(CC)COC1
*CC(*)(C)C(=O)OCC1CC(NC(=O)C2CC(NC(=O)OC(C)(C)C)CN2C(=O)OC(C)(C)C)CN1C(=O)C1CC(NC(=O)OC(C)(C)C)CN1C(=O)OC(C)(C)C
*CC(*)(C)C(=O)OCC1CC(NC(=O)OC(C)(C)C)CN1C(=O)OC(C)(C)C
*CC(*)(C)C(=O)OCC1CCC1
*CC(*)(C)C(=O)OCC1CCCO1
*CC(*)(C)C(=O)OCC1CO1
*CC(*)(C)C(=O)OCC1COC(=O)O1
*CC(*)(C)C(=O)OCC1COC(CP(=O)(OCC)OCC)O1
*CC(*)(C)C(=O)OCC1COC(c2ccccc2)O1
*CC(*)(C)C(=O)OCC1COCCOCCOCCO1
*CC(*)(C)C(=O)OCC1COCCOCCOCCOCCOC1
*CC(*)(C)C(=O)OCC1OC(n2ccc(=O)[nH]c2=O)C(O)C1O
*CC(*)(C)C(=O)OCC=C
*CC(*)(C)C(=O)OCCBr
*CC(*)(C)C(=O)OCCC
*CC(*)(C)C(=O)OCCC#N
*CC(*)(C)C(=O)OCCC(C)(C)C
*CC(*)(C)C(=O)OCCC(C)CC(C)(C)C
*CC(*)(C)C(=O)OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)(C)C(=O)OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)(C)C(=O)OCCC1CCCCC1
*CC(*)(C)C(=O)OCCC1CCN(CCCCCCOc2ccc(/N=N/c3ccc([N+](=O)[O-])cc3)cc2)CC1
*CC(*)(C)C(=O)OCCC1CC[N+](C)(CCCCCCOc2ccc(/N=N/c3ccc([N+](=O)[O-])cc3)cc2)CC1
*CC(*)(C)C(=O)OCCCC
*CC(*)(C)C(=O)OCCCC1CCCCC1
*CC(*)(C)C(=O)OCCCCC
*CC(*)(C)C(=O)OCCCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)(C)C(=O)OCCCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)(C)C(=O)OCCCCC1CCCCC1
*CC(*)(C)C(=O)OCCCCCC
*CC(*)(C)C(=O)OCCCCCC(=O)Oc1ccc(OC(=O)c2ccc(OC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCC(=O)Oc1ccc(OC(=O)c2ccc(OCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCC(=O)Oc1ccc(OC(=O)c2ccc(OCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCC(=O)Oc1ccc(OC(=O)c2ccc(OCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCC(=O)Oc1ccc(OC(=O)c2ccc(OCCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCC(=O)Oc1ccc(OC(=O)c2ccc(OCCCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)(C)C(=O)OCCCCCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)(C)C(=O)OCCCCCCCC
*CC(*)(C)C(=O)OCCCCCCCC/C=C/CCCCCCCC
*CC(*)(C)C(=O)OCCCCCCCCCC
*CC(*)(C)C(=O)OCCCCCCCCCCCC
*CC(*)(C)C(=O)OCCCCCCCCCCCCCC
*CC(*)(C)C(=O)OCCCCCCCCCCCCCCC(=O)NC1OC(CO)C(OC2OC(CO)C(O)C(O)C2O)C(O)C1O
*CC(*)(C)C(=O)OCCCCCCCCCCCCCCC(=O)NC1OC(COC(C)=O)C(OC2OC(COC(C)=O)C(OC(C)=O)C(OC(C)=O)C2OC(C)=O)C(OC(C)=O)C1OC(C)=O
*CC(*)(C)C(=O)OCCCCCCCCCCCCCCCCCC
*CC(*)(C)C(=O)OCCCCCCCCCCCCOc1ccc(C(=O)Oc2ccc(-c3cccc(OC(=O)c4ccc(OC(=O)c5ccc(OCCCCCCCCCCCC)cc5)cc4)c3)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCCCOc1ccc2cc(C(=O)Oc3ccccc3)ccc2c1
*CC(*)(C)C(=O)OCCCCCCCCCCCOc1ccc(/C=N/c2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCCOc1ccc(/N=N/c2ccc(OC(=O)c3ccc4c(c3)OCCOCCOCCOCCO4)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCCOc1ccc(C(=O)Oc2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCCOc1ccc2cc(C(=O)Oc3ccccc3)ccc2c1
*CC(*)(C)C(=O)OCCCCCCCCCCCn1c2ccccc2c2ccccc21
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(/N=N/c2ccc(C3OCC4(CO3)CC3C=CC4C3)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(/N=N/c2ccc(Cl)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(/N=N/c2ccc(F)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(/N=N/c2ccc(OC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(/N=N/c2ccccc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(C#Cc2cc(C)c(/C=C/c3ccncc3)cc2C)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(C#Cc2cc(OC)c(/C=C/c3ccncc3)cc2OC)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(C(=O)Oc2ccc(C(=O)/C=C/c3c(C)c4ccccc4n3C)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(C(=O)Oc2ccc(C(=O)/C=C/c3c(C)c4ccccc4n3CCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(OC(=O)c2ccc(-c3ccc(OC)cc3)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(OC(=O)c2ccc(OC)c(Br)c2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(OC(=O)c2ccc(OC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(OC(=O)c2ccc(OCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(OC(=O)c2ccc(OCCCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc(OC(=O)c2ccc(OCCCCCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCCCOc1ccc2cc(C(=O)Oc3ccccc3)ccc2c1
*CC(*)(C)C(=O)OCCCCCCCCCCn1c2ccccc2c2cc(/C=C(\C#N)C(=O)OC)ccc21
*CC(*)(C)C(=O)OCCCCCCCCCCn1c2ccccc2c2cc(/C=C(\C#N)c3ccc([N+](=O)[O-])cc3)ccc21
*CC(*)(C)C(=O)OCCCCCCCCCCn1c2ccccc2c2cc(/C=C/C3=C(C#N)C(=C(C#N)C#N)OC3(C)C)ccc21
*CC(*)(C)C(=O)OCCCCCCCCCCn1c2ccccc2c2cc(/N=N/c3ccc([N+](=O)[O-])cc3)ccc21
*CC(*)(C)C(=O)OCCCCCCCCCCn1c2ccccc2c2cc(/N=N/c3ccc([N+](=O)[O-])cc3[N+](=O)[O-])ccc21
*CC(*)(C)C(=O)OCCCCCCCCCOc1ccc2cc(C(=O)Oc3ccccc3)ccc2c1
*CC(*)(C)C(=O)OCCCCCCCCOc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCOc1ccc(/N=N/c2ccc(C3OCC4(CO3)CC3C=CC4C3)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCOc1ccc(C(=O)Oc2ccc(C(=O)/C=C/c3c(C)c4ccccc4n3C)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCOc1ccc(C(=O)Oc2ccc(C(=O)/C=C/c3c(C)c4ccccc4n3CCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCCCOc1ccc2cc(C(=O)Oc3ccccc3)ccc2c1
*CC(*)(C)C(=O)OCCCCCCCOc1ccc2cc(C(=O)Oc3ccccc3)ccc2c1
*CC(*)(C)C(=O)OCCCCCCN(C)c1ccc(/N=N/c2ccc(S(C)(=O)=O)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCN(C)c1ccc(N(C)C)cc1
*CC(*)(C)C(=O)OCCCCCCN1CCN(c2ccc(/N=N/c3ccc(C#N)cc3)cc2)CC1
*CC(*)(C)C(=O)OCCCCCCN1CCN(c2ccc(/N=N/c3ccc(C=C(C#N)C#N)cc3)cc2)CC1
*CC(*)(C)C(=O)OCCCCCCN1CCN(c2ccc(/N=N/c3ccc([N+](=O)[O-])cc3)cc2)CC1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(-c2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(/C=N/c2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(/N=N/c2ccc(C3OCC4(CO3)CC3C=CC4C3)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(/N=N/c2ccc(OC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(/N=N/c2ccc(OCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(/N=N/c2ccc(OCCCCCCCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(/N=N/c2ccncc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(-c3cccc(OC(=O)c4ccc(OC(=O)c5ccc(OCCCCCCCCCCCC)cc5)cc4)c3)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(/N=N/c3ccc(Br)cc3)c3ccccc23)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(/N=N/c3ccc(C#N)cc3)c3ccccc23)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(/N=N/c3ccc(C)cc3)c3ccccc23)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(/N=N/c3ccc(Cl)cc3)c3ccccc23)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(/N=N/c3ccc(F)cc3)c3ccccc23)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(/N=N/c3ccc(I)cc3)c3ccccc23)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(/N=N/c3ccccc3)c3ccccc23)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(C(=O)/C=C/c3c(C)c4ccccc4n3C)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(C(=O)/C=C/c3c(C)c4ccccc4n3CCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(C)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(OC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(OC)cc2)cc1OC
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(OCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc3ccc(=O)oc3c2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(OC(=O)c2ccc(-c3ccc(OC)cc3)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(OC(=O)c2ccc(OC)c(Br)c2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(OC(=O)c2ccc(OC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(OC(=O)c2ccc(OCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(OC(=O)c2ccc(OCCCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc(OC(=O)c2ccc(OCCCCCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCOc1ccc2cc(C(=O)Oc3ccccc3)ccc2c1
*CC(*)(C)C(=O)OCCCCCCOc1cccc(C(=O)Oc2ccc(C#N)cc2)c1
*CC(*)(C)C(=O)OCCCCCCc1ccc(/C(C)=C/c2ccc(OC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCc1ccc(/N=N/c2ccc(OC(F)(F)F)cc2)cc1
*CC(*)(C)C(=O)OCCCCCCn1c2ccccc2c2ccccc21
*CC(*)(C)C(=O)OCCCCCOc1ccc(-c2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCCCCOc1ccc(OC(=O)c2ccc(OC)cc2)cc1
*CC(*)(C)C(=O)OCCCCCOc1ccc2cc(C(=O)Oc3ccccc3)ccc2c1
*CC(*)(C)C(=O)OCCCCCc1ccc(/N=N/c2ccc(OC(F)(F)F)cc2)cc1
*CC(*)(C)C(=O)OCCCCCn1c2ccccc2c2ccccc21
*CC(*)(C)C(=O)OCCCCOc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCCCOc1ccc(C(=O)Oc2ccc(OC)cc2)cc1
*CC(*)(C)C(=O)OCCCCOc1ccc(OC(=O)c2ccc(-c3ccc(OC)cc3)cc2)cc1
*CC(*)(C)C(=O)OCCCCOc1ccc(OC(=O)c2ccc(OC)c(Br)c2)cc1
*CC(*)(C)C(=O)OCCCCOc1ccc(OC(=O)c2ccc(OC)cc2)cc1
*CC(*)(C)C(=O)OCCCCOc1ccc(OC(=O)c2ccc(OCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCOc1ccc(OC(=O)c2ccc(OCCCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCOc1ccc(OC(=O)c2ccc(OCCCCCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCCCOc1ccc2cc(C(=O)Oc3ccccc3)ccc2c1
*CC(*)(C)C(=O)OCCCCc1ccc(/N=N/c2ccc(OC(F)(F)F)cc2)cc1
*CC(*)(C)C(=O)OCCCCc1ccccc1
*CC(*)(C)C(=O)OCCCCl
*CC(*)(C)C(=O)OCCCCn1c2ccccc2c2cc(/N=N/c3ccc([N+](=O)[O-])cc3[N+](=O)[O-])ccc21
*CC(*)(C)C(=O)OCCCCn1c2ccccc2c2ccccc21
*CC(*)(C)C(=O)OCCCN(C)c1ccc(N(C)C)cc1
*CC(*)(C)C(=O)OCCCN(CCO)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CC(*)(C)C(=O)OCCCOc1ccc(-c2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCCOc1ccc(C(=O)Oc2ccc3ccc(=O)oc3c2)cc1
*CC(*)(C)C(=O)OCCCOc1ccc2cc(C(=O)Oc3ccccc3)ccc2c1
*CC(*)(C)C(=O)OCCC[Si](OC)(OC)OC
*CC(*)(C)C(=O)OCCC[Si](O[Si](C)(C)C)(O[Si](C)(C)C)O[Si](C)(C)C
*CC(*)(C)C(=O)OCCC[Si]12O[Si]3(CC(C)C)O[Si]4(CC(C)C)O[Si](CC(C)C)(O1)O[Si]1(CC(C)C)O[Si](CC(C)C)(O2)O[Si](CC(C)C)(O3)O[Si](CC(C)C)(O4)O1
*CC(*)(C)C(=O)OCCCc1ccc(/N=N/c2ccc(OC(F)(F)F)cc2)cc1
*CC(*)(C)C(=O)OCCCl
*CC(*)(C)C(=O)OCCCn1c2ccccc2c2ccccc21
*CC(*)(C)C(=O)OCCF
*CC(*)(C)C(=O)OCCN(C)C
*CC(*)(C)C(=O)OCCN(C)c1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1
*CC(*)(C)C(=O)OCCN(C)c1ccc(/N=N/c2ccc(/N=N/c3ccc(C#N)cc3)cc2C)cc1
*CC(*)(C)C(=O)OCCN(C)c1ccc(/N=N/c2ccc(S(=O)(=O)Nc3cc(C)on3)cc2)cc1
*CC(*)(C)C(=O)OCCN(C)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CC(*)(C)C(=O)OCCN(C)c1ccc(N(C)C)cc1
*CC(*)(C)C(=O)OCCN(CC)CC
*CC(*)(C)C(=O)OCCN(CC)c1ccc(/N=N/c2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCN(CC)c1ccc(/N=N/c2ccc(OC)cc2)c(C)c1
*CC(*)(C)C(=O)OCCN(CC)c1ccc(/N=N/c2ccc(S(C)(=O)=O)cc2)cc1
*CC(*)(C)C(=O)OCCN(CC)c1ccc(/N=N/c2ccc([N+](=O)[O-])c3ccccc23)cc1
*CC(*)(C)C(=O)OCCN(CC)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CC(*)(C)C(=O)OCCN(CC)c1ccc(/N=N/c2nc3ccc([N+](=O)[O-])cc3s2)cc1
*CC(*)(C)C(=O)OCCN(CC)c1ccc([N+](=O)[O-])cc1
*CC(*)(C)C(=O)OCCN(CC)c1ccccc1
*CC(*)(C)C(=O)OCCN(CCCC)c1ccc(/N=N/c2ccc(S(=O)(=O)Nc3cc(C)on3)cc2)cc1
*CC(*)(C)C(=O)OCCN(CCCC)c1ccc(/N=N/c2ccc(S(=O)(=O)Nc3nccs3)cc2)cc1
*CC(*)(C)C(=O)OCCNC(C)(C)C
*CC(*)(C)C(=O)OCCO
*CC(*)(C)C(=O)OCCOC
*CC(*)(C)C(=O)OCCOC(=O)c1cc([N+](=O)[O-])cc([N+](=O)[O-])c1
*CC(*)(C)C(=O)OCCOCC
*CC(*)(C)C(=O)OCCOCCO
*CC(*)(C)C(=O)OCCOCCOC
*CC(*)(C)C(=O)OCCOCCOCC1(C)COCCOCCOCCOC1
*CC(*)(C)C(=O)OCCOCCOCC1COCCOCCOCCO1
*CC(*)(C)C(=O)OCCOCCOCCOCCOCCOCCOCCOCCOC
*CC(*)(C)C(=O)OCCOCCOCCOCCOc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCOCCOCCOc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCOCCOCC[N+](CC)(CC)CCCCS(=O)(=O)O
*CC(*)(C)C(=O)OCCOCCOc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCOCC[N+](CC)(CC)CCCCS(=O)(=O)O
*CC(*)(C)C(=O)OCCO[N+](=O)[O-]
*CC(*)(C)C(=O)OCCO[Si](C)(C)C
*CC(*)(C)C(=O)OCCOc1ccc(-c2ccc(-c3ccc(C#N)cc3)cc2)cc1
*CC(*)(C)C(=O)OCCOc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)OCCOc1ccc(/N=N/c2ccc(OC)cc2)cc1
*CC(*)(C)C(=O)OCCOc1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CC(*)(C)C(=O)OCCOc1ccc(C(=O)Oc2ccc(OCCCC)cc2)cc1
*CC(*)(C)C(=O)OCCS(=O)(=O)c1ccc(/N=N/c2ccc(N(CC)CC)cc2)cc1
*CC(*)(C)C(=O)OCCS(=O)CC
*CC(*)(C)C(=O)OCC[N+](C)(C)CCCCCCC
*CC(*)(C)C(=O)OCC[N+](C)(C)CCCCS(=O)(=O)O
*CC(*)(C)C(=O)OCCc1ccc(/N=N/c2ccc(OC(F)(F)F)cc2)cc1
*CC(*)(C)C(=O)OCCc1ccc(/N=N/c2ccccc2)cc1
*CC(*)(C)C(=O)OCCc1ccccc1
*CC(*)(C)C(=O)OCCn1c2ccccc2c2cc(/N=N/c3ccc([N+](=O)[O-])cc3)ccc21
*CC(*)(C)C(=O)OCCn1c2ccccc2c2cc(/N=N/c3ccc([N+](=O)[O-])cc3[N+](=O)[O-])ccc21
*CC(*)(C)C(=O)OCCn1c2ccccc2c2ccccc21
*CC(*)(C)C(=O)OCSC
*CC(*)(C)C(=O)OCc1c(Cl)cccc1Cl
*CC(*)(C)C(=O)OCc1c(F)cccc1F
*CC(*)(C)C(=O)OCc1cc(Cl)cc(Cl)c1
*CC(*)(C)C(=O)OCc1cc(Cl)ccc1Cl
*CC(*)(C)C(=O)OCc1ccc(/C=C/C(=O)C2c3ccccc3C(=O)c3ccccc32)cc1
*CC(*)(C)C(=O)OCc1ccc(/C=C/C(=O)Oc2ccc3oc(=O)ccc3c2)cc1
*CC(*)(C)C(=O)OCc1ccc(Cl)c(Cl)c1
*CC(*)(C)C(=O)OCc1ccc(Cl)cc1
*CC(*)(C)C(=O)OCc1ccc(Cl)cc1Cl
*CC(*)(C)C(=O)OCc1ccc(F)cc1F
*CC(*)(C)C(=O)OCc1ccc(OCCOCC)c(OCCOCC)c1
*CC(*)(C)C(=O)OCc1ccc(OCCOCCOCC)c(OCCOCCOCC)c1
*CC(*)(C)C(=O)OCc1ccc(OCCOCCOCCOCC)c(OCCOCCOCCOCC)c1
*CC(*)(C)C(=O)OCc1ccc2c(c1)OCCOCCOCCOCCO2
*CC(*)(C)C(=O)OCc1ccc2c(c1)c1ccccc1n2CC
*CC(*)(C)C(=O)OCc1ccc2c(c1)c1ccccc1n2CCCC
*CC(*)(C)C(=O)OCc1ccc2c(c1)c1ccccc1n2CCCCCC
*CC(*)(C)C(=O)OCc1ccc2c(c1)c1ccccc1n2CCCCCCCC
*CC(*)(C)C(=O)OCc1ccc2c(c1)c1ccccc1n2CCCCCCCCCC
*CC(*)(C)C(=O)OCc1ccc2c(c1)c1ccccc1n2CCCCCCCCCCCC
*CC(*)(C)C(=O)OCc1ccc2c(c1)c1ccccc1n2CCCCCCCCCCCCCC
*CC(*)(C)C(=O)OCc1ccc2c(c1)c1ccccc1n2CCCCCCCCCCCCCCCC
*CC(*)(C)C(=O)OCc1ccc[se]1
*CC(*)(C)C(=O)OCc1cccc(Cl)c1
*CC(*)(C)C(=O)OCc1cccc(Cl)c1Cl
*CC(*)(C)C(=O)OCc1ccccc1
*CC(*)(C)C(=O)OCc1ccccc1Cl
*CC(*)(C)C(=O)OCc1ccco1
*CC(*)(C)C(=O)OCc1cccs1
*CC(*)(C)C(=O)OCc1ccsc1
*CC(*)(C)C(=O)O[Si](C)(C)C
*CC(*)(C)C(=O)Oc1c(C(C)C)cccc1C(C)C
*CC(*)(C)C(=O)Oc1c(C)cccc1C
*CC(*)(C)C(=O)Oc1c(Cl)c(Cl)c(Cl)c(Cl)c1Cl
*CC(*)(C)C(=O)Oc1c(OC)cc(/C=C/c2ccccc2)cc1OC
*CC(*)(C)C(=O)Oc1cc(C)cc(C)c1
*CC(*)(C)C(=O)Oc1cc(C)ccc1C
*CC(*)(C)C(=O)Oc1ccc(-c2nnc(-c3ccc(C(C)(C)C)cc3)o2)cc1
*CC(*)(C)C(=O)Oc1ccc(/C=C/c2ccccc2)cc1
*CC(*)(C)C(=O)Oc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*CC(*)(C)C(=O)Oc1ccc(/N=N/c2ccc(OC)cc2)cc1
*CC(*)(C)C(=O)Oc1ccc(/N=N/c2ccc(OCCCCCCCCCCCOC(=O)c3ccc4c(c3)OCCOCCOCCOCCO4)cc2)cc1
*CC(*)(C)C(=O)Oc1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CC(*)(C)C(=O)Oc1ccc(C#N)cc1
*CC(*)(C)C(=O)Oc1ccc(C(=O)/C=C/c2ccc(Cl)cc2)cc1
*CC(*)(C)C(=O)Oc1ccc(C(=O)/C=C/c2ccc(OC)c(OC)c2)cc1
*CC(*)(C)C(=O)Oc1ccc(C(=O)/C=C/c2ccc(OCc3ccccc3)cc2)cc1
*CC(*)(C)C(=O)Oc1ccc(C(=O)O)cc1
*CC(*)(C)C(=O)Oc1ccc(C(=O)OC)cc1
*CC(*)(C)C(=O)Oc1ccc(C(=O)Oc2ccc(-c3ccccc3)cc2)cc1
*CC(*)(C)C(=O)Oc1ccc(C(=O)Oc2ccc(C(=O)c3ccccc3)cc2)cc1
*CC(*)(C)C(=O)Oc1ccc(C(=O)Oc2ccc(C)cc2)cc1
*CC(*)(C)C(=O)Oc1ccc(C(=O)Oc2ccc(OC)cc2)cc1
*CC(*)(C)C(=O)Oc1ccc(C(=O)Oc2ccc(Oc3ccccc3)cc2)cc1
*CC(*)(C)C(=O)Oc1ccc(C(=O)Oc2ccccc2)cc1
*CC(*)(C)C(=O)Oc1ccc(C(=O)c2ccccc2)c(O)c1
*CC(*)(C)C(=O)Oc1ccc(C(=O)c2ccccc2)cc1
*CC(*)(C)C(=O)Oc1ccc(C(C)(C)C)cc1
*CC(*)(C)C(=O)Oc1ccc(C)cc1C
*CC(*)(C)C(=O)Oc1ccc(CC#N)cc1
*CC(*)(C)C(=O)Oc1ccc(OCc2ccccc2)cc1
*CC(*)(C)C(=O)Oc1ccc([N+](=O)[O-])c(C)c1
*CC(*)(C)C(=O)Oc1ccc2c(c1)CCC2
*CC(*)(C)C(=O)Oc1cccc(C)c1C
*CC(*)(C)C(=O)Oc1ccccc1
*CC(*)(C)C(N)=O
*CC(*)(C)CC
*CC(*)(C)CCC
*CC(*)(C)OC
*CC(*)(C)c1ccc(C(C)C)cc1
*CC(*)(C)c1ccc(C(O)C(F)(F)F)cc1
*CC(*)(C)c1ccc(C)cc1
*CC(*)(C)c1ccc2ccccc2c1
*CC(*)(C)c1ccc2ccccc2n1
*CC(*)(C)c1ccccc1
*CC(*)(C)c1nc(N(CCOCCOCCOC)CCOCCOCCOC)nc(N(CCOCCOCCOC)c2ccccc2)n1
*CC(*)(CC(=O)O)C(=O)O
*CC(*)(CC(=O)OC(C)C)C(=O)OC(C)C
*CC(*)(CC(=O)OC(C)C)C(=O)OC12CC3CC(C)(CC(C)(C3)C1)C2
*CC(*)(CC(=O)OC(C)C)C(=O)OC12CC3CC(CC(C3)C1)C2
*CC(*)(CC(=O)OC)C(=O)O
*CC(*)(CC(=O)OC)C(=O)OC
*CC(*)(CC(=O)OC)C(=O)OC12CC3CC(C)(CC(C)(C3)C1)C2
*CC(*)(CC(=O)OC)C(=O)OC12CC3CC(CC(C3)C1)C2
*CC(*)(CC(=O)OC1CCC1)C(=O)OC1CCC1
*CC(*)(CC(=O)OC1CCCCC1)C(=O)OC1CCCCC1
*CC(*)(CC(=O)OC1CCCCCC1)C(=O)OC1CCCCCC1
*CC(*)(CC(=O)OC1CCCCCCC1)C(=O)OC1CCCCCCC1
*CC(*)(CC(=O)OCC(C)C)C(=O)OCC(C)C
*CC(*)(CC(=O)OCC)C(=O)O
*CC(*)(CC(=O)OCC)C(=O)OCC
*CC(*)(CC(=O)OCC1CCCCC1)C(=O)OCC1CCCCC1
*CC(*)(CC(=O)OCCC)C(=O)OCCC
*CC(*)(CC(=O)OCCC1CCCCC1)C(=O)OCCC1CCCCC1
*CC(*)(CC(=O)OCCCC)C(=O)OCCCC
*CC(*)(CC(=O)OCCCC1CCCCC1)C(=O)OCCCC1CCCCC1
*CC(*)(CC(=O)OCCCCC)C(=O)OCCCCC
*CC(*)(CC(=O)OCCCCCC)C(=O)OCCCCCC
*CC(*)(CC(=O)OCCCCCCC)C(=O)OCCCCCCC
*CC(*)(CC(=O)OCCCCCCCC)C(=O)OCCCCCCCC
*CC(*)(CC(=O)OCCCCCCCCC)C(=O)OCCCCCCCCC
*CC(*)(CC(=O)OCCCCl)C(=O)OCCCCl
*CC(*)(CC(=O)OCCCc1ccccc1)C(=O)OCCCc1ccccc1
*CC(*)(CC(=O)OCCCl)C(=O)OCCCl
*CC(*)(CC(=O)OCCOC)C(=O)OCCOC
*CC(*)(CC(=O)OCCOCCOC)C(=O)OCCOCCOC
*CC(*)(CC(=O)OCCOCCOCCOC)C(=O)OCCOCCOCCOC
*CC(*)(CC(=O)OCCOCCOCCOCCOCCOCCOCCOC)C(=O)OCCOCCOCCOCCOCCOCCOCCOC
*CC(*)(CC(=O)OCCc1ccccc1)C(=O)OCCc1ccccc1
*CC(*)(CC(=O)OCc1ccccc1)C(=O)OCc1ccccc1
*CC(*)(CC(=O)Oc1ccc(OC)cc1)C(=O)Oc1ccc(OC)cc1
*CC(*)(CC(=O)Oc1ccc(OCC)cc1)C(=O)Oc1ccc(OCC)cc1
*CC(*)(CC(=O)Oc1ccc(OCCC)cc1)C(=O)Oc1ccc(OCCC)cc1
*CC(*)(CC(=O)Oc1ccc(OCCCCC)cc1)C(=O)Oc1ccc(OCCCCC)cc1
*CC(*)(CC(=O)Oc1ccc(OCCCCCCC)cc1)C(=O)Oc1ccc(OCCCCCCC)cc1
*CC(*)(CC(=O)Oc1ccccc1)C(=O)Oc1ccccc1
*CC(*)(CC)C(=O)OCC
*CC(*)(CCC(C)C(=O)OC)C(=O)OC
*CC(*)(CCC)C(=O)OC
*CC(*)(CCCCC)C(=O)OC
*CC(*)(CCCCCCCC)C(=O)OC
*CC(*)(CF)C(=O)OCC
*CC(*)(Cl)C(=O)OC
*CC(*)(Cl)C(=O)OCC
*CC(*)(Cl)C(=O)OCCC
*CC(*)(Cl)C(=O)OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)(Cl)C(=O)OCCCC
*CC(*)(Cl)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(OCCCC)cc2)cc1
*CC(*)(Cl)C(=O)OCCOc1ccc(C(=O)Oc2ccc(OCCCC)cc2)cc1
*CC(*)(Cl)Cl
*CC(*)(F)C(=O)OC
*CC(*)(F)C(=O)OC(C(F)(F)F)C(F)(F)F
*CC(*)(F)C(=O)OCC
*CC(*)(F)C(=O)OCC(Cl)(Cl)Cl
*CC(*)(F)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)(F)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)F
*CC(*)(F)C(=O)OCC(F)(F)C(F)(F)C(F)(F)F
*CC(*)(F)C(=O)OCC(F)(F)C(F)(F)F
*CC(*)(F)C(=O)OCC(F)(F)C(F)F
*CC(*)(F)C(=O)OCC(F)(F)F
*CC(*)(F)C(=O)OCCC
*CC(*)(F)C(=O)OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)(F)C(=O)OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)(F)C(=O)OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)(F)C(=O)OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)(F)C(C)=O
*CC(*)(F)C(F)(F)F
*CC(*)(F)F
*CC(*)C
*CC(*)C#N
*CC(*)C(=O)N(C)C
*CC(*)C(=O)N1CCCCC1
*CC(*)C(=O)N1CCOCC1
*CC(*)C(=O)N1CC[NH+](CC)CC1
*CC(*)C(=O)NC
*CC(*)C(=O)NC(C)(C)C
*CC(*)C(=O)NC(C)(C)CC(C)=O
*CC(*)C(=O)NC(C)(C)CS(=O)(=O)O
*CC(*)C(=O)NC(C)C
*CC(*)C(=O)NCCCCCC(=O)O
*CC(*)C(=O)NCCC[N+](C)(C)C
*CC(*)C(=O)NCCC[N+](C)(C)CCCS(=O)(=O)O
*CC(*)C(=O)NCP(=O)(O)O
*CC(*)C(=O)Nc1ccc(C(=O)/C=C/c2ccc(Br)cc2)cc1
*CC(*)C(=O)Nc1ccc(C(=O)/C=C/c2cccc(Br)c2)cc1
*CC(*)C(=O)Nc1ccc(Cl)cc1
*CC(*)C(=O)Nc1ccc2c(c1)C(=O)c1ccccc1C2=O
*CC(*)C(=O)Nc1cccc(Cl)c1
*CC(*)C(=O)Nc1ccccc1
*CC(*)C(=O)Nc1ccccc1Cl
*CC(*)C(=O)O
*CC(*)C(=O)OC
*CC(*)C(=O)OC(C(F)(F)F)C(F)(F)F
*CC(*)C(=O)OC(C)(C)C
*CC(*)C(=O)OC(C)C
*CC(*)C(=O)OC(C)CC
*CC(*)C(=O)OC(CC)CC
*CC(*)C(=O)OC(Cl)C(Cl)(Cl)Cl
*CC(*)C(=O)OC(F)(C(F)(F)F)C(F)(F)F
*CC(*)C(=O)OC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)C(=O)OC(OCC)C(F)(F)F
*CC(*)C(=O)OC([2H])([2H])[2H]
*CC(*)C(=O)OC12CC3CC(C)(CC(C)(C3)C1)C2
*CC(*)C(=O)OC12CC3CC(CC(C3)C1)C2
*CC(*)C(=O)OC1CC(C)CC(C)(C)C1
*CC(*)C(=O)OC1CC2CCC1(C)C2(C)C
*CC(*)C(=O)OC1CCCCC1
*CC(*)C(=O)OC1CCCCCCCCCCC1
*CC(*)C(=O)OCC
*CC(*)C(=O)OCC(C)(C)C
*CC(*)C(=O)OCC(C)C
*CC(*)C(=O)OCC(C)CC
*CC(*)C(=O)OCC(C)O
*CC(*)C(=O)OCC(CC)CCCC
*CC(*)C(=O)OCC(COC(=O)c1ccccc1)(COC(=O)c1ccccc1)COC(=O)c1ccccc1
*CC(*)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)F
*CC(*)C(=O)OCC(F)(F)C(F)(F)C(F)(F)F
*CC(*)C(=O)OCC(F)(F)C(F)(F)F
*CC(*)C(=O)OCC(F)(F)C(F)(F)OC(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)C(=O)OCC(F)(F)C(F)(F)OC(F)(F)C(F)(F)C(F)(F)F
*CC(*)C(=O)OCC(F)(F)C(F)(F)OC(F)(F)C(F)(F)F
*CC(*)C(=O)OCC(F)(F)C(F)(F)OC(F)(F)F
*CC(*)C(=O)OCC(F)(F)C(F)C(F)(F)F
*CC(*)C(=O)OCC(F)(F)F
*CC(*)C(=O)OCC1(CC)COC(c2ccccc2)OC1
*CC(*)C(=O)OCC1(CC)COCOC1
*CC(*)C(=O)OCCC
*CC(*)C(=O)OCCC#N
*CC(*)C(=O)OCCC(C)OC
*CC(*)C(=O)OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)C(=O)OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)C(=O)OCCCC
*CC(*)C(=O)OCCCCC
*CC(*)C(=O)OCCCCCC
*CC(*)C(=O)OCCCCCC(=O)Oc1ccc(-c2ccc(C#N)cc2)cc1
*CC(*)C(=O)OCCCCCCC
*CC(*)C(=O)OCCCCCCCC
*CC(*)C(=O)OCCCCCCCCC
*CC(*)C(=O)OCCCCCCCCCCCCCCCCCC
*CC(*)C(=O)OCCCCCCCCCCCCOC(=O)c1ccc(OC(=O)c2ccc(C#N)cc2)cc1
*CC(*)C(=O)OCCCCCCCCCCCCOc1ccc(C(=O)Oc2ccc(-c3cccc(OC(=O)c4ccc(OC(=O)c5ccc(OCCCCCCCCCCCC)cc5)cc4)c3)cc2)cc1
*CC(*)C(=O)OCCCCCCCCCCCOc1ccc(C(=O)Oc2ccc(-c3ccc(OC(C)CCCCCC)cc3)cc2)cc1
*CC(*)C(=O)OCCCCCCCCCCCn1c2ccccc2c2ccccc21
*CC(*)C(=O)OCCCCCCCCCCOC(=O)c1ccc(OC(=O)c2ccc(C#N)cc2)cc1
*CC(*)C(=O)OCCCCCCCCCCOc1cnc(-c2ccc(OC(=O)C3COC(C)(C)O3)cc2)nc1
*CC(*)C(=O)OCCCCCCCCCOC(=O)c1ccc(OC(=O)c2ccc(C#N)cc2)cc1
*CC(*)C(=O)OCCCCCCCCOC(=O)c1ccc(OC(=O)c2ccc(C#N)cc2)cc1
*CC(*)C(=O)OCCCCCCCCn1c2ccccc2c2ccccc21
*CC(*)C(=O)OCCCCCCCOC(=O)c1ccc(OC(=O)c2ccc(C#N)cc2)cc1
*CC(*)C(=O)OCCCCCCOC(=O)c1ccc(OC(=O)c2ccc(C#N)cc2)cc1
*CC(*)C(=O)OCCCCCCOc1ccc(-c2ccc(C#N)cc2)cc1
*CC(*)C(=O)OCCCCCCOc1ccc(/C=N/c2ccc(OCCCCC)cc2)c(O)c1
*CC(*)C(=O)OCCCCCCOc1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CC(*)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(C#N)cc2)cc1
*CC(*)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(OC)cc2)cc1
*CC(*)C(=O)OCCCCCCSCC#N
*CC(*)C(=O)OCCCCCCn1c2ccccc2c2ccccc21
*CC(*)C(=O)OCCCCCOC(=O)c1ccc(OC(=O)c2ccc(C#N)cc2)cc1
*CC(*)C(=O)OCCCCCn1c2ccccc2c2ccccc21
*CC(*)C(=O)OCCCCOC(=O)c1ccc(OC(=O)c2ccc(C#N)cc2)cc1
*CC(*)C(=O)OCCCCOc1ccc(-c2ccc(C#N)cc2)cc1
*CC(*)C(=O)OCCCCOc1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CC(*)C(=O)OCCCCSC
*CC(*)C(=O)OCCCCn1c2ccccc2c2ccccc21
*CC(*)C(=O)OCCCOC
*CC(*)C(=O)OCCCOCC
*CC(*)C(=O)OCCCOc1ccc(-c2ccc(C#N)cc2)cc1
*CC(*)C(=O)OCCCOc1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CC(*)C(=O)OCCCOc1ccc(C(=O)OCCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc1
*CC(*)C(=O)OCCCOc1ccc(C(=O)OCCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc1
*CC(*)C(=O)OCCCSC
*CC(*)C(=O)OCCCSCC
*CC(*)C(=O)OCCCSCCC#N
*CC(*)C(=O)OCCCn1c2ccccc2c2ccccc21
*CC(*)C(=O)OCCN(C)C
*CC(*)C(=O)OCCN(CC)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CC(*)C(=O)OCCN(CC)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2Cl)cc1
*CC(*)C(=O)OCCO
*CC(*)C(=O)OCCOC
*CC(*)C(=O)OCCOC(=O)c1cc([N+](=O)[O-])cc([N+](=O)[O-])c1
*CC(*)C(=O)OCCOC(F)(F)C(F)F
*CC(*)C(=O)OCCOCC
*CC(*)C(=O)OCCOCC(F)(F)C(F)(F)C(F)(F)F
*CC(*)C(=O)OCCOCC(F)(F)F
*CC(*)C(=O)OCCOCC1(CC)COC1
*CC(*)C(=O)OCCOCCOC(F)(F)C(F)F
*CC(*)C(=O)OCCOc1ccc(C(=O)Oc2ccc(OC(=O)c3ccc(OCCCC)cc3)cc2)cc1
*CC(*)C(=O)OCCOc1ccc(C(=O)Oc2ccc(OC(=O)c3ccc(OCCCCC)cc3)cc2)cc1
*CC(*)C(=O)OCCOc1ccc(C(=O)Oc2ccc(OC)cc2)cc1
*CC(*)C(=O)OCCOc1ccc(C(C)(C)c2ccc(OCCO)cc2)cc1
*CC(*)C(=O)OCCSC
*CC(*)C(=O)OCCSCC
*CC(*)C(=O)OCCSCC#N
*CC(*)C(=O)OCCSCCC#N
*CC(*)C(=O)OCCSCCCC#N
*CC(*)C(=O)OCCc1ccccc1
*CC(*)C(=O)OCCn1c2ccccc2c2ccccc21
*CC(*)C(=O)OCF
*CC(*)C(=O)OCc1c(Br)c(Br)c(Br)c(Br)c1Br
*CC(*)C(=O)OCc1ccc(C#N)cc1
*CC(*)C(=O)OCc1ccccc1
*CC(*)C(=O)O[Na]
*CC(*)C(=O)Oc1c(Cl)c(Cl)c(Cl)c(Cl)c1Cl
*CC(*)C(=O)Oc1c(F)c(F)c(F)c(F)c1F
*CC(*)C(=O)Oc1cc(C)cc(C)c1
*CC(*)C(=O)Oc1ccc(-c2ccccc2)cc1
*CC(*)C(=O)Oc1ccc(/N=N/c2ccc(OC)cc2)cc1
*CC(*)C(=O)Oc1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CC(*)C(=O)Oc1ccc(C#N)cc1
*CC(*)C(=O)Oc1ccc(C(=O)O)cc1
*CC(*)C(=O)Oc1ccc(C(=O)OC)cc1
*CC(*)C(=O)Oc1ccc(C(=O)OCC)cc1
*CC(*)C(=O)Oc1ccc(C(=O)OCCCC)cc1
*CC(*)C(=O)Oc1ccc(C(=O)OCc2ccccc2)cc1
*CC(*)C(=O)Oc1ccc(C(=O)Oc2ccc(OC(=O)c3ccc(OCCCC)cc3)cc2)cc1
*CC(*)C(=O)Oc1ccc(C(=O)Oc2ccc(OC(=O)c3ccc(OCCCCC)cc3)cc2)cc1
*CC(*)C(=O)Oc1ccc(C(=O)c2ccc(Cl)cc2)cc1
*CC(*)C(=O)Oc1ccc(C(C)(C)C)cc1
*CC(*)C(=O)Oc1ccc(C)cc1
*CC(*)C(=O)Oc1ccc(Cl)cc1
*CC(*)C(=O)Oc1ccc(Cl)cc1Cl
*CC(*)C(=O)Oc1ccc(OC)cc1
*CC(*)C(=O)Oc1ccc([N+](=O)[O-])cc1
*CC(*)C(=O)Oc1cccc(C(=O)OC)c1
*CC(*)C(=O)Oc1cccc(C(=O)OCC)c1
*CC(*)C(=O)Oc1cccc(C)c1
*CC(*)C(=O)Oc1cccc(N(C)C)c1
*CC(*)C(=O)Oc1cccc2ccccc12
*CC(*)C(=O)Oc1ccccc1
*CC(*)C(=O)Oc1ccccc1C
*CC(*)C(=O)Oc1ccccc1C(=O)OC
*CC(*)C(=O)Oc1ccccc1C(=O)OCC
*CC(*)C(=O)Oc1ccccc1C(C)(C)C
*CC(*)C(=O)Oc1ccccc1Cl
*CC(*)C(=O)c1ccc(Br)c(OC)c1
*CC(*)C(=O)c1ccc(Br)cc1
*CC(*)C(=O)c1ccc(C(C)(C)C)cc1
*CC(*)C(=O)c1ccc(C(C)C)cc1
*CC(*)C(=O)c1ccc(C)c(C)c1
*CC(*)C(=O)c1ccc(C)cc1
*CC(*)C(=O)c1ccc(CC)cc1
*CC(*)C(=O)c1ccc(CCC)cc1
*CC(*)C(=O)c1ccc(Cl)cc1
*CC(*)C(=O)c1ccc(OC)cc1
*CC(*)C(=O)c1ccccc1
*CC(*)C(=O)n1c2ccccc2c2ccccc21
*CC(*)C(C)=O
*CC(*)C(C)C
*CC(*)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)C(F)(F)C(F)(F)F
*CC(*)C(F)(F)F
*CC(*)C(N)=O
*CC(*)C1=NCCN1
*CC(*)C1CCC(C(C)(C)C)CC1
*CC(*)C1CCC(C)CC1
*CC(*)C1CCCC(C)C1
*CC(*)C1CCCC1
*CC(*)C1CCCCC1
*CC(*)C1CCCCC1C
*CC(*)C=C
*CC(*)CC
*CC(*)CC(C)(C)C
*CC(*)CC(C)C
*CC(*)CC1CCCC1
*CC(*)CC1CCCCC1
*CC(*)CCC
*CC(*)CCC1CCCCC1
*CC(*)CCCC
*CC(*)CCCCC
*CC(*)CCCCCC
*CC(*)CCCCCCC
*CC(*)CCCCCCCC
*CC(*)CCCCCCCCCC
*CC(*)CCCCCCCCCCCC
*CC(*)CCCCCCCCCCCCCC
*CC(*)CCCCCCCCCCCCCCCC
*CC(*)CCCCCCCCCCCCCCCCCC
*CC(*)CCCCCCCCCI
*CC(*)CCCI
*CC(*)CCI
*CC(*)CC[Si](C)(C)C
*CC(*)CCc1ccccc1
*CC(*)CN
*CC(*)CNc1ccc([N+](=O)[O-])c(C(F)(F)F)c1
*CC(*)CNc1ccc([N+](=O)[O-])cc1
*CC(*)CNc1ccc([N+](=O)[O-])cc1C
*CC(*)CNc1ccc([N+](=O)[O-])cc1[N+](=O)[O-]
*CC(*)CNc1ccc([N+](=O)[O-])cn1
*CC(*)CO
*CC(*)C[Si](C)(C)C
*CC(*)Cc1ccc(C)cc1
*CC(*)Cc1cccc(C)c1
*CC(*)Cc1ccccc1
*CC(*)Cc1ccccc1C
*CC(*)Cl
*CC(*)F
*CC(*)N
*CC(*)N1CCCC1=O
*CC(*)N1CCCC1=S
*CC(*)N1CCCCCC1=O
*CC(*)NC(=O)OC(C)(C)C
*CC(*)NC=O
*CC(*)O
*CC(*)OC
*CC(*)OC(=O)/C=C/c1ccccc1
*CC(*)OC(=O)C(C)(C(C)C)C(C)C
*CC(*)OC(=O)C(C)(C)C(C)(C)CC
*CC(*)OC(=O)C(C)(C)C(C)C(C)C
*CC(*)OC(=O)C(C)(C)CC(C)(C)C
*CC(*)OC(=O)C(C)(C)CCC
*CC(*)OC(=O)C(C)(CC)C(C)(C)C
*CC(*)OC(=O)C(Cl)(Cl)Cl
*CC(*)OC(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)OC(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)OC(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)OC(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)OC(=O)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)OC(=O)C(F)(F)C(F)(F)F
*CC(*)OC(=O)C(F)(F)F
*CC(*)OC(=O)C1(C)CCCCC1
*CC(*)OC(=O)C1(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C1(F)F
*CC(*)OC(=O)C1CCCC1
*CC(*)OC(=O)C1CCCCC1
*CC(*)OC(=O)CC
*CC(*)OC(=O)CC(C)(C)c1ccccc1
*CC(*)OC(=O)CC1CCCC1
*CC(*)OC(=O)CC1CCCCC1
*CC(*)OC(=O)CCCC(=O)c1ccccc1
*CC(*)OC(=O)CCCC1CCCCC1
*CC(*)OC(=O)CCl
*CC(*)OC(=O)OC([2H])([2H])[2H]
*CC(*)OC(=O)OCC(Cl)(Cl)Cl
*CC(*)OC(=O)OCC(F)(F)F
*CC(*)OC(=O)OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(*)OC(=O)c1ccc(Br)cc1
*CC(*)OC(=O)c1ccc(C(C)(C)C)cc1
*CC(*)OC(=O)c1ccc(C(C)C)cc1
*CC(*)OC(=O)c1ccc(C)cc1
*CC(*)OC(=O)c1ccc(CC)cc1
*CC(*)OC(=O)c1ccc(Cl)cc1
*CC(*)OC(=O)c1ccc(OC(=O)CCC)cc1
*CC(*)OC(=O)c1ccc(OC(C)=O)cc1
*CC(*)OC(=O)c1ccc(OC)cc1
*CC(*)OC(=O)c1ccc(OCC)cc1
*CC(*)OC(=O)c1ccc([Si](C)(C)C)cc1
*CC(*)OC(=O)c1cccc(Br)c1
*CC(*)OC(=O)c1cccc(C)c1
*CC(*)OC(=O)c1cccc(Cl)c1
*CC(*)OC(=O)c1cccc(OC)c1
*CC(*)OC(=O)c1cccc([N+](=O)[O-])c1
*CC(*)OC(=O)c1cccc([Si](C)(C)C)c1
*CC(*)OC(=O)c1ccccc1
*CC(*)OC(=O)c1ccccc1C
*CC(*)OC(=O)c1ccccc1Cl
*CC(*)OC(=O)c1ccccc1OC
*CC(*)OC(=O)c1ccccc1OC(C)=O
*CC(*)OC(=O)c1cccnc1
*CC(*)OC(=O)c1ccncc1
*CC(*)OC(=O)c1nn(C(C)=O)c2ccccc12
*CC(*)OC(C)=O
*CC(*)OC(C)C
*CC(*)OC(C)CC
*CC(*)OC(F)(C(F)(F)F)C(F)(F)F
*CC(*)OC=O
*CC(*)OCC
*CC(*)OCC(C)(C)CC
*CC(*)OCC(C)C
*CC(*)OCC(CC)CCCC
*CC(*)OCCCC
*CC(*)OCCCCC
*CC(*)OCCCCCC
*CC(*)OCCCCCCCC
*CC(*)OCCCCCCCCCC
*CC(*)OCCCCCCCCCCCOc1ccc(C(=O)Oc2ccc(C#N)cc2)cc1
*CC(*)OCCCCCCCCCCCOc1ccc(C(=O)Oc2ccc(OCC)cc2)cc1
*CC(*)OCCCCCCCOc1ccc(-c2ccc(C#N)cc2)cc1
*CC(*)OCCOCCOCCOC
*CC(*)OCCOc1ccc(/C=C(\C#N)C(=O)OC)cc1
*CC(*)OCCOc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*CC(*)OCCOc1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CC(*)OCCOc1ccc(C=C(C#N)C#N)cc1
*CC(*)O[N+](=O)[O-]
*CC(*)P(=O)(N(C)C)N(C)C
*CC(*)P(=O)(O)O
*CC(*)P(=O)(OCC)N(C)C
*CC(*)P(=O)(Oc1ccccc1)N(C)C
*CC(*)P(=O)(c1ccccc1)c1ccccc1
*CC(*)S(=O)(=O)c1ccccc1
*CC(*)SC
*CC(*)SCC
*CC(*)SCCCC
*CC(*)[Si](C)(C)C
*CC(*)[Si](C)(C)Cc1ccccc1
*CC(*)c1c(C)cc(C)cc1C
*CC(*)c1c(Cl)cccc1Cl
*CC(*)c1c(F)c(F)c(C(F)(F)F)c(F)c1F
*CC(*)c1c(F)c(F)c(OC)c(F)c1F
*CC(*)c1c(F)c(F)c(OCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)c(F)c1F
*CC(*)c1c(F)c(F)c(OCC(F)(F)C(F)(F)F)c(F)c1F
*CC(*)c1c(F)c(F)c(P(=O)(O)O)c(F)c1F
*CC(*)c1c([2H])c([2H])c([2H])c([2H])c1[2H]
*CC(*)c1cc(-c2ccc(C(=O)OC(C)CC)cc2)ccc1-c1ccc(C(=O)OC(C)CC)cc1
*CC(*)c1cc(-c2ccc(C(=O)OC(C)CCCCCC)cc2)ccc1-c1ccc(C(=O)OC(C)CCCCCC)cc1
*CC(*)c1cc(-c2ccc(C(=O)OC3CC(C)CCC3C(C)C)cc2)ccc1-c1ccc(C(=O)OC2CC(C)CCC2C(C)C)cc1
*CC(*)c1cc(Br)ccc1OC
*CC(*)c1cc(Br)ccc1OC(C)C
*CC(*)c1cc(Br)ccc1OCC
*CC(*)c1cc(Br)ccc1OCCC
*CC(*)c1cc(Br)ccc1OCCC(C)C
*CC(*)c1cc(Br)ccc1OCCCC
*CC(*)c1cc(Br)ccc1OCCCCC
*CC(*)c1cc(C(=O)OC)ccc1-c1ccc(OCCCCCCCC)cc1
*CC(*)c1cc(C(=O)OCC)ccc1-c1ccc(OCCCCCCCC)cc1
*CC(*)c1cc(C(=O)OCCCC)ccc1-c1ccc(OCCCCCCCC)cc1
*CC(*)c1cc(C(=O)OCCCCCC)ccc1-c1ccc(OCCCCCCCC)cc1
*CC(*)c1cc(C(=O)OCCCCCCCC)ccc1-c1ccc(OCCCCCCCC)cc1
*CC(*)c1cc(C(=O)OCCCCCCCCCC)ccc1-c1ccc(OCCCCCCCC)cc1
*CC(*)c1cc(C(=O)OCCCCCCCCCCCC)ccc1-c1ccc(OCCCCCCCC)cc1
*CC(*)c1cc(C(=O)Oc2ccc(OC)cc2)ccc1C(=O)Oc1ccc(OC)cc1
*CC(*)c1cc(C(=O)Oc2ccc(OCCCC)cc2)ccc1C(=O)Oc1ccc(OCCCC)cc1
*CC(*)c1cc(C(C)(C)C)ccc1C
*CC(*)c1cc(C(F)(F)F)cc(C(F)(F)F)c1
*CC(*)c1cc(C)c(C)cc1C
*CC(*)c1cc(C)cc(C)c1
*CC(*)c1cc(C)ccc1C
*CC(*)c1cc(Cl)ccc1Cl
*CC(*)c1cc(OC)c(O)c(OC)c1
*CC(*)c1cc(OC)c(OC(C)=O)c(OC)c1
*CC(*)c1cc[n+](CCCCCCCC)cc1
*CC(*)c1ccc(-c2ccc3c(c2)C(CCCCCC)(CCCCCC)c2cc(-c4ccc5c(c4)C(CCCCCC)(CCCCCC)c4ccccc4-5)ccc2-3)cc1
*CC(*)c1ccc(-c2ccc3c(c2)C(CCCCCC)(CCCCCC)c2ccccc2-3)cc1
*CC(*)c1ccc(-c2cccc3nsnc23)cc1
*CC(*)c1ccc(-c2ccccc2)cc1
*CC(*)c1ccc(/C(C)=N/O)cc1
*CC(*)c1ccc(/C(Cc2ccccc2)=N/O)cc1
*CC(*)c1ccc(B(O[Si](C)(C)C)O[Si](C)(C)C)cc1
*CC(*)c1ccc(B(Oc2ccc(-c3c(F)c(F)c(F)c(F)c3F)c3cccnc23)c2ccc(C(C)(C)C)cc2)cc1
*CC(*)c1ccc(B(Oc2ccc(-c3ccc(N(C)C)cc3)c3cccnc23)c2ccc(C(C)(C)C)cc2)cc1
*CC(*)c1ccc(B(Oc2ccc(-c3ccc(OC)cc3)c3cccnc23)c2ccc(C(C)(C)C)cc2)cc1
*CC(*)c1ccc(B(Oc2ccc(B3OC(C)(C)C(C)(C)O3)c3cccnc23)c2ccc(C(C)(C)C)cc2)cc1
*CC(*)c1ccc(B(Oc2ccc(Cl)c3cccnc23)c2ccc(C(C)(C)C)cc2)cc1
*CC(*)c1ccc(B(Oc2cccc3cccnc23)c2ccc(C(C)(C)C)cc2)cc1
*CC(*)c1ccc(Br)cc1
*CC(*)c1ccc(C#N)cc1
*CC(*)c1ccc(C(=O)C(F)(F)F)cc1
*CC(*)c1ccc(C(=O)CC)cc1
*CC(*)c1ccc(C(=O)CCC)cc1
*CC(*)c1ccc(C(=O)CCCC)cc1
*CC(*)c1ccc(C(=O)CCCCC)cc1
*CC(*)c1ccc(C(=O)CCCCCCC)cc1
*CC(*)c1ccc(C(=O)CCN2CCCCC2)cc1
*CC(*)c1ccc(C(=O)CCN2CCOCC2)cc1
*CC(*)c1ccc(C(=O)Cc2ccccc2)cc1
*CC(*)c1ccc(C(=O)N(C)C)cc1
*CC(*)c1ccc(C(=O)N(CC)CC)cc1
*CC(*)c1ccc(C(=O)N2CCCCC2)cc1
*CC(*)c1ccc(C(=O)N2CCOCC2)cc1
*CC(*)c1ccc(C(=O)OC(C)C)cc1
*CC(*)c1ccc(C(=O)OC)cc1
*CC(*)c1ccc(C(=O)OCC(C)C)cc1
*CC(*)c1ccc(C(=O)OCC)cc1
*CC(*)c1ccc(C(=O)OCCC)cc1
*CC(*)c1ccc(C(=O)OCCCC)cc1
*CC(*)c1ccc(C(=O)OCCCCCC)cc1
*CC(*)c1ccc(C(=O)OCCN(C)C)cc1
*CC(*)c1ccc(C(=O)OCCN(CC)CC)cc1
*CC(*)c1ccc(C(=O)c2ccc(C)cc2)cc1
*CC(*)c1ccc(C(=O)c2ccc(OC)cc2)cc1
*CC(*)c1ccc(C(=O)c2ccccc2)cc1
*CC(*)c1ccc(C(C)(C)C)cc1
*CC(*)c1ccc(C(C)(C)O)cc1
*CC(*)c1ccc(C(C)(O)CC)cc1
*CC(*)c1ccc(C(C)(O)CCC)cc1
*CC(*)c1ccc(C(C)(O)CCCC)cc1
*CC(*)c1ccc(C(C)(O)CCCCC)cc1
*CC(*)c1ccc(C(C)=O)cc1
*CC(*)c1ccc(C(C)C)cc1
*CC(*)c1ccc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc1
*CC(*)c1ccc(C(O)C(F)(F)F)cc1
*CC(*)c1ccc(C(O)CCN(C)C)cc1
*CC(*)c1ccc(C(O)CCN2CCCCC2)cc1
*CC(*)c1ccc(C(O)CCN2CCOCC2)cc1
*CC(*)c1ccc(C([Si](C)(C)C)[Si](C)(C)C)cc1
*CC(*)c1ccc(C([Sn](C)(C)C)[Sn](C)(C)C)cc1
*CC(*)c1ccc(C)c(C)c1
*CC(*)c1ccc(C)cc1
*CC(*)c1ccc(C)cc1C
*CC(*)c1ccc(C)nc1
*CC(*)c1ccc(CC)cc1
*CC(*)c1ccc(CCC(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc1
*CC(*)c1ccc(CCC)cc1
*CC(*)c1ccc(CCCC)cc1
*CC(*)c1ccc(CCCCCC)cc1
*CC(*)c1ccc(CCCCCCCC)cc1
*CC(*)c1ccc(CCCCCCCCC)cc1
*CC(*)c1ccc(CCCCCCCCCC)cc1
*CC(*)c1ccc(CCCCCCCCCCCC)cc1
*CC(*)c1ccc(CCCCCCCCCCCCCC)cc1
*CC(*)c1ccc(CCCCCCCCCCCCCCCC)cc1
*CC(*)c1ccc(CCCCCCCCCCCCCCCCCC)cc1
*CC(*)c1ccc(CCl)cc1
*CC(*)c1ccc(COC(C)CC)cc1
*CC(*)c1ccc(COC)cc1
*CC(*)c1ccc(COCC(CC)CCCC)cc1
*CC(*)c1ccc(COCC(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc1
*CC(*)c1ccc(COCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc1
*CC(*)c1ccc(COCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc1
*CC(*)c1ccc(COCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc1
*CC(*)c1ccc(COCCC)cc1
*CC(*)c1ccc(COCCCC)cc1
*CC(*)c1ccc(COCCCCCC)cc1
*CC(*)c1ccc(COCCCCO)cc1
*CC(*)c1ccc(COCCO)cc1
*CC(*)c1ccc(COCCOCC)cc1
*CC(*)c1ccc(COCCOCCCC)cc1
*CC(*)c1ccc(COCCOCCCCCCCC)cc1
*CC(*)c1ccc(COc2ccc(-c3ccc(-c4ccc(-c5ccc(CCCCCC)s5)s4)c4nsnc34)cc2)cc1
*CC(*)c1ccc(COc2ccc(-c3ccc(-c4ccc(C)cc4)c4nsnc34)cc2)cc1
*CC(*)c1ccc(COc2ccc(-c3ccc(-c4ccc(C)s4)c4nsnc34)cc2)cc1
*CC(*)c1ccc(COc2ccc(-c3ccc(C#N)cc3)cc2)cc1
*CC(*)c1ccc(C[Si](C)(C)C)cc1
*CC(*)c1ccc(Cl)c(C)c1
*CC(*)c1ccc(Cl)c(Cl)c1
*CC(*)c1ccc(Cl)c(F)c1
*CC(*)c1ccc(Cl)c([N+](=O)[O-])c1
*CC(*)c1ccc(Cl)cc1
*CC(*)c1ccc(Cl)cc1C
*CC(*)c1ccc(Cl)cc1Cl
*CC(*)c1ccc(Cn2c3ccccc3c3ccccc32)cc1
*CC(*)c1ccc(F)cc1
*CC(*)c1ccc(I)cc1
*CC(*)c1ccc(N(c2ccccc2)c2ccccc2)cc1
*CC(*)c1ccc(O)c(OC)c1
*CC(*)c1ccc(O)cc1
*CC(*)c1ccc(OC(C)=O)c(OC)c1
*CC(*)c1ccc(OC(C)=O)cc1
*CC(*)c1ccc(OC)cc1
*CC(*)c1ccc(OCC)cc1
*CC(*)c1ccc(OCCC(C)C)cc1
*CC(*)c1ccc(OCCC)cc1
*CC(*)c1ccc(OCCCC)cc1
*CC(*)c1ccc(OCCOCCOC(C)=O)cc1
*CC(*)c1ccc(OP2(OCCOCCOCCOC)=NP(OCCOCCOCCOC)(OCCOCCOCCOC)=NP(OCCOCCOCCOC)(OCCOCCOCCOC)=N2)cc1
*CC(*)c1ccc(Oc2ccccc2)cc1
*CC(*)c1ccc(S(=O)(=O)OCCC)cc1
*CC(*)c1ccc([SiH](C)C)cc1
*CC(*)c1ccc([Si](C)(C)C)cc1
*CC(*)c1ccc([Si](C)(C)O)cc1
*CC(*)c1ccc([Si](C)(C)OC(C)C)cc1
*CC(*)c1ccc([Si](C)(C)O[Si](C)(C)C)cc1
*CC(*)c1ccc([Si](C)(C)O[Si](C)(C)O[Si](C)(C)C)cc1
*CC(*)c1ccc([Si](C)(O[Si](C)(C)C)O[Si](C)(C)C)cc1
*CC(*)c1ccc([Si](O[Si](C)(C)C)(O[Si](C)(C)C)O[Si](C)(C)C)cc1
*CC(*)c1ccc2c(c1)OCO2
*CC(*)c1ccc2ccccc2c1
*CC(*)c1cccc(B(O[Si](C)(C)C)O[Si](C)(C)C)c1
*CC(*)c1cccc(Br)c1
*CC(*)c1cccc(C)c1
*CC(*)c1cccc(C)n1
*CC(*)c1cccc(Cl)c1
*CC(*)c1cccc(F)c1
*CC(*)c1cccc(N(c2ccc(C)cc2)c2ccc(C)cc2)c1
*CC(*)c1cccc(NC(C)=O)c1
*CC(*)c1cccc(O)c1
*CC(*)c1cccc(OC(C)=O)c1
*CC(*)c1cccc2c1OCO2
*CC(*)c1cccc2ccccc12
*CC(*)c1ccccc1
*CC(*)c1ccccc1
*CC(*)c1ccccc1Br
*CC(*)c1ccccc1C
*CC(*)c1ccccc1C(=O)N(C)C
*CC(*)c1ccccc1C(=O)NC
*CC(*)c1ccccc1C(=O)Nc1ccccc1
*CC(*)c1ccccc1C(=O)O
*CC(*)c1ccccc1C(=O)OC
*CC(*)c1ccccc1C(=O)OC(C)C
*CC(*)c1ccccc1C(=O)OCC
*CC(*)c1ccccc1C(=O)OCC(C)C
*CC(*)c1ccccc1C(=O)OCCC
*CC(*)c1ccccc1C(=O)OCCC(C)C
*CC(*)c1ccccc1C(=O)OCCCC
*CC(*)c1ccccc1C(=O)OCCCCC
*CC(*)c1ccccc1C(=O)OCCCCCC
*CC(*)c1ccccc1C(=O)OCCN(C)C
*CC(*)c1ccccc1C(=O)Oc1ccccc1
*CC(*)c1ccccc1C(F)(F)F
*CC(*)c1ccccc1COC
*CC(*)c1ccccc1COC(C)C
*CC(*)c1ccccc1COCC
*CC(*)c1ccccc1COCCC
*CC(*)c1ccccc1COCCC(C)C
*CC(*)c1ccccc1COCCCC
*CC(*)c1ccccc1COCCCCC
*CC(*)c1ccccc1COCCCCCCCC
*CC(*)c1ccccc1COCCc1ccccc1
*CC(*)c1ccccc1COCc1ccccc1
*CC(*)c1ccccc1Cl
*CC(*)c1ccccc1F
*CC(*)c1ccccc1O
*CC(*)c1ccccc1OC(C)=O
*CC(*)c1ccccn1
*CC(*)c1ccccn1
*CC(*)c1cccs1
*CC(*)c1ccncc1
*CC(*)c1nc(C)nc(C)n1
*CC(*)c1nnn[nH]1
*CC(*)n1c2ccccc2c2c3ccccc3ccc21
*CC(*)n1c2ccccc2c2cc3ccccc3cc21
*CC(*)n1c2ccccc2c2ccccc21
*CC(*)n1cc2ccccc2n1
*CC(*)n1ccc2ccccc21
*CC(*)n1ccnc1
*CC(*)n1cncn1
*CC(=C)COCC(=C)COC(=O)O*
*CC(=O)C(*)C
*CC(=O)C(*)c1ccc(C(C)(C)C)cc1
*CC(=O)C(*)c1ccccc1
*CC(=O)NC(C)C(=O)O*
*CC(=O)NCC(=O)O*
*CC(=O)NCCCCCCNC(=O)CC1COC(*)CO1
*CC(=O)NCCCCCCNC(=O)CCc1ccc(O*)cc1
*CC(=O)NCCCCCCNC(=O)CO*
*CC(=O)NCCCCCCNC(=O)COC(=O)CCC(=O)O*
*CC(=O)NCCCCCCNC(=O)COC(=O)CCCCC(=O)O*
*CC(=O)NCCCCCCNC(=O)COC(=O)CCCCCCC(=O)O*
*CC(=O)NCCCCCCNC(=O)COC(=O)CCCCCCCCC(=O)O*
*CC(=O)NCCCCCCNC(=O)COC(=O)CCCCCCCCCCC(=O)O*
*CC(=O)NCCCCCCNC(=O)COc1ccc(O*)cc1
*CC(=O)NCCCCCCNC(=O)CS*
*CC(=O)NCCCCCCNC(=O)Cc1ccc(O*)cc1
*CC(=O)Nc1ccc(C2(c3ccc(NC(=O)Cn4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)cc3)c3ccccc3-c3ccccc32)cc1
*CC(=O)Nc1ccc(C2(c3ccc(NC(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3)c3ccccc3-c3ccccc32)cc1
*CC(=O)Nc1ccc(Cc2ccc(NC(=O)CN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*CC(=O)Nc1ccc(Cc2ccc(NC(=O)COc3ccc4ccccc4c3Sc3c(O*)ccc4ccccc34)cc2)cc1
*CC(=O)Nc1ccc(Cc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2)cc1
*CC(=O)Nc1ccc(NC(=O)CN2C(=O)c3ccc(C(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)cc1
*CC(=O)Nc1ccc(NC(=O)COc2ccc3ccccc3c2Sc2c(O*)ccc3ccccc23)cc1
*CC(=O)Nc1ccc(NC(=O)c2ccc3c(c2)C(=O)N(*)C3=O)cc1
*CC(=O)Nc1ccc(Oc2c(F)c(F)c(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3)c(F)c2F)cc1
*CC(=O)Nc1ccc(Oc2ccc(-c3ccc(Oc4ccc(NC(=O)CN5C(=O)c6ccc(C(c7ccc8c(c7)C(=O)N(*)C8=O)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)cc3)cc2)cc1
*CC(=O)Nc1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(NC(=O)CN5C(=O)c6ccc(C(c7ccc8c(c7)C(=O)N(*)C8=O)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)cc3)cc2)cc1
*CC(=O)Nc1ccc(Oc2ccc(NC(=O)CN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*CC(=O)Nc1ccc(Oc2ccc(NC(=O)COc3ccc4ccccc4c3Sc3c(O*)ccc4ccccc34)cc2)cc1
*CC(=O)Nc1ccc(Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2)cc1
*CC(=O)Nc1ccc(Oc2ccc(Oc3ccc(NC(=O)CN4C(=O)c5ccc(C(=O)c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)cc2)cc1
*CC(=O)Nc1ccc(Oc2ccc(Oc3ccc(NC(=O)CN4C(=O)c5ccc(C(c6ccc7c(c6)C(=O)N(*)C7=O)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc2)cc1
*CC(=O)Nc1ccc(Oc2ccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2)cc1
*CC(=O)Nc1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(NC(=O)CN5C(=O)c6ccc(C(c7ccc8c(c7)C(=O)N(*)C8=O)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)cc3)cc2)cc1
*CC(=O)Nc1ccc(Oc2cccc(NC(=O)CN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)cc1
*CC(=O)Nc1ccc(Oc2cccc(Oc3ccc(NC(=O)CN4C(=O)c5ccc(C(c6ccc7c(c6)C(=O)N(*)C7=O)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)c2)cc1
*CC(=O)Nc1ccc(Oc2cccc(Oc3ccc(NC(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3)c2)cc1
*CC(=O)Nc1ccc(S(=O)(=O)c2ccc(NC(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2)cc1
*CC(=O)Nc1cccc(NC(=O)COc2ccc3ccccc3c2Sc2c(O*)ccc3ccccc23)c1
*CC(=O)Nc1cccc2c(NC(=O)COc3ccc4ccccc4c3Sc3c(O*)ccc4ccccc34)cccc12
*CC(=O)O*
*CC(=O)OC(=O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1
*CC(=O)OC(=O)COc1ccc(O*)cc1
*CC(=O)c1ccc(Oc2ccc(C(=O)COc3ccc(/C=C4\CCC/C(=C\c5ccc(O*)c(OC)c5)C4=O)cc3OC)cc2)cc1
*CC(C#N)C(CCC(C)(O)C=C)C(*)(C)C
*CC(C(C)C)C(COC(=O)c1ccc(C(=O)O*)cc1)C(C)C
*CC(C)(/C=C(/*)C)C(=O)OC
*CC(C)(C)C(=O)O*
*CC(C)(C)C1C(=O)N(C(C)C)C(=O)C1*
*CC(C)(C)C1C(=O)N(C)C(=O)C1*
*CC(C)(C)C1C(=O)N(C2CCCCC2)C(=O)C1*
*CC(C)(C)C1C(=O)N(CCCC)C(=O)C1*
*CC(C)(C)C1C(=O)N(c2c(C)cccc2C)C(=O)C1*
*CC(C)(C)C1C(=O)N(c2c(CC)cccc2CC)C(=O)C1*
*CC(C)(C)C1C(=O)N(c2ccccc2)C(=O)C1*
*CC(C)(C)C1C(=O)N(c2ccccc2C)C(=O)C1*
*CC(C)(C)C1C(=O)OC(=O)C1*
*CC(C)(C)C1CC=C(*)CC1
*CC(C)(C)CO*
*CC(C)(C)COC(=O)C1CCC(C(=O)O*)CC1
*CC(C)(C)COC(=O)CCC(=O)O*
*CC(C)(C)COC(=O)CCCCC(=O)O*
*CC(C)(C)COC(=O)Cc1ccc(C(=O)O*)cc1
*CC(C)(C)COC(=O)NC(=O)c1cc(C(=O)NC(=O)O*)cc(C(C)(C)C)c1
*CC(C)(C)COC(=O)NC(=O)c1ccc(C(=O)NC(=O)O*)cc1
*CC(C)(C)COC(=O)NC(=O)c1cccc(C(=O)NC(=O)O*)c1
*CC(C)(C)COC(=O)Nc1ccc(C)c(NC(=O)O*)c1
*CC(C)(C)COC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CC(C)(C)COC(=O)O*
*CC(C)(C)COC(=O)c1ccc(-c2ccc(-c3ccc(C(=O)O*)cc3)cc2)cc1
*CC(C)(C)COC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CC(C)(C)COC(=O)c1ccc(/C=C/c2ccc(C(=O)O*)cc2)cc1
*CC(C)(C)COC(=O)c1ccc(C(=O)O*)cc1
*CC(C)(C)CS(*)(=O)=O
*CC(C)(C)CS(=O)(=O)CC(C)(C)COC(=O)NCCCCCCNC(=O)O*
*CC(C)(C)CS(=O)(=O)CC(C)(C)COC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CC(C)(C)CS(=O)(=O)CC(C)(C)COC(=O)O*
*CC(C)(C)CS(=O)(=O)CC(C)(C)COC(=O)c1ccc(C(=O)O*)cc1
*CC(C)(C)CS*
*CC(C)(C)O*
*CC(C)(C)S*
*CC(C)(CC)C(=O)O*
*CC(C)(CC)CO*
*CC(C)(CC)COC(=O)CCCCCCCCC(=O)O*
*CC(C)(CC)COC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CC(C)(CC)COC(=O)c1ccc(C(=O)O*)cc1
*CC(C)(CC)CS*
*CC(C)(CCC)C(=O)O*
*CC(C)(CCC)COC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CC(C)(CO)CO*
*CC(C)(CO*)COCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CC(C)(CO*)COCC(F)(F)C(F)(F)C(F)(F)F
*CC(C)(CO*)COCC(F)(F)F
*CC(C)(CO*)COCCC#N
*CC(C)(CO*)COCCCCCCCCCCOc1ccc(-c2ccc(C#N)cc2)cc1
*CC(C)(CO*)COCCOCCOCCOC
*CC(C)(COC(=O)NCCCCCCNC(=O)O*)C(=O)O
*CC(C)(COC(=O)O*)C(=O)OCc1ccccc1
*CC(C)(N=C=O)C1C(=O)OC(=O)C1*
*CC(C)/C=C(/*)C
*CC(C)C(*)(C)C
*CC(C)C(=O)N*
*CC(C)C(=O)O*
*CC(C)C(C)COC(=O)c1ccc(C(=O)O*)cc1
*CC(C)CC(*)(C)C(=O)OC
*CC(C)CCC(C)CNC(=O)c1cc(C(=O)N*)cc(C(C)(C)C)c1
*CC(C)CNC(=O)O*
*CC(C)CO*
*CC(C)COC(=O)c1ccc(C(=O)O*)cc1
*CC(C)N1C(=O)C2C3C=CC(C4C(=O)N(*)C(=O)C34)C2C1=O
*CC(C)O*
*CC(C)S*
*CC(CBr)O*
*CC(CC(*)(C#N)C#N)c1ccc(CCl)cc1
*CC(CC(*)(C#N)C#N)c1ccc(Cl)cc1
*CC(CC(*)(C#N)C#N)c1ccc(F)cc1
*CC(CC(*)(C#N)C#N)c1ccc(OC(C)=O)cc1
*CC(CC(C)C)S(*)(=O)=O
*CC(CC(Cl)(Cl)Cl)O*
*CC(CC)(CC)C(=O)O*
*CC(CC)(CC)CO*
*CC(CC)(CC)COC(=O)Nc1ccc(C)c(NC(=O)O*)c1
*CC(CC)(CC)COC(=O)c1ccc(-c2ccc(-c3ccc(C(=O)O*)cc3)cc2)cc1
*CC(CC)(CC)CS*
*CC(CC)(CCl)CO*
*CC(CC)(CO)CO*
*CC(CC)(CO*)COC(=O)C(=C)C
*CC(CC)C(CC)COC(=O)c1ccc(C(=O)O*)cc1
*CC(CC)N(*)C(=O)CCCC
*CC(CC)O*
*CC(CC)S*
*CC(CCC)C(CCC)COC(=O)c1ccc(C(=O)O*)cc1
*CC(CCCC)C(CCCC)COC(=O)c1ccc(C(=O)O*)cc1
*CC(CCCC)COC(=O)NC(=O)c1cccc(C(=O)NC(=O)O*)c1
*CC(CCCC)O*
*CC(CCCCCC)C(CCCCCC)COC(=O)c1ccc(C(=O)O*)cc1
*CC(CCCCCC)O*
*CC(CCCCCCCC)COC(=O)NC(=O)c1cccc(C(=O)NC(=O)O*)c1
*CC(CCCCCCCCCC)C(CCCCCCCCCC)COC(=O)c1ccc(C(=O)O*)cc1
*CC(CCCCCCCCCC)O*
*CC(CCCCCCCCCCCC)COC(=O)NC(=O)c1ccc(C(=O)NC(=O)O*)cc1
*CC(CCCCCCCCCCCC)COC(=O)NC(=O)c1cccc(C(=O)NC(=O)O*)c1
*CC(CCCCCCCCCCCCCCCC)C(CCCCCCCCCCCCCCCC)COC(=O)c1ccc(C(=O)O*)cc1
*CC(CCCCCCCCCCCCCCCC)C1C(=O)N(CCCCCCCCCCCC)C(=O)C1*
*CC(CCCCCCCCCCCCCCCC)C1C(=O)N(CCCCNC(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)C(=O)C1*
*CC(CCCCCCOc1ccc(/N=N/c2ccc(Br)cc2)cc1)COC(=O)CCCCC(=O)O*
*CC(CCCCCCOc1ccc(/N=N/c2ccc(C#N)cc2)cc1)COC(=O)CCCCCCCCCCCCC(=O)O*
*CC(CCCCCCOc1ccc(/N=N/c2ccc(C(F)(F)F)cc2)cc1)COC(=O)CCCCC(=O)O*
*CC(CCCCCCOc1ccc(/N=N/c2ccc(C)cc2)cc1)COC(=O)CCCCC(=O)O*
*CC(CCCCCCOc1ccc(/N=N/c2ccc(Cl)cc2)cc1)COC(=O)CCCCC(=O)O*
*CC(CCCCCCOc1ccc(/N=N/c2ccc(OC)cc2)cc1)COC(=O)CCCCC(=O)O*
*CC(CCl)(CCl)CO*
*CC(CCl)O*
*CC(CN1C(=O)C2C3C=CC(C3)C2C1=O)O*
*CC(CO)(CCl)COc1ccc(C(c2ccc(O*)cc2)(C(F)(F)F)C(F)(F)F)cc1
*CC(CO)(CO)CO*
*CC(CO*)(CS(=O)(=O)C(C)C)CS(=O)(=O)C(C)C
*CC(CO*)(CS(=O)(=O)CC)CS(=O)(=O)CC
*CC(CO*)(CS(=O)(=O)CCC)CS(=O)(=O)CCC
*CC(CO*)(CS(=O)(=O)CCCC)CS(=O)(=O)CCCC
*CC(CO*)(CS(=O)(=O)CCCCC)CS(=O)(=O)CCCCC
*CC(CO*)(CS(C)(=O)=O)CS(C)(=O)=O
*CC(CO*)(CSC(C)C)CSC(C)C
*CC(CO*)(CSC)CSC
*CC(CO*)(CSCC)CSCC
*CC(CO*)(CSCCCCC)CSCCCCC
*CC(CO*)C(C)(C)C
*CC(COC(=O)CCCCCCCCCCN1C(=O)C2C3C=CC(C3)C2C1=O)O*
*CC(COC(=O)CCCCCN1C(=O)C2C3C=CC(C3)C2C1=O)O*
*CC(COC(=O)O*)OCCCCCCCCCCCOc1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1
*CC(COC(=O)O*)OCCCCCCOc1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1
*CC(COC(=O)O*)OCCCCOc1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1
*CC(COC(=O)O*)OCCOc1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1
*CC(COC(=O)c1ccc(N2C(=O)C3C4C=CC(C4)C3C2=O)cc1)O*
*CC(COC(C)(C)C)O*
*CC(COC(C)C)O*
*CC(COC(F)(C(F)(F)F)C(F)(F)F)O*
*CC(COC)O*
*CC(COCC)O*
*CC(COCC=C)O*
*CC(COCC=C)S*
*CC(COCCCC)O*
*CC(COCCCCCC)O*
*CC(COCCOCCOC)O*
*CC(COCOCC(C)([N+](=O)[O-])[N+](=O)[O-])O*
*CC(COc1c(Cl)cc(C(C)(C)c2cc(Cl)c(O*)c(Cl)c2)cc1Cl)OC(C)=O
*CC(COc1ccc(-c2ccc(C#N)cc2)cc1)O*
*CC(COc1ccc(-c2ccc(OC)cc2)cc1)O*
*CC(COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1)OC(=O)/C=C/c1ccccc1
*CC(COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1)OC(=O)/C=C/c1ccco1
*CC(COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1)OC(=O)CC
*CC(COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1)OC(=O)CCl
*CC(COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1)OC(=O)c1ccccc1
*CC(COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1)OC(=O)c1ccccc1Cl
*CC(COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1)OC(C)=O
*CC(COc1ccc(C(C)(c2ccccc2)c2ccc(O*)cc2)cc1)OC(=O)c1ccccc1
*CC(COc1ccc(C(C)(c2ccccc2)c2ccc(O*)cc2)cc1)OC(C)=O
*CC(COc1ccc(C2CCCC(CC)(c3ccc(O*)cc3)C2)cc1)OC(C)=O
*CC(COc1ccc(O*)cc1)OC(C)=O
*CC(COc1ccc(S(=O)(=O)c2ccc(O*)cc2)cc1)OC(C)=O
*CC(COc1ccccc1)O*
*CC(CS(=O)(=O)C(C)C)O*
*CC(CS(=O)(=O)CC)O*
*CC(CS(=O)(=O)CCC)O*
*CC(CS(=O)(=O)CCCC)O*
*CC(CS(=O)(=O)CCCCC)O*
*CC(CS(C)(=O)=O)O*
*CC(CSC(C)C)O*
*CC(CSC)O*
*CC(CSCC)O*
*CC(CSCCC)O*
*CC(CSCCCC)O*
*CC(CSCCCCC)O*
*CC(C[N-][N+]#N)(C[N-][N+]#N)CO*
*CC(C[N-][N+]#N)O*
*CC(C[Si](*)(C)C)c1cccc2ccccc12
*CC(C[Si](*)(C)C)c1ccccc1
*CC(Cc1ccc(-c2ccccc2)cc1)C[Si](*)(C)C
*CC(Cc1cccc2ccccc12)C[Si](*)(C)C
*CC(F)(F)/C(F)=C(/*)Cl
*CC(F)(F)C(F)(F)C(F)(F)C(F)(F)COC(=O)NCC(F)(F)C(F)(F)C(F)(F)C(F)(F)CNC(=O)O*
*CC(F)(F)C(F)(F)C(F)(F)C(F)(F)COC(=O)NCCCCCCNC(=O)O*
*CC(F)(F)C(F)(F)C(F)(F)C(F)(F)COC(=O)O*
*CC(F)(F)C(F)(F)C(F)(F)COC(=O)CCCCC(=O)O*
*CC(F)(F)C(F)(F)C(F)(F)COC(=O)NCC(F)(F)C(F)(F)C(F)(F)C(F)(F)CNC(=O)O*
*CC(F)(F)C(F)(F)C(F)(F)COC(=O)NCCCCCCNC(=O)O*
*CC(F)(F)C(F)(F)C(F)(F)COC(=O)c1cc(C(=O)O*)cc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)c1
*CC(F)(F)C(F)(F)C(F)(F)COC(=O)c1cc(OCCCCC)cc(C(=O)O*)c1
*CC(F)(F)C(F)(F)C(F)(F)COC(=O)c1ccc(OCCCCC)c(C(=O)O*)c1
*CC(F)(F)C(F)(F)C(F)(F)COC(=O)c1cccc(-c2cccc(C(=O)O*)c2)c1
*CC(F)(F)C(F)(F)C(F)(F)COC(=O)c1cccc(C(=O)O*)c1
*CC(F)(F)C(F)(F)C(F)(F)COC(=O)c1cccc(C(=O)O*)c1OCCCCC
*CC(F)(F)C(F)(F)C(F)(F)COC(=O)c1cccc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)c2cccc(C(=O)O*)c2)c1
*CC(F)(F)C(F)(F)C(F)(F)COC(=O)c1cccc(C(F)(F)C(F)(F)C(F)(F)c2cccc(C(=O)O*)c2)c1
*CC(F)(F)C(F)(F)COC(=O)NCC(F)(F)C(F)(F)C(F)(F)C(F)(F)CNC(=O)O*
*CC(F)(F)C(F)(F)COC(=O)NCCCCCCNC(=O)O*
*CC(F)(F)C1(F)C(*)CC(O)(C(F)(F)F)C1(F)F
*CC(F)(F)C1(F)CC(C(O)(C(F)(F)F)C(F)(F)F)CC1*
*CC(F)(F)C1(F)CC(CC(O)(C(F)(F)F)C(F)(F)F)CC1*
*CC(F)(F)CC(*)(C(F)(F)F)C(F)(F)F
*CC(O)C(O)CN(*)c1ccc(N(C)C)cc1
*CC(O)C(O)COC(=O)O*
*CC(O)CCCCC(O)CN(*)c1ccc(N(C)C)cc1
*CC(O)CN(*)c1cc(C)c(/N=N/c2ccc(C#N)cc2)c(C)c1
*CC(O)CN(*)c1cc(C)c(/N=N/c2ccc(Cl)cc2)c(C)c1
*CC(O)CN(*)c1cc(C)c(/N=N/c2ccc([N+](=O)[O-])cc2)c(C)c1
*CC(O)CN(*)c1cc(C)c(/N=N/c2ccc([N+](=O)[O-])cc2C)c(C)c1
*CC(O)CN(*)c1cc(C)cc(C)c1
*CC(O)CN(*)c1ccc(/N=N/c2ccc(C(=O)O)cc2)cc1
*CC(O)CN(*)c1ccc(/N=N/c2ccc(Cl)cc2)cc1
*CC(O)CN(*)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2C)cc1
*CC(O)CN(*)c1ccccc1
*CC(O)CN(C)S(=O)(=O)c1ccc(-c2ccc(S(=O)(=O)N(C)CC(O)COc3ccc(C(C)(C)c4ccc(O*)cc4)cc3)cc2)cc1
*CC(O)CN(C)S(=O)(=O)c1ccc(-c2ccc(S(=O)(=O)N(C)CC(O)COc3ccc(O*)cc3)cc2)cc1
*CC(O)CN(C)S(=O)(=O)c1cccc(S(=O)(=O)N(C)CC(O)COc2ccc(-c3ccc(O*)cc3)cc2)c1
*CC(O)CN(C)S(=O)(=O)c1cccc(S(=O)(=O)N(C)CC(O)COc2ccc(C(C)(C)c3ccc(O*)cc3)cc2)c1
*CC(O)CN(C)S(=O)(=O)c1cccc(S(=O)(=O)N(C)CC(O)COc2ccc(C3(c4ccc(O*)cc4)c4ccccc4-c4ccccc43)cc2)c1
*CC(O)CN(C)S(=O)(=O)c1cccc(S(=O)(=O)N(C)CC(O)COc2ccc(O*)cc2)c1
*CC(O)CN(C)S(=O)(=O)c1cccc(S(=O)(=O)N(C)CC(O)COc2ccc(S(=O)(=O)c3ccc(O*)cc3)cc2)c1
*CC(O)CN(C)S(=O)(=O)c1cccc(S(=O)(=O)N(C)CC(O)COc2ccc(Sc3ccc(O*)cc3)cc2)c1
*CC(O)CN(CC(O)CN(*)c1ccccc1)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CC(O)CN(CC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1)c1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1
*CC(O)CN(CC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CC(O)CN(CC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1)c1ccc(C#N)cc1
*CC(O)CN(CCO)CC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1
*CC(O)CN(CCO)S(=O)(=O)c1ccc(-c2ccc(S(=O)(=O)N(CCO)CC(O)COc3ccc(C(C)(C)c4ccc(O*)cc4)cc3)cc2)cc1
*CC(O)CN(Cc1ccccc1)CC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1
*CC(O)CO*
*CC(O)COC(=O)/C=C/C(=O)Oc1ccc(C(C)(C)c2ccc(O*)cc2)cc1
*CC(O)COC(=O)C1CCC(C(=O)OCC(O)COc2ccc(C(C)(C)c3ccc(O*)cc3)cc2)CC1
*CC(O)COC(=O)C1CCC(C(=O)OCC(O)COc2ccc(C3(c4ccc(O*)cc4)c4ccccc4-c4ccccc43)cc2)CC1
*CC(O)COC(=O)CCCCC(=O)OCC(O)COc1ccc(/C=C(\C)c2ccc(O*)cc2)cc1
*CC(O)COC(=O)CCCCC(=O)OCC(O)COc1ccc(C(=O)Oc2ccc(O*)cc2)cc1
*CC(O)COC(=O)CCCCC(=O)OCC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1
*CC(O)COC(=O)CCCCC(=O)OCC(O)COc1ccc(O*)cc1
*CC(O)COC(=O)CCCCCCC(=O)OCC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1
*CC(O)COC(=O)CCCCCCCCC(=O)OCC(O)COc1ccc(/C=C(\C)c2ccc(O*)cc2)cc1
*CC(O)COC(=O)CCCCCCCCC(=O)OCC(O)COc1ccc(C(=O)Oc2ccc(O*)cc2)cc1
*CC(O)COC(=O)CCCCCCCCC(=O)OCC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1
*CC(O)COC(=O)CCCCCCCCCCC(=O)OCC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1
*CC(O)COC(=O)CCCCCCCCCCC(=O)OCC(O)COc1ccc(O*)cc1
*CC(O)COC(=O)c1cccc(C(=O)OCC(O)COc2ccc(C(C)(C)c3ccc(O*)cc3)cc2)c1
*CC(O)COc1c(C)cc(C(C)(C)c2cc(C)c(O*)c(C)c2)cc1C
*CC(O)COc1c(C)cc(C(C)(C)c2cc(C)c(OCC(O)COc3c(C)cc(S(=O)(=O)c4cc(C)c(O*)c(C)c4)cc3C)c(C)c2)cc1C
*CC(O)COc1c(C)cc(C(C)(C)c2cc(C)c(OCC(O)COc3ccc(S(=O)(=O)c4ccc(O*)cc4)cc3)c(C)c2)cc1C
*CC(O)COc1c(C)cc(S(=O)(=O)c2cc(C)c(O*)c(C)c2)cc1C
*CC(O)COc1c(Cl)cc(C(C)(C)c2cc(Cl)c(O*)c(Cl)c2)cc1Cl
*CC(O)COc1c(Cl)cc(C(C)(C)c2cc(Cl)c(OCC(O)COc3ccc(C(C)(C)c4ccc(O*)cc4)cc3)c(Cl)c2)cc1Cl
*CC(O)COc1ccc(C(=O)c2ccc(O*)cc2)cc1
*CC(O)COc1ccc(C(=O)c2ccc(OCC(O)COc3ccc(C(C)(C)c4ccc(O*)cc4)cc3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)C2CCC(C)(c3ccc(O*)cc3)CC2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(O*)c(Cl)c2)cc1Cl
*CC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)CN3CCN(*)CC3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3c(C)cc(C(C)(C)c4cc(C)c(O*)c(C)c4)cc3C)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3c(C)cc(S(=O)(=O)c4cc(C)c(O*)c(C)c4)cc3C)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3ccc(C(=O)Nc4ccc(O*)cc4)cc3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3ccc(C4(c5ccc(O*)cc5)c5ccccc5-c5ccccc54)cc3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3ccc(NC(=O)CCCC(=O)Nc4ccc(O*)cc4)cc3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3ccc(NC(=O)CCCCC(=O)Nc4ccc(O*)cc4)cc3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3ccc(NC(=O)CCCCCC(=O)Nc4ccc(O*)cc4)cc3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3ccc(Oc4ccc(O*)cc4)cc3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3ccc(S(=O)(=O)c4ccc(O*)c(C)c4)cc3C)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3ccc(S(=O)(=O)c4ccc(O*)cc4)cc3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3cccc(C(=O)Nc4cccc(O*)c4)c3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3cccc(NC(=O)C(=O)Nc4cccc(O*)c4)c3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3cccc(NC(=O)CCCC(=O)Nc4cccc(O*)c4)c3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3cccc(NC(=O)CCCCC(=O)Nc4cccc(O*)c4)c3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3cccc(NC(=O)CCCCCC(=O)Nc4cccc(O*)c4)c3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3cccc(NC(=O)CCCCCCC(=O)Nc4cccc(O*)c4)c3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3cccc(NC(=O)CCCCCCCCCCC(=O)Nc4cccc(O*)c4)c3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3cccc(NC(=O)Cc4cccc(CC(=O)Nc5cccc(O*)c5)c4)c3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3cccc(NC(=O)c4cccc(C(=O)Nc5cccc(O*)c5)c4)c3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3cccc(O*)c3)cc2)cc1
*CC(O)COc1ccc(C(C)(C)c2ccc(OCC(O)COc3ccccc3C(=O)Nc3ccc(O*)cc3)cc2)cc1
*CC(O)COc1ccc(C(C)(CC)c2ccc(O*)cc2)cc1
*CC(O)COc1ccc(C(C)(c2ccccc2)c2ccc(O*)cc2)cc1
*CC(O)COc1ccc(C(C)CC(C)(C)c2ccc(O*)cc2)cc1
*CC(O)COc1ccc(C2(C)CC(C)(C)c3cc(O*)ccc32)cc1
*CC(O)COc1ccc(C2CC(C(C)C)CCC2(C)c2ccc(O*)cc2)cc1
*CC(O)COc1ccc(C2CCCC(CC)(c3ccc(O*)cc3)C2)cc1
*CC(O)COc1ccc(Cc2ccc(O*)cc2)cc1
*CC(O)COc1ccc(O*)cc1
*CC(O)COc1ccc(S(=O)(=O)c2ccc(O*)c(C)c2)cc1C
*CC(O)COc1ccc(S(=O)(=O)c2ccc(O*)cc2)cc1
*CC(O)COc1ccc(S(=O)(=O)c2ccc(OCC(O)COc3c(C)cc(S(=O)(=O)c4cc(C)c(O*)c(C)c4)cc3C)cc2)cc1
*CC(O)COc1ccc(S(=O)(=O)c2ccc(OCC(O)COc3ccc(S(=O)(=O)c4ccc(O*)c(C)c4)cc3C)cc2)cc1
*CC(O)COc1ccc(S(=O)(=O)c2ccc(OCC(O)COc3cccc(O*)c3)cc2)cc1
*CC(O)COc1cccc(O*)c1
*CC(O*)C(C)(C)C
*CC(O*)C1CC(F)(F)C1(F)F
*CC(O*)c1ccccc1
*CC(O*)c1ccco1
*CC(OC(=O)C(=O)OCC)C(COC(=O)O*)OC(=O)C(=O)OCC
*CC(OC(=O)C(CC(C)C)NC(=O)OCc1ccccc1)C(COC(=O)O*)OC(=O)C(CC(C)C)NC(=O)OCc1ccccc1
*CC(OC(=O)C(Cc1ccccc1)NC(=O)OC(C)(C)C)C(COC(=O)O*)OC(=O)C(Cc1ccccc1)NC(=O)OC(C)(C)C
*CC(OC(=O)OCC)C(COC(=O)O*)OC(=O)OCC
*CC(OC(=O)Oc1ccc(C(=O)OC)cc1)C(COC(=O)O*)OC(=O)Oc1ccc(C(=O)OC)cc1
*CC(OC(C)=O)C(CCC(C)(O)C=C)C(*)(C)C
*CC(OC(C)=O)C(COC(=O)O*)OC(C)=O
*CC(OC(C)C)C1C(=O)OC(=O)C1*
*CC(OC)C(C(=O)O)C(*)C(=O)O
*CC(OC)C(C(=O)OC)C(*)C(=O)OC
*CC(OC)C(C)(C(=O)OC)C(*)(C)C(=O)OC
*CC(OC)C1(C)C(=O)OC(=O)C1(*)C
*CC(OC)C1C(=O)N(c2ccc(N(C)C)cc2)C(=O)C1*
*CC(OC)C1C(=O)OC(=O)C1*
*CC(OC1CCCCC1)C1C(=O)OC(=O)C1*
*CC(OC=O)C(COC(=O)O*)OC=O
*CC(OCC(C)C)C(C(=O)OC)C(*)C(=O)OC
*CC(OCC(C)C)C(C)(C(=O)OC)C(*)(C)C(=O)OC
*CC(OCC(C)C)C1(C)C(=O)OC(=O)C1(*)C
*CC(OCC(C)C)C1C(=O)OC(=O)C1*
*CC(OCC)C(C(=O)OC)C(*)C(=O)OC
*CC(OCC)C(C)(C(=O)OC)C(*)(C)C(=O)OC
*CC(OCC)C1(C)C(=O)OC(=O)C1(*)C
*CC(OCC)C1C(=O)OC(=O)C1*
*CC(OCCCC)C(C(=O)OC)C(*)C(=O)OC
*CC(OCCCC)C(C)(C(=O)OC)C(*)(C)C(=O)OC
*CC(OCCCC)C1(C)C(=O)OC(=O)C1(*)C
*CC(OCCCC)C1C(=O)OC(=O)C1*
*CC(OO*)c1ccc(Br)cc1
*CC(OO*)c1ccc(C(C)(C)C)cc1
*CC(OO*)c1ccc(C)cc1
*CC(S*)c1ccccc1
*CC(SS*)c1ccccc1
*CC(c1ccccc1)C1C(=O)N(C(C)(C)C)C(=O)C1*
*CC(c1ccccc1)C1C(=O)N(C)C(=O)C1*
*CC(c1ccccc1)C1C(=O)N(CCCCCCCCCCCC)C(=O)C1*
*CC(c1ccccc1)C1C(=O)N(CCCCNC(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)C(=O)C1*
*CC(c1ccccc1)C1C(=O)N(CCCN(CC)c2ccc(/N=N/c3ccc([N+](=O)[O-])cc3)c(C)c2)C(=O)C1*
*CC(c1ccccc1)C1C(=O)N(CCCN(CC)c2ccc(/N=N/c3ccc([N+](=O)[O-])cc3Cl)c(C)c2)C(=O)C1*
*CC(c1ccccc1)C1C(=O)N(CCCOc2ccc(-c3ccc(C#N)cc3)cc2)C(=O)C1*
*CC(c1ccccc1)C1C(=O)N(CCN(CC)c2ccc(/N=N/c3ccc([N+](=O)[O-])cc3)cc2)C(=O)C1*
*CC(c1ccccc1)C1C(=O)N(c2ccc(C(C)(C)C)cc2)C(=O)C1*
*CC(c1ccccc1)C1C(=O)N(c2ccc(NC(=O)OCC3CCCN3c3ccc([N+](=O)[O-])cc3)cc2)C(=O)C1*
*CC(c1ccccc1)C1C(=O)N(c2ccc(Oc3ccccc3)cc2)C(=O)C1*
*CC(c1ccccc1)C1C(=O)N(c2ccccc2)C(=O)C1*
*CC(c1ccccc1)C1C(=O)NC(=O)C1*
*CC(c1ccccc1)C1C(=O)OC(=O)C1*
*CC(c1ccccn1)C(c1ccccc1)C(*)c1ccccn1
*CC/C(=C(/*)c1ccccc1)c1ccccc1
*CC/C(C)=C(/*)C
*CC/C(C)=C(/*)C
*CC/C=C(/*)C
*CC/C=C(/*)C
*CC/C=C(/*)C
*CC/C=C(/*)C(C)(C)C
*CC/C=C(/*)C(C)(C)C
*CC/C=C(/*)C(C)C
*CC/C=C(/*)CCCCCCC
*CC/C=C(/*)CCCCCCCCCC
*CC/C=C(/*)Cl
*CC/C=C(/*)Cl
*CC/C=C(/*)[Sn](CCCC)(CCCC)CCCC
*CC/C=C(/*)c1ccccc1
*CC/C=C/C[Si](*)(C)c1ccccc1
*CC1(*)CC(=O)N(c2ccccc2)C1=O
*CC1(*)CCCCC1
*CC1(*)OCC(c2ccccc2)O1
*CC1(C)CC(*)(C)C(=O)OC1=O
*CC1(C)CC(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)CC(C)(C)C1
*CC1(C)CC(N2C(=O)c3ccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)CC(C)(C)C1
*CC1CC(*)C(=O)OC1=O
*CC1CC(*)C(OC(=O)CCCCCCCCCCOc2ccc(-c3ccc(C#N)cc3)cc2)C1
*CC1CC(*)C(OC(=O)CCCCCCCCCCOc2ccc(-c3ccc(C#N)cc3)cc2)C1OC(=O)CCCCCCCCCCOc1ccc(-c2ccc(C#N)cc2)cc1
*CC1CC(*)OC(CCC)O1
*CC1CC(*)OCO1
*CC1CC(CNC(=O)c2cccc(C(=O)N*)c2)CC(C(C)(C)C)C1
*CC1CC(COC(=O)c2ccc(C(=O)O*)cc2)CC(C(C)(C)C)C1
*CC1CC2CC1C1CCC(CN3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)C21
*CC1CC2CC1C1CCC(CN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)C21
*CC1CC2CC1C1CCC(CN3C(=O)c4ccc(C(c5ccc6c(c5)C(=O)N(*)C6=O)(C(F)(F)F)C(F)(F)F)cc4C3=O)C21
*CC1CC2CC1C1CCC(CN3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)C21
*CC1CC2CC1C1CCC(Cn3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)C21
*CC1CCC(CNC(=O)CCCCC(=O)N*)CC1
*CC1CCC(CNC(=O)CCCCCCCCC(=O)N*)CC1
*CC1CCC(CNC(=O)CCCCCCCCCCC(=O)N*)CC1
*CC1CCC(CNC(=O)c2cc(C(=O)N*)cc(C(C)(C)C)c2)CC1
*CC1CCC(CNC(=O)c2cccc(C(=O)N*)c2)CC1
*CC1CCC(COC(=O)C2CCC(C(=O)O*)CC2)CC1
*CC1CCC(COC(=O)CCC(=O)O*)CC1
*CC1CCC(COC(=O)Nc2ccc(Cc3ccc(NC(=O)O*)cc3)cc2)CC1
*CC1CCC(COC(=O)O*)CC1
*CC1CCC(COC(=O)c2ccc(C(=O)O*)cc2)CC1
*CC1CCC(COC(=O)c2cccc(C(=O)O*)c2)CC1
*CC1CCC(COC(C)OC(=O)c2ccc(C(=O)OC(C)O*)c3ccccc23)CC1
*CC1CCC(COC(C)OC(=O)c2ccc(C(=O)OC(C)O*)cc2)CC1
*CC1CCC(COC(C)OC(=O)c2cccc(C(=O)OC(C)O*)c2)CC1
*CC1CCC(COP(=O)(/N=N/c2ccc(-c3ccc(/N=N/P(=O)(O*)OC)cc3)cc2)OC)CC1
*CC1CCC(COP(=O)(/N=N/c2ccc(C(=O)c3ccc(/N=N/P(=O)(O*)OC)cc3)cc2)OC)CC1
*CC1CCC(COP(=O)(/N=N/c2ccc(Oc3ccc(/N=N/P(=O)(O*)OC)cc3)cc2)OC)CC1
*CC1CCCC(CN2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)C1
*CC1CCCC(CN2C(=O)c3ccc(C(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)C1
*CC1CCCC(CN2C(=O)c3ccc(C(c4ccc5c(c4)C(=O)N(*)C5=O)(C(F)(F)F)C(F)(F)F)cc3C2=O)C1
*CC1CCCC(CN2C(=O)c3ccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)C1
*CC1CCCC(CNC(=O)CCCCC(=O)N*)C1
*CC1CCCC(CNC(=O)c2cc(C(=O)N*)cc(C(C)(C)C)c2)C1
*CC1CCCC(CNC(=O)c2cccc(C(=O)N*)c2)C1
*CC1CCCC(COC(=O)c2ccc(C(=O)O*)cc2)C1
*CC1CCCC(Cn2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)C1
*CCC(*)(C)C
*CCC(*)(C)c1ccccc1
*CCC(*)=O
*CCC(=O)N*
*CCC(=O)NCCCCCCNC(=O)CCS*
*CCC(=O)NCc1c(C)c(C)c(CNC(=O)CCO*)c(C)c1C
*CCC(=O)NCc1cc(C)c(CNC(=O)CCO*)cc1C
*CCC(=O)Nc1ccc(Cc2ccc(NC(=O)CCN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*CCC(=O)Nc1ccc(NC(=O)CCN2C(=O)c3ccc(C(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)cc1
*CCC(=O)Nc1ccc(Oc2ccc(NC(=O)CCN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*CCC(=O)Nc1ccc(Oc2ccc(Oc3ccc(NC(=O)CCN4C(=O)c5ccc(C(=O)c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)cc2)cc1
*CCC(=O)Nc1ccc(Oc2cccc(NC(=O)CCN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)cc1
*CCC(=O)O*
*CCC(C(=O)OC)C(*)C(=O)OC
*CCC(C)(C)C(*)(C)C
*CCC(C)(C)CC(C)CNC(=O)CCCCC(=O)N*
*CCC(C)(C)CC(C)CNC(=O)c1ccc(C(=O)N*)cc1
*CCC(C)(C)CC(C)COC(=O)CCCCC(=O)O*
*CCC(C)(C)CC(C)COC(=O)c1ccc(C(=O)O*)cc1
*CCC(C)C(*)(C)C(=O)OC
*CCC(C)C(*)C
*CCC(C)C(C)CCNC(=O)c1cc(C(=O)N*)cc(C(C)(C)C)c1
*CCC(C)C(C)CCNC(=O)c1cccc(C(=O)N*)c1
*CCC(C)CC(=O)O*
*CCC(C)CC(C)(C)CNC(=O)c1ccc(C(=O)N*)cc1
*CCC(C)CCC(=O)O*
*CCC(C)CCC(=O)S*
*CCC(C)CCOC(=O)CCCCC(=O)O*
*CCC(C)O*
*CCC(C)O[Si](C)(C)O*
*CCC(C)Oc1ccc(-c2ccc(C(=O)OCC(C)COC(=O)c3ccc(-c4ccc(O*)cc4)cc3)cc2)cc1
*CCC(C)S*
*CCC(CC)CC(C)CNC(=O)c1ccc(C(=O)N*)cc1
*CCC(Cl)C(*)Cl
*CCC(F)(F)C(*)(F)F
*CCC([2H])C(*)([2H])C
*CCC(c1ccccc1)C(*)c1ccccc1
*CCC(c1ccccc1)[Si](*)(C)C
*CCC/C=C(/*)c1ccc(Cl)cc1
*CCC/C=C(/*)c1ccc(OC)cc1
*CCC/C=C(/*)c1ccccc1
*CCC1C(=O)N(C)C(=O)C1*
*CCC1C(=O)N(CC)C(=O)C1*
*CCC1C(=O)N(CCC)C(=O)C1*
*CCC1C(=O)N(CCCC)C(=O)C1*
*CCC1C(=O)N(CCCCCC)C(=O)C1*
*CCC1C(=O)N(CCCCCCCCCCCC)C(=O)C1*
*CCC1C(=O)N(CCCCCCCCCCCCCC)C(=O)C1*
*CCC1C(=O)N(CCCCCCCCCCCCCCCC)C(=O)C1*
*CCC1C(=O)N(CCCCCCCCCCCCCCCCCC)C(=O)C1*
*CCC1C(=O)N(CCCCNC(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)C(=O)C1*
*CCC1C(=O)N(c2ccc(N(C)C)cc2)C(=O)C1*
*CCC1CC(*)C2C3CC(C12)C(C)(C(=O)OC)C3
*CCC1CC(*)C2C3CC(CC)C(C3)C12
*CCC1CC(*)C2C3CC(c4ccc(COC(C)CC)cc4)C(C3)C12
*CCC1CC(*)C2C3CCC(C3)C12
*CCC1CC(*)C2CCCC12
*CCC1CC(=O)N(*)C1=O
*CCC1CC(OC(=O)CCCCCCCCCCOc2ccc(-c3ccc(C#N)cc3)cc2)CC1*
*CCC1CC2C(CC(*)C2OC(=O)CCCCCCCCCCOc2ccc(-c3ccc(C#N)cc3)cc2)C1OC(=O)CCCCCCCCCCOc1ccc(-c2ccc(C#N)cc2)cc1
*CCC1C[N+](C)(C)CC1*
*CCCC(*)(C)C
*CCCC(*)(C)C(=O)O
*CCCC(*)(C)CC
*CCCC(*)C
*CCCC(=O)N*
*CCCC(=O)NNC(=O)c1ccc(C(=O)NNC(=O)CCCOc2ccc(O*)c(C)c2)cc1
*CCCC(=O)Nc1ccc(Cc2ccc(NC(=O)CCCN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*CCCC(=O)Nc1ccc(NC(=O)CCCN2C(=O)c3ccc(C(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)cc1
*CCCC(=O)Nc1ccc(Oc2ccc(NC(=O)CCCN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*CCCC(=O)Nc1ccc(Oc2ccc(Oc3ccc(NC(=O)CCCN4C(=O)c5ccc(C(=O)c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)cc2)cc1
*CCCC(=O)Nc1ccc(Oc2cccc(NC(=O)CCCN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)cc1
*CCCC(=O)O*
*CCCC(=O)Oc1ccc(-c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2)cc1
*CCCC(=O)Oc1ccc(OC(=O)c2ccc3c(c2)C(=O)N(*)C3=O)cc1
*CCCC(C)(C)CCCNC(=O)c1ccc(C(=O)N*)cc1
*CCCC(C)(C)CCCn1c(=O)c2cc3c(=O)n(*)c(=O)c3cc2c1=O
*CCCC(C)(C)CNC(=O)C(=O)N*
*CCCC(C)(C)CNC(=O)CCCC(=O)N*
*CCCC(C)(C)CNC(=O)CCCCC(=O)N*
*CCCC(C)(C)CNC(=O)CCCCCC(=O)N*
*CCCC(C)(C)CNC(=O)CCCCCCCC(=O)N*
*CCCC(C)(C)CNC(=O)c1ccc(C(=O)N*)cc1
*CCCC(C)(C)CNC(=O)c1cccc(C(=O)N*)c1
*CCCC(C)CN1C(=O)c2ccc(C(=O)Oc3ccc(-c4ccc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4)cc3)cc2C1=O
*CCCC(C)CN1C(=O)c2ccc(C(=O)Oc3ccc4cc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)ccc4c3)cc2C1=O
*CCCC(C)CNC(=O)c1ccc(C(=O)N*)cc1
*CCCC(C)O[Si](C)(C)O*
*CCCC(CC)CCNC(=O)c1ccc(C(=O)N*)cc1
*CCCC(CCNC(=O)c1ccc(C(=O)N*)cc1)C(C)C
*CCCC(Cl)C(*)Cl
*CCCC(O)CN(CCCC)c1ccc(C#Cc2ccc(S(*)(=O)=O)cc2)cc1
*CCCC1(C)CCN(C(=O)CC(C)C(=O)N2CCC(*)(C)CC2)CC1
*CCCC1(C)CCN(C(=O)CCC(=O)N2CCC(*)(C)CC2)CC1
*CCCC1(C)CCN(C(=O)CCCC(=O)N2CCC(*)(C)CC2)CC1
*CCCC1(CCCNC(=O)CCC2(CCC(=O)N*)c3ccccc3-c3ccccc32)c2ccccc2-c2ccccc21
*CCCC1(CCCNC(=O)CCCCC(=O)N*)c2ccccc2-c2ccccc21
*CCCC1(CCCNC(=O)CCCCCCCCC(=O)N*)c2ccccc2-c2ccccc21
*CCCC1(CCCNC(=O)c2cccc(C(=O)N*)c2)c2ccccc2-c2ccccc21
*CCCC1(CCCNC(=O)c2ccccc2-c2ccccc2C(=O)N*)c2ccccc2-c2ccccc21
*CCCC1CC(=O)N(*)C(=O)C1
*CCCCC(=O)NCc1ccc(CNC(=O)CCCCO*)cc1
*CCCCC(=O)NCc1ccc(CNC(=O)CCCCS*)cc1
*CCCCC(=O)NNC(=O)c1ccc(C(=O)NNC(=O)CCCCOc2ccc(O*)c(C)c2)cc1
*CCCCC(=O)O*
*CCCCC(=O)Oc1ccc(-c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2)cc1
*CCCCC(=O)Oc1ccc(OC(=O)c2ccc3c(c2)C(=O)N(*)C3=O)c(C)c1
*CCCCC(=O)Oc1ccc(OC(=O)c2ccc3c(c2)C(=O)N(*)C3=O)cc1
*CCCCC(C)CCn1c(=O)c2cc3c(=O)n(*)c(=O)c3cc2c1=O
*CCCCC(c1ccc(O)cc1)C(*)c1ccc(O)cc1C
*CCCCCC(*)C
*CCCCCC(*)CC
*CCCCCC(*)CCC
*CCCCCC(*)CCCC
*CCCCCC(*)CCCCCC
*CCCCCC(*)CCCCCCCC
*CCCCCC(*)CCCCCCCCCC
*CCCCCC(=O)N*
*CCCCCC(=O)NNC(=O)c1ccc(C(=O)NNC(=O)CCCCCOc2ccc(O*)c(C)c2)cc1
*CCCCCC(=O)Nc1ccc(Cc2ccc(NC(=O)CCCCCN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*CCCCCC(=O)Nc1ccc(NC(=O)CCCCCN2C(=O)c3ccc(C(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)cc1
*CCCCCC(=O)Nc1ccc(Oc2ccc(NC(=O)CCCCCN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*CCCCCC(=O)Nc1ccc(Oc2ccc(Oc3ccc(NC(=O)CCCCCN4C(=O)c5ccc(C(=O)c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)cc2)cc1
*CCCCCC(=O)Nc1ccc(Oc2cccc(NC(=O)CCCCCN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)cc1
*CCCCCC(=O)O*
*CCCCCC(=O)Oc1ccc(-c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2)cc1
*CCCCCC(=O)Oc1ccc(C(C)(C)c2ccc(C(C)(C)c3ccc(OC(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2)cc1
*CCCCCC(=O)Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2)cc1
*CCCCCC(=O)Oc1ccc(C2(c3ccc(OC(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3)CCCCC2)cc1
*CCCCCC(=O)Oc1ccc(OC(=O)CCCCCn2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)c(-c2ccccc2)c1
*CCCCCC(=O)Oc1ccc(OC(=O)CCCCCn2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)c(C)c1
*CCCCCC(=O)Oc1ccc(OC(=O)CCCCCn2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)c(Cl)c1
*CCCCCC(=O)Oc1ccc(OC(=O)c2ccc3c(c2)C(=O)N(*)C3=O)cc1
*CCCCCC(=O)Oc1ccc(Oc2ccc(OC(=O)CCCCCn3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2)cc1
*CCCCCC(=O)Oc1ccc2ccc(OC(=O)CCCCCn3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2c1
*CCCCCC(=O)Oc1ccc2ccc(OC(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2c1
*CCCCCC(=O)S*
*CCCCCC(Cl)C(*)Cl
*CCCCCC(c1ccc(O)cc1)C(*)c1ccc(O)cc1C
*CCCCCCC(=O)N*
*CCCCCCC(=O)NCc1ccc(CNC(=O)CCCCCCS*)cc1
*CCCCCCC(=O)O*
*CCCCCCC(=O)Oc1ccc(-c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2)cc1
*CCCCCCC(=O)Oc1ccc(OC(=O)c2ccc3c(c2)C(=O)N(*)C3=O)c(C)c1
*CCCCCCC(=O)Oc1ccc(OC(=O)c2ccc3c(c2)C(=O)N(*)C3=O)cc1
*CCCCCCC(Cl)C(*)Cl
*CCCCCCC(c1ccc(O)cc1)C(*)c1ccc(O)cc1C
*CCCCCCCC(*)C
*CCCCCCCC(=O)N*
*CCCCCCCC(=O)OC1COC(*)OC1
*CCCCCCCC(=O)OCC1(C)COC(*)OC1
*CCCCCCCC(=O)OCC1COC(*)O1
*CCCCCCCC(=O)OCCCCC1COC(*)O1
*CCCCCCCC(=O)OCCOC(=O)CCCCCCCC1OCC2(COC(*)OC2)CO1
*CCCCCCCC(c1ccc(O)cc1)C(*)c1ccc(O)cc1C
*CCCCCCCCC(*)Cl
*CCCCCCCCC(=O)N*
*CCCCCCCCC(Cl)C(*)Cl
*CCCCCCCCC(c1ccc(O)cc1)C(*)c1ccc(O)cc1C
*CCCCCCCCCC(=O)N*
*CCCCCCCCCC(c1ccc(O)cc1)C(*)c1ccc(O)cc1C
*CCCCCCCCCCC(=O)N(*)C
*CCCCCCCCCCC(=O)N*
*CCCCCCCCCCC(=O)NCCCCCC(=O)N*
*CCCCCCCCCCC(=O)NCCc1ccc(CCNC(=O)CCCCCCCCCCS*)cc1
*CCCCCCCCCCC(=O)Nc1ccc(Cc2ccc(NC(=O)CCCCCCCCCCN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*CCCCCCCCCCC(=O)Nc1ccc(NC(=O)CCCCCCCCCCN2C(=O)c3ccc(C(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)cc1
*CCCCCCCCCCC(=O)Nc1ccc(Oc2ccc(NC(=O)CCCCCCCCCCN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*CCCCCCCCCCC(=O)Nc1ccc(Oc2ccc(Oc3ccc(NC(=O)CCCCCCCCCCN4C(=O)c5ccc(C(=O)c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)cc2)cc1
*CCCCCCCCCCC(=O)Nc1ccc(Oc2cccc(NC(=O)CCCCCCCCCCN3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)cc1
*CCCCCCCCCCC(=O)O*
*CCCCCCCCCCC(=O)Oc1ccc(-c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2)cc1
*CCCCCCCCCCC(=O)Oc1ccc2cc(OC(=O)CCCCCCCCCCn3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)ccc2c1
*CCCCCCCCCCC(Cl)C(*)Cl
*CCCCCCCCCCC(c1ccc(O)cc1)C(*)c1ccc(O)cc1C
*CCCCCCCCCCCC(=O)N(*)C
*CCCCCCCCCCCC(=O)N*
*CCCCCCCCCCCC(=O)Oc1ccc(-c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2)cc1
*CCCCCCCCCCCC(=O)Oc1ccc(C(C)(C)c2ccc(C(C)(C)c3ccc(OC(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2)cc1
*CCCCCCCCCCCC(=O)Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2)cc1
*CCCCCCCCCCCC(=O)Oc1ccc(C2(c3ccc(OC(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3)CCCCC2)cc1
*CCCCCCCCCCCC(=O)Oc1ccc(C2(c3ccc(OC(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3)OC(=O)c3ccccc32)cc1
*CCCCCCCCCCCC(=O)Oc1ccc(OC(=O)c2ccc3c(c2)C(=O)N(*)C3=O)cc1
*CCCCCCCCCCCC(=O)Oc1ccc2cc(OC(=O)CCCCCCCCCCCn3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)ccc2c1
*CCCCCCCCCCCC(=O)Oc1ccc2ccc(OC(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2c1
*CCCCCCCCCCCC(c1ccc(O)cc1)C(*)c1ccc(O)cc1C
*CCCCCCCCCCCCC(=O)N*
*CCCCCCCCCCCCC(=O)O*
*CCCCCCCCCCCCC(c1ccc(O)cc1)C(*)c1ccc(O)cc1C
*CCCCCCCCCCCCCCC(=O)N*
*CCCCCCCCCCCCCCC(=O)O*
*CCCCCCCCCCCCCCCCCCCCC(*)COCCOCCOCCOCCOCc1ccc2ccc3cccc4ccc1c2c34
*CCCCCCCCCCCCCCCCCCCCOC(=O)CC(=O)O*
*CCCCCCCCCCCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCC(=O)N*
*CCCCCCCCCCCCCCCCCCNC(=O)CCc1ccc(CCC(=O)N*)cc1
*CCCCCCCCCCCCCCCCCCNC(=O)Cc1ccc(CC(=O)N*)cc1
*CCCCCCCCCCCCCCCCCCNC(=O)NCc1ccc(CNC(=O)N*)cc1
*CCCCCCCCCCCCCCCCOC(=O)C/C=C/CC(=O)O*
*CCCCCCCCCCCCCCCCOC(=O)CC/C=C/CCC(=O)O*
*CCCCCCCCCCCCCCCCOC(=O)CCCCCCC(=O)O*
*CCCCCCCCCCCCCCCCOC(=O)NCCCCCCCCCCNC(=O)O*
*CCCCCCCCCCCCCCCCOC(=O)NCCCCCCNC(=O)O*
*CCCCCCCCCCCCCCCCOC(=O)NCc1ccc(CNC(=O)O*)cc1
*CCCCCCCCCCCCCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CCCCCCCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCC(=O)N*
*CCCCCCCCCCCCCCNC(=O)CCc1ccc(CCC(=O)N*)cc1
*CCCCCCCCCCCCCCNC(=O)NCCCCCCCCCCNC(=O)N*
*CCCCCCCCCCCCCCNC(=O)NCCCCCCNC(=O)N*
*CCCCCCCCCCCCCCNC(=O)NCc1ccc(CNC(=O)N*)cc1
*CCCCCCCCCCCCCCc1ccc(-c2c(-c3ccccc3)cc(-c3ccc(-c4cc(-c5ccccc5)c(-c5ccc(*)cc5)c(-c5ccccc5)c4-c4ccccc4)cc3)c(-c3ccccc3)c2-c2ccccc2)cc1
*CCCCCCCCCCCCCCc1ccc(-c2c(-c3ccccc3)cc(-c3cccc(-c4cc(-c5ccccc5)c(-c5ccc(*)cc5)c(-c5ccccc5)c4-c4ccccc4)c3)c(-c3ccccc3)c2-c2ccccc2)cc1
*CCCCCCCCCCCCCNC(=O)CCCCCCCCCCCC(=O)N*
*CCCCCCCCCCCCN1C(=O)c2ccc(-c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2C1=O
*CCCCCCCCCCCCN1C(=O)c2ccc(C(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCCCCCCCCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CCCCCCCCCCCCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(CCCCCCCCCCCCn3c(=O)c5ccc6c7ccc8c(=O)n(*)c(=O)c9ccc(c%10ccc(c3=O)c5c6%10)c7c89)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CCCCCCCCCCCCN1C(=O)c2ccc(Oc3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCCCCCCCCN1C(=O)c2cccc3c(-c4ccc5c6c(cccc46)C(=O)N(CCCCCCCCCCCCn4c(=O)c6ccc7c8ccc9c(=O)n(*)c(=O)c%10ccc(c%11ccc(c4=O)c6c7%11)c8c9%10)C5=O)ccc(c23)C1=O
*CCCCCCCCCCCCNC(=O)C(OC)C(OC)C(=O)N*
*CCCCCCCCCCCCNC(=O)CCCCCCCCCCCCC(=O)N*
*CCCCCCCCCCCCNC(=O)CCCCCCCCCCCCCCC(=O)N*
*CCCCCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCC(=O)N*
*CCCCCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCCCC(=O)N*
*CCCCCCCCCCCCNC(=O)CCP(C)(=O)CCC(=O)N*
*CCCCCCCCCCCCNC(=O)CCc1ccc(CCC(=O)N*)cc1
*CCCCCCCCCCCCNC(=O)NCCCCCCNC(=O)N*
*CCCCCCCCCCCCNC(=O)c1ccc(C(=O)N*)c(Oc2ccccc2)c1
*CCCCCCCCCCCCNC(=O)c1ccc(C(=O)N*)c(Sc2ccccc2)c1
*CCCCCCCCCCCCNC(=O)c1ccc(C(=O)N*)cc1
*CCCCCCCCCCCCOC(=O)NCCCCCCCCCCNC(=O)O*
*CCCCCCCCCCCCOC(=O)NCCCCCCNC(=O)O*
*CCCCCCCCCCCCOC(=O)NCc1ccc(CNC(=O)O*)cc1
*CCCCCCCCCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CCCCCCCCCCCCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CCCCCCCCCCCCOC(=O)c1ccc2cc(C(=O)O*)ccc2c1
*CCCCCCCCCCCCOc1c(OC)cc(/C=C/c2ccc(/C=C/c3cc(OC)c(O*)c(OC)c3)cc2)cc1OC
*CCCCCCCCCCCCOc1ccc(/C=N/N=C/c2ccc(O*)cc2O)c(O)c1
*CCCCCCCCCCCCOc1ccc(C(C)(C)c2ccc(OCOc3ccc(C(C)(C)c4ccc(O*)cc4)cc3)cc2)cc1
*CCCCCCCCCCCCOc1ccc(C(C)Cc2ccc(-c3ccc(O*)cc3)cc2)cc1
*CCCCCCCCCCCCOc1ccc(CCc2ccc(O*)cc2C)cc1
*CCCCCCCCCCCCOc1ccc(N(c2ccccc2)c2ccc(-c3ccc(N(c4ccccc4)c4ccc(O*)cc4)cc3)cc2)cc1
*CCCCCCCCCCCCOc1ccc(OCc2ccc(COc3ccc(O*)cc3)cc2)cc1
*CCCCCCCCCCCCc1nc2ccc(-c3ccc4nc(*)oc4c3)cc2o1
*CCCCCCCCCCCN1C(=O)c2ccc(-c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2C1=O
*CCCCCCCCCCCN1C(=O)c2ccc(C(=O)Oc3ccc(-c4ccc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4)cc3)cc2C1=O
*CCCCCCCCCCCN1C(=O)c2ccc(Oc3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCCCCCCCNC(=O)CCCCCCCCC(=O)N*
*CCCCCCCCCCCNC(=O)CCCCCCCCCCC(=O)N*
*CCCCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCCCC(=O)N*
*CCCCCCCCCCCOC(=O)Nc1ccc(NC(=O)OCCCCCCCCCCCOc2ccc(-c3ccc(O*)cc3)cc2)c(C)c1
*CCCCCCCCCCCOC(=O)c1cc(OCCCCCCCCCCOc2ccc(/N=N/c3ccc(OC)cc3)cc2)cc(C(=O)OCCCCCCCCCCCOc2ccc(-c3ccc(O*)cc3)cc2)c1
*CCCCCCCCCCCOC(=O)c1cc(OCCCCCCCCOc2ccc(/N=N/c3ccc(OC)cc3)cc2)cc(C(=O)OCCCCCCCCCCCOc2ccc(-c3ccc(O*)cc3)cc2)c1
*CCCCCCCCCCCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CCCCCCCCCCCOC(=O)c1ccc2cc(C(=O)O*)ccc2c1
*CCCCCCCCCCCOc1ccc(CCc2ccc(O*)cc2C)cc1
*CCCCCCCCCCCc1nc2ccc(-c3ccc4nc(*)oc4c3)cc2o1
*CCCCCCCCCCN/C(=N/*)c1ccccc1
*CCCCCCCCCCN/C(C)=N/*
*CCCCCCCCCCN1C(=O)C2C3C=CC(C4C(=O)N(*)C(=O)C34)C2C1=O
*CCCCCCCCCCN1C(=O)c2ccc(-c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2C1=O
*CCCCCCCCCCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CCCCCCCCCCN1C(=O)c2ccc(Oc3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCCCCCCNC(=O)/C=C(\C)C(=O)N*
*CCCCCCCCCCNC(=O)CCCCC(=O)N*
*CCCCCCCCCCNC(=O)CCCCCCCCC(=O)N*
*CCCCCCCCCCNC(=O)CCCCCCCCCCC(=O)N*
*CCCCCCCCCCNC(=O)CCCCCCCCCCCCCCC(=O)N*
*CCCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCC(=O)N*
*CCCCCCCCCCNC(=O)CCP(C)(=O)CCC(=O)N*
*CCCCCCCCCCNC(=O)NCc1ccc(CNC(=O)N*)cc1
*CCCCCCCCCCNC(=O)c1ccc(C(=O)N*)c(Oc2ccccc2)c1
*CCCCCCCCCCNC(=O)c1ccc(C(=O)N*)c(Sc2ccccc2)c1
*CCCCCCCCCCNC(=O)c1ccc(Cc2ccc(C(=O)N*)cc2)cc1
*CCCCCCCCCCOC(=O)CC/C=C/CCC(=O)O*
*CCCCCCCCCCOC(=O)CCCCC(=O)O*
*CCCCCCCCCCOC(=O)CCCCCCC(=O)O*
*CCCCCCCCCCOC(=O)CCCCCCCCC(=O)O*
*CCCCCCCCCCOC(=O)CCCCCCCCCCCCCCCCC(=O)O*
*CCCCCCCCCCOC(=O)CCCCCNC(=O)CCCCC(=O)NCCCCCC(=O)O*
*CCCCCCCCCCOC(=O)NCCCCCCNC(=O)O*
*CCCCCCCCCCOC(=O)NCc1ccc(CNC(=O)O*)cc1
*CCCCCCCCCCOC(=O)NNC(=O)CCCCCCCCC(=O)NNC(=O)O*
*CCCCCCCCCCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1
*CCCCCCCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CCCCCCCCCCOC(=O)O*
*CCCCCCCCCCOC(=O)c1ccc(-c2ccc(-c3ccc(C(=O)O*)cc3)cc2)cc1
*CCCCCCCCCCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CCCCCCCCCCOC(=O)c1ccc(C(=O)O*)cc1
*CCCCCCCCCCOC(=O)c1ccc2cc(C(=O)O*)ccc2c1
*CCCCCCCCCCOC(=O)c1cccc(C(=O)O*)c1
*CCCCCCCCCCOC(=O)c1ccccc1C(=O)O*
*CCCCCCCCCCOc1c(OC)cc(/C=C/c2ccc(/C=C/c3cc(OC)c(O*)c(OC)c3)cc2)cc1OC
*CCCCCCCCCCOc1c(OC)cc(/C=C2\CCC/C(=C\c3cc(OC)c(O*)c(OC)c3)C2=O)cc1OC
*CCCCCCCCCCOc1ccc(-c2cc(-c3ccc(-c4ccccc4)cc3)c(-c3ccc(NC(=O)c4ccc(C(=O)Nc5ccc(-c6c(-c7ccc(-c8ccccc8)cc7)cc(-c7ccc(O*)cc7)cc6-c6ccc(-c7ccccc7)cc6)cc5)cc4)cc3)c(-c3ccc(-c4ccccc4)cc3)c2)cc1
*CCCCCCCCCCOc1ccc(-c2cc(-c3ccccc3)c(-c3ccc(NC(=O)c4ccc(C(=O)Nc5ccc(-c6c(-c7ccccc7)cc(-c7ccc(O*)cc7)cc6-c6ccccc6)cc5)cc4)cc3)c(-c3ccccc3)c2)cc1
*CCCCCCCCCCOc1ccc(/C=C/c2ccc(OCCCCCCCCCCOP(=O)(O*)OCCCCCCCCCCOc3ccc(/N=N/c4ccc(-c5ccccc5)cc4)cc3)cc2)cc1
*CCCCCCCCCCOc1ccc(/C=C/c2ccc(OCCCCCCCCCCOP(=O)(O*)OCCCCCCCCCCOc3ccc(/N=N/c4ccc(C#N)cc4)cc3)cc2)cc1
*CCCCCCCCCCOc1ccc(/C=C/c2ccc(OCCCCCCCCCCOP(=O)(O*)OCCCCCCCCCCOc3ccc(/N=N/c4ccc(Cl)cc4)cc3)cc2)cc1
*CCCCCCCCCCOc1ccc(/C=C/c2ccc(OCCCCCCCCCCOP(=O)(O*)OCCCCCCCCCCOc3ccc(/N=N/c4ccc(OC)cc4)cc3)cc2)cc1
*CCCCCCCCCCOc1ccc(/C=C/c2ccc(OCCCCCCCCCCOP(=O)(O*)OCCCCCCCCCCOc3ccc(/N=N/c4ccc([N+](=O)[O-])cc4)cc3)cc2)cc1
*CCCCCCCCCCOc1ccc(/C=C/c2ccc(OCCCCCCCCCCOP(=O)(O*)OCCCCCCCCCCOc3ccc(/N=N/c4ccccc4)cc3)cc2)cc1
*CCCCCCCCCCOc1ccc(/C=C2\CC/C(=C\c3ccc(OCCCCCCCCCCOP(=O)(O*)OC)c(OC)c3)C2=O)cc1OC
*CCCCCCCCCCOc1ccc(/C=C2\CC/C(=C\c3ccc(OCCCCCCCCCCOP(=O)(O*)OCC)c(OC)c3)C2=O)cc1OC
*CCCCCCCCCCOc1ccc(/C=C2\CCC/C(=C\c3ccc(O*)c(OC)c3)C2=O)cc1OC
*CCCCCCCCCCOc1ccc(/C=C2\CCC/C(=C\c3ccc(O*)cc3)C2=O)cc1
*CCCCCCCCCCOc1ccc(/C=N/c2ccc(-c3ccc(/N=C/c4ccc(O*)cc4O)c(OC)c3)cc2OC)c(O)c1
*CCCCCCCCCCOc1ccc(/N=C/c2ccc(/C=N/c3ccc(O*)cc3C)cc2)c(C)c1
*CCCCCCCCCCOc1ccc(CCc2ccc(O*)cc2C)cc1
*CCCCCCCCCCOc1ccc(N(c2ccccc2)c2ccc(-c3ccc(N(c4ccccc4)c4ccc(O*)cc4)cc3)cc2)cc1
*CCCCCCCCCCOc1ccc(OC(=O)c2ccc(OCCOCCOCCOc3ccc(C(=O)Oc4ccc(O*)cc4)cc3)cc2)cc1
*CCCCCCCCCCSS*
*CCCCCCCCCCSSS*
*CCCCCCCCCCSSSS*
*CCCCCCCCCC[N+](*)(C)C
*CCCCCCCCCCc1ccc(-c2c(-c3ccccc3)cc(-c3ccc(-c4cc(-c5ccccc5)c(-c5ccc(*)cc5)c(-c5ccccc5)c4-c4ccccc4)cc3)c(-c3ccccc3)c2-c2ccccc2)cc1
*CCCCCCCCCCc1ccc(-c2c(-c3ccccc3)cc(-c3cccc(-c4cc(-c5ccccc5)c(-c5ccc(*)cc5)c(-c5ccccc5)c4-c4ccccc4)c3)c(-c3ccccc3)c2-c2ccccc2)cc1
*CCCCCCCCCCc1nc2ccc(-c3ccc4nc(*)oc4c3)cc2o1
*CCCCCCCCCCn1c2ccccc2c2cc(/C=C(\C#N)C(=O)OCCCCCCOC(=O)/C(C#N)=C/c3ccc4c(c3)c3ccccc3n4*)ccc21
*CCCCCCCCCN1C(=O)c2ccc(-c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2C1=O
*CCCCCCCCCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CCCCCCCCCN1C(=O)c2ccc(Oc3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCCCCCNC(=O)C(OC)C(OC)C(=O)N*
*CCCCCCCCCNC(=O)CCCCC(=O)N*
*CCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCCCC(=O)N*
*CCCCCCCCCNC(=O)N*
*CCCCCCCCCNC(=O)c1ccc(C(=O)N*)cc1
*CCCCCCCCCNC(=S)N*
*CCCCCCCCCOC(=O)C(O)C(O)C(=O)O*
*CCCCCCCCCOC(=O)CCC(=O)O*
*CCCCCCCCCOC(=O)NCCCCCCNC(=O)O*
*CCCCCCCCCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1
*CCCCCCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CCCCCCCCCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CCCCCCCCCOC(=O)c1ccc(C(=O)O*)cc1
*CCCCCCCCCOC(=O)c1ccc2cc(C(=O)O*)ccc2c1
*CCCCCCCCCOc1ccc(CCc2ccc(O*)cc2C)cc1
*CCCCCCCCCc1nc2ccc(-c3ccc4nc(*)oc4c3)cc2o1
*CCCCCCCCCn1c(=O)c2cc3c(=O)n(*)c(=O)c3cc2c1=O
*CCCCCCCCCn1c2ccccc2c2cc(/C=C(\C#N)C(=O)OCCCCCCOC(=O)/C(C#N)=C/c3ccc4c(c3)c3ccccc3n4*)ccc21
*CCCCCCCCN(C)c1ccc(C(=O)c2ccc(N(*)C)cc2)cc1
*CCCCCCCCN1C(=O)c2ccc(-c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2C1=O
*CCCCCCCCN1C(=O)c2ccc(C(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCCCCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CCCCCCCCN1C(=O)c2ccc(Oc3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCCCCNC(=O)C(OC)C(OC)C(=O)N*
*CCCCCCCCNC(=O)CC(=O)N*
*CCCCCCCCNC(=O)CCCCC(=O)N*
*CCCCCCCCNC(=O)CCCCCCCCC(=O)N*
*CCCCCCCCNC(=O)CCCCCCCCCCC(=O)N*
*CCCCCCCCNC(=O)CCCCCCCCCCCCCCC(=O)N*
*CCCCCCCCNC(=O)CCCCCCCCCCCCCCCCC(=O)N*
*CCCCCCCCNC(=O)CCCCCCCCCCCCCCCCCCCCC(=O)N*
*CCCCCCCCNC(=O)CCP(C)(=O)CCC(=O)N*
*CCCCCCCCNC(=O)Cc1ccc(CC(=O)N*)cc1
*CCCCCCCCNC(=O)NCc1ccc(CNC(=O)N*)cc1
*CCCCCCCCNC(=O)c1ccc(C(=O)N*)c(Oc2ccccc2)c1
*CCCCCCCCNC(=O)c1ccc(C(=O)N*)c(Sc2ccccc2)c1
*CCCCCCCCNC(=O)c1ccc(C(=O)N*)cc1
*CCCCCCCCNC(=O)c1cccc(C(=O)N*)c1
*CCCCCCCCO*
*CCCCCCCCOC(=O)NCCCCCCNC(=O)O*
*CCCCCCCCOC(=O)NNC(=O)CCCCCCCCC(=O)NNC(=O)O*
*CCCCCCCCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1
*CCCCCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CCCCCCCCOC(=O)Nc1ccc(NC(=O)OCCCCCCCCOc2ccc(-c3ccc(O*)cc3)cc2)c(C)c1
*CCCCCCCCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CCCCCCCCOC(=O)c1ccc(C(=O)O*)cc1
*CCCCCCCCOC(=O)c1ccc2cc(C(=O)O*)ccc2c1
*CCCCCCCCOC(=O)c1cccc(C(=O)O*)c1
*CCCCCCCCOc1c(OC)cc(/C=C/c2cc(C(C)C)c(/C=C/c3cc(OC)c(O*)c(OC)c3)cc2C(C)C)cc1OC
*CCCCCCCCOc1c(OC)cc(/C=C/c2cc(CCCCCC)c(/C=C/c3cc(OC)c(O*)c(OC)c3)cc2CCCCCC)cc1OC
*CCCCCCCCOc1c(OC)cc(/C=C/c2ccc(/C=C/c3cc(OC)c(O*)c(OC)c3)cc2)cc1OC
*CCCCCCCCOc1c(OC)cc(/C=C/c2ccc(/C=C/c3cc(OC)c(O*)c(OC)c3)cc2)cc1OC
*CCCCCCCCOc1c(OC)cc(/C=C/c2ccc(/C=C/c3ccc(/C=C/c4cc(OC)c(O*)c(OC)c4)cc3)cc2)cc1OC
*CCCCCCCCOc1c(OC)cc(/C=C/c2ccc(/C=C/c3ccc(/C=C/c4cc(OC)c(O*)c(OC)c4)cc3)cc2)cc1OC
*CCCCCCCCOc1c(OC)cc(/C=C2\CCC/C(=C\c3cc(OC)c(O*)c(OC)c3)C2=O)cc1OC
*CCCCCCCCOc1ccc(/C=C/c2cc(OCCCCCCCC)c(/C=C/c3ccc(O*)cc3)cc2OCCCCCCCC)cc1
*CCCCCCCCOc1ccc(/C=C2\CC/C(=C\c3ccc(OCCCCCCCCOP(=O)(O*)OC)c(OC)c3)C2=O)cc1OC
*CCCCCCCCOc1ccc(/C=C2\CC/C(=C\c3ccc(OCCCCCCCCOP(=O)(O*)OCC)c(OC)c3)C2=O)cc1OC
*CCCCCCCCOc1ccc(/C=C2\CCC/C(=C\c3ccc(O*)c(OC)c3)C2=O)cc1OC
*CCCCCCCCOc1ccc(/C=C2\CCC/C(=C\c3ccc(O*)cc3)C2=O)cc1
*CCCCCCCCOc1ccc(C(C)(C)c2ccc(O*)cc2)cc1
*CCCCCCCCOc1ccc(C(c2ccc(O*)cc2)(C(F)(F)F)C(F)(F)F)cc1
*CCCCCCCCOc1ccc(CCc2ccc(O*)cc2C)cc1
*CCCCCCCCOc1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(O*)cc3)c2)cc1
*CCCCCCCCOc1cccc(NC(=O)c2ccc(C(=O)Nc3cccc(O*)c3)cc2)c1
*CCCCCCCCOc1cccc(NC(=O)c2cccc(C(=O)Nc3cccc(O*)c3)c2)c1
*CCCCCCCCc1nc2cc(-c3ccc4[nH]c(*)nc4c3)ccc2[nH]1
*CCCCCCCCc1nc2cc(NC(=O)CCCCC(=O)Nc3ccc4oc(*)nc4c3)ccc2o1
*CCCCCCCCc1nc2cc(NC(=O)CCCCCCCCC(=O)Nc3ccc4oc(*)nc4c3)ccc2o1
*CCCCCCCCc1nc2ccc(-c3ccc4nc(*)oc4c3)cc2o1
*CCCCCCCCc1nc2ccc(-c3ccc4nc(*)sc4c3)cc2s1
*CCCCCCCCn1c2ccccc2c2cc(/C=C(\C#N)C(=O)OCCCCCCOC(=O)/C(C#N)=C/c3ccc4c(c3)c3ccccc3n4*)ccc21
*CCCCCCCN1C(=O)c2ccc(-c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2C1=O
*CCCCCCCN1C(=O)c2ccc(C(=O)Oc3ccc(-c4ccc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4)cc3)cc2C1=O
*CCCCCCCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CCCCCCCN1C(=O)c2ccc(Oc3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCCCNC(=O)C(OC)C(OC)C(=O)N*
*CCCCCCCNC(=O)CCCCC(=O)N*
*CCCCCCCNC(=O)CCCCCC(=O)N*
*CCCCCCCNC(=O)CCCCCCCCCCCCCCCCCCC(=O)N*
*CCCCCCCNC(=O)N*
*CCCCCCCNC(=O)c1ccc(C(=O)N*)cc1
*CCCCCCCNC(=S)N*
*CCCCCCCOC(=O)NCCCCCCNC(=O)O*
*CCCCCCCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1
*CCCCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CCCCCCCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CCCCCCCOC(=O)c1ccc(C(=O)O*)cc1
*CCCCCCCOC(=O)c1ccc2cc(C(=O)O*)ccc2c1
*CCCCCCCOc1ccc(-c2ccc(O*)c3ccccc23)c2ccccc12
*CCCCCCCOc1ccc(CCc2ccc(O*)cc2C)cc1
*CCCCCCCc1nc2ccc(-c3ccc4nc(*)oc4c3)cc2o1
*CCCCCCCn1c2ccccc2c2cc(/C=C(\C#N)C(=O)OCCCCCCOC(=O)/C(C#N)=C/c3ccc4c(c3)c3ccccc3n4*)ccc21
*CCCCCCN(C(=O)C(F)(F)C(F)(F)C(F)(F)C(=O)N(*)C(C)C)C(C)C
*CCCCCCN(C)C(=O)CCC(=O)N(*)C
*CCCCCCN(C)C(=O)CCCC(=O)N(*)C
*CCCCCCN(C)c1ccc(C(=O)c2ccc(N(*)C)cc2)cc1
*CCCCCCN(CC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)C(=O)C(F)(F)C(F)(F)C(F)(F)C(=O)N(*)CC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CCCCCCN(CC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)C(=O)CCCCC(=O)N(*)CC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CCCCCCN(CC(F)(F)C(F)(F)F)C(=O)C(F)(F)C(F)(F)C(F)(F)C(=O)N(*)CC(F)(F)C(F)(F)F
*CCCCCCN1C(=O)C(=O)N(*)C1=O
*CCCCCCN1C(=O)C(=O)N(c2ccc(C)c(N3C(=O)C(=O)N(*)C3=O)c2)C1=O
*CCCCCCN1C(=O)C(=O)N(c2ccc(CCc3ccccc3N3C(=O)C(=O)N(*)C3=O)cc2)C1=O
*CCCCCCN1C(=O)C(=O)N(c2ccc(Cc3ccc(N4C(=O)C(=O)N(*)C4=O)cc3)cc2)C1=O
*CCCCCCN1C(=O)C(=O)N(c2ccc(Oc3ccc(N4C(=O)C(=O)N(*)C4=O)cc3)cc2)C1=O
*CCCCCCN1C(=O)C(C)C(SCCOCCSC2C(=O)N(CCCCCCN3C(=O)CC(C)(SCCOCCSC4(C)CC(=O)N(*)C4=O)C3=O)C(=O)C2C)C1=O
*CCCCCCN1C(=O)c2ccc(-c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2C1=O
*CCCCCCN1C(=O)c2ccc(C(=O)Nc3ccc(S(=O)(=O)c4ccc(NC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4)cc3)cc2C1=O
*CCCCCCN1C(=O)c2ccc(C(=O)Nc3ccc(S(=O)(=O)c4cccc(NC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)c4)cc3)cc2C1=O
*CCCCCCN1C(=O)c2ccc(C(=O)Oc3ccc(-c4ccc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4)cc3)cc2C1=O
*CCCCCCN1C(=O)c2ccc(C(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CCCCCCN1C(=O)c2ccc(Oc3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCCNC(=O)/C=C(\C)C(=O)N*
*CCCCCCNC(=O)C(=O)N*
*CCCCCCNC(=O)C(CC)CC(CC)C(=O)N*
*CCCCCCNC(=O)C(OC)C(OC)C(=O)N*
*CCCCCCNC(=O)CC(=O)N*
*CCCCCCNC(=O)CC(C)(C)c1ccc(C(C)(C)CC(=O)N*)cc1
*CCCCCCNC(=O)CC(C)c1ccc(C(C)CC(=O)N*)cc1
*CCCCCCNC(=O)CCC(C)(C)c1ccc(C(C)(C)CCC(=O)N*)cc1
*CCCCCCNC(=O)CCC(C)CC(=O)N*
*CCCCCCNC(=O)CCC1(CCC(=O)N*)c2ccccc2-c2ccccc21
*CCCCCCNC(=O)CCCC(=O)O*
*CCCCCCNC(=O)CCCCC(=O)N*
*CCCCCCNC(=O)CCCCC(=O)NCC(=O)N*
*CCCCCCNC(=O)CCCCCC(=O)N*
*CCCCCCNC(=O)CCCCCCC(=O)N*
*CCCCCCNC(=O)CCCCCCCC(=O)N*
*CCCCCCNC(=O)CCCCCCCC1C(CCCCCCCC(=O)N*)C=CC(CCCCCC)C1CCCCCCCC
*CCCCCCNC(=O)CCCCCCCCC(=O)N*
*CCCCCCNC(=O)CCCCCCCCCCC(=O)N*
*CCCCCCNC(=O)CCCCCCCCCCCCCCC(=O)N*
*CCCCCCNC(=O)CCCCCCCCCCCCCCCCC(=O)N*
*CCCCCCNC(=O)CCP(=O)(CCC(=O)N*)c1ccccc1
*CCCCCCNC(=O)CCP(=S)(CCC(=O)N*)c1ccccc1
*CCCCCCNC(=O)CCP(C)(=O)CCC(=O)N*
*CCCCCCNC(=O)CCc1ccc(C(C)CC(=O)N*)cc1
*CCCCCCNC(=O)CCc1ccc(CC(=O)N*)cc1
*CCCCCCNC(=O)CCc1ccc(CCC(=O)N*)cc1
*CCCCCCNC(=O)Cc1ccc(C(=O)N*)cc1
*CCCCCCNC(=O)Cc1ccc(CC(=O)N*)cc1
*CCCCCCNC(=O)NCc1ccc(CNC(=O)N*)cc1
*CCCCCCNC(=O)c1c(Cl)c(Cl)c(C(=O)N*)c(Cl)c1Cl
*CCCCCCNC(=O)c1cc(C(=O)N*)cc(C(C)(C)C)c1
*CCCCCCNC(=O)c1cc(NC(=O)C(C(C)CC)N2C(=O)c3ccccc3C2=O)cc(C(=O)N*)c1
*CCCCCCNC(=O)c1cc(NC(=O)c2ccc(NC(=O)C(CC(C)C)N3C(=O)c4ccccc4C3=O)cc2)cc(C(=O)N*)c1
*CCCCCCNC(=O)c1ccc(C(=O)N*)c(Oc2ccc(-c3ccccc3)cc2)c1
*CCCCCCNC(=O)c1ccc(C(=O)N*)c(Oc2ccc(Br)cc2)c1
*CCCCCCNC(=O)c1ccc(C(=O)N*)c(Oc2ccc(Cl)cc2)c1
*CCCCCCNC(=O)c1ccc(C(=O)N*)c(Oc2ccc(F)cc2)c1
*CCCCCCNC(=O)c1ccc(C(=O)N*)c(Oc2ccccc2)c1
*CCCCCCNC(=O)c1ccc(C(=O)N*)c(Sc2ccccc2)c1
*CCCCCCNC(=O)c1ccc(C(=O)N*)cc1
*CCCCCCNC(=O)c1ccc(C(C)(CC)c2ccc(C(=O)N*)cc2)cc1
*CCCCCCNC(=O)c1ccc(C2(C)CC(C)(C)c3ccc(C(=O)N*)cc32)cc1
*CCCCCCNC(=O)c1cccc(C(=O)N*)c1
*CCCCCCNC(=O)c1ccccc1-c1ccccc1C(=O)N*
*CCCCCCOC(=O)C(=O)O*
*CCCCCCOC(=O)C(C(=O)OCCCCCCOc1ccc(-n2on2-c2ccc(O*)cc2)cc1)c1ccccc1
*CCCCCCOC(=O)C(CCCCCBr)C(=O)OCCCCCCOc1ccc(-n2on2-c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)C(CCCCCCCCCCBr)C(=O)OCCCCCCOc1ccc(-n2on2-c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)C(CCCCCCCCCCP(=O)(O)O)C(=O)OCCCCCCOc1ccc(-n2on2-c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)C(CCCCCCCCCCP(=O)(OCC)OCC)C(=O)OCCCCCCOc1ccc(-n2on2-c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)C(CCCCCCP(=O)(O)O)C(=O)OCCCCCCOc1ccc(-n2on2-c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)C(CCCCCCP(=O)(OCC)OCC)C(=O)OCCCCCCOc1ccc(-n2on2-c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)C(CCCCCOc1ccc(/N=N/c2ccncc2)cc1)C(=O)OCCCCCCOc1ccc(-c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)C(CCCCCP(=O)(O)O)C(=O)OCCCCCCOc1ccc(-n2on2-c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)C(CCCCCP(=O)(OCC)OCC)C(=O)OCCCCCCOc1ccc(-n2on2-c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)C(CCCCP(=O)(O)O)C(=O)OCCCCCCOc1ccc(-n2on2-c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)C(CCCCP(=O)(OCC)OCC)C(=O)OCCCCCCOc1ccc(-n2on2-c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)C(Cc1ccccc1)NC(=O)/C=C/C(=O)NC(Cc1ccccc1)C(=O)O*
*CCCCCCOC(=O)CC(=O)OCCCCCCOc1ccc(-n2on2-c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)CCC(=O)O*
*CCCCCCOC(=O)CCCCC(=O)O*
*CCCCCCOC(=O)CCCCCCCCC(=O)O*
*CCCCCCOC(=O)CCCCCCCCCCCCCCCCCCC(=O)OCCCCCCN1C(=O)c2ccc(S(=O)(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCCOC(=O)CCCCCNC(=O)CCCCC(=O)NCCCCCC(=O)O*
*CCCCCCOC(=O)NC1CCC(CC2CCC(NC(=O)OCCCCCCOc3ccc(CCc4ccc(O*)cc4)cc3)CC2)CC1
*CCCCCCOC(=O)NCC(F)(F)C(F)(F)C(F)(F)C(F)(F)CNC(=O)O*
*CCCCCCOC(=O)NCCCCCCNC(=O)O*
*CCCCCCOC(=O)NCCCCNC(=O)O*
*CCCCCCOC(=O)NCc1ccc(CNC(=O)O*)cc1
*CCCCCCOC(=O)NNC(=O)CCCCCCCCC(=O)NNC(=O)O*
*CCCCCCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1
*CCCCCCOC(=O)Nc1ccc(C)c(NC(=O)OCCCCCCOc2ccc(-c3ccc(O*)cc3)cc2)c1
*CCCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CCCCCCOC(=O)Nc1ccc(NC(=O)OCCCCCCOc2ccc(-c3ccc(O*)cc3)cc2)c(C)c1
*CCCCCCOC(=O)Nc1cccc(P(=O)(c2ccccc2)c2cccc(NC(=O)O*)c2)c1
*CCCCCCOC(=O)O*
*CCCCCCOC(=O)OCCCCCCCCCCCCOC(=O)OCCCCCCOc1ccc(C(=O)c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCCCCCCCCCCOC(=O)OCCCCCCOc1ccc(Oc2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCCCCCCCCCCOC(=O)OCCCCCCn1c(=O)c2cc3c(=O)n(*)c(=O)c3cc2c1=O
*CCCCCCOC(=O)OCCCCCCCCCCOC(=O)OCCCCCCOc1ccc(C(=O)c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCCCCCCCCOC(=O)OCCCCCCOc1ccc(Oc2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCCCCCCCCOC(=O)OCCCCCCn1c(=O)c2cc3c(=O)n(*)c(=O)c3cc2c1=O
*CCCCCCOC(=O)OCCCCCCCCCOC(=O)OCCCCCCOc1ccc(C(=O)c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCCCCCCCOC(=O)OCCCCCCOc1ccc(Oc2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCCCCCCOC(=O)OCCCCCCOc1ccc(C(=O)c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCCCCCCOC(=O)OCCCCCCOc1ccc(Oc2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCCCCCCOC(=O)OCCCCCCn1c(=O)c2cc3c(=O)n(*)c(=O)c3cc2c1=O
*CCCCCCOC(=O)OCCCCCCN1C(=O)c2ccc(-c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2C1=O
*CCCCCCOC(=O)OCCCCCCN1C(=O)c2ccc(-c3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCCOC(=O)OCCCCCCN1C(=O)c2ccc(C(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCCOC(=O)OCCCCCCOC(=O)OCCCCCCN1C(=O)c2ccc(C(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCCOC(=O)OCCCCCCOC(=O)OCCCCCCOc1ccc(-c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCCCCOC(=O)OCCCCCCOc1ccc(C(=O)c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCCCCOC(=O)OCCCCCCOc1ccc(Oc2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCCCCOC(=O)OCCCCCCn1c(=O)c2cc3c(=O)n(*)c(=O)c3cc2c1=O
*CCCCCCOC(=O)OCCCCCOC(=O)OCCCCCCOc1ccc(C(=O)c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCCCOC(=O)OCCCCCCOc1ccc(Oc2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCCOC(=O)OCCCCCCOc1ccc(C(=O)c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCCOC(=O)OCCCCCCOc1ccc(Oc2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCOC(=O)OCCCCCCN1C(=O)c2ccc(C(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCCOC(=O)OCCCOC(=O)OCCCCCCOc1ccc(C(=O)c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCCOC(=O)OCCCCCCOc1ccc(Oc2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCOC(=O)OCCCCCCOc1ccc(C(=O)c2ccc(O*)cc2)cc1
*CCCCCCOC(=O)OCCOC(=O)OCCCCCCOc1ccc(Oc2ccc(O*)cc2)cc1
*CCCCCCOC(=O)c1cc(/N=N/c2ccc(OCC)cc2)ccc1-c1ccc(/N=N/c2ccc(OCC)cc2)cc1C(=O)O*
*CCCCCCOC(=O)c1cc(OCCCCCCOc2ccc(/N=N/c3ccc(C#N)cc3)cc2)cc(C(=O)OCCCCCCN2C(=O)c3ccc(-c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4)cc3C2=O)c1
*CCCCCCOC(=O)c1cc(OCCCCCCOc2ccc(/N=N/c3ccc(C#N)cc3)cc2)cc(C(=O)OCCCCCCN2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*CCCCCCOC(=O)c1cc(OCCCCCCOc2ccc(/N=N/c3ccc(C#N)cc3)cc2)cc(C(=O)OCCCCCCN2C(=O)c3ccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*CCCCCCOC(=O)c1cc(OCCCCCCOc2ccc(/N=N/c3ccc(C#N)cc3)cc2)cc(C(=O)OCCCCCCn2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)c1
*CCCCCCOC(=O)c1ccc(-c2ccc(-c3ccc(C(=O)O*)cc3)cc2)cc1
*CCCCCCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CCCCCCOC(=O)c1ccc(C(=O)O*)cc1
*CCCCCCOC(=O)c1ccc2cc(C(=O)O*)ccc2c1
*CCCCCCOC(=O)c1cccc(C(=O)O*)c1
*CCCCCCOP(=O)(/N=N/c1ccc(-c2ccc(/N=N/P(=O)(O*)OC)cc2)cc1)OC
*CCCCCCOP(=O)(/N=N/c1ccc(C(=O)c2ccc(/N=N/P(=O)(O*)OC)cc2)cc1)OC
*CCCCCCOP(=O)(/N=N/c1ccc(CCOC(=O)c2cc(C(=O)OCCc3ccc(/N=N/P(=O)(O*)OC)cc3)cc(C(C)(C)C)c2)cc1)OC
*CCCCCCOP(=O)(/N=N/c1ccc(COC(=O)c2cc(C(=O)OCc3ccc(/N=N/P(=O)(O*)OC)cc3)cc(C(C)(C)C)c2)cc1)OC
*CCCCCCOP(=O)(/N=N/c1ccc(Oc2ccc(/N=N/P(=O)(O*)OC)cc2)cc1)OC
*CCCCCCOc1c(OC)cc(/C=C/c2ccc(/C=C/c3cc(OC)c(O*)c(OC)c3)cc2)cc1OC
*CCCCCCOc1c(OC)cc(/C=C2\CCC/C(=C\c3cc(OC)c(O*)c(OC)c3)C2=O)cc1OC
*CCCCCCOc1ccc(-c2cc(-c3ccc(-c4ccccc4)cc3)c(-c3ccc(NC(=O)c4ccc(C(=O)Nc5ccc(-c6c(-c7ccc(-c8ccccc8)cc7)cc(-c7ccc(O*)cc7)cc6-c6ccc(-c7ccccc7)cc6)cc5)cc4)cc3)c(-c3ccc(-c4ccccc4)cc3)c2)cc1
*CCCCCCOc1ccc(-c2cc(-c3ccccc3)c(-c3ccc(NC(=O)c4ccc(C(=O)Nc5ccc(-c6c(-c7ccccc7)cc(-c7ccc(O*)cc7)cc6-c6ccccc6)cc5)cc4)cc3)c(-c3ccccc3)c2)cc1
*CCCCCCOc1ccc(-c2ccc(O*)c3ccccc23)c2ccccc12
*CCCCCCOc1ccc(/C=C/c2ccc(O*)c3ccccc23)cc1
*CCCCCCOc1ccc(/C=C/c2ccc(O*)cc2)cc1
*CCCCCCOc1ccc(/C=C2\CC/C(=C\c3ccc(OCCCCCCOP(=O)(O*)OC)c(OC)c3)C2=O)cc1OC
*CCCCCCOc1ccc(/C=C2\CC/C(=C\c3ccc(OCCCCCCOP(=O)(O*)OCC)c(OC)c3)C2=O)cc1OC
*CCCCCCOc1ccc(/C=C2\CCC/C(=C\c3ccc(O*)c(OC)c3)C2=O)cc1OC
*CCCCCCOc1ccc(/C=C2\CCC/C(=C\c3ccc(O*)cc3)C2=O)cc1
*CCCCCCOc1ccc(C(=O)N(C(=O)c2ccc(O*)cc2)c2cc(C)cc(C)c2)cc1
*CCCCCCOc1ccc(C(=O)N(C(=O)c2ccc(O*)cc2)c2ccc(Oc3ccccc3)cc2)cc1
*CCCCCCOc1ccc(C(=O)N(C(=O)c2ccc(O*)cc2)c2cccc3ccccc23)cc1
*CCCCCCOc1ccc(C(=O)N(C(=O)c2ccc(O*)cc2)c2ccccc2)cc1
*CCCCCCOc1ccc(C(=O)OC(=O)c2ccc(O*)cc2)cc1
*CCCCCCOc1ccc(C(C)(C)c2ccc(O*)cc2)cc1
*CCCCCCOc1ccc(CCc2ccc(O*)cc2C)cc1
*CCCCCCOc1ccc(N(c2ccccc2)c2ccc(-c3ccc(N(c4ccccc4)c4ccc(O*)cc4)cc3)cc2)cc1
*CCCCCCOc1ccc(NC(=O)c2cc(C(=O)Nc3ccc(O*)cc3)cc(C(C)(C)C)c2)cc1
*CCCCCCOc1ccc(OC(=O)c2ccc(C(=O)Oc3ccc(O*)cc3)cc2)cc1
*CCCCCCS(=O)(=O)c1ccc(/N=N/c2ccc(N(C)CCCCCC(=O)O*)cc2)cc1
*CCCCCCSS*
*CCCCCCc1ccc(-c2c(-c3ccccc3)cc(-c3ccc(-c4cc(-c5ccccc5)c(-c5ccc(*)cc5)c(-c5ccccc5)c4-c4ccccc4)cc3)c(-c3ccccc3)c2-c2ccccc2)cc1
*CCCCCCc1ccc(-c2c(-c3ccccc3)cc(-c3cccc(-c4cc(-c5ccccc5)c(-c5ccc(*)cc5)c(-c5ccccc5)c4-c4ccccc4)c3)c(-c3ccccc3)c2-c2ccccc2)cc1
*CCCCCCc1nc2cc(NC(=O)CCCCCCC(=O)Nc3ccc4oc(*)nc4c3)ccc2o1
*CCCCCCc1nc2cc(NC(=O)CCCCCCCCC(=O)Nc3ccc4oc(*)nc4c3)ccc2o1
*CCCCCCn1c2ccccc2c2cc(/C=C(\C#N)C(=O)OCCCCCCOC(=O)/C(C#N)=C/c3ccc4c(c3)c3ccccc3n4*)ccc21
*CCCCCCn1ccc(-c2ccc(-c3ccn(*)n3)cc2)n1
*CCCCCCn1ccc(-c2cccc(-c3ccn(*)n3)c2)n1
*CCCCCN1C(=O)c2ccc(C(=O)Oc3ccc(-c4ccc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4)cc3)cc2C1=O
*CCCCCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CCCCCNC(=O)C(OC)C(OC)C(=O)N*
*CCCCCNC(=O)CC(=O)N*
*CCCCCNC(=O)CCCCC(=O)N*
*CCCCCNC(=O)CCCCCCCCCCCCCCCCCCC(=O)N*
*CCCCCNC(=O)Cc1ccc(C(=O)N*)cc1
*CCCCCNC(=O)c1ccc(C(=O)N*)cc1
*CCCCCNC(=S)N*
*CCCCCNc1nc(NCCCCCc2nc3cc(-c4ccc5[nH]c(*)nc5c4)ccc3[nH]2)nc(N(CC)CC)n1
*CCCCCOC(=O)CCCCC(=O)O*
*CCCCCOC(=O)CCCCCCCCCCN1C(=O)c2ccc(-c3ccc(-c4ccc5c(c4)C(=O)N(CCCCCCCCCCC(=O)OCCCCCN4C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C4=O)C5=O)cc3)cc2C1=O
*CCCCCOC(=O)NCC(F)(F)C(F)(F)C(F)(F)C(F)(F)CNC(=O)O*
*CCCCCOC(=O)NCCCCCCNC(=O)O*
*CCCCCOC(=O)NNC(=O)CCCCCCCCC(=O)NNC(=O)O*
*CCCCCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1
*CCCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CCCCCOC(=O)O*
*CCCCCOC(=O)OCCCCCN1C(=O)c2ccc(-c3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCOC(=O)OCCCCCN1C(=O)c2ccc(C(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCCOC(=O)c1ccc(-c2ccc(-c3ccc(C(=O)O*)cc3)cc2)cc1
*CCCCCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CCCCCOC(=O)c1ccc(/C=C/c2ccc(C(=O)O*)cc2)cc1
*CCCCCOC(=O)c1ccc(C(=O)O*)cc1
*CCCCCOC(=O)c1ccc2cc(C(=O)O*)ccc2c1
*CCCCCOC(=O)c1cccc(C(=O)O*)c1
*CCCCCOC(=O)c1ccccc1C(=O)O[Cd]OC(=O)c1ccccc1C(=O)OCCCCCOC(=O)NCCCCCCNC(=O)O*
*CCCCCOC(=O)c1ccccc1C(=O)O[Cd]OC(=O)c1ccccc1C(=O)OCCCCCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1
*CCCCCOC(=O)c1ccccc1C(=O)O[Pb]OC(=O)c1ccccc1C(=O)OCCCCCOC(=O)NCCCCCCNC(=O)O*
*CCCCCOC(=O)c1ccccc1C(=O)O[Pb]OC(=O)c1ccccc1C(=O)OCCCCCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1
*CCCCCOCO*
*CCCCCOc1ccc(-c2ccc(O*)c3ccccc23)c2ccccc12
*CCCCCOc1ccc(/C=C/C(=O)OCCN(CCOC(=O)/C=C/c2ccc(O*)cc2)c2ccc(/N=N/c3ccc([N+](=O)[O-])cc3)cc2)cc1
*CCCCCOc1ccc(C(=O)OC(=O)c2ccc(O*)cc2)cc1
*CCCCCOc1ccc(CCc2ccc(O*)cc2C)cc1
*CCCCCOc1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(O*)cc3)c2)cc1
*CCCCCOc1ccc(OC(=O)c2ccc(C(=O)Oc3ccc(O*)cc3)cc2)cc1
*CCCCCOc1ccc(OCCCCCc2nnc(-c3ccc(-c4nnc(*)o4)cc3)o2)c(C)c1
*CCCCCOc1cccc(C(=O)OC(=O)c2cccc(O*)c2)c1
*CCCCCOc1cccc(NC(=O)c2ccc(C(=O)Nc3cccc(O*)c3)cc2)c1
*CCCCCOc1cccc(NC(=O)c2cccc(C(=O)Nc3cccc(O*)c3)c2)c1
*CCCCCSS*
*CCCCC[Si](*)(C)c1ccccc1
*CCCCN1C(=O)C2C3C=CC(C4C(=O)N(*)C(=O)C34)C2C1=O
*CCCCN1C(=O)c2ccc(C(=O)Oc3ccc(-c4ccc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4)cc3)cc2C1=O
*CCCCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CCCCNC(=O)C(=O)N*
*CCCCNC(=O)C(OC)C(OC)C(=O)N*
*CCCCNC(=O)CC(=O)N*
*CCCCNC(=O)CCCCC(=O)N*
*CCCCNC(=O)Cc1ccc(C(=O)N*)cc1
*CCCCNC(=O)c1ccc(C(=O)N*)c(Oc2ccccc2)c1
*CCCCNC(=O)c1ccc(C(=O)N*)c(Sc2ccccc2)c1
*CCCCNC(=O)c1ccc(C(C)(CC)c2ccc(C(=O)N*)cc2)cc1
*CCCCNC(=O)c1cccc(C(=O)N*)c1
*CCCCO*
*CCCCOC(=O)/C=C/C(=O)O*
*CCCCOC(=O)/C=C/C(=O)O*
*CCCCOC(=O)C(Cc1ccccc1)NC(=O)/C=C/C(=O)NC(Cc1ccccc1)C(=O)O*
*CCCCOC(=O)C1CCC(C(=O)O*)CC1
*CCCCOC(=O)CCC(=O)O*
*CCCCOC(=O)CCCC(=O)O*
*CCCCOC(=O)CCCCC(=O)O*
*CCCCOC(=O)CCCCC(=O)OCCCCOC(=O)c1ccc(C(=O)O*)cc1
*CCCCOC(=O)CCCCCCCC(=O)O*
*CCCCOC(=O)CCCCCCCCC(=O)O*
*CCCCOC(=O)CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC(=O)O*
*CCCCOC(=O)CCCCCNC(=O)CCCCC(=O)NCCCCCC(=O)O*
*CCCCOC(=O)CCSCCCCCCSCCC(=O)O*
*CCCCOC(=O)COCC(=O)O*
*CCCCOC(=O)NCC(F)(F)C(F)(F)C(F)(F)C(F)(F)CNC(=O)O*
*CCCCOC(=O)NCCCCCCNC(=O)O*
*CCCCOC(=O)NCCCCNC(=O)O*
*CCCCOC(=O)NNC(=O)CCCCCCCCC(=O)NNC(=O)O*
*CCCCOC(=O)Nc1ccc(-c2ccc(NC(=O)OCCCCn3c4ccccc4c4cc(/C=C(\C#N)C(=O)OCCCCCCOC(=O)/C(C#N)=C/c5ccc6c(c5)c5ccccc5n6*)ccc43)c(C)c2)cc1C
*CCCCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1
*CCCCOC(=O)Nc1ccc(C)c(NC(=O)OCCCCn2c3ccccc3c3cc(/C=C(\C#N)C(=O)OCCCCCCOC(=O)/C(C#N)=C/c4ccc5c(c4)c4ccccc4n5*)ccc32)c1
*CCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CCCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)OCCCCn3c4ccccc4c4cc(/C=C(\C#N)C(=O)OCCCCCCOC(=O)/C(C#N)=C/c5ccc6c(c5)c5ccccc5n6*)ccc43)cc2)cc1
*CCCCOC(=O)O*
*CCCCOC(=O)OCC1CCC(COC(=O)O*)CC1
*CCCCOC(=O)OCCCCCCOC(=O)OCCCCn1c(=O)c2cc3c(=O)n(*)c(=O)c3cc2c1=O
*CCCCOC(=O)OCCCCN1C(=O)c2ccc(-c3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCCOC(=O)c1ccc(-c2ccc(-c3ccc(C(=O)O*)cc3)cc2)cc1
*CCCCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CCCCOC(=O)c1ccc(C(=O)O*)cc1
*CCCCOC(=O)c1ccc(C(C)(C)c2ccc(C(=O)O*)cc2)cc1
*CCCCOC(=O)c1ccc(N/C=N/c2ccc(C(=O)O*)cc2)cc1
*CCCCOC(=O)c1ccc([Si](C)(C)c2ccc(C(=O)O*)cc2)cc1
*CCCCOC(=O)c1ccc2cc(C(=O)O*)ccc2c1
*CCCCOC(=O)c1cccc(C(=O)O*)c1
*CCCCOCC(O)CN(CC(O)CO*)c1ccc(N(C)C)cc1
*CCCCOCCCCOCCCCOC(=O)c1ccc(/N=C/c2cc(OCCC(C)CCCC(C)C)c(/C=N/c3ccc(C(=O)O*)cc3)cc2OCCC(C)CCCC(C)C)cc1
*CCCCOCCCCOCCCCOC(=O)c1ccc(/N=C/c2cc(OCCCCCC)c(/C=C/c3cc(OCCCCCC)c(/C=C/c4cc(OCCCCCC)c(/C=N/c5ccc(C(=O)O*)cc5)cc4OCCCCCC)cc3OCCCCCC)cc2OCCCCCC)cc1
*CCCCOCCCCOCCCCOC(=O)c1ccc(/N=C/c2ccc(/C=N/c3ccc(C(=O)O*)cc3)cc2)cc1
*CCCCOCO*
*CCCCOc1ccc(-c2ccc(O*)c3ccccc23)c2ccccc12
*CCCCOc1ccc(/C=C2\CC/C(=C\c3ccc(OCCCCOP(=O)(O*)OC)c(OC)c3)C2=O)cc1OC
*CCCCOc1ccc(/C=C2\CC/C(=C\c3ccc(OCCCCOP(=O)(O*)OCC)c(OC)c3)C2=O)cc1OC
*CCCCOc1ccc(C(=O)Nc2cccc(P(=O)(c3ccccc3)c3cccc(NC(=O)c4ccc(O*)cc4)c3)c2)cc1
*CCCCOc1ccc(C(=O)OC(=O)c2ccc(O*)cc2)cc1
*CCCCOc1ccc(C(=O)OCCOC(=O)c2ccc(O*)cc2)cc1
*CCCCOc1ccc(CCc2ccc(O*)cc2C)cc1
*CCCCOc1ccc(OC(=O)c2ccc(C(=O)Oc3ccc(O*)cc3)c(C(=O)OCC=C)c2)cc1
*CCCCOc1ccc(OCCCCc2nnc(-c3ccc(-c4nnc(*)o4)cc3)o2)c(C)c1
*CCCCOc1cccc(C(=O)OC(=O)c2cccc(O*)c2)c1
*CCCCP(C)(=O)CCCNC(=O)CCCCC(=O)N*
*CCCCSS*
*CCCCSSCCCCO*
*CCCCSSCCCCOCO*
*CCCCc1ccc(-c2c(-c3ccccc3)cc(-c3ccc(-c4cc(-c5ccccc5)c(-c5ccc(*)cc5)c(-c5ccccc5)c4-c4ccccc4)cc3)c(-c3ccccc3)c2-c2ccccc2)cc1
*CCCCc1ccc(-c2c(-c3ccccc3)cc(-c3cccc(-c4cc(-c5ccccc5)c(-c5ccc(*)cc5)c(-c5ccccc5)c4-c4ccccc4)c3)c(-c3ccccc3)c2-c2ccccc2)cc1
*CCCN(*)C
*CCCN(*)C(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*CCCN(*)C(=O)CC
*CCCN(*)C(=O)CCCCC
*CCCN(*)C(=O)c1ccccc1
*CCCN(*)C(C)=O
*CCCN(C)c1ccc(C(=O)c2ccc(N(*)C)cc2)cc1
*CCCN(Cc1ccccc1)C(=O)S*
*CCCN1C(=O)C2C3C=CC(C4C(=O)N(*)C(=O)C34)C2C1=O
*CCCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CCCNC(=O)C(Cc1ccccc1)NC(=O)CCCCCCCCC(=O)NC(Cc1ccccc1)C(=O)NCCCOCCOCCO*
*CCCNC(=O)C(NC(=O)C(Cc1ccccc1)NC(=O)CCCCCCCCC(=O)NC(Cc1ccccc1)C(=O)NC(C(=O)NCCCOCCOCCO*)C(C)C)C(C)C
*CCCNC(=O)C(OC)C(OC)C(=O)N*
*CCCNC(=O)CCCCC(=O)NCCCN(*)C
*CCCNC(=O)CCCCC(=O)NCCCO*
*CCCNC(=O)CCCCCCCCC(=O)NCCCOCCOCCO*
*CCCNC(=O)CNC(=O)C(Cc1ccccc1)NC(=O)CCCCCCCCC(=O)NC(Cc1ccccc1)C(=O)NCC(=O)NCCCOCCOCCO*
*CCCNC(=O)CNC(=O)C(NC(=O)C(Cc1ccccc1)NC(=O)CCCCCCCCC(=O)NC(Cc1ccccc1)C(=O)NC(C(=O)NCC(=O)NCCCOCCOCCO*)C(C)C)C(C)C
*CCCNC(=O)Cc1ccc(C(=O)N*)cc1
*CCCNC(=O)O*
*CCCNC(=O)c1ccc(C(=O)N*)cc1
*CCCNC(=O)c1ccc(C(=O)NCCCO*)cc1
*CCCO*
*CCCOC(=O)C(C)O*
*CCCOC(=O)C1CCC(C(=O)O*)CC1
*CCCOC(=O)CCC(=O)O*
*CCCOC(=O)CCCC(=O)O*
*CCCOC(=O)CCCCC(=O)O*
*CCCOC(=O)CCCCCCC(=O)O*
*CCCOC(=O)NCCCCCCNC(=O)O*
*CCCOC(=O)NNC(=O)CCCCCCCCC(=O)NNC(=O)O*
*CCCOC(=O)Nc1cc(NC(=O)O*)ccc1C
*CCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CCCOC(=O)O*
*CCCOC(=O)OCCCCCCOC(=O)OCCCN1C(=O)c2ccc(C(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCCOC(=O)OCCCCCCOC(=O)OCCCn1c(=O)c2cc3c(=O)n(*)c(=O)c3cc2c1=O
*CCCOC(=O)c1cc(/N=N/c2ccc(OCC)cc2)ccc1-c1ccc(/N=N/c2ccc(OCC)cc2)cc1C(=O)O*
*CCCOC(=O)c1ccc(C(=O)O*)cc1
*CCCOC(=O)c1ccc2cc(C(=O)O*)ccc2c1
*CCCOC(=O)c1cccc(C(=O)O*)c1
*CCCOCCCCOCCCN1C(=O)c2ccc(C(=O)Oc3ccc(-c4ccc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4)cc3)cc2C1=O
*CCCOCCCCOCCCN1C(=O)c2ccc(C(=O)Oc3ccc(C(C)(C)c4ccc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4)cc3)cc2C1=O
*CCCOCCCCOCCCN1C(=O)c2ccc(C(=O)Oc3ccc(OC(=O)c4ccc5c(c4)C(=O)N(*)C5=O)c(-c4ccccc4)c3)cc2C1=O
*CCCOCCCCOCCCN1C(=O)c2ccc(C(=O)Oc3ccc(OC(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2C1=O
*CCCOCCCCOCCCN1C(=O)c2ccc(C(=O)Oc3ccc4cc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)ccc4c3)cc2C1=O
*CCCOCCCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CCCOc1ccc(-c2ccc(O*)c3ccccc23)c2ccccc12
*CCCOc1ccc(C(=O)OC(=O)c2ccc(O*)cc2)cc1
*CCCOc1ccc(OCCCc2nnc(-c3ccc(-c4nnc(*)o4)cc3)o2)c(C)c1
*CCCOc1cccc(C(=O)OC(=O)c2cccc(O*)c2)c1
*CCCP(=O)(CCCNC(=O)CCCCC(=O)N*)c1ccccc1
*CCCP(C)(=O)CCCNC(=O)CCCCC(=O)N*
*CCCP(CCCCCCCC)CCCNC(=O)CCCCC(=O)N*
*CCCP(CCCNC(=O)CCCCC(=O)N*)CC(C)C
*CCCP(CCCNC(=O)CCCCC(=O)N*)c1ccccc1
*CCCS(=O)(=O)CCCCS(=O)(=O)CCCOC(=O)O*
*CCCS(=O)(=O)CCCCS(=O)(=O)CCCOC(=O)c1cccc(C(=O)O*)c1
*CCCS(=O)(=O)CCCCS(=O)(=O)CCCOC(=O)c1ccccc1C(=O)O*
*CCCS(=O)(=O)CCCS(=O)(=O)CCCOC(=O)c1cccc(C(=O)O*)c1
*CCCS(=O)(=O)CCCS(=O)(=O)CCCOC(=O)c1ccccc1C(=O)O*
*CCCS(=O)(=O)c1ccc(/N=N/c2ccc(N(CCCC)CC(=O)O*)cc2)cc1
*CCCS*
*CCC[Si](*)(C)C
*CCC[Si](*)(C)CCC[Si](C)(C)C
*CCC[Si](*)(C)C[Si](C)(C)C
*CCC[Si](*)(C)O[Si](C)(C)CCCOc1ccc(-c2ccc(C#N)cc2)cc1
*CCC[Si](*)(C)O[Si](C)(C)CCCOc1ccc(C#N)cc1
*CCC[Si](*)(C)O[Si](C)(C)CCCOc1ccc(C(=O)Oc2ccc(-c3ccc(C#N)cc3)cc2)cc1
*CCC[Si](*)(C)O[Si](C)(C)CCCOc1ccc(C(=O)Oc2ccc(C#N)cc2)cc1
*CCC[Si](*)(C)c1ccc(N(C)C)cc1
*CCC[Si](*)(C)c1cccc(C)c1
*CCC[Si](*)(C)c1ccccc1
*CCC[Si](*)(C[Si](C)(C)c1ccccc1)C[Si](C)(C)c1ccccc1
*CCC[Si](*)(c1ccc(C)cc1)c1ccc(C)cc1
*CCC[Si](*)(c1cccc2ccccc12)C1CCCCC1
*CCC[Si](*)(c1ccccc1)c1ccc(N(C)C)cc1
*CCC[Si](C)(C)O[Si](C)(C)CCCN1C(=O)c2ccc(-c3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCC[Si](C)(C)O[Si](C)(C)CCCN1C(=O)c2ccc(C(=O)c3ccc4c(c3)C(=O)N(*)C4=O)cc2C1=O
*CCC[Sn](C)(C)CCCOC(=O)C(CCCCCCCCOc1ccc(-c2ccc(OCc3ccc(C#N)cc3)cc2)cc1)C(=O)O*
*CCC[Sn](C)(C)CCCOC(=O)C(CCCCCCOc1ccc(-c2ccc(OCc3ccc([N+](=O)[O-])cc3)cc2)cc1)C(=O)O*
*CCCc1ccc(-c2c(-c3ccccc3)cc(-c3ccc(-c4cc(-c5ccccc5)c(-c5ccc(*)cc5)c(-c5ccccc5)c4-c4ccccc4)cc3)c(-c3ccccc3)c2-c2ccccc2)cc1
*CCCc1ccc(-c2c(-c3ccccc3)cc(-c3cccc(-c4cc(-c5ccccc5)c(-c5ccc(*)cc5)c(-c5ccccc5)c4-c4ccccc4)c3)c(-c3ccccc3)c2-c2ccccc2)cc1
*CCCc1nc2cc(-c3ccc4[nH]c(*)nc4c3)ccc2[nH]1
*CCN(*)C
*CCN(*)C(=O)C(C)C
*CCN(*)C(=O)C(OC)(c1ccccc1)C(F)(F)F
*CCN(*)C(=O)C1(c2ccc(Cl)cc2)CCC1
*CCN(*)C(=O)C12CC3CC(CC(C3)C1)C2
*CCN(*)C(=O)C1CC1
*CCN(*)C(=O)CC
*CCN(*)C(=O)CC(C)C
*CCN(*)C(=O)CC12CC3CC(CC(C3)C1)C2
*CCN(*)C(=O)CCC
*CCN(*)C(=O)CCC(=O)OC
*CCN(*)C(=O)CCCC
*CCN(*)C(=O)CCCCC
*CCN(*)C(=O)CCCCCC
*CCN(*)C(=O)CCCCCCC
*CCN(*)C(=O)CCCCCCCCCCC
*CCN(*)C(=O)CCCCCCCCCCCCCCCCC
*CCN(*)C(=O)CSC
*CCN(*)C(=O)Cc1ccc(C(F)(F)F)cc1
*CCN(*)C(=O)c1c(F)c(F)c(F)c(F)c1F
*CCN(*)C(=O)c1c(F)c(F)cc(F)c1F
*CCN(*)C(=O)c1c(F)cc(F)c(F)c1F
*CCN(*)C(=O)c1c(F)cc(F)cc1F
*CCN(*)C(=O)c1c(F)ccc(F)c1F
*CCN(*)C(=O)c1c(F)cccc1F
*CCN(*)C(=O)c1cc(F)c(F)c(F)c1
*CCN(*)C(=O)c1cc(F)c(F)c(F)c1F
*CCN(*)C(=O)c1cc(F)c(F)cc1F
*CCN(*)C(=O)c1cc(F)cc(F)c1
*CCN(*)C(=O)c1cc(F)cc(F)c1F
*CCN(*)C(=O)c1cc(F)ccc1F
*CCN(*)C(=O)c1ccc(F)c(F)c1
*CCN(*)C(=O)c1ccc(F)c(F)c1F
*CCN(*)C(=O)c1ccc(F)cc1
*CCN(*)C(=O)c1ccc(F)cc1F
*CCN(*)C(=O)c1ccc(SC(F)(F)F)cc1
*CCN(*)C(=O)c1ccc2ccccc2c1
*CCN(*)C(=O)c1cccc(F)c1
*CCN(*)C(=O)c1cccc(F)c1F
*CCN(*)C(=O)c1ccccc1
*CCN(*)C(=O)c1ccccc1F
*CCN(*)C(C)=O
*CCN(*)CC
*CCN(*)CCOCCOC
*CCN(*)CC[Si](C)(C)C
*CCN(*)Cc1ccccc1
*CCN(C)C(=O)CCCCCCCCC(=O)N(*)C
*CCN(CCCCCCOc1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1)CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1
*CCN(CCCCCCOc1ccc(/N=N/c2ccccc2)cc1)CCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CCN(CCCCCOc1ccc(/N=N/c2ccccc2)cc1)CCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CCN(CCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O)c1ccc(/C=C/C2=CC(=C(C#N)C#N)CC(C)(C)C2)cc1
*CCN(CCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O)c1ccc(/N=N/c2ccc(C#N)cc2)cc1
*CCN(CCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O)c1ccc(/N=N/c2ccc(C3OCCO3)cc2)cc1
*CCN(CCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O)c1ccc(/N=N/c2ccc(C=O)cc2)cc1
*CCN(CCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CCN(CCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2C)cc1
*CCN(CCNC(=O)c1ccc2c(c1)C(=O)N(*)C2=O)c1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1
*CCN(CCOC(=O)/C=C/c1ccc(/C=C/C(=O)O*)cc1)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CCN(CCOC(=O)CCC#CC#CCCC(=O)O*)c1ccc(/N=N/c2c(C)cc(/N=N/c3ccc(C#N)cc3)cc2C)cc1
*CCN(CCOC(=O)CCC#CC#CCCC(=O)O*)c1ccc([N+](=O)[O-])cc1
*CCN(CCOC(=O)CCCCC(=O)O*)c1ccc(/C=N/N(c2ccccc2)c2ccccc2)cc1
*CCN(CCOC(=O)NC(C)(C)c1cccc(C(C)(C)NC(=O)O*)c1)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CCN(CCOC(=O)NCC1(C)CC(NC(=O)O*)CC(C)(C)C1)c1ccc(/N=N/c2ccc(B(c3c(C)cc(C)cc3C)c3c(C)cc(C)cc3C)cc2)cc1
*CCN(CCOC(=O)NCC1(C)CC(NC(=O)O*)CC(C)(C)C1)c1ccc(/N=N/c2ccc(C#N)cc2)cc1
*CCN(CCOC(=O)NCC1(C)CC(NC(=O)O*)CC(C)(C)C1)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CCN(CCOC(=O)NCC1(C)CC(NC(=O)O*)CC(C)(C)C1)c1ccc(C=C(C#N)C#N)cc1
*CCN(CCOC(=O)NCCCCCCNC(=O)O*)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)c(C)c1
*CCN(CCOC(=O)NCCCCCCNC(=O)O*)c1ccccc1
*CCN(CCOC(=O)Nc1ccc(-c2ccc(NC(=O)O*)c(C)c2)cc1C)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CCN(CCOC(=O)Nc1ccc(-c2ccc(NC(=O)O*)c(OC)c2)cc1OC)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccc(-c2nc3ccc([N+](=O)[O-])cc3o2)cc1
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccc(/C=C/c2cc[n+](CCCC)cc2)cc1
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccc(/C=C2/NC(=O)NC2=O)cc1
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccc(/N=N/c2ccc(-c3nc4cc([N+](=O)[O-])ccc4[nH]3)cc2)cc1
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccc(/N=N/c2ccc(-c3nc4ccc([N+](=O)[O-])cc4n3CC)cc2)cc1
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccc(/N=N/c2ccc(-c3nc4ccccc4o3)cc2)cc1
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccc(/N=N/c2ccc(/C=C/c3nc4ccc([N+](=O)[O-])cc4n3CC)cc2)cc1
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccc(/N=N/c2ccc(/N=N/c3cc(OC)c(/N=N/c4ccc([N+](=O)[O-])cc4)cc3OC)c(C)c2)cc1
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccc(/N=N/c2ccc(B(c3c(C)cc(C)cc3C)c3c(C)cc(C)cc3C)cc2)cc1
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2C)c(C)c1
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2C)cc1
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccc(C(C#N)=C(C#N)C#N)cc1
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccc([N+](=O)[O-])cc1C
*CCN(CCOC(=O)Nc1ccc(C)c(NC(=O)O*)c1)c1ccccc1
*CCN(CCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)c(C)c1
*CCN(CCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CCN(CCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2C)c(C)c1
*CCN(CCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1)c1ccccc1
*CCN(CCOC(=O)Nc1cccc(NC(=O)O*)c1C)c1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1
*CCN(CCOC(=O)Nc1cccc(NC(=O)O*)c1C)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CCN(CCOC(=O)OCc1ccc(COC(=O)O*)cc1)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
*CCN(CCOC(=O)c1cc(OC2CCN(c3ccc(S(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc3)CC2)cc(C(=O)O*)c1)c1ccc(S(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc1
*CCN(CCOC(=O)c1cc(OCCC2CCN(c3ccc(S(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc3)CC2)cc(C(=O)O*)c1)c1ccc(S(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc1
*CCN(CCOC(=O)c1cc(OCCN(C)c2ccc(S(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc2)cc(C(=O)O*)c1)c1ccc(S(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc1
*CCN(CCOC(=O)c1cc(OCCN(C)c2ccc(S(=O)(=O)CCCCCCCC)cc2)cc(C(=O)O*)c1)c1ccc(S(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc1
*CCN(CCOC(=O)c1cc(OCc2c(F)c(F)c(OC)c(F)c2F)cc(C(=O)O*)c1)c1ccc(/C=C/c2ccc(/C=C/C3=C(C#N)C(=C(C#N)C#N)OC3(c3ccccc3)C(F)(F)F)s2)cc1
*CCN(CCOC(=O)c1cc(OCc2cc(OCc3ccccc3)cc(OCc3ccccc3)c2)cc(C(=O)O*)c1)c1ccc(/C=C/c2ccc(/C=C/C3=C(C#N)C(=C(C#N)C#N)OC3(c3ccccc3)C(F)(F)F)s2)cc1
*CCN(CCOC(=O)c1ccc(C(=O)O*)c(OC)c1)c1ccc(/N=N/c2ccc(-c3nc4ccc([N+](=O)[O-])cc4n3CC)cc2)cc1
*CCN(CCOC(=O)c1ccc(C(=O)O*)c(OC)c1)c1ccc(/N=N/c2ccc(/C=C/c3nc4ccc([N+](=O)[O-])cc4n3CC)cc2)cc1
*CCN(CCOC(=O)c1ccc(C(=O)O*)c(OCCC)c1)c1ccc(-c2nc3ccc([N+](=O)[O-])cc3o2)cc1
*CCN(CCOC(=O)c1ccc(C(=O)O*)c(OCCC)c1)c1ccc(/N=N/c2ccc(-c3nc4cc([N+](=O)[O-])ccc4[nH]3)cc2)cc1
*CCN(CCOC(=O)c1ccc(C(=O)O*)c(OCCC)c1)c1ccc(/N=N/c2ccc(-c3nc4ccccc4o3)cc2)cc1
*CCN(CCOC(=O)c1ccc(C(=O)O*)c(OCCC)c1)c1ccc(/N=N/c2nc(C#N)c(C#N)[nH]2)cc1
*CCN(CCOC(=O)c1ccc(C(=O)O*)c(OCCC)c1)c1ccc(C=C(C#N)C#N)cc1
*CCN(CCOC(=O)c1ccc(C(=O)O*)c(OCCCCC)c1)c1ccc(/N=N/c2nc(C#N)c(C#N)[nH]2)cc1
*CCN(CCOC(=O)c1ccc(C(=O)O*)cc1)c1ccc(-c2nc3ccc([N+](=O)[O-])cc3o2)cc1
*CCN(CCOC(=O)c1ccc(C(=O)O*)cc1)c1ccc(/N=N/c2ccc(-c3nc4ccc([N+](=O)[O-])cc4n3CC)cc2)cc1
*CCN(CCOC(=O)c1ccc(C(=O)O*)cc1)c1ccc(C=C(C#N)C#N)cc1
*CCN(CCOC(=O)c1ccc([Si](C)(C)c2ccc(C(=O)O*)cc2)cc1)c1ccc(/C=C/c2ccc([N+](=O)[O-])cc2)cc1
*CCN(CCOC(=O)c1cccc(C(=O)O*)c1)c1ccc(/N=N/c2ccc(-c3nc4ccc([N+](=O)[O-])cc4n3CC)cc2)cc1
*CCN(CCOC(=O)c1cccc(C(=O)O*)c1)c1ccccc1
*CCN(Cc1ccccc1)C(=O)S*
*CCN*
*CCN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(*)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CCNC(=O)CCCCC(=O)N*
*CCNC(=O)Nc1ccc(Cc2ccc(NC(=O)NCCOCCO*)cc2)cc1
*CCNC(=O)c1ccc(C(=O)N*)c(Oc2ccccc2)c1
*CCNC(=O)c1ccc(C(=O)N*)c(Sc2ccccc2)c1
*CCNC(=O)c1ccc(O*)cc1
*CCNC(=O)c1ccc([Si](C)(C)c2ccc(C(=O)NCCN(*)c3ccc(/C=C/c4ccc([N+](=O)[O-])cc4)cc3)cc2)cc1
*CCNC(=O)c1ccc([Si](CCCC)(CCCC)c2ccc(C(=O)NCCN(*)c3ccc(/C=C/c4ccc([N+](=O)[O-])cc4)cc3)cc2)cc1
*CCNC(=O)c1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(C(=O)NCCN(*)c3ccc(/C=C/c4ccc([N+](=O)[O-])cc4)cc3)cc2)cc1
*CCNC(=O)c1cccc(O*)c1
*CCNC(=O)c1ccccc1O*
*CCO*
*CCOC(=O)/C=C/C(=O)NCCCCCCNC(=O)/C=C/C(=O)O*
*CCOC(=O)/C=C/C(=O)O*
*CCOC(=O)C1=CCCCC(N2CCCC2)=C1C(=O)O*
*CCOC(=O)C1C2C=CC(C2)C1C(=O)O*
*CCOC(=O)C1CCC(C(=O)O*)CC1
*CCOC(=O)C1CCC(C(=O)OCCOc2ccc(C(C)(C)c3ccc(O*)cc3)cc2)CC1
*CCOC(=O)C1CCC(C(=O)OCCOc2ccc(C3(c4ccc(O*)cc4)c4ccccc4-c4ccccc43)cc2)CC1
*CCOC(=O)CC(C)O*
*CCOC(=O)CCC(=O)O*
*CCOC(=O)CCC(=O)OCCOC(=O)c1ccc(C(=O)O*)cc1
*CCOC(=O)CCCCC(=O)O*
*CCOC(=O)CCCCC(=O)OCCOC(=O)c1ccc(C(=O)O*)cc1
*CCOC(=O)CCCCCC(=O)O*
*CCOC(=O)CCCCCCC(=O)OCCOC(=O)c1ccc(C(=O)O*)cc1
*CCOC(=O)CCCCCCCC(=O)O*
*CCOC(=O)CCCCCCCCC(=O)O*
*CCOC(=O)CCCCCCCCC(=O)OCCOC(=O)c1ccc(C(=O)O*)cc1
*CCOC(=O)CCCCCCCCCCC(=O)OCCOC(=O)c1ccc(C(=O)O*)cc1
*CCOC(=O)CCSCCC(=O)O*
*CCOC(=O)Cc1ccc(C(=O)O*)cc1
*CCOC(=O)NC1CCC(CC2CCC(NC(=O)OCCOc3ccc(C4(c5ccc(O*)cc5)c5ccccc5-c5ccccc54)cc3)CC2)CC1
*CCOC(=O)NCC1(C)CC(NC(=O)O*)CC(C)(C)C1
*CCOC(=O)NCCCCCCCCCCNC(=O)O*
*CCOC(=O)NCCCCCCCCCNC(=O)O*
*CCOC(=O)NCCCCCCNC(=O)O*
*CCOC(=O)NCCCCCCNC(=O)OCCOc1ccc(C2(c3ccc(O*)cc3)c3ccccc3-c3ccccc32)cc1
*CCOC(=O)NNC(=O)CCCCCCCCC(=O)NNC(=O)O*
*CCOC(=O)Nc1cc(NC(=O)O*)ccc1C
*CCOC(=O)Nc1ccc(C)c(NC(=O)OCCOc2cc([N+](=O)[O-])ccc2/N=N/c2cn(*)c3ccc(-c4ccc5c(c4)c4ccccc4n5CCCC)cc23)c1
*CCOC(=O)Nc1ccc(C)c(NC(=O)OCCOc2cc([N+](=O)[O-])ccc2/N=N/c2cn(*)c3ccc(-c4ccccc4)cc23)c1
*CCOC(=O)Nc1ccc(C)c(NC(=O)OCCOc2cc([N+](=O)[O-])ccc2/N=N/c2cn(*)c3ccccc23)c1
*CCOC(=O)Nc1ccc(C)c(NC(=O)OCCOc2ccc(C3(c4ccc(O*)cc4)c4ccccc4-c4ccccc43)cc2)c1
*CCOC(=O)Nc1ccc(CCCCc2ccc(NC(=O)O*)cc2)cc1
*CCOC(=O)Nc1ccc(CCCc2ccc(NC(=O)O*)cc2)cc1
*CCOC(=O)Nc1ccc(CCc2ccc(NC(=O)O*)cc2)cc1
*CCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CCOC(=O)Nc1ccc(Cc2ccc(NC(=O)OCCOc3ccc(C(C)(C)c4ccc(O*)cc4)cc3)cc2)cc1
*CCOC(=O)Nc1ccc(Cc2ccc(NC(=O)OCCOc3ccc(C4(c5ccc(O*)cc5)c5ccccc5-c5ccccc54)cc3)cc2)cc1
*CCOC(=O)Nc1ccc(Cc2ccc(NC(=O)OCCOc3ccc(O*)cc3)cc2)cc1
*CCOC(=O)O*
*CCOC(=O)c1cc(C(=O)O*)cc(C(C)(C)C)c1
*CCOC(=O)c1ccc(-c2ccc(-c3ccc(C(=O)O*)cc3)cc2)cc1
*CCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2C)c(C)c1
*CCOC(=O)c1ccc(/C(C#N)=C(\c2ccc(OC)cc2)N2CCC(*)CC2)cc1
*CCOC(=O)c1ccc(C(=O)O*)c2ccccc12
*CCOC(=O)c1ccc(C(=O)O*)cc1
*CCOC(=O)c1ccc(C(=O)OCCOc2ccc(C(C)(C)c3ccc(O*)cc3)cc2)cc1
*CCOC(=O)c1ccc(C(C)(CC)c2ccc(C(=O)O*)cc2)cc1
*CCOC(=O)c1ccc(C2(C)CC(C)(C)c3ccc(C(=O)O*)cc32)cc1
*CCOC(=O)c1ccc(O*)cc1
*CCOC(=O)c1ccc2cc(C(=O)O*)ccc2c1
*CCOC(=O)c1ccc2ccc(C(=O)O*)cc2c1
*CCOC(=O)c1cccc(C(=O)O*)c1
*CCOC(=O)c1cccc2c(C(=O)O*)cccc12
*CCOC(=O)c1ccccc1C(=O)O*
*CCOCC(=O)O*
*CCOCC(CCl)O*
*CCOCCC(=O)O*
*CCOCCOC(=O)/C=C/C(=O)NCCCCCCNC(=O)/C=C/C(=O)O*
*CCOCCOC(=O)/C=C/C(=O)O*
*CCOCCOC(=O)C(=O)O*
*CCOCCOC(=O)C(C)C(=O)O*
*CCOCCOC(=O)C(CCC)C(=O)O*
*CCOCCOC(=O)C(CCCCC)C(=O)O*
*CCOCCOC(=O)C(CCCCCCC)C(=O)O*
*CCOCCOC(=O)C(CCCCCCCCC)C(=O)O*
*CCOCCOC(=O)C1CCC(C(=O)O*)CC1
*CCOCCOC(=O)CC(=O)O*
*CCOCCOC(=O)CCC(=O)O*
*CCOCCOC(=O)CCCC(=O)O*
*CCOCCOC(=O)CCCCC(=O)O*
*CCOCCOC(=O)CCCCCC(=O)O*
*CCOCCOC(=O)CCCCCCC(=O)O*
*CCOCCOC(=O)CCCCCCCC(=O)O*
*CCOCCOC(=O)CCCCCCCCC(=O)O*
*CCOCCOC(=O)CCCCCCCCCCC(=O)O*
*CCOCCOC(=O)CCCCCCCCCCCCCCCCC(=O)O*
*CCOCCOC(=O)CCSCCC(=O)O*
*CCOCCOC(=O)NCCCCCCNC(=O)O*
*CCOCCOC(=O)O*
*CCOCCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CCOCCOC(=O)c1ccc(C(=O)O*)cc1
*CCOCCOC(=O)c1ccc(N/C=N/c2ccc(C(=O)O*)cc2)cc1
*CCOCCOC(=O)c1cccc(C(=O)O*)c1
*CCOCCOC(=O)c1ccccc1-c1ccccc1C(=O)O*
*CCOCCOC(=O)c1ccccc1C(=O)O*
*CCOCCOCCN1C(=O)c2ccc(C(=O)Oc3ccc(-c4ccc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4)cc3)cc2C1=O
*CCOCCOCCN1C(=O)c2ccc(C(=O)Oc3ccc(C(C)(C)c4ccc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4)cc3)cc2C1=O
*CCOCCOCCN1C(=O)c2ccc(C(=O)Oc3ccc(OC(=O)c4ccc5c(c4)C(=O)N(*)C5=O)c(-c4ccccc4)c3)cc2C1=O
*CCOCCOCCN1C(=O)c2ccc(C(=O)Oc3ccc(OC(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2C1=O
*CCOCCOCCN1C(=O)c2ccc(C(=O)Oc3ccc4cc(OC(=O)c5ccc6c(c5)C(=O)N(*)C6=O)ccc4c3)cc2C1=O
*CCOCCOCCOC(=O)/C=C/C(=O)NCCCCCCNC(=O)/C=C/C(=O)O*
*CCOCCOCCOC(=O)C1CCC(C(=O)O*)CC1
*CCOCCOCCOC(=O)CCCCC(=O)O*
*CCOCCOCCOC(=O)CCCCCC(=O)O*
*CCOCCOCCOC(=O)CCSCCC(=O)O*
*CCOCCOCCOC(=O)NCCCCCCNC(=O)O*
*CCOCCOCCOC(=O)c1cc(/N=N/c2ccc(OCC)cc2)ccc1-c1ccc(/N=N/c2ccc(OCC)cc2)cc1C(=O)O*
*CCOCCOCCOC(=O)c1ccc(-c2ccc(-c3ccc(C(=O)O*)cc3)cc2)cc1
*CCOCCOCCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CCOCCOCCOCCOC(=O)/C=C/C(=O)NCCCCCCNC(=O)/C=C/C(=O)O*
*CCOCCOCCOCCOC(=O)CCSCCC(=O)O*
*CCOCCOCCOCCOC(=O)NCCCCCCNC(=O)O*
*CCOCCOCCOCCOC(=O)Nc1ccc(Cc2ccc(NC(=O)O*)cc2)cc1
*CCOCCOCCOCCOC(=O)c1ccc(-c2ccc(-c3ccc(C(=O)O*)cc3)cc2)cc1
*CCOCCOCCOCCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
*CCOCCOCCOCCOCCOC(=O)CCSCCC(=O)O*
*CCOCCOCCOCCOCCOCCOC(=O)NCCCCCCNC(=O)O*
*CCOCCOCCOCCOCCOCCOC(=O)c1cc(OCCCCCCOc2ccc(-c3ccc(OC)cc3)cc2)cc(C(=O)O*)c1
*CCOCCOCCOCCOCCOCCOC(=O)c1cccc(C(=O)O*)c1
*CCOCCOCCOCCOCCOCCOCCOCCOCCOCCOC(=O)c1ccc(-c2ccc(-c3ccc(C(=O)O*)cc3)cc2)cc1
*CCOCCOCCOCCOCCOCCOc1ccc(C(=O)Nc2cc(NC(=O)c3ccc(O*)cc3)cc(-c3nc4ccccc4[nH]3)c2)cc1
*CCOCCOCCOCCOCCOc1ccc(/C=C(\C)c2ccc(O*)cc2)cc1
*CCOCCOCCOCCOCCOc1ccc(C(=O)Nc2cc(NC(=O)c3ccc(O*)cc3)cc(-c3nc4ccccc4[nH]3)c2)cc1
*CCOCCOCCOCCOc1ccc(/C=C(\C)c2ccc(O*)cc2)cc1
*CCOCCOCCOCCOc1ccc(C(=O)Nc2cc(NC(=O)c3ccc(O*)cc3)cc(-c3nc4ccccc4[nH]3)c2)cc1
*CCOCCOCCOc1ccc(/C=C(\C)c2ccc(O*)cc2)cc1
*CCOCCOCCOc1ccc(/C=C/c2cc(OC)c(/C=C/c3ccc(O*)c4ccccc34)cc2OC)c2ccccc12
*CCOCCOCCOc1ccc(/C=C/c2ccc(/C=C/c3ccc(O*)c4ccccc34)cc2)c2ccccc12
*CCOCCOCCOc1ccc(C(=O)Nc2cc(NC(=O)c3ccc(O*)cc3)cc(-c3nc4ccccc4[nH]3)c2)cc1
*CCOCCOCCOc1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(O*)cc3)c2)cc1
*CCOCCOCCOc1cccc(NC(=O)c2ccc(C(=O)Nc3cccc(O*)c3)cc2)c1
*CCOCCOCCOc1cccc(NC(=O)c2cccc(C(=O)Nc3cccc(O*)c3)c2)c1
*CCOCCOP(=O)(/N=N/c1ccc(-c2ccc(/N=N/P(=O)(O*)OC)cc2)cc1)OC
*CCOCCOP(=O)(/N=N/c1ccc(C(=O)c2ccc(/N=N/P(=O)(O*)OC)cc2)cc1)OC
*CCOCCOP(=O)(/N=N/c1ccc(Oc2ccc(/N=N/P(=O)(O*)OC)cc2)cc1)OC
*CCOCCOc1ccc(/C=C(\C)c2ccc(O*)cc2)cc1
*CCOCCOc1ccc(C(=O)Nc2cc(NC(=O)c3ccc(O*)cc3)cc(-c3nc4ccccc4[nH]3)c2)cc1
*CCOCCOc1ccc(C(=O)OC(=O)c2ccc(O*)cc2)cc1
*CCOCCOc1ccc(N/C=N/c2ccc(O*)cc2)cc1
*CCOCCOc1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(O*)cc3)c2)cc1
*CCOCCOc1cccc(NC(=O)c2ccc(C(=O)Nc3cccc(O*)c3)cc2)c1
*CCOCCOc1cccc(NC(=O)c2cccc(C(=O)Nc3cccc(O*)c3)c2)c1
*CCOCO*
*CCOP(=O)(/N=N/c1ccc(-c2ccc(/N=N/P(=O)(O*)OCC)cc2)cc1)OCC
*CCOP(=O)(/N=N/c1ccc(C(=O)c2ccc(/N=N/P(=O)(O*)OCC)cc2)cc1)OCC
*CCOP(=O)(/N=N/c1ccc(CCOC(=O)c2cc(C(=O)OCCc3ccc(/N=N/P(=O)(O*)OC)cc3)cc(C(C)(C)C)c2)cc1)OC
*CCOP(=O)(/N=N/c1ccc(COC(=O)c2cc(C(=O)OCc3ccc(/N=N/P(=O)(O*)OC)cc3)cc(C(C)(C)C)c2)cc1)OC
*CCOP(=O)(/N=N/c1ccc(Oc2ccc(/N=N/P(=O)(O*)OCC)cc2)cc1)OCC
*CCOc1ccc(/C=C2\CC/C(=C\c3ccc(OCCOP(=O)(O*)OC)c(OC)c3)C2=O)cc1OC
*CCOc1ccc(/C=C2\CC/C(=C\c3ccc(OCCOP(=O)(O*)OCC)c(OC)c3)C2=O)cc1OC
*CCOc1ccc(C(=O)Nc2cc(NC(=O)c3ccc(O*)cc3)cc(-c3nc4ccccc4[nH]3)c2)cc1
*CCOc1ccc(C(=O)OC(=O)c2ccc(O*)cc2)cc1
*CCOc1ccc(C2(c3ccc(OCCO[Si](O*)(c4ccccc4)c4ccccc4)cc3)c3ccccc3-c3ccccc32)cc1
*CCOc1ccc(OC(=O)c2ccc(C(=O)Oc3ccc(O*)cc3)c(OCCCCC)c2)cc1
*CCOc1ccc(S(=O)(=O)c2ccc(OCCOP(=O)(O*)Oc3ccc(Br)cc3)cc2)cc1
*CCOc1ccc(S(=O)(=O)c2ccc(OCCOP(=O)(O*)Oc3ccc(C)cc3)cc2)cc1
*CCOc1ccc(S(=O)(=O)c2ccc(OCCOP(=O)(O*)Oc3ccc(OC)cc3)cc2)cc1
*CCOc1ccc(S(=O)(=O)c2ccc(OCCOP(=O)(O*)Oc3ccc([N+](=O)[O-])cc3)cc2)cc1
*CCOc1ccc(S(=O)(=O)c2ccc(OCCOP(=O)(O*)Oc3ccccc3)cc2)cc1
*CCS*
*CCSCc1ccc(Cc2ccc(CSCCOC(=O)CCSCc3ccc(Cc4ccc(CSCCC(=O)O*)cc4)cc3)cc2)cc1
*CCSCc1ccc(Cc2ccc(CSCCOC(=O)CSCc3ccc(Cc4ccc(CSCC(=O)O*)cc4)cc3)cc2)cc1
*CCSS*
*CCSSCCO*
*CCSSCCOCO*
*CCSSSS*
*CCSSSSCCO*
*CC[Si](C)(C)O[Si]1(*)O[Si](C)(C)O[Si](C)(C)O1
*CCc1cc(C)c(*)cc1C
*CCc1cc(Cl)c(*)c(Cl)c1
*CCc1ccc(*)c(Br)c1
*CCc1ccc(*)c(C#N)c1
*CCc1ccc(*)c(C)c1
*CCc1ccc(*)c(CC)c1
*CCc1ccc(*)c(CCCC)c1
*CCc1ccc(*)c(Cl)c1
*CCc1ccc(*)c2ccccc12
*CCc1ccc(*)cc1
*CCc1ccc(*)o1
*CCc1ccc(*)s1
*CCc1ccc(CCNC(=O)CCCCCCCCC(=O)N*)cc1
*CCc1ccc(CCNC(=O)CCCCCCCCCC(=O)N*)cc1
*CCc1ccc(CCNC(=O)CCCCCCCCCCCCC(=O)N*)cc1
*CCc1ccc(CCNC(=O)CCCCCCCCCCCCCCCCC(=O)N*)cc1
*CCc1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(CCOC(=O)c4cccc(C(=O)O*)c4)cc3)c2)cc1
*CCc1cccc(*)c1
*CN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(Cc3nc5cc(-c6ccc7oc(*)nc7c6)ccc5o3)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(Cc3nc5ccc(Cc6ccc7nc(*)oc(=O)c7c6)cc5c(=O)o3)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(Cc3nnc(-c5ccc(-c6nnc(*)o6)c(Oc6ccccc6)c5)o3)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(Cc3nnc(-c5ccc(-c6nnc(*)o6)cc5)o3)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CN1C(=O)c2ccc(C(c3ccc4c(c3)C(=O)N(Cc3nnc(-c5ccc(Oc6ccc(-c7nnc(*)o7)cc6)cc5)o3)C4=O)(C(F)(F)F)C(F)(F)F)cc2C1=O
*CNC(=O)CCCCC(=O)NCC1COC(*)CO1
*CNC(=O)CCCCCCCCC(=O)NCC1COC(*)CO1
*CNC(=O)OC(c1ccco1)C(OC(=O)NCc1ccc(C(C)(C)c2ccc(*)o2)o1)c1ccco1
*CNC(=O)OCC(OC(=O)NCc1ccc(*)o1)c1ccco1
*CNC(=O)OCC(OC(=O)NCc1ccc(C(C)(C)c2ccc(*)o2)o1)c1ccco1
*CNC(=O)OCC(OC(=O)NCc1ccc(C(CCC)c2ccc(*)o2)o1)c1ccco1
*CNC(=O)OCCOC(=O)NCc1ccc(C(C)(C)c2ccc(*)o2)o1
*CNC(=O)OCc1ccc(COC(=O)NCc2ccc(C(C)(C)c3ccc(*)o3)o2)o1
*CNC(=O)OCc1ccc(COCc2ccc(COC(=O)NCc3ccc(C(C)(C)c4ccc(*)o4)o3)o2)o1
*CNC(=O)OCc1cocc1COC(=O)NCc1ccc(C(C)(C)c2ccc(*)o2)o1
*CO*
*COC(=O)NCCCCCCNC(=O)OCc1ccc(*)o1
*COC(=O)NCc1ccc(CNC(=O)OCc2ccc(*)o2)o1
*COC(=O)Nc1ccc(Cc2ccc(NC(=O)OCc3ccc(*)o3)cc2)cc1
*COC(=O)Nc1ccc(Cc2ccc(NC(=O)OCc3cocc3*)cc2)cc1
*COC(=O)OC1C(*)OC2OC(C)(C)OC21
*COC(=O)OCC1OC(C)(C)OC1*
*COC(=O)OCC1OC(OCC)(OCC)OC1*
*COC(=O)OCC1OC(OCC)OC1*
*COC(=O)c1ccc(/C(C#N)=C(\c2ccc(OC)cc2)N2CCCC(*)C2)cc1
*COC1OC(*)C(O)C(O)C1O
*COCOc1ccc(C(=O)OC(=O)c2ccc(O*)cc2)cc1
*COc1ccc(C(=C(Cl)Cl)c2ccc(O*)cc2)cc1
*COc1ccc(C(=O)OC(=O)c2ccc(O*)cc2)cc1
*COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1
*COc1ccc(C(C)(C)c2ccc(Oc3ccc(*)n3-c3ccc(C#N)cc3)cc2)cc1
*COc1ccc(C(C)(C)c2ccc(Oc3ccc(*)n3-c3ccc([N+](=O)[O-])cc3)cc2)cc1
*COc1ccc(C(c2ccc(O*)cc2)(C(F)(F)F)C(F)(F)F)cc1
*COc1ccc(C(c2ccc(OCC3(*)COC3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*C[Ge](C)(C)Cc1cocc1*
*C[Si](*)(C)C
*C[Si](*)(C)CCCCCC
*C[Si](*)(C)CCCN(CC)CC
*C[Si](*)(C)CCCOCCOCCOC
*C[Si](*)(C)CCCn1c2ccccc2c2ccccc21
*C[Si](*)(C)c1ccccc1
*C[Si](*)(CCC)CCC
*C[Si](*)(CCCC)CCCC
*C[Si](*)(CCCCC)CCCCC
*C[Si](*)(CCCCCC)CCCCCC
*C[Si](C)(C)[Si](C)(C)[Si](C)(C)[Si](*)(C)C
*Cc1c(C)c(C)c(CNC(=O)CCCC(=O)N*)c(C)c1C
*Cc1c(C)c(C)c(CNC(=O)CCCCCC(C)CC(=O)N*)c(C)c1C
*Cc1cc(C(C)(C)C)cc(*)c1O
*Cc1cc(C)c(CNC(=O)CCCCC(=O)N*)cc1C
*Cc1cc(C)c(CNC(=O)c2ccc(C(=O)N*)cc2)cc1C
*Cc1cc(C23CC4CC(CC(C4)C2)C3)cc(CN(*)C)c1O
*Cc1cc(C23CC4CC(CC(C4)C2)C3)cc(CN(*)c2ccccc2)c1O
*Cc1cc(CNC(=O)CCCCC(=O)N*)cc(C(C)(C)C)c1
*Cc1cc(CNC(=O)c2cccc(C(=O)N*)c2)cc(C(C)(C)C)c1
*Cc1cc(COC(=O)Nc2ccc(Cc3ccc(NC(=O)O*)cc3)cc2)cc(C(C)(C)C)c1
*Cc1cc2cc(C(=O)Nc3ccc(-c4ccc(NC(=O)c5cc6cc(*)c(OC(=O)COc7ccc8ccc(=O)oc8c7)cc6oc5=O)cc4)cc3)c(=O)oc2cc1OC(=O)COc1ccc2ccc(=O)oc2c1
*Cc1cc2cc(C(=O)Nc3ccc(C(c4ccc(NC(=O)c5cc6cc(*)c(OC(=O)COc7ccc8ccc(=O)oc8c7)cc6oc5=O)cc4)(C(F)(F)F)C(F)(F)F)cc3)c(=O)oc2cc1OC(=O)COc1ccc2ccc(=O)oc2c1
*Cc1cc2cc(C(=O)Nc3ccc(Cc4ccc(NC(=O)c5cc6cc(*)c(OC(=O)COc7ccc8ccc(=O)oc8c7)cc6oc5=O)cc4)cc3)c(=O)oc2cc1OC(=O)COc1ccc2ccc(=O)oc2c1
*Cc1cc2cc(C(=O)Nc3ccc(NC(=O)c4cc5cc(*)c(OC(=O)COc6ccc7ccc(=O)oc7c6)cc5oc4=O)cc3)c(=O)oc2cc1OC(=O)COc1ccc2ccc(=O)oc2c1
*Cc1cc2cc(C(=O)Nc3ccc(Oc4ccc(NC(=O)c5cc6cc(*)c(OC(=O)COc7ccc8ccc(=O)oc8c7)cc6oc5=O)cc4)cc3)c(=O)oc2cc1OC(=O)COc1ccc2ccc(=O)oc2c1
*Cc1cc2cc(C(=O)Nc3ccc(S(=O)(=O)c4ccc(NC(=O)c5cc6cc(*)c(OC(=O)COc7ccc8ccc(=O)oc8c7)cc6oc5=O)cc4)cc3)c(=O)oc2cc1OC(=O)COc1ccc2ccc(=O)oc2c1
*Cc1cc2cc(C(=O)Nc3cccc(NC(=O)c4cc5cc(*)c(OC(=O)COc6ccc7ccc(=O)oc7c6)cc5oc4=O)c3)c(=O)oc2cc1OC(=O)COc1ccc2ccc(=O)oc2c1
*Cc1ccc(C(C)(C)c2ccc(CN3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)o2)o1
*Cc1ccc(C(C)(C)c2ccc(CN3C(=O)c4ccc(C(c5ccc6c(c5)C(=O)N(*)C6=O)(C(F)(F)F)C(F)(F)F)cc4C3=O)o2)o1
*Cc1ccc(C(C)(C)c2ccc(CN3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)o2)o1
*Cc1ccc(C(C)(C)c2ccc(Cn3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)o2)o1
*Cc1ccc(C(C)(CC)c2ccc(Cn3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)o2)o1
*Cc1ccc(C(C)(CCC)c2ccc(Cn3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)o2)o1
*Cc1ccc(C(C)c2ccc(CN3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)o2)o1
*Cc1ccc(C(C)c2ccc(Cn3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)o2)o1
*Cc1ccc(CNC(=O)CC(C)(C)CC(C)C(=O)N*)cc1
*Cc1ccc(CNC(=O)CCCCCCCCC(=O)N*)cc1
*Cc1ccc(CNC(=O)CCCCCCCCCC(=O)N*)cc1
*Cc1ccc(CNC(=O)CCCCCCCCCCC(=O)N*)cc1
*Cc1ccc(CNC(=O)CCCCCCCCCCCC(=O)N*)cc1
*Cc1ccc(CNC(=O)CCCCCCCCCCCCCC(=O)N*)cc1
*Cc1ccc(CNC(=O)CCCCCCCCCCCCCCCCC(=O)N*)cc1
*Cc1ccc(CNC(=O)c2cc(C(=O)N*)cc(C(C)(C)C)c2)cc1
*Cc1ccc(CO*)cc1
*Cc1ccc(COC(=O)Cc2ccc(C(=O)O*)cc2)cc1
*Cc1ccc(COC(=O)c2cccc(C(=O)O*)c2)cc1
*Cc1ccc(COP(=O)(/N=N/c2ccc(-c3ccc(/N=N/P(=O)(O*)OC)cc3)cc2)OC)cc1
*Cc1ccc(COP(=O)(/N=N/c2ccc(C(=O)c3ccc(/N=N/P(=O)(O*)OC)cc3)cc2)OC)cc1
*Cc1ccc(COP(=O)(/N=N/c2ccc(Oc3ccc(/N=N/P(=O)(O*)OC)cc3)cc2)OC)cc1
*Cc1ccc(CSS*)cc1
*Cc1ccc(CSSS*)cc1
*Cc1ccc(CSSSS*)cc1
*Cc1ccc(C[n+]2ccc(-c3cc[n+](*)cc3)cc2)cc1
*Cc1ccc2nc(-c3cc(-c4nc5ccc(*)cc5c(=O)o4)cc(N4C(=O)c5ccccc5C4=O)c3)oc(=O)c2c1
*Cc1ccc2oc(=O)c(C(=O)Nc3ccc(C(c4ccc(NC(=O)c5cc6cc(*)ccc6oc5=O)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc2c1
*Cc1ccc2oc(=O)c(C(=O)Nc3ccc(Oc4ccc(C(c5ccc(Oc6ccc(NC(=O)c7cc8cc(*)ccc8oc7=O)cc6)cc5)(C(F)(F)F)C(F)(F)F)cc4)cc3)cc2c1
*Cc1ccc2oc(=O)c(C(=O)Nc3ccc(Oc4ccc(NC(=O)c5cc6cc(*)ccc6oc5=O)cc4)cc3)cc2c1
*Cc1ccc2oc(=O)c(C(=O)Nc3ccc(Oc4ccc(Oc5ccc(NC(=O)c6cc7cc(*)ccc7oc6=O)cc5)cc4)cc3)cc2c1
*Cc1cccc(CNC(=O)/C=C(\C)C(=O)N*)c1
*Cc1cccc(CNC(=O)C2=C(C(=O)N*)C3C=CC2C3)c1
*Cc1cccc(CNC(=O)CCC2(CCC(=O)N*)c3ccccc3-c3ccccc32)c1
*Cc1cccc(CNC(=O)CCCCC(=O)N*)c1
*Cc1cccc(CNC(=O)c2cc(C(=O)N*)cc(C(C)(C)C)c2)c1
*Cc1cccc(CNC(=O)c2cccc(C(=O)N*)c2)c1
*Cc1cccc(CNC(=O)c2ccccc2-c2ccccc2C(=O)N*)c1
*Cc1cccc(COC(=O)Nc2ccc(Cc3ccc(NC(=O)O*)cc3)cc2)c1
*Cc1cccc(C[n+]2ccc(-c3cc[n+](*)cc3)cc2)c1
*Cc1ccccc1CN(CC)c1ccc(/C=C(\C#N)C(=O)Nc2cccc(NC(=O)/C(C#N)=C/c3ccc(N(*)CC)cc3)c2)cc1
*Cc1ccccc1CN(CC)c1ccc(/C=C/c2ccc(/C=C(\C#N)C(=O)NC3CCCCC3NC(=O)/C(C#N)=C/c3ccc(/C=C/c4ccc(N(*)CC)cc4)cc3)cc2)cc1
*Cc1ccccc1CSS*
*Cc1ccccc1C[Si](*)(C)C
*Cc1ccccc1C[n+]1ccc(-c2cc[n+](*)cc2)cc1
*Cc1ccccc1[Si](*)(C)C
*Cc1ccccc1[Si](*)(C)c1ccccc1
*Cc1ccccc1[Si](*)(c1ccccc1)c1ccccc1
*N(C)[Si](*)(C)C
*N(C)[Si](*)(C)C=C
*N(C)[Si](C)(C)N(C)[Si](*)(C)CC
*N(C)[Si](C)(C=C)N(C)[Si](*)(C)C
*N(CCC)[Si](C)(C)C[Si](*)(C)C
*N/C(C#N)=C(\C#N)NC(=O)c1ccc(C(*)=O)cc1
*N1CCOC1(*)CC
*N=P(*)(C)C
*N=P(*)(C)CCCC
*N=P(*)(C)CCCCCC
*N=P(*)(C)c1ccccc1
*N=P(*)(CC)c1ccccc1
*N=P(*)(CC1COCCOCCOCCO1)OCC1COCCOCCOCCO1
*N=P(*)(CCC)CCC
*N=P(*)(Cl)Cl
*N=P(*)(Cl)Oc1ccc(C)cc1
*N=P(*)(N(C)C)N(C)C
*N=P(*)(N1CCCCC1)N1CCCCC1
*N=P(*)(NC(C)C(=O)OCC)NC(C)C(=O)OCC
*N=P(*)(NCC)NCC
*N=P(*)(NCCC)NCCC
*N=P(*)(NCCCC)NCCCC
*N=P(*)(Nc1ccccc1)Nc1ccccc1
*N=P(*)(OC)OC
*N=P(*)(OC/C=C/c1ccccc1)OC/C=C/c1ccccc1
*N=P(*)(OC1COCCOCCOCCOCCOC1)OC1COCCOCCOCCOCCOC1
*N=P(*)(OCC(COC)OC)OCC(COC)OC
*N=P(*)(OCC(COCCOC(C)C)OCCOC(C)C)OCC(COCCOC(C)C)OCCOC(C)C
*N=P(*)(OCC(COCCOC)OCCOC)OCC(COCCOC)OCCOC
*N=P(*)(OCC(COCCOCCCC)OCCOCCCC)OCC(COCCOCCCC)OCCOCCCC
*N=P(*)(OCC(COCCOCCOC)OCCOCCOC)OCC(COCCOCCOC)OCCOCCOC
*N=P(*)(OCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)OCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*N=P(*)(OCC(F)(F)C(F)(F)F)OCC(F)(F)C(F)(F)F
*N=P(*)(OCC(F)(F)F)OCC(F)(F)F
*N=P(*)(OCC)OCC
*N=P(*)(OCC1COCCOCCOCCOCCO1)OCC1COCCOCCOCCOCCO1
*N=P(*)(OCC1COCCOCCOCCOCCOCCO1)OCC1COCCOCCOCCOCCOCCO1
*N=P(*)(OCCC)OCCC
*N=P(*)(OCCC)OCCCCC
*N=P(*)(OCCCC)OCCCC
*N=P(*)(OCCCCC)OCCCCC
*N=P(*)(OCCCCCC)OCCCCCC
*N=P(*)(OCCCCCCC)OCCCCCCC
*N=P(*)(OCCCCCCCC)OCCCCCCCC
*N=P(*)(OCCCCCCCCC)OCCCCCCCCC
*N=P(*)(OCCCCCCCCCC)OCCCCCCCCCC
*N=P(*)(OCCCCCCOC1COCCOCCOCCOCCOC1)OCCCCCCOC1COCCOCCOCCOCCOC1
*N=P(*)(OCCCOC1COCCOCCOCCOCCOC1)OCCCOC1COCCOCCOCCOCCOC1
*N=P(*)(OCCCc1ccccc1)OCCCc1ccccc1
*N=P(*)(OCCOC)OCCOC
*N=P(*)(OCCOCCOC)OCCOCCOC
*N=P(*)(OCCOCCOCC)OCCOCCOCC
*N=P(*)(OCCOCCOCCCC)OCCOCCOCCCC
*N=P(*)(OCCOCCOCCOC)OCCOCCOCCOC
*N=P(*)(OCCOCCOCCOCCOC)OCCOCCOCCOCCOC
*N=P(*)(OCCOCCOCCOCCOCCCCCCCCCCCC)OCCOCCOCCOCCOCCCCCCCCCCCC
*N=P(*)(OCCOCCOCCOCCOCCOC)OCCOCCOCCOCCOCCOC
*N=P(*)(OCCOCCOCCOCCOCCOCCOC)OCCOCCOCCOCCOCCOCCOC
*N=P(*)(OCCOCCOCCOCCOCCOCCOCCOCCOC)OCCOCCOCCOCCOCCOCCOCCOCCOC
*N=P(*)(OCCOCCOCCOP1(OCCOCCOC)=NP(OCCOCCOC)(OCCOCCOC)=NP(OCCOCCOC)(OCCOCCOC)=N1)OCCOCCOCCOP1(OCCOCCOC)=NP(OCCOCCOC)(OCCOCCOC)=NP(OCCOCCOC)(OCCOCCOC)=N1
*N=P(*)(OCCOCCOc1ccc(CCCCCCCC)cc1)OCCOCCOc1ccc(CCCCCCCC)cc1
*N=P(*)(OCCOc1ccc(-c2ccc(Br)cc2)cc1)OCCOc1ccc(-c2ccc(Br)cc2)cc1
*N=P(*)(OCCOc1ccc(-c2ccc(I)cc2)cc1)OCCOc1ccc(-c2ccc(I)cc2)cc1
*N=P(*)(OCCOc1ccc(-c2ccccc2)cc1)OCCOc1ccc(-c2ccccc2)cc1
*N=P(*)(OCCOc1ccc(-c2ccccc2)cc1I)OCCOc1ccc(-c2ccccc2)cc1I
*N=P(*)(OCCOc1ccc(/N=N/c2ccc(CCCC)cc2)cc1)OCCOc1ccc(/N=N/c2ccc(CCCC)cc2)cc1
*N=P(*)(OCCOc1ccc2cc(Br)ccc2c1)OCCOc1ccc2cc(Br)ccc2c1
*N=P(*)(OCCOc1ccc2cc(I)ccc2c1)OCCOc1ccc2cc(I)ccc2c1
*N=P(*)(OCCOc1ccc2ccccc2c1)OCCOc1ccc2ccccc2c1
*N=P(*)(OCCc1ccccc1)OCCc1ccccc1
*N=P(*)(OCc1ccc(-c2ccccc2)cc1)OCc1ccc(-c2ccccc2)cc1
*N=P(*)(OCc1ccc(Br)cc1)OCc1ccc(Br)cc1
*N=P(*)(OCc1ccccc1)OCc1ccccc1
*N=P(*)(Oc1ccc(-c2ccccc2)cc1)Oc1ccc(-c2ccccc2)cc1
*N=P(*)(Oc1ccc(Br)cc1)Oc1ccc(Br)cc1
*N=P(*)(Oc1ccc(C(=O)OC)cc1)Oc1ccc(C(=O)OC)cc1
*N=P(*)(Oc1ccc(C(C)(C)C)cc1)Oc1ccc(C(C)(C)C)cc1
*N=P(*)(Oc1ccc(C(C)C)cc1)Oc1ccc(C(C)C)cc1
*N=P(*)(Oc1ccc(C(C)CC)cc1)Oc1ccc(C(C)CC)cc1
*N=P(*)(Oc1ccc(C)c(C)c1)Oc1ccc(C)c(C)c1
*N=P(*)(Oc1ccc(C)cc1)Oc1ccc(C)cc1
*N=P(*)(Oc1ccc(C)cc1)n1cncn1
*N=P(*)(Oc1ccc(CC)cc1)Oc1ccc(CC)cc1
*N=P(*)(Oc1ccc(Cc2ccccc2)cc1)Oc1ccc(Cc2ccccc2)cc1
*N=P(*)(Oc1ccc(Cl)cc1)Oc1ccc(Cl)cc1
*N=P(*)(Oc1ccc(Cl)cc1Cl)Oc1ccc(Cl)cc1Cl
*N=P(*)(Oc1ccc(F)cc1)Oc1ccc(F)cc1
*N=P(*)(Oc1ccc(I)cc1)Oc1ccc(I)cc1
*N=P(*)(Oc1ccc(OC)cc1)Oc1ccc(OC)cc1
*N=P(*)(Oc1ccc2cc(I)ccc2c1)Oc1ccc2cc(I)ccc2c1
*N=P(*)(Oc1ccc2ccccc2c1)Oc1ccc2ccccc2c1
*N=P(*)(Oc1cccc(Br)c1)Oc1cccc(Br)c1
*N=P(*)(Oc1cccc(C(F)(F)F)c1)Oc1cccc(C(F)(F)F)c1
*N=P(*)(Oc1cccc(C)c1)Oc1cccc(C)c1
*N=P(*)(Oc1cccc(Cl)c1)Oc1cccc(Cl)c1
*N=P(*)(Oc1cccc(F)c1)Oc1cccc(F)c1
*N=P(*)(Oc1ccccc1)Oc1ccccc1
*N=P(Cl)(Cl)N=P(Cl)(Cl)/N=S(/*)Cl
*N=P(Cl)(Cl)N=P(Cl)(Cl)N=S(*)(=O)F
*N=P(Cl)(N=P(/N=S(/*)Oc1cccc(-c2ccccc2)c1)(Oc1cccc(-c2ccccc2)c1)Oc1cccc(-c2ccccc2)c1)Oc1cccc(-c2ccccc2)c1
*N=P(Cl)(N=P(Cl)(/N=S(/*)Oc1ccccc1-c1ccccc1)Oc1ccccc1-c1ccccc1)Oc1ccccc1-c1ccccc1
*N=P(N=P(/N=S(/*)Oc1ccc(-c2ccccc2)cc1)(Oc1ccc(-c2ccccc2)cc1)Oc1ccc(-c2ccccc2)cc1)(Oc1ccc(-c2ccccc2)cc1)Oc1ccc(-c2ccccc2)cc1
*N=P(N=P(/N=S(/*)Oc1ccc(C(C)(C)C)cc1)(Oc1ccc(-c2ccccc2)cc1)Oc1ccc(-c2ccccc2)cc1)(Oc1ccc(-c2ccccc2)cc1)Oc1ccc(-c2ccccc2)cc1
*N=P(N=P(N=S(*)(=O)NC)(NC)NC)(NC)NC
*N=P(N=P(N=S(*)(=O)NCC)(NCC)NCC)(NCC)NCC
*N=P(N=P(N=S(*)(=O)NCC=C)(NCC=C)NCC=C)(NCC=C)NCC=C
*N=P(N=P(N=S(*)(=O)NCCC)(NCCC)NCCC)(NCCC)NCCC
*N=P(N=P(N=S(*)(=O)NCCCC)(NCCCC)NCCCC)(NCCCC)NCCCC
*N=P(N=P(N=S(*)(=O)NCCCCCC)(NCCCCCC)NCCCCCC)(NCCCCCC)NCCCCCC
*N=P(N=P(N=S(*)(=O)Nc1ccccc1)(Nc1ccccc1)Nc1ccccc1)(Nc1ccccc1)Nc1ccccc1
*N=P1(*)Oc2ccc3ccccc3c2-c2c(ccc3ccccc23)O1
*N=P1(*)Oc2ccccc2-c2ccccc2O1
*NC(=O)NCCCCCCNC(=O)Nc1cccc(*)n1
*NC(=O)Nc1ccc(Cc2ccc(NC(=O)Nc3cccc(*)n3)cc2)cc1
*NC(C)(CC)C(*)=O
*NC(C)CC(*)=O
*NC(C)CCC(C)(C)CCCNC(=O)c1ccc(C(*)=O)cc1
*NC(CCC(=O)Nc1ccc(N(c2ccccc2)c2ccccc2)cc1)C(*)=O
*NC(CCC(=O)OC)C(*)=O
*NC(CCC(=O)OCC)C(*)=O
*NC(CCC(=O)OCCC)C(*)=O
*NC(CCC(=O)OCCCC)C(*)=O
*NC(CCC(=O)OCCCCC)C(*)=O
*NC(CCC(=O)OCCCCCC)C(*)=O
*NC(CCC(=O)OCCCCCCCC)C(*)=O
*NC(CCC(=O)OCCCCCCCCCC)C(*)=O
*NC(CCC(=O)OCc1ccc(C(F)(F)F)cc1)C(*)=O
*NC(CCC(=O)OCc1ccc(F)cc1)C(*)=O
*NC(CCC(=O)OCc1ccc([N+](=O)[O-])cc1)C(*)=O
*NC(CCC(=O)OCc1ccccc1)C(*)=O
*NC1CC(C)(C)CC(C)(CNC(=O)CCCCC(*)=O)C1
*NC1CC(C)(C)CC(C)(CNC(=O)c2cc(NC(=O)C(C(C)CC)N3C(=O)c4ccccc4C3=O)cc(C(*)=O)c2)C1
*NC1CCC(CC(C)(C)NC(=O)CCCCC(*)=O)CC1
*NC1CCC(CC2CCC(NC(=O)CCCCC(*)=O)CC2)CC1
*NC1CCC(CC2CCC(NC(=O)CCCCCCCCC(*)=O)CC2)CC1
*NC1CCC(CC2CCC(NC(=O)CCCCCCCCCCC(*)=O)CC2)CC1
*NC1CCC(CC2CCC(NC(=O)CCCCCCCCCCCCC(*)=O)CC2)CC1
*NC1CCC(CCC2CCC(NC(=O)CCCCCCCCC(*)=O)CC2)CC1
*NC1CCC(CCC2CCC(NC(=O)CCCCCCCCCCC(*)=O)CC2)CC1
*NCCCCCCNc1nc(*)nc(-c2ccccc2)n1
*NNC(=O)/C=C/C(=O)NCCCCCCNC(=O)/C=C/C(*)=O
*NNC(=O)/C=C/C(=O)Nc1cccc(/C=C2\CC/C(=C\c3cccc(NC(=O)/C=C/C(*)=O)c3)C2=O)c1
*NNC(=O)/C=C/C(=O)Nc1cccc(/C=C2\CCC/C(=C\c3cccc(NC(=O)/C=C/C(*)=O)c3)C2=O)c1
*NNC(=O)c1ccc(C(*)=O)cc1OCC
*NNC(=O)c1ccc(C(*)=O)cc1OCCCCC
*NNC(=O)c1ccc(C(*)=O)cc1OCCCCCCCC
*NNC(=O)c1ccc(C(*)=O)cc1OCCCCCCCCCC
*NNC(=O)c1ccc(C(=O)NNC(=O)c2ccc([Si](c3ccccc3)(c3ccccc3)c3ccc(C(*)=O)cc3)cc2)c2ccccc12
*NNC(=O)c1ccccc1C(=O)NCCCCCCNC(=O)c1ccccc1C(*)=O
*NNC(=O)c1ccccc1C(=O)Nc1cccc(/C=C2\CC/C(=C\c3cccc(NC(=O)c4ccccc4C(*)=O)c3)C2=O)c1
*NNC(=O)c1ccccc1C(=O)Nc1cccc(/C=C2\CCC/C(=C\c3cccc(NC(=O)c4ccccc4C(*)=O)c3)C2=O)c1
*Nc1c(-c2ccccc2)cc(-c2ccc(-c3cc(-c4ccccc4)c(NC(=O)c4ccc(C(*)=O)cc4)c(-c4ccccc4)c3)cc2)cc1-c1ccccc1
*Nc1c(C)c(C)c(C(=O)c2ccc(NC(=O)c3ccc(C(*)=O)cc3)cc2)c(C)c1C
*Nc1cc(NC(=O)C(*)=O)ccc1C
*Nc1cc(NC(=O)CCCCC(*)=O)cc(-c2nnc(-c3ccccn3)o2)c1
*Nc1cc(NC(=O)CCCCCCCCC(*)=O)cc(-c2nnc(-c3ccccn3)o2)c1
*Nc1cc(NC(=O)NC2CCC(CC3CCC(NC(*)=O)CC3)CC2)cc(C(=O)Nc2ccc3c(c2)C(=O)c2ccccc2C3=O)c1
*Nc1cc(NC(=O)NC2CCC(CC3CCC(NC(*)=O)CC3)CC2)cc(C(=O)Nc2cccc3c2C(=O)c2ccccc2C3=O)c1
*Nc1cc(NC(=O)Nc2ccc(CCc3ccccc3NC(*)=O)cc2)ccc1C
*Nc1cc(NC(=O)Nc2ccc(Cc3ccc(NC(*)=O)cc3)cc2)cc(C(=O)Nc2ccc3c(c2)C(=O)c2ccccc2C3=O)c1
*Nc1cc(NC(=O)Nc2ccc(Cc3ccc(NC(*)=O)cc3)cc2)cc(C(=O)Nc2cccc3c2C(=O)c2ccccc2C3=O)c1
*Nc1cc(NC(=O)Nc2ccc(NC(*)=O)cc2)cc(C(=O)Nc2ccc3c(c2)C(=O)c2ccccc2C3=O)c1
*Nc1cc(NC(=O)Nc2ccc(NC(*)=O)cc2)cc(C(=O)Nc2cccc3c2C(=O)c2ccccc2C3=O)c1
*Nc1cc(NC(=O)c2cc(NC(=O)C(C(C)CC)N3C(=O)c4ccccc4C3=O)cc(C(*)=O)c2)ccc1C
*Nc1cc(NC(=O)c2cc(OCCCN(C)c3ccc(/N=N/c4ccc(S(C)(=O)=O)cc4)cc3)cc(C(*)=O)c2)cc(C(=O)OCCCN(C)c2ccc(/N=N/c3ccc(S(C)(=O)=O)cc3)cc2)c1
*Nc1cc(NC(=O)c2cc(OCCCS(=O)(=O)c3ccc(/N=N/c4ccc(N(C)C)cc4)cc3)cc(C(*)=O)c2)cc(C(=O)OCCCN(C)c2ccc(/N=N/c3ccc(S(C)(=O)=O)cc3)cc2)c1
*Nc1cc(NC(=O)c2cc(OCCN(C)c3ccc(C#N)cc3)cc(C(*)=O)c2)cc(C(=O)OCCN(C)c2ccc(C#N)cc2)c1
*Nc1cc(NC(=O)c2cc(OCCN(C)c3ccc(C#N)cc3)cc(C(*)=O)c2)cc(C(=O)OCCN(C)c2ccc(S(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc2)c1
*Nc1cc(NC(=O)c2cc(OCCN(C)c3ccc(S(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc3)cc(C(*)=O)c2)cc(C(=O)OCCN(C)c2ccc(C#N)cc2)c1
*Nc1cc(NC(=O)c2cc(OCCN(C)c3ccc([N+](=O)[O-])cc3)cc(C(*)=O)c2)cc(C(=O)OC)c1
*Nc1cc(NC(=O)c2cc(OCCN(C)c3ccc([N+](=O)[O-])cc3)cc(C(*)=O)c2)cc(C(=O)OC2CC(C)CCC2C(C)C)c1
*Nc1cc(NC(=O)c2ccc(-c3ccc(C(*)=O)cc3)cc2)cc(-c2nnc(-c3ccccn3)o2)c1
*Nc1cc(NC(=O)c2ccc(-c3ccc(C(*)=O)cc3)cc2)cc(C(=O)OCCOc2ccc(/C=C/C(=O)c3ccccc3)cc2)c1
*Nc1cc(NC(=O)c2ccc(C(*)=O)cc2)cc(-c2nc3ccccc3[nH]2)c1
*Nc1cc(NC(=O)c2ccc(C(*)=O)cc2)cc(-c2nc3ccccc3oc2=O)c1
*Nc1cc(NC(=O)c2ccc(C(*)=O)cc2)cc(-c2nnc(-c3ccccn3)o2)c1
*Nc1cc(NC(=O)c2ccc(C(*)=O)cc2)cc(C(=O)OC(c2ccccc2)c2ccccc2)c1
*Nc1cc(NC(=O)c2ccc(C(*)=O)cc2)cc(C(=O)OCCOC(=O)/C=C/c2ccc(N(C)C)cc2)c1
*Nc1cc(NC(=O)c2ccc(C(*)=O)cc2)cc(C(=O)OCCOc2ccc(/C=C/C(=O)c3ccccc3)cc2)c1
*Nc1cc(NC(=O)c2ccc(C(*)=O)cc2)cc(C(=O)Oc2ccc(-c3ccccc3)cc2)c1
*Nc1cc(NC(=O)c2ccc(C(*)=O)cc2)cc(C(=O)Oc2cccc3ccccc23)c1
*Nc1cc(NC(=O)c2ccc(C(*)=O)cc2)cc(C(=O)Oc2ccccc2)c1
*Nc1cc(NC(=O)c2ccc3cc(C(*)=O)ccc3c2)cc(C(=O)OCCOc2ccc(/C=C/C(=O)c3ccccc3)cc2)c1
*Nc1cc(NC(=O)c2cccc(C(*)=O)c2)cc(-c2nnc(-c3ccccn3)o2)c1
*Nc1cc(NC(=O)c2cccc(C(*)=O)c2)cc(C(=O)OCCOC(=O)/C=C/c2ccc(N(C)C)cc2)c1
*Nc1cc(NC(=O)c2cccc(C(*)=O)c2)cc(C(=O)OCCOc2ccc(/C=C/C(=O)c3ccccc3)cc2)c1
*Nc1cc(NC(=O)c2cccc(C(*)=O)c2)cc(C(=O)Oc2ccc(Oc3ccc(C#N)c(C#N)c3)cc2)c1
*Nc1cc(NC(=O)c2ccccc2C(*)=O)cc(-c2nnc(-c3ccccn3)o2)c1
*Nc1cc([N+](=O)[O-])c(NC(=O)CCCCC(*)=O)cc1/C=C/c1ccc(N(C)C)cc1
*Nc1cc([N+](=O)[O-])c(NC(=O)CCCCCCC(*)=O)cc1/C=C/c1ccc(N(C)C)cc1
*Nc1cc([N+](=O)[O-])c(NC(=O)CCCCCCCCC(*)=O)cc1/C=C/c1ccc(N(C)C)cc1
*Nc1cc([N+](=O)[O-])c(NC(=O)CCCCCCCCCCC(*)=O)cc1/C=C/c1ccc(N(C)C)cc1
*Nc1cc([N+](=O)[O-])c(NC(=O)CCCCCCCCCCCCC(*)=O)cc1/C=C/c1ccc(N(C)C)cc1
*Nc1ccc(*)cc1
*Nc1ccc(*)cc1C
*Nc1ccc(*)cc1OC
*Nc1ccc(*)cc1OCCCCCCCCCC
*Nc1ccc(*)cc1OCCCCCCCCCCCC
*Nc1ccc(*)cc1OCCCCCCCCCCCCCCCC
*Nc1ccc(*)cc1OCCCCCCCCCCOc1ccc(C2CCC(CCCCC)CC2)cc1
*Nc1ccc(-c2c(-c3ccc(-c4ccccc4)cc3)cc(-c3ccc(-c4cc(-c5ccc(-c6ccccc6)cc5)c(-c5ccc(NC(=O)c6ccc(C(*)=O)cc6)cc5)c(-c5ccc(-c6ccccc6)cc5)c4)cc3)cc2-c2ccc(-c3ccccc3)cc2)cc1
*Nc1ccc(-c2c(-c3ccccc3)cc(-c3ccc(-c4cc(-c5ccccc5)c(-c5ccc(NC(=O)c6ccc(C(*)=O)cc6)cc5)c(-c5ccccc5)c4)cc3)cc2-c2ccccc2)cc1
*Nc1ccc(-c2ccc(NC(=O)Cc3cc(C)c(CC(*)=O)cc3C)cc2)cc1
*Nc1ccc(-c2ccc(NC(=O)Cc3cc(CC(*)=O)c(C)cc3C)cc2)cc1
*Nc1ccc(-c2ccc(NC(=O)c3cc(C(*)=O)cc(S(=O)(=O)c4ccccc4)c3)cc2)cc1
*Nc1ccc(-c2ccc(NC(=O)c3cc(NC(=O)c4ccc(NC(=O)C(C)N5C(=O)c6ccccc6C5=O)cc4)cc(C(*)=O)c3)cc2)cc1
*Nc1ccc(-c2ccc(NC(=O)c3cc(NC(=O)c4ccncc4)cc(C(*)=O)c3)cc2)cc1
*Nc1ccc(-c2ccc(NC(=O)c3ccc(C(*)=O)cc3)cc2)cc1
*Nc1ccc(-c2ccc(NC(=O)c3ccc(NC(=O)c4ccc([Si](C)(C)c5ccc(C(=O)Nc6ccc(C(*)=O)cc6)cc5)cc4)cc3)cc2)cc1
*Nc1ccc(-c2ccc(NC(=O)c3cccc(C(*)=O)c3)c(O)c2)cc1O
*Nc1ccc(-c2ccc(NC(=O)c3cccc(C(*)=O)c3)c(OC)c2)cc1OC
*Nc1ccc(-c2ccc(NC(=O)c3cccc(C(*)=O)c3)c(S)c2)cc1S
*Nc1ccc(-c2ccc(NC(=O)c3cccc(C(*)=O)c3)cc2)cc1
*Nc1ccc(-c2ccc(Nc3ccc4c(c3)c3cc(*)ccc3n4CC(CC)CCCC)cc2)cc1
*Nc1ccc(/C=C/c2ccc(NC(=C(C#N)C#N)c3ccc(C(*)=C(C#N)C#N)cc3)cc2)cc1
*Nc1ccc(Br)cc1-c1ccc(NC(=O)c2ccc(C(*)=O)cc2)cc1
*Nc1ccc(Br)cc1-c1ccc(NC(=O)c2cccc(C(*)=O)c2)cc1
*Nc1ccc(C(=O)Nc2ccc(Cc3ccc(NC(=O)c4ccc(NC(=O)c5ccc([Si](C)(C)c6ccc(C(*)=O)cc6)cc5)cc4)cc3)cc2)cc1
*Nc1ccc(C(=O)c2c(C)c(C)c(C(=O)c3ccc(NC(=O)c4ccc(C(*)=O)cc4)cc3)c(C)c2C)cc1
*Nc1ccc(C(=O)c2ccc(C(=O)c3ccc(Nc4ccc5c(c4)c4cc(*)ccc4n5CC(CC)CCCC)cc3)cc2)cc1
*Nc1ccc(C(=O)c2ccc(NC(=O)c3cc(C(*)=O)cc(C(C)(C)C)c3)cc2)cc1
*Nc1ccc(C(=O)c2ccc(NC(=O)c3ccc(C(*)=O)cc3)cc2)cc1
*Nc1ccc(C(=O)c2ccc(NC(=O)c3cccc(C(*)=O)c3)cc2)cc1
*Nc1ccc(C(=O)c2ccc(Nc3ccc4c(c3)c3cc(*)ccc3n4CC(CC)CCCC)cc2)cc1
*Nc1ccc(C(C)(C)c2ccc(C(C)(C)c3ccc(Nc4ccc(C(=O)c5ccc(C(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Nc1ccc(C(C)(C)c2ccc(C(C)(C)c3ccc(Nc4ccc(C(O)(c5ccccc5)c5ccc(C(O)(c6ccccc6)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Nc1ccc(C(C)(C)c2ccc(NC(=O)c3cc(C(*)=O)cc(N4C(=O)C5C6CCC(C6)C5C4=O)c3)cc2)cc1
*Nc1ccc(C(C)(C)c2ccc(NC(=O)c3cc(C(*)=O)cc(N4C(=O)C5CC=CCC5C4=O)c3)cc2)cc1
*Nc1ccc(C(C)(C)c2ccc(NC(=O)c3cc(C(*)=O)cc(N4C(=O)C=CC4=O)c3)cc2)cc1
*Nc1ccc(C(C)(C)c2ccc(NC(=O)c3cc(C(*)=O)cc(S(=O)(=O)c4ccccc4)c3)cc2)cc1
*Nc1ccc(C(C)(C)c2ccc(NC(=O)c3cc(NC(=O)c4ccccc4)cc(C(*)=O)c3)cc2)cc1
*Nc1ccc(C(C)(C)c2ccc(NC(=O)c3cccc(C(*)=O)c3)cc2)cc1
*Nc1ccc(C(C)(C)c2cccc(C(C)(C)c3ccc(NC(=C(C#N)C#N)c4ccc(C(*)=C(C#N)C#N)cc4)cc3)c2)cc1
*Nc1ccc(C(C)(C)c2cccc(C(C)(C)c3ccc(Nc4ccc(C(=O)c5ccc(C(=O)c6ccc(*)cc6)cc5)cc4)cc3)c2)cc1
*Nc1ccc(C(c2ccc(NC(=O)c3cc(C(*)=O)cc(C(C)(C)C)c3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Nc1ccc(C(c2ccc(NC(=O)c3cc(C(*)=O)cc(C(C)(C)C)c3)cc2)(P2(=O)Oc3ccccc3-c3ccccc32)P2(=O)Oc3ccccc3-c3ccccc32)cc1
*Nc1ccc(C(c2ccc(NC(=O)c3cc(NC(=O)C45CC6CC(CC(C6)C4)C5)cc(C(*)=O)c3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Nc1ccc(C(c2ccc(NC(=O)c3cc(NC(=O)CCCCCN4C(=O)c5ccccc5C4=O)cc(C(*)=O)c3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Nc1ccc(C(c2ccc(NC(=O)c3cc(NC(=O)CCCN4C(=O)c5ccccc5C4=O)cc(C(*)=O)c3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Nc1ccc(C(c2ccc(NC(=O)c3ccc(-c4ccc(C(*)=O)c(C)c4)cc3C)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Nc1ccc(C(c2ccc(NC(=O)c3ccc(-c4ccc(C(*)=O)cc4)cc3)cc2)(P2(=O)Oc3ccccc3-c3ccccc32)P2(=O)Oc3ccccc3-c3ccccc32)cc1
*Nc1ccc(C(c2ccc(NC(=O)c3ccc(C(*)=O)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Nc1ccc(C(c2ccc(NC(=O)c3ccc(C(*)=O)cc3)cc2)(P2(=O)Oc3ccccc3-c3ccccc32)P2(=O)Oc3ccccc3-c3ccccc32)cc1
*Nc1ccc(C(c2ccc(NC(=O)c3ccc(C(c4ccc(C(*)=O)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc2)(P2(=O)Oc3ccccc3-c3ccccc32)P2(=O)Oc3ccccc3-c3ccccc32)cc1
*Nc1ccc(C(c2ccc(NC(=O)c3ccc4cc(C(*)=O)ccc4c3)cc2)(P2(=O)Oc3ccccc3-c3ccccc32)P2(=O)Oc3ccccc3-c3ccccc32)cc1
*Nc1ccc(C(c2ccc(NC(=O)c3cccc(C(*)=O)c3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Nc1ccc(C(c2ccc(NC(=O)c3cccc(C(*)=O)c3)cc2)(P2(=O)Oc3ccccc3-c3ccccc32)P2(=O)Oc3ccccc3-c3ccccc32)cc1
*Nc1ccc(C2(c3ccc(NC(=O)c4cc(C(*)=O)cc(C(C)(C)C)c4)cc3)c3ccccc3-c3ccccc32)cc1
*Nc1ccc(C2(c3ccc(NC(=O)c4cc(NC(=O)C56CC7CC(CC(C7)C5)C6)cc(C(*)=O)c4)cc3)c3ccccc3-c3ccccc32)cc1
*Nc1ccc(C2(c3ccc(NC(=O)c4ccc(-c5ccc(C(*)=O)c(C)c5)cc4C)cc3)c3ccccc3-c3ccccc32)cc1
*Nc1ccc(C2(c3ccc(NC(=O)c4ccc(C(*)=O)cc4)cc3)c3ccccc3-c3ccccc32)cc1
*Nc1ccc(C2(c3ccc(NC(=O)c4cccc(C(*)=O)c4)cc3)c3ccccc3-c3ccccc32)cc1
*Nc1ccc(CC(C)(C)NC(=O)CCC(*)=O)cc1
*Nc1ccc(CC(C)(C)NC(=O)CCCCC(*)=O)cc1
*Nc1ccc(CC(C)(C)NC(=O)CCCCCCCCC(*)=O)cc1
*Nc1ccc(CC(C)(C)NC(=O)c2ccc(C(*)=O)cc2)cc1
*Nc1ccc(CCNC(=O)CCCCC(*)=O)cc1
*Nc1ccc(Cc2ccc(NC(=O)/C=C/c3ccc(/C=C/C(*)=O)cc3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)CCCCCCCC(*)=O)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)CCCCCCCCC(*)=O)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)NCCCCCCCCCCCCCCCCCCNC(*)=O)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)NCCCCCCCCCCCCNC(*)=O)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)NCCCCCCCCCCNC(*)=O)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)NCCCCCCCCCNC(*)=O)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)NCCCCCCCCNC(*)=O)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)NCCCCCCNC(*)=O)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)Nc3ccccc3CCc3ccc(NC(*)=O)cc3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3cc(C(*)=O)cc(N4C(=O)C5C6C=CC(C6)C5C4=O)c3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3cc(C(*)=O)cc(N4C(=O)C5C6CCC(C6)C5C4=O)c3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3cc(C(*)=O)cc(N4C(=O)C5CC=CCC5C4=O)c3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3cc(C(*)=O)cc(N4C(=O)C=CC4=O)c3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3cc(C(*)=O)cc(S(=O)(=O)c4ccccc4)c3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3cc(NC(=O)C(C(C)CC)N4C(=O)c5ccccc5C4=O)cc(C(*)=O)c3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3cc(NC(=O)CCCCCCCCCCN4C(=O)c5ccccc5C4=O)cc(C(*)=O)c3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3cc(NC(=O)CCCCCN4C(=O)c5ccccc5C4=O)cc(C(*)=O)c3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3cc(NC(=O)CCCN4C(=O)c5ccccc5C4=O)cc(C(*)=O)c3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3cc(NC(=O)CCN4C(=O)c5ccccc5C4=O)cc(C(*)=O)c3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3cc(NC(=O)c4ccc(NC(=O)C(CC(C)C)N5C(=O)c6ccccc6C5=O)cc4)cc(C(*)=O)c3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3cc(NC(=O)c4ccccc4)cc(C(*)=O)c3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3cc(NC(=O)c4ccncc4)cc(C(*)=O)c3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3ccc(C(*)=O)cc3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3ccc(P(=O)(c4ccccc4)c4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Nc1ccc(Cc2ccc(NC(=O)c3cccc(C(*)=O)c3)cc2)cc1
*Nc1ccc(NC(=O)CCCC(*)=O)cc1
*Nc1ccc(NC(=O)CCCCC(*)=O)cc1
*Nc1ccc(NC(=O)CCCCCC(*)=O)cc1
*Nc1ccc(NC(=O)CCCCCCC(*)=O)cc1
*Nc1ccc(NC(=O)CCCCCCCC(*)=O)cc1
*Nc1ccc(NC(=O)CCCCCCCCC(*)=O)cc1
*Nc1ccc(NC(=O)Cc2cc(C)c(CC(*)=O)cc2C)cc1
*Nc1ccc(NC(=O)Cc2cc(CC(*)=O)c(C)cc2C)cc1
*Nc1ccc(NC(=O)c2cc(-c3ccc(C(=O)O)c(C(*)=O)c3)ccc2C(=O)O)cc1
*Nc1ccc(NC(=O)c2cc(-c3ccc(C(=O)OC)c(C(*)=O)c3)ccc2C(=O)OC)cc1
*Nc1ccc(NC(=O)c2cc(-c3ccc(C(=O)OCC)c(C(*)=O)c3)ccc2C(=O)OCC)cc1
*Nc1ccc(NC(=O)c2cc(C(*)=O)c(C(=O)O)cc2C(=O)O)cc1
*Nc1ccc(NC(=O)c2cc(C(*)=O)cc(S(=O)(=O)c3ccccc3)c2)cc1
*Nc1ccc(NC(=O)c2cc(C(=O)c3ccc(C(=O)O)c(C(*)=O)c3)ccc2C(=O)O)cc1
*Nc1ccc(NC(=O)c2cc(NC(=O)c3ccccc3)cc(C(*)=O)c2)cc1
*Nc1ccc(NC(=O)c2cc(NC(=O)c3ccncc3)cc(C(*)=O)c2)cc1
*Nc1ccc(NC(=O)c2ccc(C(*)=O)cc2)cc1
*Nc1ccc(NC(=O)c2ccc(C(=O)c3ccc(C(*)=O)c(C(=O)O)c3)cc2C(=O)O)cc1
*Nc1ccc(NC(=O)c2ccc(C(=O)c3ccc(C(*)=O)c(C(=O)OCC)c3)cc2C(=O)OCC)cc1
*Nc1ccc(NC(=O)c2ccc(C(c3ccc(C(*)=O)c(C(=O)OCC)c3)(C(F)(F)F)C(F)(F)F)cc2C(=O)OCC)cc1
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCCCCCCCC
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCC
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCCCC
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCCCCCC
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCCCCCCCC
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCC
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCCCC
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCCCCCC
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCCCCCCCC
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCC
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCCCC
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCCCCCC
*Nc1ccc(NC(=O)c2ccc(NC(=O)CCCCCCCCCCCCC(=O)Nc3ccc(C(*)=O)cc3)cc2)cc1C(=O)OCCCCCCCCCCCCCCCCCC
*Nc1ccc(NC(=O)c2ccc(NC(=O)c3cc(NC(=O)C45CC6CC(CC(C6)C4)C5)cc(C(*)=O)c3)cc2)cc1
*Nc1ccc(NC(=O)c2ccc(NC(=O)c3ccc(P(=O)(c4ccccc4)c4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Nc1ccc(NC(=O)c2ccc(NC(=O)c3ccc([Si](C)(C)c4ccc(C(=O)Nc5ccc(C(*)=O)cc5)cc4)cc3)cc2)cc1
*Nc1ccc(NC(=O)c2ccc(P(=O)(c3ccccc3)c3ccc(C(*)=O)cc3)cc2)cc1
*Nc1ccc(NC(=O)c2cccc(C(*)=O)c2)cc1
*Nc1ccc(NC(=O)c2cccc(C(=O)c3ccc(C(*)=O)cc3)c2)cc1
*Nc1ccc(N[Se]*)cc1
*Nc1ccc(Nc2ccc(C(=O)c3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Nc1ccc(Nc2ccc(C(O)(c3ccccc3)c3ccc(C(O)(c4ccccc4)c4ccc(*)cc4)cc3)cc2)cc1
*Nc1ccc(Oc2ccc(Nc3ccc4c(c3)c3cc(*)ccc3n4CC(CC)CCCC)cc2)cc1
*Nc1cccc(/C=C/c2ccc(NC(=O)c3cccc(C(*)=O)c3)cc2)c1
*Nc1cccc(C#Cc2ccc(NC(=O)c3cccc(C(*)=O)c3)cc2)c1
*Nc1cccc(C(=O)c2ccc(NC(=O)c3cccc(C(=O)c4ccc(C(*)=O)cc4)c3)cc2)c1
*Nc1cccc(CCc2ccc(NC(=O)c3ccc(C(*)=O)cc3)cc2)c1
*Nc1cccc(CCc2ccc(NC(=O)c3cccc(C(*)=O)c3)cc2)c1
*Nc1cccc(NC(=C(C#N)C#N)c2cccc(C(*)=C(C#N)C#N)c2)c1
*Nc1cccc(NC(=O)CCCCC(*)=O)c1
*Nc1cccc(NC(=O)CCCCC(=O)Nc2ccc(C(=O)NC(*)=S)cc2)c1
*Nc1cccc(NC(=O)CCCCCCC(*)=O)c1
*Nc1cccc(NC(=O)CCCCCCCCC(*)=O)c1
*Nc1cccc(NC(=O)CCCCCCCCC(=O)Nc2ccc(C(=O)NC(*)=S)cc2)c1
*Nc1cccc(NC(=O)Cc2cc(C)c(CC(*)=O)cc2C)c1
*Nc1cccc(NC(=O)Cc2cc(CC(*)=O)c(C)cc2C)c1
*Nc1cccc(NC(=O)c2cc(C(*)=O)cc(S(=O)(=O)c3ccccc3)c2)c1
*Nc1cccc(NC(=O)c2cc(NC(=O)C(CCSC)N3C(=O)c4ccccc4C3=O)cc(C(*)=O)c2)c1
*Nc1cccc(NC(=O)c2cc(NC(=O)C34CC5CC(CC(C5)C3)C4)cc(C(*)=O)c2)c1
*Nc1cccc(NC(=O)c2cc(NC(=O)c3ccccc3)cc(C(*)=O)c2)c1
*Nc1cccc(NC(=O)c2cc(NC(=O)c3ccncc3)cc(C(*)=O)c2)c1
*Nc1cccc(NC(=O)c2ccc(C(*)=O)cc2)c1
*Nc1cccc(NC(=O)c2ccc(C(=O)Nc3ccc(C(=O)NC(*)=S)cc3)cc2)c1
*Nc1cccc(NC(=O)c2ccc(NC(=O)c3ccc([Si](C)(C)c4ccc(C(=O)Nc5ccc(C(*)=O)cc5)cc4)cc3)cc2)c1
*Nc1cccc(NC(=O)c2cccc(C(*)=O)c2)c1
*Nc1cccc(NC(=O)c2cccc(C(=O)Nc3ccc(C(=O)NC(*)=S)cc3)c2)c1
*Nc1cccc2c(NC(=O)Nc3cc(NC(*)=O)cc(C(=O)Nc4ccc5c(c4)C(=O)c4ccccc4C5=O)c3)cccc12
*Nc1cccc2c(NC(=O)Nc3cc(NC(*)=O)cc(C(=O)Nc4cccc5c4C(=O)c4ccccc4C5=O)c3)cccc12
*Nc1cccc2c(NC(=O)c3cc(NC(=O)C(CCSC)N4C(=O)c5ccccc5C4=O)cc(C(*)=O)c3)cccc12
*Nc1cccc2c(NC(=O)c3ccc(C(*)=O)cc3)cccc12
*Nc1cccc2c(NC(=O)c3ccc(NC(=O)c4ccc([Si](C)(C)c5ccc(C(=O)Nc6ccc(C(*)=O)cc6)cc5)cc4)cc3)cccc12
*Nc1cccc2c(NC(=O)c3cccc(C(*)=O)c3)cccc12
*Nc1cccc2c(N[Se]*)cccc12
*Nc1cccc2c1C(=O)c1cccc(N[Se]*)c1C2=O
*Nc1ccccc1-c1ccc(NC(=O)c2ccc(C(*)=O)cc2)cc1
*Nc1ccccc1-c1ccc(NC(=O)c2cccc(C(*)=O)c2)cc1
*Nc1ccccc1CCc1ccc(NC(*)=O)cc1
*Nc1ccccc1CCc1ccc(NC(=O)NCCCCCCNC(*)=O)cc1
*Nc1ccccc1CCc1ccc(NC(=O)Nc2ccc(CCc3ccc(NC(*)=O)cc3)cc2)cc1
*Nc1ccccc1CCc1ccccc1NC(=O)Nc1ccc(CCc2ccc(NC(*)=O)cc2)cc1
*Nc1cnc(*)s1
*O/C(=C/CC)C(CC)C(*)=O
*OC(*)C
*OC(*)CCC
*OC(*)CCCCCC
*OC(=O)/C=C/C(=O)OC1COC2C(*)COC12
*OC(=O)C(C)NC(=O)CCCCC(=O)NC(C)C(=O)OC1COC2C(*)COC12
*OC(=O)C(C)NC(=O)CCCCCC(=O)NC(C)C(=O)OC1COC2C(*)COC12
*OC(=O)C(C)NC(=O)CCCCCCC(=O)NC(C)C(=O)OC1COC2C(*)COC12
*OC(=O)C(C)NC(=O)CCCCCCCC(=O)NC(C)C(=O)OC1COC2C(*)COC12
*OC(=O)C(C)NC(=O)CCCCCCCCC(=O)NC(C)C(=O)OC1COC2C(*)COC12
*OC(=O)C(C)NC(=O)CCCCCCCCCCC(=O)NC(C)C(=O)OC1COC2C(*)COC12
*OC(=O)C(CC(C)C)NC(=O)CCCCC(=O)NC(CC(C)C)C(=O)OC1COC2C(*)COC12
*OC(=O)C(CC(C)C)NC(=O)CCCCCCCCC(=O)NC(CC(C)C)C(=O)OC1COC2C(*)COC12
*OC(=O)C(CCSC)NC(=O)CCCCC(=O)NC(CCSC)C(=O)OC1COC2C(*)COC12
*OC(=O)C(CCSC)NC(=O)CCCCCCCCC(=O)NC(CCSC)C(=O)OC1COC2C(*)COC12
*OC(=O)C(Cc1ccccc1)NC(=O)CCCCC(=O)NC(Cc1ccccc1)C(=O)OC1COC2C(*)COC12
*OC(=O)C(Cc1ccccc1)NC(=O)CCCCCCC(=O)NC(Cc1ccccc1)C(=O)OC1COC2C(*)COC12
*OC(=O)C(Cc1ccccc1)NC(=O)CCCCCCCCC(=O)NC(Cc1ccccc1)C(=O)OC1COC2C(*)COC12
*OC(=O)C(Cc1ccccc1)NC(=O)CCCCCCCCCCC(=O)NC(Cc1ccccc1)C(=O)OC1COC2C(*)COC12
*OC(=O)C(NC(=O)CCCCC(=O)NC(C(=O)OC1COC2C(*)COC12)C(C)CC)C(C)CC
*OC(=O)C(NC(=O)CCCCCCCCC(=O)NC(C(=O)OC1COC2C(*)COC12)C(C)CC)C(C)CC
*OC(=O)C1CCC(C(=O)OC2COC3C(*)COC23)CC1
*OC(=O)CCC(=O)OC1COC2C(*)COC12
*OC(=O)CCCCCCCCC(*)=O
*OC(=O)CCCCCCCCC(=O)OC1COC2C(*)COC12
*OC(=O)CNC(=O)CCCCCCCCC(=O)NCC(=O)OC1COC2C(*)COC12
*OC(=O)CNC(=O)CNC(=O)CCCCCCCCC(=O)NCC(=O)NCC(=O)OC1COC2C(*)COC12
*OC(=O)Cc1ccc(CC(*)=O)cc1
*OC(=O)NC1CCC(CC2CCC(NC(=O)OC3CC4CC(*)CC(C3)O4)CC2)CC1
*OC(=O)Nc1ccc(C)c(NC(=O)OC2CCN(c3ccc(/C=C/C4=CC(=C(C#N)C#N)C=C(/C=C/c5ccc(N6CCC(*)CC6)s5)O4)s3)CC2)c1
*OC(=O)Nc1ccc(Cc2ccc(NC(=O)OC3CC4CC(*)CC(C3)O4)cc2)cc1
*OC(=O)OC1CC2CC(*)CC(C1)O2
*OC(=O)OC1COC2C(*)COC12
*OC(=O)OCC(O)C(O)COC(=O)OC1COC2C(*)COC12
*OC(=O)OCC(OC)C(COC(=O)OC1COC2C(*)COC12)OC
*OC(=O)OCC1OC(C)(C)OC1COC(=O)OC1COC2C(*)COC12
*OC(=O)OCCCCCCCCCCOC(=O)OC1COC2C(*)COC12
*OC(=O)OCCCCCCCCOC(=O)OC1COC2C(*)COC12
*OC(=O)OCCCCCCOC(=O)OC1COC2C(*)COC12
*OC(=O)OCCCCOC(=O)OC1COC2C(*)COC12
*OC(=O)OCCOCCOC(=O)OC1COC2C(*)COC12
*OC(=O)OCCOCCOCCOC(=O)OC1COC2C(*)COC12
*OC(=O)OCCOCCOCCOCCOC(=O)OC1COC2C(*)COC12
*OC(=O)Oc1ccc(C(=O)Oc2ccc(OC(=O)OC3COC4C(*)COC34)cc2)cc1
*OC(=O)Oc1ccc(C(C)(C)c2ccc(OC(=O)OC3CC4CC(*)CC(C3)O4)cc2)cc1
*OC(=O)Oc1ccc(S(=O)(=O)c2ccc(OC(=O)OC3CC4CC(*)CC(C3)O4)cc2)cc1
*OC(=O)Oc1ccc2c(c1)Oc1cc(*)ccc1C21c2ccccc2-c2ccccc21
*OC(=O)c1ccc(/N=N/c2ccc(C(=O)OC3COC4C(*)COC34)cc2)cc1
*OC(=O)c1ccc(C(=O)OC2CC3CC(*)CC(C2)O3)cc1
*OC(=O)c1ccc(C(=O)OC2COC3C(*)COC23)cc1
*OC(=O)c1ccc(C(=O)OC2COC3C(*)COC23)o1
*OC(=O)c1ccc(C(=O)Oc2ccc3c(c2)Oc2cc(*)ccc2C32c3ccccc3-c3ccccc32)cc1
*OC(=O)c1ccc(C(C)(C)c2ccc(C(*)=O)cc2)cc1
*OC(=O)c1ccc(C(F)(F)C(F)(F)C(F)(F)c2ccc(C(*)=O)cc2)cc1
*OC(=O)c1ccc(C2(C)CC(C)(C)c3ccc(C(=O)Oc4ccc5c(c4)Oc4cc(*)ccc4C5(C)C)cc32)cc1
*OC(=O)c1ccc(CCCCCCc2ccc(C(*)=O)cc2)cc1
*OC(=O)c1ccc(CCCCCc2ccc(C(*)=O)cc2)cc1
*OC(=O)c1ccc(CCCCc2ccc(C(*)=O)cc2)cc1
*OC(=O)c1ccc(Cc2ccc(C(*)=O)cc2)cc1
*OC(=O)c1ccc(SCCCCSc2ccc(C(*)=O)cc2)cc1
*OC(=O)c1cccc(C(*)=O)c1
*OC(=O)c1cccc(C(=O)OC2CC3CC(*)CC(C2)O3)c1
*OC(=O)c1cccc(C(=O)Oc2ccc3c(c2)Oc2cc(*)ccc2C32OC(=O)c3ccccc32)c1
*OC(=O)c1cccc(OCCCCCCCCCCOc2cccc(C(=O)OC3COC4C(*)COC34)c2)c1
*OC(=O)c1cccc(OCCCCCCCCOc2cccc(C(=O)OC3COC4C(*)COC34)c2)c1
*OC(=O)c1cccc(OCCCCCCOc2cccc(C(=O)OC3COC4C(*)COC34)c2)c1
*OC(C)(CC(*)=O)C(F)(F)F
*OC(C)C#CC#CC(C)OC(=O)c1ccc(C(*)=O)cc1
*OC(C)C(*)=O
*OC(C)C(=O)NC(C)C(*)=O
*OC(C)C(=O)OCC(*)=O
*OC(C)CC(*)=O
*OC(C)CCC(C)OC(=O)CCCCCCCCC(*)=O
*OC(C)CCCC(C)C(*)=O
*OC(C)CCCCC(*)=O
*OC(C)CCOC(=O)C(CCCCCCOc1ccc(/N=C/c2ccc([N+](=O)[O-])cc2)cc1)C(*)=O
*OC(C)CCOC(=O)c1ccc(-c2ccc(C(*)=O)cc2)cc1
*OC(C)COC(*)=O
*OC(C)COC(=O)/C=C/C(*)=O
*OC(C)COC(=O)c1ccc(C(*)=O)cc1
*OC(C)COC(=O)c1cccc(C(*)=O)c1
*OC(C)COC(=O)c1ccccc1C(*)=O
*OC(C)COC(C)COC(=O)c1ccc(C(*)=O)cc1
*OC(C)COCC(C)OC(=O)c1ccccc1C(*)=O
*OC(CC(*)=O)C(C)(Cl)Cl
*OC(CC(*)=O)C(Cl)(Cl)Cl
*OC(CC)C(*)=O
*OC(CC)CC(*)=O
*OC(CCCCC)CC(*)=O
*OC(CCCCCC)CC(*)=O
*OC(CCCCCCC=C)CC(*)=O
*OC(CCOc1ccccc1)CC(*)=O
*OC(CCc1ccccc1)CC(*)=O
*OC(CCl)C(*)CCl
*OC(CCl)COC(=O)C1=C(C(=O)OCC(CCl)OC(=O)c2ccc(C(*)=O)cc2)C2C=CC1C2
*OC(COC(*)=O)COC(C)(C)C
*OC(COC(*)=O)COc1ccccc1
*OC(COC(=O)C12CC3CC(CC(C(*)=O)(C3)C1)C2)COc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*OC(COC(=O)C1=C(C(=O)OCC(COC(=O)C2=C(c3ccccc3)C3C=CC2C3)OC(=O)c2ccc(C(*)=O)cc2)C2C=CC1C2)COC(=O)C1=C(c2ccccc2)C2C=CC1C2
*OC(COC(=O)C1CCC(C(*)=O)CC1)COc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*OC(COC(=O)NCCCCCCNC(*)=O)c1ccco1
*OC(COC(=O)Nc1ccc(Cc2ccc(NC(*)=O)cc2)cc1)c1ccco1
*OC(COC(=O)c1c(F)c(F)c(-c2c(F)c(F)c(C(*)=O)c(F)c2F)c(F)c1F)COc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*OC(COC(=O)c1cc(C(*)=O)cc(C(C)(C)C)c1)COc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*OC(COC(=O)c1ccc(-c2cc(CCCCCCCC)c(-c3ccc(C(*)=O)c(F)c3F)cc2CCCCCCCC)c(F)c1F)COc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*OC(COC(=O)c1ccc(-c2ccc(C(*)=O)cc2)cc1)COc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*OC(COC(=O)c1ccc(C(*)=O)c(OC)c1)COc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*OC(COC(=O)c1ccc(C(*)=O)c2ccccc12)COc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*OC(COC(=O)c1ccc(C(*)=O)cc1)COc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*OC(COC(=O)c1ccc(C)cc1C(*)=O)COc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*OC(COC(=O)c1ccc2cc(C(*)=O)ccc2c1)COc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*OC(COC(=O)c1cccc(-c2ccc(C(*)=O)cc2)c1)COc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*OC(COC(=O)c1cccc(C(*)=O)c1)COc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*OC(COC(=O)c1ccccc1-c1ccccc1C(*)=O)COc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*OC(COC(=O)c1ccccc1C(*)=O)COc1ccc(/N=N/c2ccc(C#N)cc2)cc1
*OC(COCCCC)COC(*)=O
*OC(COCCOC)COC(*)=O
*OC(COc1ccccc1)CC(*)=O
*OC(F)(C(*)(F)F)C(F)(F)F
*OC(F)(C(=O)N([Li])S(=O)(=O)C(F)(F)C(*)(F)F)C(F)(F)F
*OC(F)(F)C(*)(F)F
*OC(F)(F)C(F)(F)C(*)(F)F
*OC(F)(F)C(F)(F)C(F)(F)C(*)(F)F
*OC(F)(F)COC(=O)c1cc(OCCCCC)cc(C(=O)OCC(*)(F)F)c1
*OC(F)(F)COC(=O)c1cccc(C(=O)OCC(*)(F)F)c1
*OC(F)(F)COC(=O)c1cccc(C(F)(F)C(F)(F)C(F)(F)c2cccc(C(=O)OCC(*)(F)F)c2)c1
*OC(c1ccco1)C(OC(=O)Nc1ccc(Cc2ccc(NC(*)=O)cc2)cc1)c1ccco1
*OC1C(C)(C)C(OC(*)=O)C1(C)C
*OC1C(C)(C)C(OC(=O)C2CCC(C(*)=O)CC2)C1(C)C
*OC1C(C)(C)C(OC(=O)c2ccc(C(*)=O)cc2)C1(C)C
*OC1C(CO)OC(*)C(N)C1O
*OC1C(CO)OC(*)C(NC(C)=O)C1O
*OC1C(CO)OC(*)C(O)C1O
*OC1C(COC(=O)Nc2ccccc2)OC(*)C(OC(=O)Nc2ccccc2)C1OC(=O)Nc1ccccc1
*OC1C(COC(C)=O)OC(*)C(OC(C)=O)C1O
*OC1C(COC(C)=O)OC(*)C(OC(C)=O)C1OC(C)=O
*OC1C(COCC)OC(*)C(OCC)C1OCC
*OC1C(COCO)OC(*)C(O)C1O
*OC1CCC(OC(*)=O)CC1
*OC1CCC(OC(=O)C(*)=O)CC1
*OC1CCC(OC(=O)CCCCC(*)=O)CC1
*OC1CCC(OC(=O)CCCCCCCCC(*)=O)CC1
*OC1CCC(OC(=O)OCCCCCCCOC(*)=O)CC1
*OC1CCC(OC(=O)OCCCCCCOC(*)=O)CC1
*OC1CCC(OC(=O)OCCCCCOC(*)=O)CC1
*OC1CCC(OC(=O)OCCCCOC(*)=O)CC1
*OC1CCC(OC(=O)c2ccc(C(*)=O)cc2)CC1
*OC1CCCCC1*
*OC1CCCCC1OC(*)=O
*OCC(F)(F)C(F)(F)C(F)(F)COc1nc(F)c(F)c(OCC(F)(F)C(F)(F)C(F)(F)COc2c(F)c(*)nc(F)c2F)c1F
*OCC(F)(F)C(F)(F)C(F)(F)COc1nc(F)nc(*)c1F
*OCCCCOC1COC2C(OC(=O)CCCCC(=O)OC3COC4C(*)COC34)COC12
*OCCOC(=O)CCCC(=O)OCCOc1nc(-c2ccc(OC)cc2)nc(*)c1-c1ccc(OC)cc1
*OCCOC(=O)CCCC(=O)OCCOc1nc(-c2ccc(OCCCC)cc2)nc(*)c1-c1ccc(OC)cc1
*OCCOC(=O)CCCC(=O)OCCOc1nc(-c2ccc(OCCCC)cc2)nc(*)c1-c1ccc(OCCCC)cc1
*OCCOC(=O)CCCCC(=O)OCCOc1nc(-c2ccc(OC)cc2)nc(*)c1-c1ccc(OC)cc1
*OCCOC(=O)CCCCC(=O)OCCOc1nc(-c2ccc(OCCCC)cc2)nc(*)c1-c1ccc(OC)cc1
*OCCOC(=O)c1cc(/N=N/c2ccc(OCC)cc2)ccc1-c1ccc(/N=N/c2ccc(OCC)cc2)cc1C(=O)OCCOc1nc(-c2ccc(OCCCC)cc2)nc(*)c1-c1ccc(OC)cc1
*OCCOC(=O)c1ccc(C(=O)OCCOc2nc(-c3ccc(OCCCC)cc3)nc(*)c2-c2ccc(OC)cc2)cc1
*OCCOC(=O)c1cccc(C(=O)OCCOc2nc(-c3ccc(OCCCC)cc3)nc(*)c2-c2ccc(OC)cc2)c1
*ON(C(F)(F)C(*)(F)F)C(F)(F)C(F)(F)Br
*ON(C(F)(F)F)C(F)(Br)C(*)(F)F
*ON(C(F)(F)F)C(F)(C(*)(F)F)C(F)(F)F
*ON(C(F)(F)F)C(F)(Cl)C(*)(F)F
*ON(C(F)(F)F)C(F)(F)/C(F)=C(\F)C(*)(F)F
*ON(C(F)(F)F)C(F)(F)C(*)(F)F
*ON(C(F)(F)F)C(F)(F)C(*)(F)SC(F)(F)F
*OP(=O)(Oc1c(Cl)c(Cl)c(*)c(Cl)c1Cl)Oc1c(Cl)c(Cl)c(Cl)c(Cl)c1Cl
*OP(=O)(Oc1c(Cl)cc(Cl)cc1Cl)Oc1c(Cl)c(Cl)c(*)c(Cl)c1Cl
*OP(=O)(Oc1c(Cl)cccc1Cl)Oc1c(Cl)c(Cl)c(*)c(Cl)c1Cl
*OP(=O)(Oc1ccc(*)cc1)OC1CCCCC1
*OP(=O)(Oc1ccc(-c2ccc(*)cc2)cc1)OC1CCCCC1
*OP(=O)(Oc1ccc(Br)cc1)OC(CCl)COc1ccc(S(=O)(=O)c2ccc(OCC(*)CCl)cc2)cc1
*OP(=O)(Oc1ccc(Br)cc1)Oc1c(Br)cc(C(c2cc(Br)c(*)c(Br)c2)(C(F)(F)F)C(F)(F)F)cc1Br
*OP(=O)(Oc1ccc(Br)cc1)Oc1c(C)cc(S(=O)(=O)c2cc(C)c(*)c(C)c2)cc1C
*OP(=O)(Oc1ccc(Br)cc1)Oc1ccc(S(=O)(=O)c2ccc(*)cc2)cc1
*OP(=O)(Oc1ccc(C(C)(C)c2ccc(*)cc2)cc1)OC1CCCCC1
*OP(=O)(Oc1ccc(C(C)(C)c2ccc(*)cc2)cc1)Oc1c(Cl)c(Cl)c(Cl)c(Cl)c1Cl
*OP(=O)(Oc1ccc(C(C)(C)c2ccc(*)cc2)cc1)Oc1c(Cl)cc(Cl)cc1Cl
*OP(=O)(Oc1ccc(C(C)(C)c2ccc(*)cc2)cc1)Oc1c(Cl)cccc1Cl
*OP(=O)(Oc1ccc(C)cc1)Oc1c(Br)cc(C(c2cc(Br)c(*)c(Br)c2)(C(F)(F)F)C(F)(F)F)cc1Br
*OP(=O)(Oc1ccc(C)cc1)Oc1c(C)cc(S(=O)(=O)c2cc(C)c(*)c(C)c2)cc1C
*OP(=O)(Oc1ccc(C)cc1)Oc1ccc(S(=O)(=O)c2ccc(*)cc2)cc1
*OP(=O)(Oc1ccc(Cl)cc1)Oc1c(Cl)c(Cl)c(*)c(Cl)c1Cl
*OP(=O)(Oc1ccc(Cl)cc1)Oc1ccc(C(C)(C)c2ccc(*)cc2)cc1
*OP(=O)(Oc1ccc(NC(=O)OCCCCCCOC(=O)Nc2ccc(*)cc2)cc1)c1ccccc1
*OP(=O)(Oc1ccc(NC(=O)Oc2ccc(-c3ccc(OC(=O)Nc4ccc(*)cc4)cc3)cc2)cc1)c1ccccc1
*OP(=O)(Oc1ccc(NC(=O)Oc2ccc(C(C)(C)c3ccc(OC(=O)Nc4ccc(*)cc4)cc3)cc2)cc1)c1ccccc1
*OP(=O)(Oc1ccc(OC)cc1)Oc1c(Br)cc(C(c2cc(Br)c(*)c(Br)c2)(C(F)(F)F)C(F)(F)F)cc1Br
*OP(=O)(Oc1ccc(OC)cc1)Oc1c(C)cc(S(=O)(=O)c2cc(C)c(*)c(C)c2)cc1C
*OP(=O)(Oc1ccc(OC)cc1)Oc1ccc(S(=O)(=O)c2ccc(*)cc2)cc1
*OP(=O)(Oc1ccc(S(=O)(=O)c2ccc(*)cc2)cc1)OC1CCCCC1
*OP(=O)(Oc1ccc([N+](=O)[O-])cc1)Oc1c(Br)cc(C(c2cc(Br)c(*)c(Br)c2)(C(F)(F)F)C(F)(F)F)cc1Br
*OP(=O)(Oc1ccc([N+](=O)[O-])cc1)Oc1c(C)cc(S(=O)(=O)c2cc(C)c(*)c(C)c2)cc1C
*OP(=O)(Oc1ccc([N+](=O)[O-])cc1)Oc1ccc(S(=O)(=O)c2ccc(*)cc2)cc1
*OP(=O)(Oc1cccc2c(*)cccc12)OC1CCCCC1
*OP(=O)(Oc1ccccc1)OC(CCl)COc1ccc(S(=O)(=O)c2ccc(OCC(*)CCl)cc2)cc1
*OP(=O)(Oc1ccccc1)Oc1c(Br)cc(C(c2cc(Br)c(*)c(Br)c2)(C(F)(F)F)C(F)(F)F)cc1Br
*OP(=O)(Oc1ccccc1)Oc1c(C)cc(S(=O)(=O)c2cc(C)c(*)c(C)c2)cc1C
*OP(=O)(Oc1ccccc1)Oc1c(Cl)c(Cl)c(*)c(Cl)c1Cl
*OP(=O)(Oc1ccccc1)Oc1ccc(*)cc1
*OP(=O)(Oc1ccccc1)Oc1ccc(-c2ccc(*)cc2)cc1
*OP(=O)(Oc1ccccc1)Oc1ccc(C(C)(C)c2ccc(*)cc2)cc1
*OP(=O)(Oc1ccccc1)Oc1ccc(S(=O)(=O)c2ccc(*)cc2)cc1
*OP(=O)(Oc1ccccc1)Oc1cccc2c(*)cccc12
*OS(=O)(=O)c1cc(C)c(*)c(C)c1
*OS(=O)(=O)c1cc(S(=O)(=O)Oc2ccc(C3(c4ccc(*)c(C)c4)CCCCC3)cc2C)ccc1C
*OS(=O)(=O)c1cc(S(=O)(=O)Oc2ccc(C3(c4ccc(*)cc4)CCCC3)cc2)ccc1C
*OS(=O)(=O)c1cc(S(=O)(=O)Oc2ccc(C3(c4ccc(*)cc4)CCCCC3)cc2)ccc1C
*OS(=O)(=O)c1ccc(*)cc1
*OS(=O)(=O)c1cccc(C(=O)Oc2ccc(C(C)(C)c3ccc(*)cc3)cc2)c1
*OS(=O)(=O)c1cccc(C(=O)c2cccc(S(=O)(=O)Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)c2)c1
*OS(=O)(=O)c1cccc(S(=O)(=O)Oc2ccc(C(C)(C)c3ccc(*)cc3)cc2)c1
*OS(=O)(=O)c1cccc(S(=O)(=O)Oc2ccc(C(C)(CC)c3ccc(*)cc3)cc2)c1
*OS(=O)(=O)c1cccc(S(=O)(=O)Oc2ccc(C3(c4ccc(*)cc4)CCCCC3)cc2)c1
*O[Ca]OC(=O)c1ccccc1C(=O)OCCCCCOC(=O)NCCCCCCNC(=O)OCCCCCOC(=O)c1ccccc1C(*)=O
*O[Ca]OC(=O)c1ccccc1C(=O)OCCCCCOC(=O)Nc1cc(NC(=O)OCCCCCOC(=O)c2ccccc2C(*)=O)ccc1C
*O[SiH](*)C
*O[SiH](*)Nc1cccc(N[Si](*)(C)O*)c1
*O[Si](*)(C([2H])([2H])[2H])C([2H])([2H])[2H]
*O[Si](*)(C)C
*O[Si](*)(C)C
*O[Si](*)(C)C1CC2CC1C1C3CC(CC3C(=O)OC(C)(C)C)C21
*O[Si](*)(C)C1CC2CC1CC2OC(=O)OC(C)(C)C
*O[Si](*)(C)C=C
*O[Si](*)(C)C=C
*O[Si](*)(C)CC
*O[Si](*)(C)CCC
*O[Si](*)(C)CCC(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*O[Si](*)(C)CCC(F)(F)F
*O[Si](*)(C)CCCC
*O[Si](*)(C)CCCC(=O)O
*O[Si](*)(C)CCCC(=O)OC
*O[Si](*)(C)CCCC(=O)OCC
*O[Si](*)(C)CCCC(=O)OCCCC
*O[Si](*)(C)CCCC(=O)OCCO
*O[Si](*)(C)CCCCC
*O[Si](*)(C)CCCCC(=O)OCCCCCCCCCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*O[Si](*)(C)CCCCC1COC(=O)O1
*O[Si](*)(C)CCCCCC
*O[Si](*)(C)CCCCCCCC
*O[Si](*)(C)CCCCCCCCCCC(=O)OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*O[Si](*)(C)CCCCCCCCCCC(=O)OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*O[Si](*)(C)CCCCCCCCCCC(=O)Oc1ccc(-c2ccc(OC(=O)CCCCC(=O)OC3CC(C)CCC3C(C)C)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCC(=O)Oc1ccc(-c2ccc(OC(=O)c3ccc(OCC(C)CC)cc3)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCC(=O)Oc1ccc(-c2ccc(OC(=O)c3ccc(OCC)cc3)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCC(=O)Oc1ccc(-c2ccc(OC(=O)c3ccc(OCCCCCCC)cc3)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCC(=O)Oc1ccc(C(=O)OCCOc2ccc(C(=O)OC3CCC4(C)C(=CCC5C4CCC4(C)C(C(C)CCCC(C)C)CCC54)C3)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCC(=O)Oc1ccc(C(=O)Oc2ccc(-c3ccc(OC(=O)CCCCC(=O)OC4CC(C)CCC4C(C)C)cc3)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCC(=O)Oc1ccc(C(=O)Oc2ccc(-c3ccc(OC(=O)c4ccc(OCC(C)CC)cc4)cc3)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCC(=O)Oc1ccc(C(=O)Oc2ccc(-c3ccc(OCC(C)CC)cc3)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCC(=O)Oc1ccc(C(=O)Oc2ccc(OC(=O)CCCCC(=O)OC3CC(C)CCC3C(C)C)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCC(=O)Oc1ccc(C(=O)Oc2ccc(OC(=O)c3ccc(OCC(C)CC)cc3)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCC(=O)Oc1ccc(OC(=O)c2ccc(OCC(C)CC)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCC(=O)Oc1ccc2cc(C(=O)OC3CCC4(C)C(=CCC5C4CCC4(C)C(C(C)CCCC(C)C)CCC54)C3)ccc2c1
*O[Si](*)(C)CCCCCCCCCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
*O[Si](*)(C)CCCCCCCCCCCOC(=O)c1cc(OC(=O)c2ccc(OCCOCCOCCOCCOCCOCCOCCOCCOC)cc2)ccc1OC(=O)c1ccc(OCCOCCOCCOCCOCCOCCOCCOCCOC)cc1
*O[Si](*)(C)CCCCCCCCCCCOc1ccc(-c2ccc(C(=O)Oc3ccc(C(=O)OC(C)C(=O)OC(C)C(=O)OC(C)C(=O)OCC(C)CC)cc3)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCCOc1ccc(-c2ccc(C(=O)Oc3ccc(C(=O)OC(C)C(=O)OC(C)C(=O)OCC(C)CC)cc3)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCCOc1ccc(/C=C/c2ccc(C#N)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCCOc1ccc(/C=C/c2ccc(OC)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCCOc1ccc(/C=C/c2ccc(OCCCC)cc2)cc1
*O[Si](*)(C)CCCCCCCCCCCOc1ccc(C(=O)OC/C=C(\C)CCC=C(C)C)cc1
*O[Si](*)(C)CCCCCCCCCCCn1c2ccccc2c2ccccc21
*O[Si](*)(C)CCCCCCCCOc1ccc(C(=O)Oc2ccc(C#N)cc2C)cc1
*O[Si](*)(C)CCCCCCOC(=O)c1cc(OC(=O)c2ccc(OCCOCCOCCOCCOCCOCCOC)cc2)ccc1OC(=O)c1ccc(OCCOCCOCCOCCOCCOCCOC)cc1
*O[Si](*)(C)CCCCCCOc1ccc(-c2ccc(C#N)cc2)cc1
*O[Si](*)(C)CCCCCCOc1ccc(C(=O)Oc2ccc(C#N)cc2)cc1
*O[Si](*)(C)CCCCCCOc1ccc(C(=O)Oc2ccc(C#N)cc2C)cc1
*O[Si](*)(C)CCCCCCOc1ccc(C(=O)Oc2ccc(C(=O)OCC(C)CC)cc2)cc1
*O[Si](*)(C)CCCCCCOc1ccc(C(=O)Oc2ccc(C(=O)Oc3ccc(C=C(C(=O)OCCCCCCCCC)C(=O)OCCCCCCCCC)cc3)cc2)cc1
*O[Si](*)(C)CCCCCCOc1ccc(C(=O)Oc2ccc(C(=O)Oc3ccc(C=C(C(=O)OCCCCCCCCC)C(=O)OCCCCCCCCC)cc3)cc2)cc1
*O[Si](*)(C)CCCCCCOc1ccc(C(=O)Oc2ccc(OC)cc2)cc1
*O[Si](*)(C)CCCCCCOc1ccc(C(=O)Oc2ccc(OCCCCCC)cc2)cc1
*O[Si](*)(C)CCCCCCn1c2ccccc2c2ccccc21
*O[Si](*)(C)CCCCCOc1ccc(/C=C/c2ccc(OC)cc2)cc1
*O[Si](*)(C)CCCCCn1c2ccccc2c2ccccc21
*O[Si](*)(C)CCCCOc1ccc(-c2ccc(C#N)cc2)cc1
*O[Si](*)(C)CCCCOc1ccc(C(=O)Oc2ccc(C#N)cc2)cc1
*O[Si](*)(C)CCCOC(=O)c1cc(OC(=O)c2ccc(OCCOCCOCCOCCOC)cc2)ccc1OC(=O)c1ccc(OCCOCCOCCOCCOC)cc1
*O[Si](*)(C)CCCOC(=O)c1cc(OC(=O)c2ccc(OCCOCCOCCOCCOCCOCCOC)cc2)ccc1OC(=O)c1ccc(OCCOCCOCCOCCOCCOCCOC)cc1
*O[Si](*)(C)CCCOCC1COC(=O)O1
*O[Si](*)(C)CCCOCCOCCOC
*O[Si](*)(C)CCCOCCOCCOCCOC
*O[Si](*)(C)CCCOCCOCCOc1ccc(COc2ccc3cc(C#N)ccc3c2)cc1
*O[Si](*)(C)CCCOc1ccc(-c2ccc(C#N)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(-c2ccc(OC(=O)CCCCC(=O)OC3CC(C)CCC3C(C)C)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(/N=N/c2ccc(S(=O)(=O)O)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(C(=O)OC2CCC3(C)C(=CCC4C3CCC3(C)C(C(C)CCCC(C)C)CCC43)C2)cc1
*O[Si](*)(C)CCCOc1ccc(C(=O)OC2CCC3(C)C(=CCC4C3CCC3(C)C(C(C)CCCC(C)C)CCC43)C2)cc1
*O[Si](*)(C)CCCOc1ccc(C(=O)OC2CCC3(C)C(=CCC4C3CCC3(C)C(C(C)CCCC(C)C)CCC43)C2)cc1S(=O)(=O)O[K]
*O[Si](*)(C)CCCOc1ccc(C(=O)OCCOc2ccc(C(=O)OC3CCC4(C)C(=CCC5C4CCC4(C)C(C(C)CCCC(C)C)CCC54)C3)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(C(=O)Oc2ccc(-c3ccc(OC(=O)CCCCC(=O)OC4CC(C)CCC4C(C)C)cc3)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(C(=O)Oc2ccc(-c3ccc(OC(=O)c4cc(OCC(C)CC)c(OCC(C)CC)c(OCC(C)CC)c4)cc3)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(C(=O)Oc2ccc(-c3ccc(OC(=O)c4cc(OCC)c(OCC)c(OCC)c4)cc3)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(C(=O)Oc2ccc(-c3ccc(OC(=O)c4cc(OCC)cc(OCC)c4)cc3)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(C(=O)Oc2ccc(-c3ccc(OC(=O)c4cc(OCCCCC)c(OCCCCC)c(OCCCCC)c4)cc3)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(C(=O)Oc2ccc(-c3ccc(OC(=O)c4cc(OCCCCCCCCCCCC)c(OCCCCCCCCCCCC)c(OCCCCCCCCCCCC)c4)cc3)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(C(=O)Oc2ccc(-c3ccc(OC(=O)c4ccc(OCC)cc4)cc3)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(C(=O)Oc2ccc(C#N)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(C(=O)Oc2ccc(C(=O)Oc3ccc(OC)cc3)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(C(=O)Oc2ccc(OC(=O)CCCCC(=O)OC3CC(C)CCC3C(C)C)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(C(=O)Oc2ccc(OC)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(C(=O)Oc2ccc(OCCCCCC)cc2)cc1
*O[Si](*)(C)CCCOc1ccc(N(c2ccc(C)cc2)c2ccc(-c3ccc(N(c4ccccc4)c4ccc(C)cc4)cc3)cc2)cc1
*O[Si](*)(C)CCCOc1ccc2c(c1)c1cc(-c3ccc4c(c3)c3ccccc3n4CC(CC)CCCC)ccc1n2CC(CC)CCCC
*O[Si](*)(C)CCCn1c2ccccc2c2ccccc21
*O[Si](*)(C)CC[Si](C)(C)O[Si](C)(C)CCCOc1cc(OC(=O)c2ccc(OCCCC)cc2)ccc1/N=N/c1ccc(OCCCC)cc1
*O[Si](*)(C)CC[Si](OCCOC)(OCCOC)OCCOC
*O[Si](*)(C)CCc1cc(-c2nnc(-c3ccc(OCCCCCCCCCCCC)cc3)o2)ccc1-c1nnc(-c2ccc(OCCCCCCCCCCCC)cc2)o1
*O[Si](*)(C)CCc1cc(C(=O)Oc2ccc(OC)cc2)ccc1C(=O)Oc1ccc(OC)cc1
*O[Si](*)(C)CCc1cc(C(=O)Oc2ccc(OCCCC)cc2)ccc1-c1ccc(C(=O)Oc2ccc(OCCCC)cc2)cc1
*O[Si](*)(C)CCc1cc(C(=O)Oc2ccc(OCCCC)cc2)ccc1C(=O)Oc1ccc(OCCCC)cc1
*O[Si](*)(C)CCc1cc(C(=O)Oc2ccc(OCCCCCCCCCCCC)cc2)ccc1-c1ccc(C(=O)Oc2ccc(OCCCCCCCCCCCC)cc2)cc1
*O[Si](*)(C)CCc1ccc(O)cc1
*O[Si](*)(C)OCCCCCCCCCCCOc1ccc(C2COC(c3ccc(OC)cc3)OC2)cc1
*O[Si](*)(C)OCCCCCCCCCCCOc1ccc(C2COC(c3ccc(OCC(C)CC)cc3)OC2)cc1
*O[Si](*)(C)OCCOCCOCCOC
*O[Si](*)(C)OCCOCCOCCOCCOCCOCCOCCOCCOC
*O[Si](*)(C)c1c(F)c(F)c(F)c(F)c1F
*O[Si](*)(C)c1ccc(C)cc1
*O[Si](*)(C)c1ccccc1
*O[Si](*)(CC)CC
*O[Si](*)(CCC)CCC
*O[Si](*)(CCC)c1ccccc1
*O[Si](*)(CCCOCCOC)CCCOCCOC
*O[Si](*)(CCCOCCOCCOC)CCCOCCOCCOC
*O[Si](*)(CCCOCCOCCOCCOC)CCCOCCOCCOCCOC
*O[Si](*)(CCCOCCOCCOCCOCCOC)CCCOCCOCCOCCOCCOC
*O[Si](*)(CCCOCCOCCOCCOCCOCCOC)CCCOCCOCCOCCOCCOCCOC
*O[Si](*)(CCCOCCOCCOCCOCCOCCOCCOC)CCCOCCOCCOCCOCCOCCOCCOC
*O[Si](*)(CCCOCCOCCOCCOCCOCCOCCOCCOC)CCCOCCOCCOCCOCCOCCOCCOCCOC
*O[Si](*)(c1ccccc1)c1ccc(OC(C)(C)C)cc1
*O[Si](C)(C)C#C[Si](C)(C)C#C[Si](C)(C)C#C[Si](C)(C)C#C[Si](*)(C)C
*O[Si](C)(C)C#C[Si](C)(C)C#C[Si](C)(C)C#C[Si](C)(C)C#C[Si](C)(C)C#C[Si](*)(C)C
*O[Si](C)(C)C#C[Si](C)(C)C#C[Si](C)(C)C#C[Si](C)(C)C#C[Si](C)(C)C#C[Si](C)(C)C#C[Si](*)(C)C
*O[Si](C)(C)C#Cc1ccc(C#C[Si](*)(C)C)cc1
*O[Si](C)(C)CC(C)(C)COC(=O)c1cccc(C(=O)OCC(C)(C)C[Si](*)(C)C)c1
*O[Si](C)(C)CCC/N=C/c1cc(Cc2ccc(O)c(/C=N/CCC[Si](*)(C)C)c2)ccc1O
*O[Si](C)(C)CCC/N=C1\C=C(O)/C(=N\CCC[Si](*)(C)C)C=C1O
*O[Si](C)(C)CCC/N=C1\C=C/C(=N/CCC[Si](*)(C)C)c2c(O)ccc(O)c21
*O[Si](C)(C)CCC/N=C1\c2ccccc2/C(=N\CCC[Si](*)(C)C)c2c(O)ccc(O)c21
*O[Si](C)(C)CCCC(=O)Oc1ccc(/C=N/c2ccc(/N=C/c3ccc(OC(=O)CCC[Si](*)(C)C)cc3)cc2)cc1
*O[Si](C)(C)CCCC(=O)Oc1ccc(/C=N/c2ccc(Cc3ccc(/N=C/c4ccc(OC(=O)CCC[Si](*)(C)C)cc4)cc3)cc2)cc1
*O[Si](C)(C)CCCC(=O)Oc1ccc(/C=N/c2ccc(Oc3ccc(/N=C/c4ccc(OC(=O)CCC[Si](*)(C)C)cc4)cc3)cc2)cc1
*O[Si](C)(C)CCCCCCCC[Si](*)(C)C
*O[Si](C)(C)CCCCCCOC(=O)C(C)Oc1ccc(OC(=O)c2ccc(-c3ccc(OCCCCCCCCCC[Si](*)(C)C)cc3)cc2)cc1
*O[Si](C)(C)CCCCCCOC(=O)C(C)Oc1ccc(OC(=O)c2ccc(-c3ccc(OCCCCCCCCCC[Si](*)(C)C)cc3)cc2)cc1[N+](=O)[O-]
*O[Si](C)(C)CCCCCC[Si](*)(C)C
*O[Si](C)(C)CCCNC(=O)C(=O)NCCC[Si](*)(C)C
*O[Si](C)(C)CC[Si](*)(C)C
*O[Si](C)(C)CC[Si](*)(C)O[Si](C)(C)C
*O[Si](C)(C)CC[Si](C)(C)Oc1c(C)cc(-c2cc(C)c(*)c(C)c2)cc1C
*O[Si](C)(C)COC(=O)CCCCCCCCC(=O)Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(/C=C4\CC/C(=C\c5ccc(Oc6ccc(S(=O)(=O)c7ccc(OC(=O)CCCCCCCCC(=O)OC[Si](*)(C)C)cc7)cc6)cc5)C4=O)cc3)cc2)cc1
*O[Si](C)(C)COC(=O)CCCCCCCCC(=O)Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(/C=C4\CCC/C(=C\c5ccc(Oc6ccc(S(=O)(=O)c7ccc(OC(=O)CCCCCCCCC(=O)OC[Si](*)(C)C)cc7)cc6)cc5)C4=O)cc3)cc2)cc1
*O[Si](C)(C)COC(=O)CCCCCCCCC(=O)Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(/C=C4\CCCC/C(=C\c5ccc(Oc6ccc(S(=O)(=O)c7ccc(OC(=O)CCCCCCCCC(=O)OC[Si](*)(C)C)cc7)cc6)cc5)C4=O)cc3)cc2)cc1
*O[Si](C)(C)OC(CCl)COc1ccc(C(C)(C)c2ccc(OCC(*)CCl)cc2)cc1
*O[Si](C)(C)O[Si](*)(C)CCCCCCCCOc1ccc(C(=O)Oc2ccc(C#N)cc2C)cc1
*O[Si](C)(C)O[Si](*)(C)CCCCOc1ccc(-c2ccc(C#N)cc2)cc1
*O[Si](C)(C)O[Si](*)(C)c1c(F)c(F)c(F)c(F)c1F
*O[Si](C)(C)O[Si](C)(C)C#Cc1ccc(C#C[Si](*)(C)C)cc1
*O[Si](C)(C)O[Si](C)(C)OC(C)CCC(*)C
*O[Si](C)(C)O[Si](C)(C)OC(C)CCCCC(*)C
*O[Si](C)(C)O[Si](C)(C)O[SiH](*)O[Si](C)(C)C=C
*O[Si](C)(C)O[Si](C)(C)O[Si](*)(C)c1c(F)c(F)c(F)c(F)c1F
*O[Si](C)(C)O[Si](C)(C)O[Si](*)(c1c(F)c(F)c(F)c(F)c1F)c1c(F)c(F)c(F)c(F)c1F
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)C#Cc1ccc(C#C[Si](*)(C)C)cc1
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)OC(C)CCC(*)C
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)OC(C)CCCCC(*)C
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)C#Cc1ccc(C#C[Si](*)(C)C)cc1
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)OC(C)CCC(*)C
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)OC(C)CCCCC(*)C
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)OC1=C(*)c2cccc3cccc1c23
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)C#Cc1ccc(C#C[Si](*)(C)C)cc1
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)OC(C)CCC(*)C
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)OC(C)CCCCC(*)C
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)OC1=C(*)c2cccc3cccc1c23
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)OC1=C(*)c2cccc3cccc1c23
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)Oc1c(*)c2ccccc2c2ccccc12
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)Oc1c(*)c2ccccc2c2ccccc12
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)c1ccc(Oc2ccc([Si](*)(C)C)cc2)cc1
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)c1ccc([Si](*)(C)C)cc1
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)Oc1c(*)c2ccccc2c2ccccc12
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)Oc1c(C)cc(-c2cc(C)c(*)c(C)c2)cc1C
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)c1ccc(Oc2ccc([Si](*)(C)C)cc2)cc1
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)c1ccc([Si](*)(C)C)cc1
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)c1cccc([Si](*)(C)C)c1
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)O[Si](C)(c1ccccc1)c1cccc([Si](*)(C)c2ccccc2)c1
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)Oc1c(C)cc(-c2cc(C)c(*)c(C)c2)cc1C
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)c1ccc(Oc2ccc([Si](*)(C)C)cc2)cc1
*O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)c1ccc([Si](*)(C)C)cc1
*O[Si](C)(C)O[Si](C)(C)Oc1c(C)cc(-c2cc(C)c(*)c(C)c2)cc1C
*O[Si](C)(C)O[Si](C)(C)Oc1ccc(-c2ccc(*)cc2)cc1
*O[Si](C)(C)O[Si](C)(C)c1c(F)c(F)c(F)c([Si](*)(C)C)c1F
*O[Si](C)(C)O[Si](C)(C)c1ccc([Si](*)(C)C)cc1
*O[Si](C)(C)Oc1ccc(*)cc1
*O[Si](C)(C)Oc1ccc(C(C)(C)c2ccc(*)cc2)cc1
*O[Si](C)(C)[Si](*)(C)C
*O[Si](C)(C)c1c(F)c(F)c(F)c([Si](*)(C)C)c1F
*O[Si](C)(C)c1cc(OCCO)c([Si](*)(C)C)cc1OCCO
*O[Si](C)(C)c1cc(OCCOCc2ccccc2)c([Si](*)(C)C)cc1OCCOCc1ccccc1
*O[Si](C)(C)c1ccc(-c2ccc([Si](*)(C)C)cc2)cc1
*O[Si](C)(C)c1ccc([Si](*)(C)C)c2ccccc12
*O[Si](C)(C)c1ccc([Si](*)(C)C)cc1
*O[Si](C)(C)c1ccc([Si](C)(C)O[SiH](*)C)cc1
*O[Si](C)(C)c1ccc([Si](C)(C)O[SiH](*)CCC(F)(F)F)cc1
*O[Si](C)(C)c1ccc([Si](C)(C)Oc2c(C)cc(-c3cc(C)c(*)c(C)c3)cc2C)cc1
*O[Si](C)(C)c1ccc2cc([Si](*)(C)C)ccc2c1
*O[Si](C)(C)c1ccc2cc3cc([Si](*)(C)C)ccc3cc2c1
*O[Si](C)(C)c1ccc2ccc([Si](*)(C)C)cc2c1
*O[Si](C)(C)c1ccc2ccc3c([Si](*)(C)C)ccc4ccc1c2c43
*O[Si](C)(C)c1cccc(C(F)(F)C(F)(F)C(F)(F)c2cccc([Si](*)(C)C)c2)c1
*O[Si](C)(C)c1cccc(C(F)(F)C(F)(F)c2cccc([Si](*)(C)C)c2)c1
*O[Si](C)(C)c1cccc([Si](*)(C)C)c1
*O[Si](C)(C)c1cccc2c([Si](*)(C)C)cccc12
*O[Si](C)(C)c1cccc2c1ccc1c([Si](*)(C)C)cccc12
*O[Si](C)(C=C)O[Si](C)(C)c1ccc([Si](*)(C)C)cc1
*O[Si](C)(C=C)O[Si](C)(C=C)O[Si](C)(C)c1ccc([Si](*)(C)C)cc1
*O[Si](C)(CCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)O[Si](C)(C)c1ccc([Si](*)(C)C)cc1
*O[Si](C)(CCC(F)(F)F)CCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)CC[Si](*)(C)CCC(F)(F)F
*O[Si](C)(CCC(F)(F)F)CCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)CC[Si](*)(C)CCC(F)(F)F
*O[Si](C)(CCC(F)(F)F)CCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)CC[Si](*)(C)CCC(F)(F)F
*O[Si](C)(CCC(F)(F)F)CCC(F)(F)C(F)(F)C(F)(F)C(F)(F)CC[Si](*)(C)CCC(F)(F)F
*O[Si](C)(CCC(F)(F)F)CCC(F)(F)C(F)(F)CC[Si](*)(C)CCC(F)(F)F
*O[Si](C)(CCC(F)(F)F)CCC(F)(F)CC[Si](*)(C)CCC(F)(F)F
*O[Si](C)(CCC(F)(F)F)O[Si](C)(CCC(F)(F)F)O[Si](C)(CCC(F)(F)F)CCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)CC[Si](*)(C)CCC(F)(F)F
*O[Si](C)(Oc1ccc(*)cc1)c1ccccc1
*O[Si](C)(Oc1ccc(C(C)(C)c2ccc(*)cc2)cc1)c1ccccc1
*O[Si](C=C)(C=C)O[Si](C)(C)c1ccc([Si](*)(C)C)cc1
*O[Si](O[Si](C)(C)c1ccc([Si](*)(C)C)cc1)(c1ccccc1)c1ccccc1
*O[Si](Oc1ccc(-c2ccc(*)cc2)cc1)(c1ccccc1)c1ccccc1
*O[Si](Oc1ccc(C2(c3ccc(*)c(C)c3)c3ccccc3-c3ccccc32)cc1C)(c1ccccc1)c1ccccc1
*O[Si](Oc1ccc(C2(c3ccc(*)cc3)c3ccccc3-c3ccccc32)cc1)(c1ccccc1)c1ccccc1
*O[Si](c1ccccc1)(c1ccccc1)c1ccc(-c2ccc([Si](*)(c3ccccc3)c3ccccc3)cc2)cc1
*O[Si](c1ccccc1)(c1ccccc1)c1ccc([Si](*)(c2ccccc2)c2ccccc2)cc1
*O[Si](c1ccccc1)(c1ccccc1)c1cccc(*)c1
*O[Sn](CCCC)(CCCC)OC(=O)/C=C/C(*)=O
*O[Sn](CCCC)(CCCC)OC(=O)CCCCC(*)=O
*O[Sn](CCCC)(CCCC)OC(=O)c1ccc(C(*)=O)cc1
*O[Zn]OC(=O)c1ccccc1C(=O)OCCCCCOC(=O)NCCCCCCNC(=O)OCCCCCOC(=O)c1ccccc1C(*)=O
*O[Zn]OC(=O)c1ccccc1C(=O)OCCCCCOC(=O)Nc1cc(NC(=O)OCCCCCOC(=O)c2ccccc2C(*)=O)ccc1C
*Oc1c(-c2ccccc2)cc(*)cc1-c1ccc(C(C)(C)C)cc1
*Oc1c(-c2ccccc2)cc(*)cc1-c1ccc(C)cc1
*Oc1c(-c2ccccc2)cc(*)cc1-c1cccc(-c2ccccc2)c1-c1ccccc1
*Oc1c(-c2ccccc2)cc(*)cc1-c1cccc(C)c1
*Oc1c(-c2ccccc2)cc(*)cc1-c1cccc2ccccc12
*Oc1c(-c2ccccc2)cc(*)cc1-c1ccccc1
*Oc1c(-c2ccccc2)cc(*)cc1-c1ccccc1-c1ccccc1
*Oc1c(-c2ccccc2)cc(-c2cc(-c3ccccc3)c(OC(=O)CCCCCCCCC(*)=O)c(-c3ccccc3)c2)cc1-c1ccccc1
*Oc1c(-c2ccccc2)cc(Cc2cc(-c3ccccc3)c(OC(=O)CCCCC(*)=O)c(-c3ccccc3)c2)cc1-c1ccccc1
*Oc1c(-c2ccccc2)cc(Cc2cc(-c3ccccc3)c(OC(=O)CCCCCC(*)=O)c(-c3ccccc3)c2)cc1-c1ccccc1
*Oc1c(-c2ccccc2)cc(Cc2cc(-c3ccccc3)c(OC(=O)CCCCCCCCC(*)=O)c(-c3ccccc3)c2)cc1-c1ccccc1
*Oc1c(Br)cc(*)cc1Br
*Oc1c(Br)cc(C(C)(C)c2cc(Br)c(OC(*)=O)c(Br)c2)cc1Br
*Oc1c(Br)cc(C(C)(C)c2cc(Br)c(OC(=O)c3ccc(OCCCCCCCCCCOc4ccc(C(*)=O)cc4)cc3)c(Br)c2)cc1Br
*Oc1c(Br)cc(C(C)(C)c2cc(Br)c(OC(=O)c3cccc(C(*)=O)c3)c(Br)c2)cc1Br
*Oc1c(Br)cc(C(c2cc(Br)c(OC(*)=O)c(Br)c2)(C(F)(F)F)C(F)(F)F)cc1Br
*Oc1c(Br)cc(C2(c3cc(Br)c(OC(*)=O)c(Br)c3)CC3CCC2C3)cc1Br
*Oc1c(Br)cc(S(=O)(=O)c2cc(Br)c(OC(*)=O)c(Br)c2)cc1Br
*Oc1c(C(C)C)cc(*)cc1C(C)C
*Oc1c(C(C)C)cc(C(=O)c2cccc(C(=O)c3ccc(*)cc3)c2)cc1C(C)C
*Oc1c(C)c(C)c(NC(=O)c2ccc(C(=O)Nc3ccc(*)cc3)cc2)c(C)c1C
*Oc1c(C)c(C)c(Oc2ccc(NC(=O)c3ccc(C(=O)Nc4ccc(*)cc4)cc3)cc2)c(C)c1C
*Oc1c(C)cc(*)cc1-c1ccccc1
*Oc1c(C)cc(*)cc1C
*Oc1c(C)cc(*)cc1C(C)CCC
*Oc1c(C)cc(*)cc1C(C)CCCC
*Oc1c(C)cc(*)cc1C(C)CCCCC
*Oc1c(C)cc(*)cc1C(C)CCCCCC
*Oc1c(C)cc(*)cc1C(C)CCCCCCCC
*Oc1c(C)cc(*)cc1C(C)CCCCCCCCCCCC
*Oc1c(C)cc(-c2cc(C)c(OC(=O)CCCCC(*)=O)c(C)c2)cc1C
*Oc1c(C)cc(-c2cc(C)c(OC(=O)CCCCCC(*)=O)c(C)c2)cc1C
*Oc1c(C)cc(-c2cc(C)c(OC(=O)CCCCCCCCC(*)=O)c(C)c2)cc1C
*Oc1c(C)cc(-c2cc(C)c(Oc3ccc(C(=O)c4ccc(*)cc4)cc3)c(C)c2)cc1C
*Oc1c(C)cc(-c2cc(C)c(Oc3ccc(C(=O)c4ccc(*)cc4)cc3)c(CBr)c2)cc1CBr
*Oc1c(C)cc(-c2cc(C)c(Oc3ccc(C(=O)c4ccc5cc(C(=O)c6ccc(*)cc6)ccc5c4)cc3)c(C)c2)cc1C
*Oc1c(C)cc(-c2cc(C)c(Oc3ccc(C(=O)c4ccccc4-c4ccccc4C(=O)c4ccc(*)cc4)cc3)c(C)c2)cc1C
*Oc1c(C)cc(-c2cc(CBr)c(Oc3ccc(C(=O)c4ccc(*)cc4)cc3)c(CBr)c2)cc1CBr
*Oc1c(C)cc(C(=O)c2ccc(*)cc2)cc1C
*Oc1c(C)cc(C(C)(C)c2cc(C)c(OC(*)=O)c(C)c2)cc1C
*Oc1c(C)cc(C(C)(C)c2cc(C)c(OC(*)=O)c(Cl)c2)cc1Cl
*Oc1c(C)cc(C(C)(C)c2cc(C)c(OC(=O)CCCCC(*)=O)c(C)c2)cc1C
*Oc1c(C)cc(C(C)(C)c2cc(C)c(OC(=O)CCCCCC(*)=O)c(C)c2)cc1C
*Oc1c(C)cc(C(C)(C)c2cc(C)c(OC(=O)CCCCCCCCC(*)=O)c(C)c2)cc1C
*Oc1c(C)cc(C(C)(C)c2cc(C)c(OC(=O)c3ccc(C(*)=O)cc3)c(C)c2)cc1C
*Oc1c(C)cc(C(C)(C)c2cc(C)c(OC(=O)c3cccc(C(*)=O)c3)c(C)c2)cc1C
*Oc1c(C)cc(C(C)(C)c2cc(C)c(ON3C(=O)c4ccc(Oc5ccc(C6(c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)CCC(C(C)(C)C)CC6)cc5)cc4C3=O)c(C)c2)cc1C
*Oc1c(C)cc(C(c2cc(C)c(OC(*)=O)c(C)c2)(C(F)(F)F)C(F)(F)F)cc1C
*Oc1c(C)cc(C2(c3cc(C)c(OC(=O)c4ccc(C(*)=O)cc4)c(C)c3)CCC3CCCCC3C2)cc1C
*Oc1c(C)cc(C2(c3cc(C)c(OC(=O)c4cccc(C(*)=O)c4)c(C)c3)CCC3CCCCC3C2)cc1C
*Oc1c(C)cc(C2(c3cc(C)c(OC(=O)c4cccc(C(*)=O)c4)c(C)c3)c3ccccc3-c3ccccc32)cc1C
*Oc1c(C)cc(C2(c3cc(C)c(Oc4ccc(C(=O)c5ccc(*)cc5)cc4)c(C)c3)CCC3CCCCC3C2)cc1C
*Oc1c(C)cc(C2(c3cc(C)c(Oc4ccc(C(=O)c5cccc(C(=O)c6ccc(*)cc6)c5)cc4)c(C)c3)CCC3CCCCC3C2)cc1C
*Oc1c(C)cc(C2(c3cc(C)c(Oc4ccc(C(=O)c5cccc(C(=O)c6ccc(*)cc6)c5)cc4)c(C)c3)c3ccccc3-c3ccccc32)cc1C
*Oc1c(Cl)cc(*)cc1Br
*Oc1c(Cl)cc(*)cc1Cl
*Oc1c(Cl)cc(C(=C(Cl)Cl)c2cc(Cl)c(OC(*)=O)c(Cl)c2)cc1Cl
*Oc1c(Cl)cc(C(C)(C)c2cc(Cl)c(OC(*)=O)c(Cl)c2)cc1Cl
*Oc1c(Cl)cc(C(C)(C)c2cc(Cl)c(OC(=O)CCCCC(*)=O)c(Cl)c2)cc1Cl
*Oc1c(Cl)cc(C(C)(C)c2cc(Cl)c(OC(=O)c3ccc(OCCCCCCCCCCOc4ccc(C(*)=O)cc4)cc3)c(Cl)c2)cc1Cl
*Oc1c(Cl)cc(C(c2cc(Cl)c(OC(*)=O)c(Cl)c2)C(Cl)(Cl)Cl)cc1Cl
*Oc1c(Cl)cc(C(c2cc(Cl)c(OC(*)=O)c(Cl)c2)C2CC3CCC2C3)cc1Cl
*Oc1c(Cl)cc(C2(c3cc(Cl)c(OC(*)=O)c(Cl)c3)CC3CC2C2CCCC32)cc1Cl
*Oc1c(Cl)cc(C2(c3cc(Cl)c(OC(*)=O)c(Cl)c3)CC3CCC2C3)cc1Cl
*Oc1c(Cl)cc(C2(c3cc(Cl)c(OC(*)=O)c(Cl)c3)CCCCC2)cc1Cl
*Oc1c(F)c(F)c(-c2c(F)c(F)c(Oc3ccc(C(c4ccc(*)cc4)(C(F)(F)F)C(F)(F)F)cc3)c(F)c2F)c(F)c1F
*Oc1c(F)c(F)c(-c2c(F)c(F)c(Oc3ccc(Cc4ccc(*)c(C=O)c4)cc3C=O)c(F)c2F)c(F)c1F
*Oc1c(F)c(F)c(COC(c2cccc(C(OCc3c(F)c(F)c(Oc4ccc(Cc5ccc(*)cc5)cc4)c(F)c3F)(C(F)(F)F)C(F)(F)F)c2)(C(F)(F)F)C(F)(F)F)c(F)c1F
*Oc1c(F)c(F)c(S(=O)(=O)c2c(F)c(F)c(Oc3ccc(C(c4ccc(*)cc4)(C(F)(F)F)C(F)(F)F)cc3)c(F)c2F)c(F)c1F
*Oc1c(F)c(F)c(Sc2c(F)c(F)c(Oc3ccc(C(c4ccc(*)cc4)(C(F)(F)F)C(F)(F)F)cc3)c(F)c2F)c(F)c1F
*Oc1c(OC)cc(*)cc1OC
*Oc1c(OC)cc(-c2cc(OC)c(OC(=O)CCCCCCCCC(*)=O)c(OC)c2)cc1OC
*Oc1c(OC)cc(-c2cc(OC)c(OC(=O)c3cccc(C(*)=O)c3)c(OC)c2)cc1OC
*Oc1c([2H])c([2H])c(S(=O)(=O)c2c([2H])c([2H])c(Oc3c([2H])c([2H])c(C(c4c([2H])c([2H])c(*)c([2H])c4[2H])(C([2H])([2H])[2H])C([2H])([2H])[2H])c([2H])c3[2H])c([2H])c2[2H])c([2H])c1[2H]
*Oc1cc(Br)c(C(C)(C)c2c(Br)cc(OC(*)=O)cc2Br)c(Br)c1
*Oc1cc(Br)cc(Br)c1*
*Oc1cc(C(=O)c2ccccc2)c(Oc2ccc(*)cc2)cc1C(=O)c1ccccc1
*Oc1cc(C(C)(C)C)c(Oc2ccc(C(=O)Nc3c(C)cc(C(C)(C)c4cc(C)c(NC(=O)c5ccc(*)cc5)c(C)c4)cc3C)cc2)cc1C(C)(C)C
*Oc1cc(C(C)(C)C)c(Oc2ccc(C(=O)Nc3c(C)cc(Cc4cc(C)c(NC(=O)c5ccc(*)cc5)c(C(C)C)c4)cc3C(C)C)cc2)cc1C(C)(C)C
*Oc1cc(C(C)(C)C)c(Oc2ccc(C(=O)Nc3ccc(C(C)(C)c4ccc(NC(=O)c5ccc(*)cc5)c(C)c4)cc3C)cc2)cc1C(C)(C)C
*Oc1cc(C(C)(C)C)c(Oc2ccc(C(=O)Nc3ccc(C(C)(C)c4ccc(NC(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1C(C)(C)C
*Oc1cc(C(C)(C)C)c(Oc2ccc(C(=O)Nc3ccc(C(c4ccc(NC(=O)c5ccc(*)cc5)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc2)cc1C(C)(C)C
*Oc1cc(C(C)(C)C)c(Oc2ccc(C(=O)c3ccc(*)cc3)cc2)cc1C(C)(C)C
*Oc1cc(C(C)(C)c2ccccc2)c(OC(=O)c2ccc(C(*)=O)cc2)cc1C(C)(C)c1ccccc1
*Oc1cc(C(C)(C)c2ccccc2)c(OC(=O)c2cccc(C(*)=O)c2)cc1C(C)(C)c1ccccc1
*Oc1cc(C)c(*)c(C)c1Br
*Oc1cc(C)c(C(c2cc(C(C)C)c(Oc3ccc(C(=O)c4ccc(*)cc4)cc3)cc2C)c2ccccc2C(=O)O)cc1C(C)C
*Oc1cc(C)cc(OC(=O)c2cccc(C(*)=O)c2)c1
*Oc1cc(CC)cc(OC(=O)c2cccc(C(*)=O)c2)c1
*Oc1cc(CCC)cc(OC(=O)c2cccc(C(*)=O)c2)c1
*Oc1cc(CCCC)cc(OC(=O)c2cccc(C(*)=O)c2)c1
*Oc1cc(CCCCCC)cc(OC(=O)c2cccc(C(*)=O)c2)c1
*Oc1cc(CCCCCCCC)cc(OC(=O)c2cccc(C(*)=O)c2)c1
*Oc1cc(CCCCCCCCC)cc(OC(=O)c2cccc(C(*)=O)c2)c1
*Oc1cc(CCCCCCCCCCC)cc(OC(=O)c2cccc(C(*)=O)c2)c1
*Oc1cc(CCCCCCCCCCCCC)cc(OC(=O)c2cccc(C(*)=O)c2)c1
*Oc1cc(Cl)c(C(C)(C)c2c(Cl)cc(OC(*)=O)cc2Cl)c(Cl)c1
*Oc1cc(Cl)cc(Cl)c1*
*Oc1cc(OC(=O)c2ccc(C(*)=O)cc2)cc(C(=O)OCCN(CC)c2ccc(/N=N/c3ccc([N+](=O)[O-])cc3)cc2)c1
*Oc1cc(OC(=O)c2ccc(C)cc2)c(OC(=O)CCCCCCCCCCCCCCC(*)=O)cc1OC(=O)c1ccc(C)cc1
*Oc1cc(OC(=O)c2ccc(OC)cc2)c(OC(=O)CCCC(*)=O)cc1OC(=O)c1ccc(OC)cc1
*Oc1cc(OC(=O)c2ccc(OC)cc2)c(OC(=O)CCCCCCCCCCCCCCC(*)=O)cc1OC(=O)c1ccc(OC)cc1
*Oc1cc(OC(=O)c2ccc(OCC(C)CC)cc2)c(OC(=O)CCCC(*)=O)cc1OC(=O)c1ccc(OCC(C)CC)cc1
*Oc1cc(OC(=O)c2ccc(OCC)cc2)c(OC(=O)CCCC(*)=O)cc1OC(=O)c1ccc(OCC)cc1
*Oc1cc(OC(=O)c2ccc(OCC)cc2)c(OC(=O)CCCCCCCCCCCCCCC(*)=O)cc1OC(=O)c1ccc(OCC)cc1
*Oc1cc(OC(=O)c2ccc(OCCC)cc2)c(OC(=O)CCCC(*)=O)cc1OC(=O)c1ccc(OCCC)cc1
*Oc1cc(OC(=O)c2ccc(OCCC)cc2)c(OC(=O)CCCCCCCCCCCCCCC(*)=O)cc1OC(=O)c1ccc(OCCC)cc1
*Oc1cc(OC(=O)c2ccc(OCCCC)cc2)c(OC(=O)C(C)(C)CCC(*)=O)cc1OC(=O)c1ccc(OCCCC)cc1
*Oc1cc(OC(=O)c2ccc(OCCCC)cc2)c(OC(=O)C(C)CCC(*)=O)cc1OC(=O)c1ccc(OCCCC)cc1
*Oc1cc(OC(=O)c2ccc(OCCCC)cc2)c(OC(=O)CCCC(*)=O)cc1OC(=O)c1ccc(OCCCC)cc1
*Oc1cc(OC(=O)c2ccc(OCCCC)cc2)c(OC(=O)CCCCCC(*)=O)cc1OC(=O)c1ccc(OCCCC)cc1
*Oc1cc(OC(=O)c2ccc(OCCCC)cc2)c(OC(=O)CCCCCCCC(*)=O)cc1OC(=O)c1ccc(OCCCC)cc1
*Oc1cc(OC(=O)c2ccc(OCCCCC)cc2)c(OC(=O)CCCC(*)=O)cc1OC(=O)c1ccc(OCCCCC)cc1
*Oc1cc(OC(=O)c2ccc(OCCCCCCCCCCCC)cc2)c(OC(=O)CCCC(*)=O)cc1OC(=O)c1ccc(OCCCCCCCCCCCC)cc1
*Oc1cc(OC(=O)c2ccc(Oc3ccc(C4(c5ccc(Oc6ccc(C(*)=O)cc6)cc5)CCC(C(C)(C)C)CC4)cc3)cc2)ccc1C12CC3CC(CC(C3)C1)C2
*Oc1cc(OCCOC)c(OC(=O)c2ccc(C(*)=O)cc2-c2ccccc2)cc1OCCOC
*Oc1cc(OCCOC)c(OC(=O)c2ccc(C(*)=O)cc2OCCOCC)cc1OCCOC
*Oc1cc(Oc2ccc(C(C)(C)c3ccc(*)cc3)cc2)cc(C(=O)Oc2ccc(/N=N/c3ccc(C#N)cc3)cc2)c1
*Oc1cc(Oc2ccc(C(C)(C)c3ccc(*)cc3)cc2)cc([N+](=O)[O-])c1
*Oc1cc(Oc2ccc(NC(=O)c3cc(NC(=O)C45CC6CC(CC(C6)C4)C5)cc(C(=O)Nc4ccc(*)cc4)c3)cc2)ccc1C12CC3CC(CC(C3)C1)C2
*Oc1cc(Oc2cccc(NC(=O)c3cc(Oc4ccc(C(=O)O)c(C(=O)Nc5cccc(*)c5)c4)ccc3C(=O)O)c2)ccc1C#N
*Oc1cc2ccccc2cc1OC(=O)c1ccc(Oc2ccc(C(*)=O)cc2)cc1
*Oc1ccc(*)cc1
*Oc1ccc(-c2cc(-c3ccccc3)c(-c3ccc(OC(=O)c4ccc(C(*)=O)cc4-c4ccccc4)cc3)c(-c3ccccc3)c2-c2ccccc2)cc1
*Oc1ccc(-c2ccc(-c3cc(-c4ccccc4)c(-c4ccc(-c5ccc(OC(=O)c6ccc(C(*)=O)cc6-c6ccccc6)cc5)cc4)c(-c4ccccc4)c3-c3ccccc3)cc2)cc1
*Oc1ccc(-c2ccc(-c3ccc(Oc4ccc(C(=O)c5cccc(C(=O)c6ccc(*)cc6)c5)cc4)cc3)cc2)cc1
*Oc1ccc(-c2ccc(-c3ccc(Oc4ccc(C(C)(C)c5ccc(*)cc5)cc4)c(C(F)(F)F)c3)cc2)cc1C(F)(F)F
*Oc1ccc(-c2ccc(-c3ccc(Oc4ccc(C(C)(C)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(-c2ccc(-c3ccc(Oc4ccc(C(c5ccc(*)cc5)(C(F)(F)F)C(F)(F)F)cc4)c(C(F)(F)F)c3)cc2)cc1C(F)(F)F
*Oc1ccc(-c2ccc(/N=N/c3ccc(C(*)=O)cc3)cc2)cc1
*Oc1ccc(-c2ccc(NC(=O)c3cc(C(=O)Nc4ccc(-c5ccc(Oc6ccc(C(C)(C)c7ccc(*)cc7)cc6)c(C(F)(F)F)c5)cc4)cc(C(C)(C)C)c3)cc2)cc1C(F)(F)F
*Oc1ccc(-c2ccc(NC(=O)c3cc(C(=O)Nc4ccc(-c5ccc(Oc6ccc(C(c7ccc(*)cc7)(C(F)(F)F)C(F)(F)F)cc6)c(C(F)(F)F)c5)cc4)cc(C(C)(C)C)c3)cc2)cc1C(F)(F)F
*Oc1ccc(-c2ccc(OC(*)(Oc3ccccc3)Oc3ccccc3)c(C)c2)cc1C
*Oc1ccc(-c2ccc(OC(*)(Oc3ccccc3)Oc3ccccc3)cc2)cc1
*Oc1ccc(-c2ccc(OC(=O)/C=C/c3ccc(/C=C/C(*)=O)cc3)cc2)cc1
*Oc1ccc(-c2ccc(OC(=O)CCCCCCCCCCCCC(*)=O)cc2)cc1
*Oc1ccc(-c2ccc(OC(=O)Nc3cccc(P(=O)(c4ccccc4)c4cccc(NC(*)=O)c4)c3)cc2)cc1
*Oc1ccc(-c2ccc(OC(=O)OC3C(C)(C)C(OC(*)=O)C3(C)C)cc2)cc1
*Oc1ccc(-c2ccc(OC(=O)c3cc(/N=N/c4ccc(OCC)cc4)ccc3-c3ccc(/N=N/c4ccc(OCC)cc4)cc3C(*)=O)cc2)cc1
*Oc1ccc(-c2ccc(OC(=O)c3cc(CCCCC)cc(C(*)=O)c3)cc2)cc1
*Oc1ccc(-c2ccc(OC(=O)c3cc(OCCCCCC)c(C(*)=O)cc3OCCCCCC)cc2)cc1
*Oc1ccc(-c2ccc(OC(=O)c3cc(OCCCc4ccccc4)c(C(*)=O)cc3OCCCc3ccccc3)cc2)cc1
*Oc1ccc(-c2ccc(OC(=O)c3ccc(-c4ccc(C(*)=O)cc4)cc3-c3ccccc3)cc2)cc1
*Oc1ccc(-c2ccc(OC(=O)c3ccc(-c4ccc(C(*)=O)cc4C(F)(F)F)c(C(F)(F)F)c3)cc2C(=O)OCCCCCCCCCCCOc2ccc(-c3ccc(C#N)cc3)cc2)c(C(=O)OCCCCCCCCCCCOc2ccc(-c3ccc(C#N)cc3)cc2)c1
*Oc1ccc(-c2ccc(OC(=O)c3ccc(C(*)=O)cc3)cc2)cc1
*Oc1ccc(-c2ccc(OC(=O)c3ccc(C(*)=O)cc3)cc2)cc1-c1ccccc1
*Oc1ccc(-c2ccc(OC(=O)c3ccc(C(*)=O)cc3Sc3ccc4ccccc4c3)cc2)cc1
*Oc1ccc(-c2ccc(OC(=O)c3ccc(C(=O)c4ccc(C(*)=O)cc4)cc3)c(-c3ccccc3)c2)cc1-c1ccccc1
*Oc1ccc(-c2ccc(OC(=O)c3ccc(Oc4ccc(C5(c6ccc(Oc7ccc(C(*)=O)cc7)cc6)CCC(C(C)(C)C)CC5)cc4)cc3)cc2C)c(C)c1
*Oc1ccc(-c2ccc(OC(=O)c3cccc(C(*)=O)c3)cc2)cc1
*Oc1ccc(-c2ccc(OC3(F)C(*)(F)C(F)(F)C3(F)F)cc2)cc1
*Oc1ccc(-c2ccc(Oc3c(F)c(F)c(COC(c4cccc(C(OCc5c(F)c(F)c(*)c(F)c5F)(C(F)(F)F)C(F)(F)F)c4)(C(F)(F)F)C(F)(F)F)c(F)c3F)cc2)cc1
*Oc1ccc(-c2ccc(Oc3ccc(*)cc3)c3ccccc23)c2ccccc12
*Oc1ccc(-c2ccc(Oc3ccc(-c4ccc(-c5ccc(*)c(C(F)(F)F)c5)cc4)cc3C(F)(F)F)cc2)cc1
*Oc1ccc(-c2ccc(Oc3ccc(/C(=N/c4ccc(/N=C(\c5ccccc5)c5ccc(*)cc5)cc4)c4ccccc4)cc3)cc2)cc1
*Oc1ccc(-c2ccc(Oc3ccc(C(=O)c4c(C(=O)c5ccc(*)cc5)c(-c5ccc(F)cc5)c(-c5ccc(F)cc5)c(-c5ccc(F)cc5)c4-c4ccc(F)cc4)cc3)cc2)cc1
*Oc1ccc(-c2ccc(Oc3ccc(C(=O)c4c(C(=O)c5ccc(*)cc5)c(-c5ccccc5)c(-c5ccc6ccccc6c5)c(-c5ccc6ccccc6c5)c4-c4ccccc4)cc3)cc2)cc1
*Oc1ccc(-c2ccc(Oc3ccc(C(=O)c4ccc(*)c(S(=O)(=O)O)c4)cc3S(=O)(=O)O)cc2)cc1
*Oc1ccc(-c2ccc(Oc3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(-c2ccc(Oc3ccc(C(=O)c4ccc(C(=O)c5ccc(*)c(C(F)(F)F)c5)cc4)cc3C(F)(F)F)cc2)cc1
*Oc1ccc(-c2ccc(Oc3ccc(C(=O)c4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(-c2ccc(Oc3ccc(C(=O)c4ccc5cc(C(=O)c6ccc(*)cc6)ccc5c4)cc3)cc2)cc1
*Oc1ccc(-c2ccc(Oc3ccc(C(=O)c4cccc(C(=O)c5ccc(*)cc5)c4)cc3)cc2)cc1
*Oc1ccc(-c2ccc(Oc3ccc(C(=O)c4cccc5cccc(C(=O)c6ccc(*)cc6)c45)cc3)cc2)cc1
*Oc1ccc(-c2ccc(Oc3ccc(C(=O)c4ccccc4-c4ccccc4C(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(-c2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(-c2ccc(Oc3ccc(Oc4cc(Oc5ccc(*)cc5)ccc4CCCCCC)cc3)c3ccccc23)c2ccccc12
*Oc1ccc(-c2ccc(Oc3ccc(Oc4ccc(Oc5ccc(*)cc5)cc4)cc3)c3ccccc23)c2ccccc12
*Oc1ccc(-c2ccc(Oc3ccc(Oc4ccc(S(=O)(=O)c5ccc(Oc6ccc(*)cc6)cc5)cc4)cc3)c3ccccc23)c2ccccc12
*Oc1ccc(-c2ccc(Oc3ccc(Oc4cccc(Oc5ccc(*)cc5)c4C)cc3)c3ccccc23)c2ccccc12
*Oc1ccc(-c2ccc(Oc3ccc4ccccc4c3-c3c(*)ccc4ccccc34)c3ccccc23)c2ccccc12
*Oc1ccc(-c2ccc(Oc3cccc(*)n3)cc2)cc1
*Oc1ccc(/C(=C/c2cc(OC)c(/C=C(/c3ccc(*)cc3)c3ccc(C(F)(F)F)cc3)cc2OC)c2ccc(C(F)(F)F)cc2)cc1
*Oc1ccc(/C(=C/c2cc(OC)c(/C=C(/c3ccc(*)cc3)c3ccc(F)cc3)cc2OC)c2ccc(F)cc2)cc1
*Oc1ccc(/C(=C/c2ccc(-c3ccc(/C=C(\c4ccccc4)c4ccc(OC(=O)CCCCC(*)=O)cc4)cc3)cc2)c2ccccc2)cc1
*Oc1ccc(/C(=C/c2ccc(-c3ccc(/C=C(\c4ccccc4)c4ccc(OC(=O)CCCCCCCCC(*)=O)cc4)cc3)cc2)c2ccccc2)cc1
*Oc1ccc(/C(=C/c2ccc(-c3ccc(/C=C(\c4ccccc4)c4ccc(OC(=O)c5ccc(C(*)=O)cc5)cc4)cc3)cc2)c2ccccc2)cc1
*Oc1ccc(/C(=C/c2ccc(-c3ccc(/C=C(\c4ccccc4)c4ccc(OC(=O)c5cccc(C(*)=O)c5)cc4)cc3)cc2)c2ccccc2)cc1
*Oc1ccc(/C(=N/c2ccc(/N=C(\c3ccccc3)c3ccc(Oc4ccc(C(C)(C)c5ccc(*)cc5)cc4)cc3)cc2)c2ccccc2)cc1
*Oc1ccc(/C(=N/c2ccc(/N=C(\c3ccccc3)c3ccc(Oc4ccc(C(c5ccccc5)(c5ccccc5)c5ccc(*)cc5)cc4)cc3)cc2)c2ccccc2)cc1
*Oc1ccc(/C(C)=N/c2ccc(/N=C(\C)c3ccc(Oc4ccc(C(C)(C)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(/C(C)=N/c2ccc(/N=C(\C)c3ccc(Oc4ccc(C(c5ccccc5)(c5ccccc5)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(/C=C/C(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)c(C)c4)cc3C)cc2)cc1
*Oc1ccc(/C=C/C(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(/C=C/C(=O)c2ccc(Oc3ccc(C(C)(c4ccccc4)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(/C=C/c2cc(OCCCCCCCC)c(/C=C/c3ccc(Oc4cc5c(=O)n(CCCCCCCC)c(=O)c6ccc7c8c(*)cc9c(=O)n(CCCCCCCC)c(=O)c%10ccc(c4c7c65)c8c%109)cc3)cc2OCCCCCCCC)cc1
*Oc1ccc(/C=C2\CC/C(=C\c3ccc(OC(=O)c4ccc(/N=C/c5ccc(/C=N/c6ccc(C(*)=O)cc6)cc5)cc4)c(OC)c3)C2=O)cc1OC
*Oc1ccc(/C=C2\CC/C(=C\c3ccc(OC(=O)c4ccc(/N=C/c5ccc(/C=N/c6ccc(C(*)=O)cc6)cc5)cc4)cc3)C2=O)cc1
*Oc1ccc(/C=C2\CCC/C(=C\c3ccc(OC(=O)CCCCCCCCC(*)=O)c(OC)c3)C2=O)cc1OC
*Oc1ccc(/C=C2\CCC/C(=C\c3ccc(OC(=O)c4cccc(C(*)=O)c4)c(OC)c3)C2=O)cc1OC
*Oc1ccc(/C=N/N=C/c2ccc(Oc3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(/C=N/N=C/c2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(/C=N/N=C/c2ccc(Oc3ccc(C(c4ccc(*)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc2)cc1
*Oc1ccc(/C=N/c2ccc(/N=C/c3ccc(Oc4ccc(C(c5ccccc5)(c5ccccc5)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(/N=C(\C)c2ccc(Oc3ccc(C(C)(C)c4ccc(Oc5ccc(/C(C)=N/c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(/N=C(\C)c2ccc(Oc3ccc(C(c4ccccc4)(c4ccccc4)c4ccc(Oc5ccc(/C(C)=N/c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(/N=C(\c2ccccc2)c2ccc(Oc3ccc(-c4ccc(Oc5ccc(/C(=N/c6ccc(*)cc6)c6ccccc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(/N=C(\c2ccccc2)c2ccc(Oc3ccc(C(C)(C)c4ccc(Oc5ccc(/C(=N/c6ccc(*)cc6)c6ccccc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(/N=C(\c2ccccc2)c2ccc(Oc3ccc(C(c4ccccc4)(c4ccccc4)c4ccc(Oc5ccc(/C(=N/c6ccc(*)cc6)c6ccccc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(/N=C/C=N/c2ccc(OC(=O)NC3CC(C)(C)CC(C)(CNC(*)=O)C3)cc2)cc1
*Oc1ccc(/N=C/C=N/c2ccc(OC(=O)NCCCCCCNC(*)=O)cc2)cc1
*Oc1ccc(/N=C/C=N/c2ccc(OC(=O)Nc3cc(NC(*)=O)ccc3C)cc2)cc1
*Oc1ccc(/N=C/C=N/c2ccc(OC(=O)Nc3ccc(Cc4ccc(NC(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(/N=C/CCC/C=N/c2ccc(OC(=O)NC3CC(C)(C)CC(C)(CNC(*)=O)C3)cc2)cc1
*Oc1ccc(/N=C/CCC/C=N/c2ccc(OC(=O)NCCCCCCNC(*)=O)cc2)cc1
*Oc1ccc(/N=C/CCC/C=N/c2ccc(OC(=O)Nc3cc(NC(*)=O)ccc3C)cc2)cc1
*Oc1ccc(/N=C/CCC/C=N/c2ccc(OC(=O)Nc3ccc(Cc4ccc(NC(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(/N=C/c2ccc(N(c3ccccc3)c3ccc(/C=N/c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(/N=C/c2ccc(Oc3ccc(C(C)(C)c4ccc(Oc5ccc(/C=N/c6ccc(Oc7ccc(-c8ccc(*)cc8)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(/N=C/c2ccc(Oc3ccc(C(c4ccc(Oc5ccc(/C=N/c6ccc(Oc7ccc(-c8ccc(*)cc8)cc7)cc6)cc5)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc2)cc1
*Oc1ccc(/N=C/c2ccc(Oc3ccc(C(c4ccccc4)(c4ccccc4)c4ccc(Oc5ccc(/C=N/c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(/N=N/c2c(C)cc(Cc3cc(C)c(/N=N/c4ccc(OC(=O)c5ccc(C(*)=O)cc5)cc4)c(C)c3[N+](=O)[O-])c([N+](=O)[O-])c2C)cc1
*Oc1ccc(/N=N/c2ccc(Cc3ccc(/N=N/c4ccc(OC(=O)c5ccc(/N=N/c6ccc(C(*)=O)cc6)cc5)cc4)cc3[N+](=O)[O-])c([N+](=O)[O-])c2)cc1
*Oc1ccc(/N=N/c2ccc(Cc3ccc(/N=N/c4ccc(OC(=O)c5ccc(C(*)=O)cc5)cc4)cc3[N+](=O)[O-])c([N+](=O)[O-])c2)cc1
*Oc1ccc(/N=N/c2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(/N=N/c2ccc(S(=O)(=O)c3ccc(/N=N/c4ccc(OC(=O)c5ccc(/N=N/c6ccc(C(*)=O)cc6)cc5)cc4)cc3[N+](=O)[O-])c([N+](=O)[O-])c2)cc1
*Oc1ccc(C(*)=O)cc1
*Oc1ccc(C(*)=O)cc1CCCCC
*Oc1ccc(C(=C(Cl)Cl)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(=O)CNc2ccc(S(=O)(=O)c3ccc(NCC(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(C(=O)NCC(C)(C)CCCNC(=O)c2ccc(*)cc2)cc1
*Oc1ccc(C(=O)NNC(=O)c2ccc(*)cc2)cc1
*Oc1ccc(C(=O)NNC(=O)c2ccc(C(=O)NNC(=O)c3ccc(OC(=O)c4cccc(C(*)=O)c4)cc3)cc2)cc1
*Oc1ccc(C(=O)NNC(=O)c2ccc(C(=O)NNC(=O)c3ccc(OC(=O)c4cccc(C(*)=O)c4)cc3)cc2Oc2ccccc2)cc1
*Oc1ccc(C(=O)NNC(=O)c2cccc(C(=O)NNC(=O)c3ccc(OC(=O)c4cccc(C(*)=O)c4)cc3)c2)cc1
*Oc1ccc(C(=O)Nc2cc(C(c3ccc(C)c(NC(=O)c4ccc(OC(=O)c5ccc(C(*)=O)cc5)cc4)c3)(C(F)(F)F)C(F)(F)F)ccc2C)cc1
*Oc1ccc(C(=O)Nc2cc(C(c3ccc(C)c(NC(=O)c4ccc(OC(=O)c5cccc(C(*)=O)c5)cc4)c3)(C(F)(F)F)C(F)(F)F)ccc2C)cc1
*Oc1ccc(C(=O)Nc2cc(NC(=O)c3ccc(*)cc3)cc(-c3nc4ccccc4[nH]3)c2)cc1
*Oc1ccc(C(=O)Nc2cc(NC(=O)c3ccc(*)cc3)cc(C(=O)OCCOC(=O)/C=C/c3ccc(N(C)C)cc3)c2)cc1
*Oc1ccc(C(=O)Nc2cc(SCCC#N)c(NC(=O)c3ccc(*)cc3)cc2SCCC#N)cc1
*Oc1ccc(C(=O)Nc2ccc(Br)cc2-c2ccc(NC(=O)c3ccc(*)cc3)cc2)cc1
*Oc1ccc(C(=O)Nc2ccc(C(c3ccc(NC(=O)c4ccc(*)cc4)cc3)(P3(=O)Oc4ccccc4-c4ccccc43)P3(=O)Oc4ccccc4-c4ccccc43)cc2)cc1
*Oc1ccc(C(=O)Nc2ccc(C(c3ccc(NC(=O)c4ccc(OC(=O)c5ccc(C(*)=O)cc5)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*Oc1ccc(C(=O)Nc2ccc(C(c3ccc(NC(=O)c4ccc(OC(=O)c5cccc(C(*)=O)c5)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*Oc1ccc(C(=O)Nc2ccc(Cc3ccc(NC(=O)c4ccc(Oc5ccc(C(=O)c6ccc(S(=O)(=O)c7ccc(C(=O)c8ccc(*)cc8)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(C(=O)Nc2ccc(Cc3ccc(NC(=O)c4ccc(Oc5nc(*)nc(Sc6ccccc6)n5)cc4)cc3)cc2)cc1
*Oc1ccc(C(=O)Nc2ccc(S(=O)(=O)c3ccc(NC(=O)c4ccc(Oc5ccc(C(=O)c6ccc(S(=O)(=O)c7ccc(C(=O)c8ccc(*)cc8)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(C(=O)Nc2ccc(S(=O)(=O)c3ccc(NC(=O)c4ccc(Oc5nc(*)nc(Sc6ccccc6)n5)cc4)cc3)cc2)cc1
*Oc1ccc(C(=O)Nc2ccccc2-c2ccc(NC(=O)c3ccc(*)cc3)cc2)cc1
*Oc1ccc(C(=O)O)c(C(=O)Nc2ccc(NC(=O)c3cc(*)ccc3C(=O)O)cc2)c1
*Oc1ccc(C(=O)OC(C)COC(C)COC(C)COC(=O)c2ccc(OC(=O)c3ccc(C(*)=O)cc3)cc2)cc1
*Oc1ccc(C(=O)OCC(C)(C)COC(=O)c2ccc(*)cc2)cc1
*Oc1ccc(C(=O)OCCCCCCCOC(=O)c2ccc(OC(=O)c3ccc(C(*)=O)cc3)cc2)cc1
*Oc1ccc(C(=O)OCCCCCCOC(=O)c2ccc(*)cc2)cc1
*Oc1ccc(C(=O)OCCCCCOC(=O)c2ccc(*)cc2)cc1
*Oc1ccc(C(=O)OCCCCOC(=O)c2ccc(*)cc2)cc1
*Oc1ccc(C(=O)OCCCOC(=O)c2ccc(*)cc2)cc1
*Oc1ccc(C(=O)OCCN(CCOC(=O)c2ccc(OC3(F)C(*)(F)C(F)(F)C3(F)F)cc2)c2ccc(/C=C/C3=CC(=C(C#N)C#N)CC(C)(C)C3)cc2)cc1
*Oc1ccc(C(=O)OCCOC(=O)c2ccc(*)cc2)cc1
*Oc1ccc(C(=O)OCCOCCOC(=O)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(=O)OCCOCCOCCOC(=O)c2ccc(OC(=O)c3ccc(C(*)=O)cc3)cc2)cc1
*Oc1ccc(C(=O)OCCOCCOCCOCCOC(=O)c2ccc(OC(=O)c3ccc(C(*)=O)cc3)cc2)cc1
*Oc1ccc(C(=O)c2ccc(*)cc2)cc1
*Oc1ccc(C(=O)c2ccc(C(=O)c3ccc(*)c(C(C)C)c3)cc2)cc1C
*Oc1ccc(C(=O)c2ccc(C(=O)c3ccc(*)cc3)cc2)cc1
*Oc1ccc(C(=O)c2ccc(C(=O)c3ccc(Oc4ccc(Cc5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(C(=O)c2ccc(C(=O)c3ccc(Oc4cccc(Cc5cccc(*)c5)c4)cc3)cc2)cc1
*Oc1ccc(C(=O)c2ccc(NC(=O)c3ccc(C(=O)Nc4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(C(=O)c2ccc(NC(=O)c3cccc(C(=O)Nc4ccc(C(=O)c5ccc(*)cc5)cc4)c3)cc2)cc1
*Oc1ccc(C(=O)c2ccc(OC(=O)OC3C(C)(C)C(OC(*)=O)C3(C)C)cc2)cc1
*Oc1ccc(C(=O)c2ccc(OC(=O)c3ccc(C(C)(C)c4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(C(=O)c2ccc(Oc3ccc(C(=O)c4c(C(=O)c5ccc(*)cc5)c(-c5ccc(F)cc5)c(-c5ccc(F)cc5)c(-c5ccc(F)cc5)c4-c4ccc(F)cc4)cc3)cc2)cc1
*Oc1ccc(C(=O)c2ccc(Oc3ccc(C(=O)c4c(C(=O)c5ccc(*)cc5)c(-c5ccccc5)c(-c5ccc6ccccc6c5)c(-c5ccc6ccccc6c5)c4-c4ccccc4)cc3)cc2)cc1
*Oc1ccc(C(=O)c2ccc(Oc3ccc(C(=O)c4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(C(=O)c2ccc(Oc3ccc(C(=O)c4ccc(Oc5ccc(Cc6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(C(=O)c2ccc(Oc3ccc(C(=O)c4ccc(Oc5cccc(Cc6cccc(*)c6)c5)cc4)cc3)cc2)cc1
*Oc1ccc(C(=O)c2ccc(Oc3ccc(C(=O)c4cccc(C(=O)c5ccc(*)cc5)c4)cc3)cc2)cc1
*Oc1ccc(C(=O)c2ccc(Oc3ccc(C(c4ccc(*)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc2)c2ccccc12
*Oc1ccc(C(=O)c2ccc(Oc3ccc(C4(c5ccc(*)cc5)c5ccccc5-c5ccccc54)cc3)cc2)c2ccccc12
*Oc1ccc(C(=O)c2ccc(Oc3ccc4c(=O)n5c6cc(-c7ccc8c(c7)nc7c9ccc(*)c%10cccc(c(=O)n87)c%109)ccc6nc5c5cccc3c45)cc2)cc1
*Oc1ccc(C(=O)c2cccc(-c3cccc(C(=O)c4ccc(*)cc4)c3)c2)cc1
*Oc1ccc(C(=O)c2cccc(C(=O)c3ccc(*)c(C(C)C)c3)c2)cc1C
*Oc1ccc(C(=O)c2cccc(C(=O)c3ccc(*)c(C(C)C)c3)c2)cc1CC
*Oc1ccc(C(=O)c2cccc(C(=O)c3ccc(*)c(CC)c3)c2)cc1CC
*Oc1ccc(C(=O)c2cccc(C(=O)c3ccc(*)cc3)c2)cc1
*Oc1ccc(C(=O)c2cccc(C(=O)c3ccc(Oc4ccc(Cc5ccc(*)cc5)cc4)cc3)c2)cc1
*Oc1ccc(C(=O)c2cccc(C(=O)c3ccc(Oc4cccc(Cc5cccc(*)c5)c4)cc3)c2)cc1
*Oc1ccc(C(=O)c2cccc(NC(=O)CCCCCCCCC(=O)Nc3ccc(C(=O)c4ccc(*)cc4)cc3)c2)cc1
*Oc1ccc(C(=O)c2cccc(NC(=O)CCCCCCCCC(=O)Nc3cccc(C(=O)c4ccc(*)cc4)c3)c2)cc1
*Oc1ccc(C(=O)c2cccc(NC(=O)c3ccc(C(=O)Nc4cccc(C(=O)c5ccc(*)cc5)c4)cc3)c2)cc1
*Oc1ccc(C(=O)c2cccc(NC(=O)c3cccc(C(=O)Nc4ccc(C(=O)c5ccc(*)cc5)cc4)c3)c2)cc1
*Oc1ccc(C(=O)c2cccc(NC(=O)c3cccc(C(=O)Nc4cccc(C(=O)c5ccc(*)cc5)c4)c3)c2)cc1
*Oc1ccc(C(=O)c2ccccc2)c(-c2cc(Oc3ccc(-c4ccc(*)cc4)cc3)ccc2C(=O)c2ccccc2)c1
*Oc1ccc(C(=O)c2ccccc2)c(-c2cc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)ccc2C(=O)c2ccccc2)c1
*Oc1ccc(C(=O)c2ccccc2)c(-c2cc(Oc3ccc(C(c4ccc(*)cc4)(C(F)(F)F)C(F)(F)F)cc3)ccc2C(=O)c2ccccc2)c1
*Oc1ccc(C(C#N)(c2ccccc2)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(C)(C)C)cc1Oc1ccc(S(=O)(=O)c2ccc(*)cc2)cc1
*Oc1ccc(C(C)(C)C)cc1Oc1ccc(S(=O)(=O)c2ccc(*)cc2)cc1
*Oc1ccc(C(C)(C)c2cc(Cl)c(OC(*)=O)c(Cl)c2)cc1
*Oc1ccc(C(C)(C)c2ccc(C(C)(C)c3ccc(OC(*)=O)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(C(C)(C)c3ccc(Oc4ccc(C(=O)Nc5ccc(Cc6ccc(NC(=O)c7ccc(*)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(C(C)(C)c3ccc(Oc4ccc(C(=O)Nc5ccc(NC(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(C(C)(C)c3ccc(Oc4ccc(C(=O)Nc5cccc(NC(=O)c6ccc(*)cc6)c5)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(*)(Oc3ccccc3)Oc3ccccc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(*)=O)c(C(C)C)c2)cc1C(C)C
*Oc1ccc(C(C)(C)c2ccc(OC(*)=O)c(C)c2)cc1C
*Oc1ccc(C(C)(C)c2ccc(OC(*)=O)c(CC)c2)cc1CC
*Oc1ccc(C(C)(C)c2ccc(OC(*)=O)c(Cl)c2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(*)=O)c(Cl)c2)cc1C
*Oc1ccc(C(C)(C)c2ccc(OC(*)=O)c(Cl)c2)cc1Cl
*Oc1ccc(C(C)(C)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(*)=S)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)C3CCC(C(*)=O)CC3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)CC(C)CCC(*)=O)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)CCCCC(*)=O)c(C)c2)cc1C
*Oc1ccc(C(C)(C)c2ccc(OC(=O)CCCCC(*)=O)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)CN(CC(*)=O)c3ccc(/N=N/c4ccc([N+](=O)[O-])cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)NC(=O)c3cc(C(=O)NC(*)=O)cc(C(C)(C)C)c3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)Nc3cccc(P(=O)(c4ccccc4)c4cccc(NC(*)=O)c4)c3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)OC3C(C)(C)C(OC(*)=O)C3(C)C)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)OCC3CCC(COC(*)=O)CC3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)OCCCCCOC(*)=O)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)OCCCCOC(*)=O)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)OCCCOC(*)=O)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)OCCN(CCOC(*)=O)c3ccc(OC)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)OCCN(CCOC(*)=O)c3ccc4[nH]c5ccccc5c4c3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)OCCN(CCOC(*)=O)c3ccccc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)Oc3ccc(Cc4ccc(OC(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)SCCCCCCSC(*)=O)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)SCCCSC(*)=O)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)Sc3ccc(C(C)(C)c4ccc(SC(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(C(*)=O)cc3)c(C(C)C)c2)cc1C(C)C
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(C(*)=O)cc3)c(C(C)CC)c2)cc1C(C)CC
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(C(*)=O)cc3)c(C)c2)cc1C
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(C(*)=O)cc3)c(CCCC)c2)cc1CCCC
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(C(*)=O)cc3)c(Cl)c2)cc1Cl
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(C(*)=O)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(C(=O)c4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(C(C)(C)c4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(C(C)(CC)c4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(C(c4ccc(C(*)=O)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(CC(*)=O)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(Cc4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(OCCCCCCCCCCOc4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(Oc4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc(S(=O)(=O)c4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc([Si](C)(C)c4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc([Si](c4ccccc4)(c4ccccc4)c4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccc4c(c3)C(C)(c3ccc(C(*)=O)cc3)CC4(C)C)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3cccc(C(*)=O)c3)c(C)c2)cc1C
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3cccc(C(*)=O)c3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3cccc(C(F)(F)C(F)(F)C(F)(F)c4cccc(C(*)=O)c4)c3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3cccc(Oc4cccc(C(*)=O)c4)c3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC(=O)c3ccccc3-c3ccccc3C(*)=O)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OC3(F)C(*)(F)C(F)(F)C3(F)F)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OP3(Oc4ccc(C(=O)OC)cc4)=NP(Oc4ccc(C(=O)OC)cc4)(Oc4ccc(C(=O)OC)cc4)=NP(*)(Oc4ccc(C(=O)OC)cc4)=N3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(OP3(Oc4ccc(C(=O)OCCC)cc4)=NP(Oc4ccc(C(=O)OCCC)cc4)(Oc4ccc(C(=O)OCCC)cc4)=NP(*)(Oc4ccc(C(=O)OCCC)cc4)=N3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3c(F)c(F)c(COC(c4cccc(C(OCc5c(F)c(F)c(*)c(F)c5F)(C(F)(F)F)C(F)(F)F)c4)(C(F)(F)F)C(F)(F)F)c(F)c3F)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(*)nn3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)C(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)Nc4ccc(-c5ccc(NC(=O)c6ccc(*)cc6)cc5C(F)(F)F)c(C(F)(F)F)c4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)Nc4ccc(NC(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)Nc4cccc(NC(=O)c5ccc(*)cc5)c4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4c(C(=O)c5ccc(*)cc5)c(-c5ccc(F)cc5)c(-c5ccc(F)cc5)c(-c5ccc(F)cc5)c4-c4ccc(F)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4c(C(=O)c5ccc(*)cc5)c(-c5ccccc5)c(-c5ccc6ccccc6c5)c(-c5ccc6ccccc6c5)c4-c4ccccc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4c(C)ccc5c(C(=O)c6ccc(*)cc6)c(C)ccc45)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4ccc(*)c(S(=O)(=O)O)c4)cc3S(=O)(=O)O)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4ccc(-c5ccc(C(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4ccc(C(=O)c5ccc(*)c(C(F)(F)F)c5)cc4)cc3C(F)(F)F)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4ccc(C(C)(C)c5ccc(C(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4ccc(C(c5ccc(C(=O)c6ccc(*)cc6)cc5)(C(F)(F)F)C(F)(F)F)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4ccc(Oc5ccc(C(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4ccc(P(=O)(c5ccccc5)c5ccc(C(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4ccc5cc(C(=O)c6ccc(*)cc6)ccc5c4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4cccc(C(=O)c5ccc(*)cc5)c4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4cccc5cccc(C(=O)c6ccc(*)cc6)c45)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)c4ccccc4-c4ccccc4C(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(O)C(O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc4c(=O)n5c6cc(-c7ccc8c(c7)nc7c9ccc(*)c%10cccc(c(=O)n87)c%109)ccc6nc5c5cccc3c45)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc4c(=O)n5c6cc(Oc7ccc8c(c7)nc7c9ccc(*)c%10cccc(c(=O)n87)c%109)ccc6nc5c5cccc3c45)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3ccc4c(c3)C(=O)N(N3C(=O)c5ccc(*)cc5C3=O)C4=O)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3cccc(*)n3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3cncc(*)n3)cc2)cc1
*Oc1ccc(C(C)(C)c2ccc(Oc3nc(*)nc(Cl)n3)cc2)cc1
*Oc1ccc(C(C)(C)c2cccc(C(C)(C)c3ccc(Oc4ccc(C(=O)Nc5ccc(Cc6ccc(NC(=O)c7ccc(*)cc7)cc6)cc5)cc4)cc3)c2)cc1
*Oc1ccc(C(C)(C)c2cccc(C(C)(C)c3ccc(Oc4ccc(C(=O)Nc5ccc(NC(=O)c6ccc(*)cc6)cc5)cc4)cc3)c2)cc1
*Oc1ccc(C(C)(C)c2cccc(C(C)(C)c3ccc(Oc4ccc(C(=O)Nc5cccc(NC(=O)c6ccc(*)cc6)c5)cc4)cc3)c2)cc1
*Oc1ccc(C(C)(CC(C)C)c2ccc(OC(*)=S)cc2)cc1
*Oc1ccc(C(C)(CC)c2ccc(OC(*)=O)c(C)c2)cc1C
*Oc1ccc(C(C)(CC)c2ccc(OC(*)=O)c(Cl)c2)cc1Cl
*Oc1ccc(C(C)(CC)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(C)(CC)c2ccc(OC(*)=S)cc2)cc1
*Oc1ccc(C(C)(CC)c2ccc(OC(=O)OC3C(C)(C)C(OC(*)=O)C3(C)C)cc2)cc1
*Oc1ccc(C(C)(CCC#N)c2ccc(OC(*)=O)c(C)c2)cc1C
*Oc1ccc(C(C)(CCC#N)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(C)(CCC)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(C)(c2ccccc2)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(C)(c2ccccc2)c2ccc(OC(*)=S)cc2)cc1
*Oc1ccc(C(C)(c2ccccc2)c2ccc(OC(=O)OC3C(C)(C)C(OC(*)=O)C3(C)C)cc2)cc1
*Oc1ccc(C(C)(c2ccccc2)c2ccc(OC(=O)c3cccc(C(*)=O)c3)cc2)cc1
*Oc1ccc(C(C)(c2ccccc2)c2ccc(OC(=O)c3ccccc3-c3ccccc3C(*)=O)cc2)cc1
*Oc1ccc(C(C)(c2ccccc2)c2ccc(Oc3c(F)c(F)c(C(=O)c4c(F)c(F)c(*)c(F)c4F)c(F)c3F)cc2)cc1
*Oc1ccc(C(C)(c2ccccc2)c2ccc(Oc3ccc(C(=O)C(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(C(C)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(C)c2ccc(OC(*)=S)cc2)cc1
*Oc1ccc(C(CC)(CC)c2ccc(OC(*)=S)cc2)cc1
*Oc1ccc(C(CC)(CC)c2ccc(OC(=O)c3ccc(C(*)=O)cc3)cc2)cc1
*Oc1ccc(C(CC)(c2ccccc2)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(CC)(c2ccccc2)c2ccc(OC(*)=S)cc2)cc1
*Oc1ccc(C(CC)(c2ccccc2)c2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)cc1
*Oc1ccc(C(CC)(c2ccccc2)c2ccc(OC(=O)c3ccc(C(*)=O)cc3)cc2)cc1
*Oc1ccc(C(CC)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(CC)c2ccc(OC(*)=S)cc2)cc1
*Oc1ccc(C(CCC)(CCC)c2ccc(OC(*)=O)c(C)c2)cc1C
*Oc1ccc(C(CCC)(CCC)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(CCC)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(CCC)c2ccc(OC(*)=S)cc2)cc1
*Oc1ccc(C(CCCC)(CCCC)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(c2ccc(OC(*)=O)cc2)(C(F)(F)Cl)C(F)(F)Cl)cc1
*Oc1ccc(C(c2ccc(OC(*)=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(OC(*)=O)cc2)C(C)C)cc1
*Oc1ccc(C(c2ccc(OC(*)=O)cc2)C(Cl)(Cl)Cl)cc1
*Oc1ccc(C(c2ccc(OC(*)=O)cc2)C(Cl)Cl)cc1
*Oc1ccc(C(c2ccc(OC(*)=O)cc2)C2C3CCC(C3)C2C)cc1
*Oc1ccc(C(c2ccc(OC(*)=O)cc2)C2C3CCC(C3)C2c2ccccc2)cc1
*Oc1ccc(C(c2ccc(OC(*)=O)cc2)C2CC3CC2C2C4CCC(C4)C32)cc1
*Oc1ccc(C(c2ccc(OC(*)=O)cc2)C2CCCCC2)cc1
*Oc1ccc(C(c2ccc(OC(*)=S)cc2)C(C)C)cc1
*Oc1ccc(C(c2ccc(OC(*)=S)cc2)C(CC)CC)cc1
*Oc1ccc(C(c2ccc(OC(*)=S)cc2)c2cccc3ccccc23)cc1
*Oc1ccc(C(c2ccc(OC(=O)Nc3ccc(Cc4ccc(NC(*)=O)cc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(OC(=O)OC3C(C)(C)C(OC(*)=O)C3(C)C)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(OC(=O)c3ccc(C(c4ccc(C(*)=O)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(OC(=O)c3ccc(OCCCCCCCCCCOc4ccc(C(*)=O)cc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(OC(=O)c3ccc(Oc4ccc(C(*)=O)cc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(OC(=O)c3ccccc3-c3ccccc3C(*)=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(OC3(F)C(*)(F)C(F)(F)C3(F)F)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(OP3(Oc4ccc(C(=O)OC)cc4)=NP(Oc4ccc(C(=O)OC)cc4)(Oc4ccc(C(=O)OC)cc4)=NP(*)(Oc4ccc(C(=O)OC)cc4)=N3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(OP3(Oc4ccc(C(=O)OCCC)cc4)=NP(Oc4ccc(C(=O)OCCC)cc4)(Oc4ccc(C(=O)OCCC)cc4)=NP(*)(Oc4ccc(C(=O)OCCC)cc4)=N3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3c(F)c(F)c(C(=O)c4c(F)c(F)c(*)c(F)c4F)c(F)c3F)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3c(F)c(F)c(COC(c4cccc(C(OCc5c(F)c(F)c(*)c(F)c5F)(C(F)(F)F)C(F)(F)F)c4)(C(F)(F)F)C(F)(F)F)c(F)c3F)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)C(=O)c4ccc(*)cc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)Nc4ccc(-c5ccc(NC(=O)c6ccc(*)cc6)cc5C(F)(F)F)c(C(F)(F)F)c4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)Nc4ccc(NC(=O)c5ccc(*)cc5)cc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)c4c(C(=O)c5ccc(*)cc5)c(-c5ccc(F)cc5)c(-c5ccc(F)cc5)c(-c5ccc(F)cc5)c4-c4ccc(F)cc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)c4c(C(=O)c5ccc(*)cc5)c(-c5ccccc5)c(-c5ccc6ccccc6c5)c(-c5ccc6ccccc6c5)c4-c4ccccc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)c4cc(C(=O)c5ccc(*)cc5)cc(C(C)(C)C)c4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)c4ccc(C(C)(C)c5ccc(C(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)c4ccc(C(c5ccc(C(=O)c6ccc(*)cc6)cc5)(C(F)(F)F)C(F)(F)F)cc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)c4ccc(P(=O)(c5ccccc5)c5ccc(C(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)c4cccc(-c5cccc(C(=O)c6ccc(*)cc6)c5)c4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)c4cccc(C(=O)c5ccc(*)cc5)c4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)c4cccc5cccc(C(=O)c6ccc(*)cc6)c45)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccc(Oc3ccc(C(=O)c4ccccc4-c4ccccc4C(=O)c4ccc(*)cc4)cc3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*Oc1ccc(C(c2ccccc2)(c2ccccc2)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(c2ccccc2)(c2ccccc2)c2ccc(OC(=O)OC3C(C)(C)C(OC(*)=O)C3(C)C)cc2)cc1
*Oc1ccc(C(c2ccccc2)(c2ccccc2)c2ccc(OC(=O)c3ccc(C(*)=O)cc3)cc2)cc1
*Oc1ccc(C(c2ccccc2)(c2ccccc2)c2ccc(OC(=O)c3cccc(C(*)=O)c3)cc2)cc1
*Oc1ccc(C(c2ccccc2)(c2ccccc2)c2ccc(Oc3ccc(C(=O)Nc4ccc(NC(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(C(c2ccccc2)(c2ccccc2)c2ccc(Oc3ccc4c(=O)n5c6cc(-c7ccc8c(c7)nc7c9ccc(*)c%10cccc(c(=O)n87)c%109)ccc6nc5c5cccc3c45)cc2)cc1
*Oc1ccc(C(c2ccccc2)c2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(C(c2ccccc2)c2ccc(OC(*)=O)cc2C)c(C)c1
*Oc1ccc(C(c2ccccc2)c2ccc(OC(*)=S)cc2)cc1
*Oc1ccc(C2(C)CCC(C(C)(C)c3ccc(Oc4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)CC2)cc1
*Oc1ccc(C2(c3ccc(OC(*)=O)c(C)c3)CC3CC2C2CCCC32)cc1C
*Oc1ccc(C2(c3ccc(OC(*)=O)c(C)c3)CC3CCC2C3)cc1C
*Oc1ccc(C2(c3ccc(OC(*)=O)c(C)c3)CCCCC2)cc1C
*Oc1ccc(C2(c3ccc(OC(*)=O)c(Cl)c3)CC3CCC2C3)cc1Cl
*Oc1ccc(C2(c3ccc(OC(*)=O)c(Cl)c3)CCCCC2)cc1Cl
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)CC(C)CC(C)(C)C2)cc1
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)CC3CC2C2C4CCC(C4)C32)cc1
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)CC3CC2C2CCCC32)cc1
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)CC3CCC2C3)cc1
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)CCCC2)cc1
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)CCCCC2)cc1
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)CCCCCC2)cc1
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)CCc3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)c3cc(N(C)C)ccc3-c3ccc([N+](=O)[O-])cc32)cc1
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)c3cc(OC)ccc3-c3ccc([N+](=O)[O-])cc32)cc1
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(*)=O)cc3C)CC3CC2C2C4CCC(C4)C32)cc1C
*Oc1ccc(C2(c3ccc(OC(*)=S)cc3)CCC(C)CC2)cc1
*Oc1ccc(C2(c3ccc(OC(*)=S)cc3)CCCC2)cc1
*Oc1ccc(C2(c3ccc(OC(*)=S)cc3)CCCCC2)cc1
*Oc1ccc(C2(c3ccc(OC(=O)C45CC6CC(CC(C(*)=O)(C6)C4)C5)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)CCC(*)=O)cc3)c3cc(N(C)C)ccc3-c3ccc([N+](=O)[O-])cc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)CCC(*)=O)cc3)c3cc(OC)ccc3-c3ccc([N+](=O)[O-])cc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)CCC(*)=O)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)CCCC(*)=O)cc3)c3cc(N(C)C)ccc3-c3ccc([N+](=O)[O-])cc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)CCCC(*)=O)cc3)c3cc(OC)ccc3-c3ccc([N+](=O)[O-])cc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)CCCC(*)=O)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)CCCCC(*)=O)cc3)c3cc(N(C)C)ccc3-c3ccc([N+](=O)[O-])cc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)CCCCC(*)=O)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)CCCCCCC(*)=O)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)CCCCCCCC(*)=O)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)CCCCCCCCC(*)=O)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)CCCCCCCCC(*)=O)cc3)c3ccccc3Cc3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)CCCCCCCCCC(*)=O)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)CCCCCCCCCCC(*)=O)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)OC4C(C)(C)C(OC(*)=O)C4(C)C)cc3)CC3CCC2C3)cc1
*Oc1ccc(C2(c3ccc(OC(=O)OC4C(C)(C)C(OC(*)=O)C4(C)C)cc3)CCC(C(C)(C)C)CC2)cc1
*Oc1ccc(C2(c3ccc(OC(=O)OC4C(C)(C)C(OC(*)=O)C4(C)C)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)c4ccc(C(*)=O)cc4)c(C)c3)CCC3CCCCC3C2)cc1C
*Oc1ccc(C2(c3ccc(OC(=O)c4ccc(C(*)=O)cc4)cc3)CC3CC2C2CCCC32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)c4ccc(C(*)=O)cc4)cc3)CCC(C(C)(C)C)CC2)cc1
*Oc1ccc(C2(c3ccc(OC(=O)c4ccc(C(*)=O)cc4)cc3)CCC3CCCCC3C2)cc1
*Oc1ccc(C2(c3ccc(OC(=O)c4ccc(C(*)=O)cc4)cc3)CCCCC2)cc1
*Oc1ccc(C2(c3ccc(OC(=O)c4ccc(C(*)=O)cc4)cc3)CCc3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)c4ccc(C(*)=O)cc4)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)c4ccc(C(*)=O)cc4)cc3)c3ccccc3C(=O)c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)c4ccc(C(*)=O)cc4)cc3)c3ccccc3Cc3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)c4ccc(Oc5ccc(C6(c7ccc(Oc8ccc(C(*)=O)cc8)cc7)CCC(C(C)(C)C)CC6)cc5)cc4)cc3)C3CC4CC(C3)CC2C4)cc1
*Oc1ccc(C2(c3ccc(OC(=O)c4ccc(Oc5ccc(C6(c7ccc(Oc8ccc(C(*)=O)cc8)cc7)CCC(C(C)(C)C)CC6)cc5)cc4)cc3)CC3CC2C2CCCC32)cc1
*Oc1ccc(C2(c3ccc(OC(=O)c4ccc(Oc5ccc(C6(c7ccc(Oc8ccc(C(*)=O)cc8)cc7)CCC(C(C)(C)C)CC6)cc5)cc4)cc3)CCCCCCCCCCC2)cc1
*Oc1ccc(C2(c3ccc(OC(=O)c4cccc(C(*)=O)c4)c(C)c3)CCC3CCCCC3C2)cc1C
*Oc1ccc(C2(c3ccc(OC(=O)c4cccc(C(*)=O)c4)c(C)c3)c3ccccc3-c3ccccc32)cc1C
*Oc1ccc(C2(c3ccc(OC(=O)c4cccc(C(*)=O)c4)cc3)CCC3CCCCC3C2)cc1
*Oc1ccc(C2(c3ccc(OC(=O)c4cccc(C(*)=O)c4)cc3)CCCCC2)cc1
*Oc1ccc(C2(c3ccc(OC(=O)c4cccc(C(*)=O)c4)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(OC4(F)C(*)(F)C(F)(F)C4(F)F)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4c(F)c(F)c(C(=O)c5c(F)c(F)c(*)c(F)c5F)c(F)c4F)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(-c5ccc(-c6ccc(*)c(C(F)(F)F)c6)cc5)cc4C(F)(F)F)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(-c5ccc(NC(=O)c6cc(C(=O)Nc7ccc(-c8ccc(*)c(C(F)(F)F)c8)cc7)cc(C(C)(C)C)c6)cc5)cc4C(F)(F)F)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(/C=C/C(=O)c5ccc(*)cc5)cc4)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(/C=N/N=C/c5ccc(*)cc5)cc4)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)C(=O)c5ccc(*)cc5)cc4)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)Oc5c(C)cc(C(C)(C)c6cc(C)c(OC(=O)c7ccc(*)cc7)c(C)c6)cc5C)cc4)cc3)CCC(C(C)(C)C)CC2)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)Oc5ccc(C(C)(C)c6ccc(OC(=O)c7ccc(*)cc7)cc6)cc5)cc4)cc3)CCC(C(C)(C)C)CC2)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)Oc5ccc(C6(c7ccc(OC(=O)c8ccc(*)cc8)cc7)CCC(c7ccccc7)CC6)cc5)cc4)cc3)CCC(C(C)(C)C)CC2)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5c(C(=O)c6ccc(*)cc6)c(-c6ccc(F)cc6)c(-c6ccc(F)cc6)c(-c6ccc(F)cc6)c5-c5ccc(F)cc5)cc4)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5c(C(=O)c6ccc(*)cc6)c(-c6ccccc6)c(-c6ccc7ccccc7c6)c(-c6ccc7ccccc7c6)c5-c5ccccc5)cc4)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5ccc(*)cc5)cc4)c(C)c3)CCC3CCCCC3C2)cc1C
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)CCC3CCCCC3C2)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5ccc(C(=O)c6ccc(*)c(C(F)(F)F)c6)cc5)cc4C(F)(F)F)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5ccc(C(=O)c6ccc(*)cc6)cc5)cc4)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5ccc(Oc6ccc(C(=O)c7ccc(*)cc7)cc6)cc5)cc4)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5cccc(-c6cccc(C(=O)c7ccc(*)cc7)c6)c5)cc4)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5cccc(C(=O)c6ccc(*)cc6)c5)cc4)c(C)c3)CCC3CCCCC3C2)cc1C
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5cccc(C(=O)c6ccc(*)cc6)c5)cc4)c(C)c3)c3ccccc3-c3ccccc32)cc1C
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5cccc(C(=O)c6ccc(*)cc6)c5)cc4)cc3)CCC3CCCCC3C2)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5cccc(C(=O)c6ccc(*)cc6)c5)cc4)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5cccc6cccc(C(=O)c7ccc(*)cc7)c56)cc4)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5ccccc5)c(-c5cc(*)ccc5C(=O)c5ccccc5)c4)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc(C(=O)c5ccccc5-c5ccccc5C(=O)c5ccc(*)cc5)cc4)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc5c(=O)n6c7cc(-c8ccc9c(c8)nc8c%10ccc(*)c%11cccc(c(=O)n98)c%11%10)ccc7nc6c6cccc4c56)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc5c(=O)n6c7cc(-c8ccc9c(c8)nc8c%10ccc(*)c%11cccc(c(=O)n98)c%11%10)ccc7nc6c6cccc4c56)cc3)c3ccccc3C(=O)c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4ccc5c(=O)n6c7cc(Oc8ccc9c(c8)nc8c%10ccc(*)c%11cccc(c(=O)n98)c%11%10)ccc7nc6c6cccc4c56)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(C2(c3ccc(Oc4nc(*)nc(OC)n4)cc3)CCCCC2)cc1
*Oc1ccc(CC(*)=O)cc1
*Oc1ccc(CC(C#N)(C#N)Cc2ccc(*)cc2)cc1
*Oc1ccc(CC(C#N)(Cc2ccc(*)cc2)c2ccccc2)cc1
*Oc1ccc(CC(Cc2ccc(*)cc2)(C(=O)OCC)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(*)=O)cc2)C(=O)OCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CC(C)CCC(*)=O)cc2)C(=O)OC(C)CC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CC(C)CCC(*)=O)cc2)C(=O)OCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCC(*)=O)cc2)C(=O)OC(C)C)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCC(*)=O)cc2)C(=O)OC(C)CC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCC(*)=O)cc2)C(=O)OC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCC(*)=O)cc2)C(=O)OCC(C)C)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCC(*)=O)cc2)C(=O)OCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCC(*)=O)cc2)C(=O)OCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCC(*)=O)cc2)C(=O)OCCCCCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCC(*)=O)cc2)C(=O)OCCOCCOCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCC(*)=O)cc2)C(=O)OCc2ccccc2)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCC(C)CC(*)=O)cc2)C(=O)OC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCC(C)CC(*)=O)cc2)C(=O)OCc2ccccc2)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCC(*)=O)cc2)C(=O)OC(C)C)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCC(*)=O)cc2)C(=O)OC(C)CC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCC(*)=O)cc2)C(=O)OC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCC(*)=O)cc2)C(=O)OCC(C)C)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCC(*)=O)cc2)C(=O)OCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCC(*)=O)cc2)C(=O)OCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCC(*)=O)cc2)C(=O)OCCCCCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCC(*)=O)cc2)C(=O)OCCOCCOCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCC(*)=O)cc2)C(=O)OCc2ccccc2)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCC(*)=O)cc2)C(=O)OC(C)C)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCC(*)=O)cc2)C(=O)OC(C)CC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCC(*)=O)cc2)C(=O)OC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCC(*)=O)cc2)C(=O)OCC(C)C)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCC(*)=O)cc2)C(=O)OCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCC(*)=O)cc2)C(=O)OCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCC(*)=O)cc2)C(=O)OCCCCCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCC(*)=O)cc2)C(=O)OCCOCCOCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCC(*)=O)cc2)C(=O)OCc2ccccc2)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCC(*)=O)cc2)C(=O)OC(C)C)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCC(*)=O)cc2)C(=O)OC(C)CC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCC(*)=O)cc2)C(=O)OC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCC(*)=O)cc2)C(=O)OCC(C)C)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCC(*)=O)cc2)C(=O)OCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCC(*)=O)cc2)C(=O)OCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCC(*)=O)cc2)C(=O)OCCCCCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCC(*)=O)cc2)C(=O)OCCOCCOCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCC(*)=O)cc2)C(=O)OCc2ccccc2)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)C(=O)OC(C)C)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)C(=O)OC(C)CC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)C(=O)OC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)C(=O)OCC(C)C)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)C(=O)OCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)C(=O)OCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)C(=O)OCCCCCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)C(=O)OCCOCCOCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)C(=O)OCc2ccccc2)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCC(*)=O)cc2)C(=O)OC(C)C)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCC(*)=O)cc2)C(=O)OC(C)CC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCC(*)=O)cc2)C(=O)OC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCC(*)=O)cc2)C(=O)OCC(C)C)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCC(*)=O)cc2)C(=O)OCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCC(*)=O)cc2)C(=O)OCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCC(*)=O)cc2)C(=O)OCCCCCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCC(*)=O)cc2)C(=O)OCCOCCOCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCC(*)=O)cc2)C(=O)OCc2ccccc2)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCCOCC(*)=O)cc2)C(=O)OC(C)C)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCCOCC(*)=O)cc2)C(=O)OC(C)CC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCCOCC(*)=O)cc2)C(=O)OC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCCOCC(*)=O)cc2)C(=O)OCC(C)C)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCCOCC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCCOCC(*)=O)cc2)C(=O)OCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCCOCC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCCOCC(*)=O)cc2)C(=O)OCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCCOCC(*)=O)cc2)C(=O)OCCCCCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCCOCC(*)=O)cc2)C(=O)OCCOCCOCC)cc1
*Oc1ccc(CC(NC(=O)CCc2ccc(OC(=O)COCCOCC(*)=O)cc2)C(=O)OCc2ccccc2)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CC(C)CCC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CC(C)CCC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CC(C)CCC(*)=O)cc2)C(=O)OCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CCC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CCC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CCC(*)=O)cc2)C(=O)OCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CCCC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CCCC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CCCC(*)=O)cc2)C(=O)OCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CCCCC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CCCCC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CCCCC(*)=O)cc2)C(=O)OCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CCCCCCC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CCCCCCC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CCCCCCC(*)=O)cc2)C(=O)OCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)CCCCCCCCC(*)=O)cc2)C(=O)OCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)COCC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)COCC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)COCC(*)=O)cc2)C(=O)OCCCCCCCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)COCCOCC(*)=O)cc2)C(=O)OCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)COCCOCC(*)=O)cc2)C(=O)OCCCCCC)cc1
*Oc1ccc(CC(NC(=O)Cc2ccc(OC(=O)COCCOCC(*)=O)cc2)C(=O)OCCCCCCCC)cc1
*Oc1ccc(CC2(Cc3ccc(*)cc3)c3ccccc3-c3ccccc32)cc1
*Oc1ccc(CCNC(=O)c2cccc(C(*)=O)c2)cc1
*Oc1ccc(CCNC(=O)c2cccc(C(=O)NCCc3ccc(OC(=O)c4cccc(C(*)=O)c4)cc3)c2)cc1
*Oc1ccc(CNC(=O)CCCCC(=O)NCc2ccc(OC3COC4C(*)COC34)cc2)cc1
*Oc1ccc(CNC(=O)c2ccc(C(=O)NCc3ccc(OC4COC5C(*)COC45)cc3)cc2)cc1
*Oc1ccc(CNC(=O)c2cccc(C(=O)NCc3ccc(OC4COC5C(*)COC45)cc3)c2)cc1
*Oc1ccc(Cc2ccc(Cc3ccc(OC(*)=O)cc3)cc2)cc1
*Oc1ccc(Cc2ccc(OC(*)=O)c(C)c2)cc1C
*Oc1ccc(Cc2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(Cc2ccc(OC(*)=S)cc2)cc1
*Oc1ccc(Cc2ccc(OC(=O)CCCCC(*)=O)cc2)cc1
*Oc1ccc(Cc2ccc(OC(=O)OCC3CCC(COC(*)=O)CC3)cc2)cc1
*Oc1ccc(Cc2ccc(OC(=O)c3cc(OCCCCC)cc(C(*)=O)c3)cc2)cc1
*Oc1ccc(Cc2ccc(OC(=O)c3ccc(C(C)(C)c4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(Cc2ccc(OC(=O)c3cccc(C(*)=O)c3)cc2)cc1
*Oc1ccc(N(CC)c2ccc(/C=C(\C#N)C(=O)NC3CCCCC3NC(=O)/C(C#N)=C/c3ccc(N(CC)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(N(c2ccc(OC)cc2)c2ccc3ccc(N(c4ccc(*)cc4)c4ccc(OC)cc4)cc3c2)cc1
*Oc1ccc(N(c2ccc(OCCCCCC)cc2)c2ccc(-c3ccc(N(c4ccc(*)cc4)c4ccc(OCCCCCC)cc4)cc3)cc2)cc1
*Oc1ccc(N(c2ccccc2)c2ccc(-c3ccc(N(c4ccccc4)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=C(C#N)C#N)c2ccc(-c3ccc(-c4ccc(C(Nc5ccc(*)cc5)=C(C#N)C#N)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=C(C#N)C#N)c2ccc(-c3ccc(C(Nc4ccc(*)cc4)=C(C#N)C#N)cc3)cc2)cc1
*Oc1ccc(NC(=C(C#N)C#N)c2ccc(C(Nc3ccc(*)cc3)=C(C#N)C#N)cc2)cc1
*Oc1ccc(NC(=C(C#N)C#N)c2ccc3cc(C(Nc4ccc(*)cc4)=C(C#N)C#N)ccc3c2)cc1
*Oc1ccc(NC(=C(C#N)C#N)c2cccc(C(Nc3ccc(Oc4ccc(NC(=O)c5ccc(C(=O)Nc6ccc(*)cc6)cc5)cc4)cc3)=C(C#N)C#N)c2)cc1
*Oc1ccc(NC(=O)/C=C/C(=O)NNC(=O)/C=C/C(=O)Nc2ccc(OC(=O)/C=C/C(=O)NNC(=O)/C=C/C(*)=O)cc2)cc1
*Oc1ccc(NC(=O)/C=C/C(=O)NNC(=O)/C=C/C(=O)Nc2ccc(OC(=O)c3ccccc3C(=O)NNC(=O)c3ccccc3C(*)=O)cc2)cc1
*Oc1ccc(NC(=O)/C=C/c2ccc(/C=C/C(=O)Nc3ccc(*)cc3)cc2)cc1
*Oc1ccc(NC(=O)CCCCC(=O)Nc2ccc(*)cc2)cc1
*Oc1ccc(NC(=O)CCCCC(=O)Nc2ccc(OC3COC4C(*)COC34)cc2)cc1
*Oc1ccc(NC(=O)CCCCCC(=O)Nc2ccc(*)cc2)cc1
*Oc1ccc(NC(=O)CCCCCCC(=O)Nc2ccc(*)cc2)cc1
*Oc1ccc(NC(=O)CCCCCCC(=O)Nc2ccc(OC3COC4C(*)COC34)cc2)cc1
*Oc1ccc(NC(=O)CCCCCCCC(=O)Nc2ccc(*)cc2)cc1
*Oc1ccc(NC(=O)CCCCCCCCC(=O)Nc2ccc(*)cc2)cc1
*Oc1ccc(NC(=O)CCCCCCCCCC(=O)Nc2ccc(*)cc2)cc1
*Oc1ccc(NC(=O)CCCCCCCCCCC(=O)Nc2ccc(*)cc2)cc1
*Oc1ccc(NC(=O)NC2CC(C)(C)CC(C)(CNC(=O)Nc3ccc(*)c(-c4ccc(Oc5ccccc5)cc4)c3)C2)cc1-c1ccc(Oc2ccccc2)cc1
*Oc1ccc(NC(=O)NC2CC(C)(C)CC(C)(CNC(=O)Nc3ccc(*)cc3)C2)cc1
*Oc1ccc(NC(=O)Nc2cc(NC(=O)Nc3ccc(*)c(-c4ccc(Oc5ccccc5)cc4)c3)ccc2C)cc1-c1ccc(Oc2ccccc2)cc1
*Oc1ccc(NC(=O)Nc2cc(NC(=O)Nc3ccc(*)cc3)ccc2C)cc1
*Oc1ccc(NC(=O)Nc2ccccc2CCc2ccc(NC(=O)Nc3ccc(*)cc3)cc2)cc1
*Oc1ccc(NC(=O)Nc2ccccc2CCc2ccccc2NC(=O)Nc2ccc(*)cc2)cc1
*Oc1ccc(NC(=O)c2cc(-c3ccc(C(=O)O)c(C(=O)Nc4ccc(*)cc4)c3)ccc2C(=O)O)cc1
*Oc1ccc(NC(=O)c2cc(-c3ccc(C(=O)OCC)c(C(=O)Nc4ccc(*)cc4)c3)ccc2C(=O)OCC)cc1
*Oc1ccc(NC(=O)c2cc(C(=O)Nc3ccc(*)cc3)c(C(=O)O)cc2C(=O)O)cc1
*Oc1ccc(NC(=O)c2cc(C(=O)Nc3ccc(*)cc3)c(C(=O)OCC)cc2C(=O)OCC)cc1
*Oc1ccc(NC(=O)c2cc(C(=O)Nc3ccc(*)cc3)cc(C(C)(C)C)c2)cc1
*Oc1ccc(NC(=O)c2cc(C(=O)Nc3ccc(*)cc3)cc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)c2)cc1
*Oc1ccc(NC(=O)c2cc(C(=O)Nc3ccc(*)cc3)cc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)c2)cc1
*Oc1ccc(NC(=O)c2cc(C(=O)Nc3ccc(*)cc3)cc(S(=O)(=O)c3ccccc3)c2)cc1
*Oc1ccc(NC(=O)c2cc(C(=O)Nc3ccc(Oc4ccc(C(C)(C)c5ccc(*)cc5)cc4)cc3)cc(C(C)(C)C)c2)cc1
*Oc1ccc(NC(=O)c2cc(C(=O)Nc3ccc(Oc4ccc(C(c5ccc(*)cc5)(C(F)(F)F)C(F)(F)F)cc4)cc3)cc(C(C)(C)C)c2)cc1
*Oc1ccc(NC(=O)c2cc(C(=O)Nc3ccc(Oc4ccc(C(c5ccc(*)cc5)(C(F)(F)F)C(F)(F)F)cc4)cc3)cc([N+](=O)[O-])c2)cc1
*Oc1ccc(NC(=O)c2cc(C(=O)Nc3ccc(Oc4ccc(C5(c6ccc(*)cc6)CC6CCC5C6)cc4)cc3)cc(C(C)(C)C)c2)cc1
*Oc1ccc(NC(=O)c2cc(C(c3ccc(C(=O)O)c(C(=O)Nc4ccc(Oc5ccc(C(c6ccc(*)cc6)(C(F)(F)F)C(F)(F)F)cc5)cc4)c3)(C(F)(F)F)C(F)(F)F)ccc2C(=O)O)cc1
*Oc1ccc(NC(=O)c2cc(NC(=O)C34CC5CC(CC(C5)C3)C4)cc(C(=O)Nc3ccc(*)cc3)c2)cc1
*Oc1ccc(NC(=O)c2cc(NC(=O)CCCCCCCCCCN3C(=O)c4ccccc4C3=O)cc(C(=O)Nc3ccc(*)cc3)c2)cc1
*Oc1ccc(NC(=O)c2cc(NC(=O)CCCCCN3C(=O)c4ccccc4C3=O)cc(C(=O)Nc3ccc(*)cc3)c2)cc1
*Oc1ccc(NC(=O)c2cc(NC(=O)CCCN3C(=O)c4ccccc4C3=O)cc(C(=O)Nc3ccc(*)cc3)c2)cc1
*Oc1ccc(NC(=O)c2cc(NC(=O)CCN3C(=O)c4ccccc4C3=O)cc(C(=O)Nc3ccc(*)cc3)c2)cc1
*Oc1ccc(NC(=O)c2cc(NC(=O)c3ccc(OC(C)=O)cc3)cc(C(=O)Nc3ccc(*)cc3)c2)cc1
*Oc1ccc(NC(=O)c2cc(NC(=O)c3ccc(OC(C)=O)cc3)cc(C(=O)Nc3ccc(Oc4ccc(-c5ccc(*)cc5)cc4)cc3)c2)cc1
*Oc1ccc(NC(=O)c2cc(NC(=O)c3ccc(OC(C)=O)cc3)cc(C(=O)Nc3ccc(Oc4ccc(C(C)(C)c5ccc(*)cc5)cc4)cc3)c2)cc1
*Oc1ccc(NC(=O)c2cc(NC(=O)c3ccccc3)cc(C(=O)Nc3ccc(*)cc3)c2)cc1
*Oc1ccc(NC(=O)c2cc(NC(=O)c3ccncc3)cc(C(=O)Nc3ccc(*)cc3)c2)cc1
*Oc1ccc(NC(=O)c2ccc(*)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(-c3ccc(C(=O)Nc4ccc(Oc5ccc(C(C)(C)c6ccc(*)cc6)cc5)cc4)c(C)c3)cc2C)cc1
*Oc1ccc(NC(=O)c2ccc(-c3ccc(C(=O)Nc4ccc(Oc5ccc(C6(c7ccc(*)cc7)CC7CCC6C7)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(C(=O)Nc3ccc(*)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(C(=O)Nc3ccc(OC4COC5C(*)COC45)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(C(=O)Nc3ccc(Oc4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(C(=O)Nc3ccc(Oc4ccc(C(c5ccc(*)cc5)(C(F)(F)F)C(F)(F)F)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(C(=O)Nc3ccc(Oc4ccc(C5(c6ccc(*)cc6)C6CC7CC(C6)CC5C7)cc4)cc3)c3ccccc23)cc1
*Oc1ccc(NC(=O)c2ccc(C(=O)Nc3ccc(Oc4ccc(C5(c6ccc(*)cc6)C6CC7CC(C6)CC5C7)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(C(=O)Nc3ccc(Oc4ccc(C5(c6ccc(*)cc6)CC6CCC5C6)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(C(c3ccc(C(=O)Nc4ccc(Oc5ccc(C6(c7ccc(*)cc7)C7CC8CC(C7)CC6C8)cc5)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(NC(=O)c3ccc([Si](C)(C)c4ccc(C(=O)Nc5ccc(C(=O)Nc6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(OC3COC4C(Oc5ccc(C(=O)Nc6ccc(OC7COC8C(*)COC78)cc6)cc5)COC34)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(Oc3ccc(C(=O)Nc4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(Oc3ccc(C(=O)Nc4ccc(Oc5ccc(C6(c7ccc(*)cc7)C7CC8CC(C7)CC6C8)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(C(C)(C)c5ccc(Oc6ccc(C(=O)Nc7ccc(*)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(C(C)(C)c5ccc(Oc6ccc(C(=O)Nc7ccc(Oc8ccc(-c9ccc(*)cc9)cc8)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(C(C)(C)c5ccc(Oc6ccc(C(=O)Nc7ccc(Oc8ccc(C(C)(C)c9ccc(*)cc9)cc8)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(C(C)(C)c5ccc(Oc6ccc(C(=O)Nc7ccc(Oc8ccc(C(c9ccc(*)cc9)(C(F)(F)F)C(F)(F)F)cc8)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(Oc3ccc(C(C)(C)c4cccc(C(C)(C)c5ccc(Oc6ccc(C(=O)Nc7ccc(*)cc7)cc6)cc5)c4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(Oc3ccc(C(C)(C)c4cccc(C(C)(C)c5ccc(Oc6ccc(C(=O)Nc7ccc(Oc8ccc(-c9ccc(*)cc9)cc8)cc7)cc6)cc5)c4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(Oc3ccc(C(C)(C)c4cccc(C(C)(C)c5ccc(Oc6ccc(C(=O)Nc7ccc(Oc8ccc(C(C)(C)c9ccc(*)cc9)cc8)cc7)cc6)cc5)c4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(Oc3ccc(C(C)(C)c4cccc(C(C)(C)c5ccc(Oc6ccc(C(=O)Nc7ccc(Oc8ccc(C(c9ccc(*)cc9)(C(F)(F)F)C(F)(F)F)cc8)cc7)cc6)cc5)c4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(P(=O)(c3ccccc3)c3ccc(C(=O)Nc4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(P(=O)(c3ccccc3)c3ccc(C(=O)Nc4ccc(Oc5ccc(P(=O)(c6ccccc6)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(S(=O)(=O)c3ccc(C(=O)Nc4ccc(Oc5ccc(C(c6ccc(*)cc6)(C(F)(F)F)C(F)(F)F)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(S(=O)(=O)c3ccc(C(=O)Nc4ccc(Oc5ccc(C6(c7ccc(*)cc7)C7CC8CC(C7)CC6C8)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc(S(=O)(=O)c3ccc(C(=O)Nc4ccc(Oc5ccc(C6(c7ccc(*)cc7)CC7CCC6C7)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc([Si](C)(C)c3ccc(C(*)=O)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc([Si](c3ccccc3)(c3ccccc3)c3ccc(C(*)=O)cc3)cc2)cc1
*Oc1ccc(NC(=O)c2ccc3cc(C(=O)Nc4ccc(Oc5ccc(C6(c7ccc(*)cc7)C7CC8CC(C7)CC6C8)cc5)cc4)ccc3c2)cc1
*Oc1ccc(NC(=O)c2ccc3cc(C(=O)Nc4ccc(Oc5ccc(C6(c7ccc(*)cc7)CC7CCC6C7)cc5)cc4)ccc3c2)cc1
*Oc1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(*)cc3)c2)cc1
*Oc1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(OC4COC5C(*)COC45)cc3)c2)cc1
*Oc1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(Oc4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)c2)cc1
*Oc1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(Oc4ccc(C(C)(C)c5cccc(C(C)(C)c6ccc(*)cc6)c5)cc4)cc3)c2)cc1
*Oc1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(Oc4ccc(C(c5ccc(*)cc5)(C(F)(F)F)C(F)(F)F)cc4)cc3)c2)cc1
*Oc1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(Oc4ccc(C5(c6ccc(*)cc6)C6CC7CC(C6)CC5C7)cc4)cc3)c2)cc1
*Oc1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(Oc4ccc(C5(c6ccc(*)cc6)CC6CCC5C6)cc4)cc3)c2)cc1
*Oc1ccc(NCC(=O)c2ccc(Sc3ccc(C(=O)CNc4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(N[Se]Nc2ccc(*)cc2)cc1
*Oc1ccc(Nc2ccc(/N=N/c3ccc(Nc4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(Nc2ccc(C(=O)c3ccc(C(=O)c4ccc(Nc5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(Nc2ccc(S(=O)(=O)c3ccc(Nc4ccc(Oc5ccc(C(C)(C)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(OC(=O)/C=C/c2ccc(/C=C/C(*)=O)cc2)cc1
*Oc1ccc(OC(=O)CCCCC(*)=O)cc1
*Oc1ccc(OC(=O)CCCCCCCCC(*)=O)cc1
*Oc1ccc(OC(=O)Oc2ccc(C(*)=O)cc2)cc1
*Oc1ccc(OC(=O)Oc2ccc(C(C)(C)c3ccc(OC(*)=O)cc3)cc2)cc1
*Oc1ccc(OC(=O)c2cc(/N=N/c3ccc(OCC)cc3)ccc2-c2ccc(/N=N/c3ccc(OCC)cc3)cc2C(*)=O)cc1
*Oc1ccc(OC(=O)c2cc(OCCCCCCCCCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)cc(C(*)=O)c2)cc1
*Oc1ccc(OC(=O)c2cc(OCCCCCCCCCCCC)c(C(*)=O)cc2OCCCCCCCCCCCC)cc1-c1ccccc1
*Oc1ccc(OC(=O)c2cc(OCCCCCCCCCCCC)c(C(*)=O)cc2OCCCCCCCCCCCC)cc1C
*Oc1ccc(OC(=O)c2cc(OCCCCCCCCCCOCC3CCCN3c3ccc([N+](=O)[O-])cc3)c(C(*)=O)cc2OCCCCCCCCCCOCC2CCCN2c2ccc([N+](=O)[O-])cc2)cc1
*Oc1ccc(OC(=O)c2cc(OCCCCCCCCCOCC3CCCN3c3ccc([N+](=O)[O-])cc3)c(C(*)=O)cc2OCCCCCCCCCOCC2CCCN2c2ccc([N+](=O)[O-])cc2)cc1
*Oc1ccc(OC(=O)c2cc(OCCCCCCCCOCC3CCCN3c3ccc([N+](=O)[O-])cc3)c(C(*)=O)cc2OCCCCCCCCOCC2CCCN2c2ccc([N+](=O)[O-])cc2)cc1
*Oc1ccc(OC(=O)c2cc(OCCCCCCCOCC3CCCN3c3ccc([N+](=O)[O-])cc3)c(C(*)=O)cc2OCCCCCCCOCC2CCCN2c2ccc([N+](=O)[O-])cc2)cc1
*Oc1ccc(OC(=O)c2cc(OCCCCCCOCC3CCCN3c3ccc([N+](=O)[O-])cc3)c(C(*)=O)cc2OCCCCCCOCC2CCCN2c2ccc([N+](=O)[O-])cc2)cc1
*Oc1ccc(OC(=O)c2cc(OCCCCCOCC3CCCN3c3ccc([N+](=O)[O-])cc3)c(C(*)=O)cc2OCCCCCOCC2CCCN2c2ccc([N+](=O)[O-])cc2)cc1
*Oc1ccc(OC(=O)c2cc(OCCCCOCC3CCCN3c3ccc([N+](=O)[O-])cc3)c(C(*)=O)cc2OCCCCOCC2CCCN2c2ccc([N+](=O)[O-])cc2)cc1
*Oc1ccc(OC(=O)c2cc(OCCCc3ccccc3)c(C(*)=O)cc2OCCCc2ccccc2)cc1C
*Oc1ccc(OC(=O)c2ccc(-c3ccc(C(*)=O)cc3)cc2-c2ccccc2)c2ccccc12
*Oc1ccc(OC(=O)c2ccc(-c3ccc(C(*)=O)cc3)cc2-c2ccccc2)cc1
*Oc1ccc(OC(=O)c2ccc(-c3ccc(C(*)=O)cc3)cc2-c2ccccc2)cc1-c1ccccc1
*Oc1ccc(OC(=O)c2ccc(-c3ccc(C(*)=O)cc3)cc2-c2ccccc2)cc1C
*Oc1ccc(OC(=O)c2ccc(-c3ccc(C(*)=O)cc3)cc2-c2ccccc2)cc1C(C)(C)c1ccccc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2)cc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2)cc1-c1cccc(C(F)(F)F)c1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2)cc1C(F)(F)F
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2)cc1C1CC2CCC1C2
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2)cc1C1CCCCC1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2)cc1C1CCCCCCC1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2)cc1CCC(C)CC
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Oc2ccc(-c3ccccc3)cc2)cc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Oc2ccc(Br)cc2)cc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Oc2ccc(C(C)(C)c3ccccc3)cc2)cc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Oc2ccc(C3CCCCC3)cc2)cc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Oc2ccc(Cl)cc2)cc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Oc2ccc(F)cc2)cc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Oc2ccc(Oc3ccccc3)cc2)cc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Oc2ccc3ccccc3c2)cc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Oc2ccccc2)cc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Sc2ccc(C)cc2)cc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Sc2ccc(Cl)cc2)cc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Sc2ccc3ccccc3c2)cc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Sc2ccccc2)cc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Sc2ccccc2)cc1-c1ccccc1
*Oc1ccc(OC(=O)c2ccc(C(*)=O)cc2Sc2ccccc2)cc1C
*Oc1ccc(OC(=O)c2ccc(C(C)(C)c3ccc(C(*)=O)cc3)cc2)cc1
*Oc1ccc(OC(=O)c2ccc(OC(=O)c3ccc(C(=O)Oc4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(OC(=O)c2ccc(OCCCCCCCCCCCCOc3ccc(C(*)=O)cc3)cc2)cc1/C=C/c1ccncc1
*Oc1ccc(OC(=O)c2ccc(OCCCCCCCCCCCCOc3ccc(C(*)=O)cc3)cc2)cc1S(=O)(=O)c1ccccc1
*Oc1ccc(OC(=O)c2ccc(OCCCCCCCCCCOc3ccc(C(*)=O)cc3)cc2)cc1S(=O)(=O)c1ccccc1
*Oc1ccc(OC(=O)c2ccc(OCCCCCCCCOc3ccc(C(*)=O)cc3)cc2)cc1C(=O)CC
*Oc1ccc(OC(=O)c2ccc(OCCCCCCOc3ccc(C(*)=O)cc3)cc2)cc1/N=N/c1ccc(C#N)cc1
*Oc1ccc(OC(=O)c2ccc(OCCCCCCOc3ccc(C(*)=O)cc3)cc2)cc1S(=O)(=O)c1ccccc1
*Oc1ccc(OC(=O)c2ccc(Oc3ccc(C(*)=O)cc3)cc2)c2ccccc12
*Oc1ccc(OC(=O)c2ccc(Sc3cccc(OC(=O)c4ccc(C(=O)Oc5cccc(Sc6ccc(C(*)=O)cc6)c5)cc4)c3)cc2)cc1C
*Oc1ccc(OC(=O)c2ccc([Si](C)(C)c3ccc(C(*)=O)cc3)cc2)cc1
*Oc1ccc(OC(=O)c2cccc(C(*)=O)c2)cc1
*Oc1ccc(OC(=O)c2cccc(Oc3cccc(C(*)=O)c3)c2)cc1
*Oc1ccc(OC(=O)c2cccc(Sc3ccc(OC(=O)c4ccc(C(=O)Oc5ccc(Sc6cccc(C(*)=O)c6)cc5)cc4)cc3)c2)cc1-c1ccccc1
*Oc1ccc(OC(=O)c2cccc(Sc3ccc(OC(=O)c4ccc(C(=O)Oc5ccc(Sc6cccc(C(*)=O)c6)cc5)cc4)cc3)c2)cc1C
*Oc1ccc(OC(C)COCCCCCCCOc2ccc(-c3ccc(C(*)=O)cc3)cc2)c([N+](=O)[O-])c1
*Oc1ccc(OC2(F)C(*)(F)C(F)(F)C2(F)F)cc1
*Oc1ccc(Oc2c(F)c(F)c(-c3c(F)c(F)c(*)c(F)c3F)c(F)c2F)cc1S(=O)(=O)O
*Oc1ccc(Oc2ccc(/C(=N/c3ccc(/N=C(\c4ccccc4)c4ccc(*)cc4)cc3)c3ccccc3)cc2)cc1
*Oc1ccc(Oc2ccc(/C(=N/c3ccc(Oc4ccc(/N=C(\c5ccccc5)c5ccc(*)cc5)cc4)cc3)c3ccccc3)cc2)cc1
*Oc1ccc(Oc2ccc(/C(=N\c3ccccc3)c3ccc(Oc4ccc(Oc5cccc(*)n5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(/C=N/CC/N=C/c3ccc(*)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(/C=N/CCCC/N=C/c3ccc(*)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(/C=N/CCCCCC/N=C/c3ccc(*)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(/C=N/CCCCCCCCCC/N=C/c3ccc(*)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(/C=N/CCCCCCCCCCCC/N=C/c3ccc(*)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(/C=N/c3ccc(/N=C/c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(/C=N/c3ccc(Oc4ccc(/N=C/c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)Nc3ccc(Cc4ccc(NC(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)Nc3ccc(Cc4ccc(NC(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1-c1ccccc1
*Oc1ccc(Oc2ccc(C(=O)Nc3ccc(Oc4ccc(-c5ccc(Oc6ccc(NC(=O)c7ccc(*)cc7)cc6)cc5)cc4)cc3)cc2)cc1-c1ccccc1
*Oc1ccc(Oc2ccc(C(=O)Nc3ccc(Oc4ccc(-c5ccc(Oc6ccc(NC(=O)c7ccc(*)cc7)cc6)cc5)cc4)cc3)cc2)cc1C
*Oc1ccc(Oc2ccc(C(=O)Nc3ccc(Oc4ccc(NC(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1-c1ccccc1
*Oc1ccc(Oc2ccc(C(=O)Nc3ccc(Oc4ccc(S(=O)(=O)c5ccc(Oc6ccc(NC(=O)c7ccc(*)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)Nc3ccc(Oc4ccc(S(=O)(=O)c5ccc(Oc6ccc(NC(=O)c7ccc(*)cc7)cc6)cc5)cc4)cc3)cc2)cc1-c1ccccc1
*Oc1ccc(Oc2ccc(C(=O)Nc3ccc(Oc4ccc(S(=O)(=O)c5ccc(Oc6ccc(NC(=O)c7ccc(*)cc7)cc6)cc5)cc4)cc3)cc2)cc1C
*Oc1ccc(Oc2ccc(C(=O)c3c(C(=O)c4ccc(*)cc4)c(-c4ccc(F)cc4)c(-c4ccc(F)cc4)c(-c4ccc(F)cc4)c3-c3ccc(F)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(*)c(S(=O)(=O)O)c3)cc2S(=O)(=O)O)cc1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(*)c(S(=O)(=O)O)c3)cc2S(=O)(=O)O)cc1S(=O)(=O)O
*Oc1ccc(Oc2ccc(C(=O)c3ccc(*)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(*)cc3)cc2)cc1-c1cc(C(F)(F)F)cc(C(F)(F)F)c1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(*)cc3)cc2)cc1-c1cc(C)cc(C)c1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(*)cc3)cc2)cc1-c1ccc(Oc2ccccc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(*)cc3)cc2)cc1-c1cccc(C(F)(F)F)c1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(*)cc3)cc2)cc1-c1ccccc1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(*)cc3)cc2)cc1C
*Oc1ccc(Oc2ccc(C(=O)c3ccc(*)cc3)cc2)cc1C(C)(C)C
*Oc1ccc(Oc2ccc(C(=O)c3ccc(*)cc3)cc2)cc1S(=O)(=O)O
*Oc1ccc(Oc2ccc(C(=O)c3ccc(-c4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1-c1ccc(S(=O)(=O)O)c(C)c1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(-c4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1-c1cccc(C)c1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(-c4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1-c1ccccc1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(C(=O)c4ccc(*)cc4)c4ccccc34)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)cc1-c1ccc(Cl)c(C(F)(F)F)c1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)cc1-c1ccc(S(=O)(=O)O)c(C)c1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)cc1-c1ccc(S(=O)(=O)O)cc1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)cc1-c1cccc(C)c1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)cc1-c1ccccc1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(NC(=O)CCCCCCCCC(=O)Nc4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(NC(=O)c4ccc(C(=O)Nc5ccc(C(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(NC(=O)c4cccc(C(=O)Nc5ccc(C(=O)c6ccc(*)cc6)cc5)c4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(Oc5ccc(C(=O)c6ccc(*)c(C(C)C)c6)cc5)cc4)cc3)cc2C(C)C)cc1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(Oc5cccc(*)n5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3ccc(P(=O)(c4ccccc4)c4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3cccc(-c4cccc(C(=O)c5ccc(*)cc5)c4)c3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(*)cc4)c3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(*)cc4)c3)cc2)cc1S(=O)(=O)O
*Oc1ccc(Oc2ccc(C(=O)c3cccc(NC(=O)CCCCCCCCC(=O)Nc4cccc(C(=O)c5ccc(*)cc5)c4)c3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3cccc(NC(=O)c4ccc(C(=O)Nc5cccc(C(=O)c6ccc(*)cc6)c5)cc4)c3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3cccc(NC(=O)c4cccc(C(=O)Nc5cccc(C(=O)c6ccc(*)cc6)c5)c4)c3)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3cccc4cccc(C(=O)c5ccc(*)cc5)c34)cc2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3cccc4cccc(C(=O)c5ccc(*)cc5)c34)cc2)cc1C(C)(C)C
*Oc1ccc(Oc2ccc(C(=O)c3ccccc3)c(-c3cc(*)ccc3C(=O)c3ccccc3)c2)cc1
*Oc1ccc(Oc2ccc(C(=O)c3ccccc3-c3ccccc3C(=O)c3ccc(*)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(NC(=C(C#N)C#N)c3ccc(C(Nc4ccc(*)cc4)=C(C#N)C#N)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(NC(=C(C#N)C#N)c3cccc(C(Nc4ccc(*)cc4)=C(C#N)C#N)c3)cc2)cc1
*Oc1ccc(Oc2ccc(NC(=O)c3cc(C(=O)Nc4ccc(*)cc4)cc(C(C)(C)C)c3)cc2)cc1
*Oc1ccc(Oc2ccc(NC(=O)c3cc(NC(=O)c4ccc(OC(C)=O)cc4)cc(C(=O)Nc4ccc(*)cc4)c3)cc2)cc1
*Oc1ccc(Oc2ccc(NC(=O)c3ccc(-c4ccc(C(=O)Nc5ccc(*)cc5)c(C)c4)cc3C)cc2)cc1
*Oc1ccc(Oc2ccc(NC(=O)c3ccc(Oc4ccc(C(C)(C)c5ccc(C(C)(C)c6ccc(Oc7ccc(C(=O)Nc8ccc(*)cc8)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(NC(=O)c3ccc(Oc4ccc(C(C)(C)c5cccc(C(C)(C)c6ccc(Oc7ccc(C(=O)Nc8ccc(*)cc8)cc7)cc6)c5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(NC(=O)c3ccc(Oc4ccc(Oc5ccc(C(=O)Nc6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(NC(=O)c3ccc(Oc4ccc(Oc5ccc(C(=O)Nc6ccc(*)cc6)cc5)cc4-c4ccccc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(NC(=O)c3ccc(Oc4ccc(Oc5ccc(C(=O)Nc6ccc(*)cc6)cc5)cc4-c4ccccc4)cc3)cc2)cc1-c1ccccc1
*Oc1ccc(Oc2ccc(NC(=O)c3ccc(Oc4ccc(Oc5ccc(C(=O)Nc6ccc(*)cc6)cc5)cc4C)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(NC(=O)c3ccc(Oc4ccc(Oc5ccc(C(=O)Nc6ccc(*)cc6)cc5)cc4C)cc3)cc2)cc1C
*Oc1ccc(Oc2ccc(NC(=O)c3cccc(C(=O)Nc4ccc(*)cc4)c3)cc2)cc1C(C)(C)C
*Oc1ccc(Oc2ccc(Nc3ccc(S(=O)(=O)c4ccc(Nc5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(Oc2ccc(OC(=O)NC(=O)c3cc(C(=O)NC(*)=O)cc(C(C)(C)C)c3)cc2)cc1
*Oc1ccc(Oc2ccc(OC(=O)c3cc(OCCCCC)cc(C(*)=O)c3)cc2)cc1
*Oc1ccc(Oc2ccc(OC(=O)c3ccc(C(c4ccc(C(*)=O)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(OC(=O)c3cccc(C(*)=O)c3)cc2)cc1
*Oc1ccc(Oc2ccc(OC(=O)c3cccc(Oc4ccc(Oc5cccc(C(*)=O)c5)cc4)c3)cc2)cc1
*Oc1ccc(Oc2ccc(OC(=O)c3ccccc3-c3ccccc3C(*)=O)cc2)cc1
*Oc1ccc(Oc2ccc(Oc3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(Oc3ccc(NC(=O)c4cc(C(=O)Nc5ccc(*)cc5)cc(C(C)(C)C)c4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(Oc3ccc(NC(=O)c4ccc(-c5ccc(C(=O)Nc6ccc(*)cc6)c(C)c5)cc4C)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(Oc3ccc(Oc4ccc(OC(=O)c5cccc(C(*)=O)c5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(Oc3ccc(S(=O)(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(Oc3cccc(C(=O)Oc4ccc(-c5ccc(OC(=O)c6cccc(*)c6)cc5)cc4)c3)cc2)cc1
*Oc1ccc(Oc2ccc(Oc3cccc(C(=O)Oc4ccc(OC(=O)c5cccc(*)c5)cc4)c3)cc2)cc1
*Oc1ccc(Oc2ccc(P(=O)(c3ccccc3)c3ccc(Oc4ccc(Oc5cccc(*)n5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(P(C)(=O)c3ccc(*)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(S(=O)(=O)c3ccc(*)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(S(=O)(=O)c3ccc(-c4ccc(-c5ccc(S(=O)(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(S(=O)(=O)c3ccc(-c4ccc(S(=O)(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(Oc5cccc(*)n5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2ccc(S(=O)c3ccc(Oc4ccc(Oc5cccc(*)n5)cc4)cc3)cc2)cc1
*Oc1ccc(Oc2cccc(*)n2)c(-c2ccccc2)c1
*Oc1ccc(Oc2cccc(*)n2)c(C)c1
*Oc1ccc(Oc2cccc(*)n2)cc1
*Oc1ccc(Oc2cccc(C(=O)Oc3ccc(OC(=O)c4cccc(*)c4)cc3)c2)cc1
*Oc1ccc(P(=O)(c2ccccc2)c2ccc(*)cc2)cc1
*Oc1ccc(P(=O)(c2ccccc2)c2ccc(Oc3ccc(-c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(P(=O)(c2ccccc2)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(P(=O)(c2ccccc2)c2ccc(Oc3ccc(C(c4ccc(*)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc2)cc1
*Oc1ccc(P(C)(=O)c2ccc(*)cc2)cc1
*Oc1ccc(P(C)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(P(C)(=O)c2ccc(Oc3ccc(C(c4ccc(*)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc2)cc1
*Oc1ccc(P(C)(=O)c2ccc(Oc3ccc(C(c4ccccc4)(c4ccc(*)cc4)C(F)(F)F)cc3)cc2)cc1
*Oc1ccc(P(C)(=O)c2ccc(Oc3ccc(P(=O)(c4ccccc4)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)N(C)CCCCN(C)S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(*)cc2)c2ccccc12
*Oc1ccc(S(=O)(=O)c2ccc(*)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(-c3ccc(*)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(-c3ccc(-c4ccc(S(=O)(=O)c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(-c3ccc(-c4ccc(S(=O)(=O)c5ccc(Oc6ccc(-c7ccc(*)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(-c3ccc(S(=O)(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(-c3ccc(S(=O)(=O)c4ccc(Oc5ccc(-c6ccc(-c7ccc(*)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(OC(=O)OC3C(C)(C)C(OC(*)=O)C3(C)C)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(OC(=O)c3cc(CCCCC)cc(C(*)=O)c3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(OC(=O)c3ccc([Si](C)(C)c4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(OC(=O)c3ccc([Si](c4ccccc4)(c4ccccc4)c4ccc(C(*)=O)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(OC(=O)c3cccc(C(*)=O)c3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(OC3(F)C(*)(F)C(F)(F)C3(F)F)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3c(C(C)C)cc(C(C)(C)c4cc(C(C)C)c(*)c(C(C)C)c4)cc3C(C)C)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3c(C)cc(-c4cc(C)c(*)c(C)c4)cc3C)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3c(C)cc(-c4cc(C)c(*)c(C)c4Br)c(Br)c3C)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3c(C)cc(C(C)(C)c4cc(C)c(*)c(C)c4)cc3C)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3c(C)cc(C(C)(C)c4cc(C)c(*)c(C)c4)cc3C)cc2C(O)(C(F)(F)F)C(F)(F)F)c(C(O)(C(F)(F)F)C(F)(F)F)c1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3c(C)cc(C(C)(C)c4cc(C)c(*)c(C)c4Br)c(Br)c3C)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3c(C)cc(C(c4cc(C)c(*)c(C)c4)(C(F)(F)F)C(F)(F)F)cc3C)c(Br)c2)cc1Br
*Oc1ccc(S(=O)(=O)c2ccc(Oc3c(C)cc(C(c4cc(C)c(*)c(C)c4)(C(F)(F)F)C(F)(F)F)cc3C)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3c(C)cc(C(c4cc(C)c(*)c(C)c4)(C(F)(F)F)C(F)(F)F)cc3C)cc2C(O)(C(F)(F)F)C(F)(F)F)c(C(O)(C(F)(F)F)C(F)(F)F)c1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3c(C)cc(C(c4cc(C)c(*)c(C)c4)(C(F)(F)F)C(F)(F)F)cc3C)cc2C(OC)(C(F)(F)F)C(F)(F)F)c(C(OC)(C(F)(F)F)C(F)(F)F)c1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3c(C)cc(C(c4cc(C)c(*)c(C)c4)(C(F)(F)F)C(F)(F)F)cc3C)cc2[Si](C)(C)C)c([Si](C)(C)C)c1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3c(C)cc(Cc4cc(C)c(*)c(C)c4)cc3C)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3cc(C)c(C(c4cc(C(C)C)c(*)cc4C)c4ccccc4C(=O)O)cc3C(C)C)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(-c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(-c4ccc(*)cc4)cc3)cc2N)c(N)c1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(-c4ccc(-c5ccc(*)c(C(F)(F)F)c5)cc4)cc3C(F)(F)F)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(-c4ccc(-c5ccc(*)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(-c4ccc(Oc5ccc(C(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(/C=C/c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(/C=C4\CC/C(=C\c5ccc(*)cc5)C4=O)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(/C=C4\CCC/C(=C\c5ccc(*)cc5)C4=O)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(/C=C4\CCC/C(=C\c5ccc(Oc6ccc(S(=O)(=O)c7ccc(Oc8ccc(C(C)(C)c9ccc(*)cc9)cc8)cc7)cc6)cc5)C4=O)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(/C=C4\CCCC/C(=C\c5ccc(*)cc5)C4=O)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(/C=C4\CCCC/C(=C\c5ccc(Oc6ccc(S(=O)(=O)c7ccc(Oc8ccc(C(C)(C)c9ccc(*)cc9)cc8)cc7)cc6)cc5)C4=O)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(/C=N/N=C/c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(/C=N/c4ccc(Oc5ccc(-c6ccc(Oc7ccc(/N=C/c8ccc(*)cc8)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(/C=N/c4ccc(Oc5ccc(/N=C/c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(/N=C/c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(=C)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(=O)c4cc(Br)cc(C(=O)c5ccc(*)cc5)c4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(=O)c4cc(C(=O)c5ccc(*)cc5)cc(C(C)(C)C)c4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(=O)c4cc(I)cc(C(=O)c5ccc(*)cc5)c4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(=O)c4ccc(-c5ccc(C(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(=O)c4cccc(C(=O)c5ccc(*)cc5)c4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)c(C(O)(C(F)(F)F)C(F)(F)F)c4)cc3C(O)(C(F)(F)F)C(F)(F)F)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)c(C(OC)(C(F)(F)F)C(F)(F)F)c4)cc3C(OC)(C(F)(F)F)C(F)(F)F)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)c(C)c4)cc3C)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)c(Cl)c4)cc3Cl)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)c(N)c4)cc3N)cc2)c(N)c1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)c(N)c4)cc3N)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)c(S(=O)(=O)O)c2)cc1S(=O)(=O)O
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)c([N+](=O)[O-])c2)cc1[N+](=O)[O-]
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)cc2)c(N)c1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)cc2N)c(N)c1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3[N+](=O)[O-])cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(OCCCCOc5ccc(C(C)(C)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(Oc5ccc(C(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(CCC(=O)O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(c4ccc(*)cc4)c4ccc(O)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(c4ccccc4)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(c4ccc(*)c(Br)c4)(C(F)(F)F)C(F)(F)F)cc3Br)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(c4ccc(*)c([Si](C)(C)C)c4)(C(F)(F)F)C(F)(F)F)cc3[Si](C)(C)C)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(c4ccc(*)c([Si](C)(C)C)c4)(C(F)(F)F)C(F)(F)F)cc3[Si](C)(C)C)cc2[Si](C)(C)C)c([Si](C)(C)C)c1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(c4ccc(*)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(c4ccc(*)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc2C(O)(C(F)(F)F)C(F)(F)F)c(C(O)(C(F)(F)F)C(F)(F)F)c1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(c4ccc(*)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc2[Si](C)(C)C)c([Si](C)(C)C)c1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(c4ccc(*)cc4)C(C)C)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(c4ccc(*)cc4)c4ccccc4C(=O)O)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(c4ccccc4)(c4ccccc4)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(*)c(C)c5)CCCCC4)cc3C)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(*)cc5)C5CC6CC(C5)CC4C6)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(*)cc5)CC5CCC4C5)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(*)cc5)CCCC4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(*)cc5)CCCCC4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(*)cc5)CCCCCC4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(*)cc5)c5ccccc5-c5ccccc54)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(Oc6ccc(S(=O)(=O)c7ccc(Oc8ccc(/C=C9\CC/C(=C\c%10ccc(*)cc%10)C9=O)cc8)cc7)cc6)cc5)CCCC4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(Oc6ccc(S(=O)(=O)c7ccc(Oc8ccc(/C=C9\CCC/C(=C\c%10ccc(*)cc%10)C9=O)cc8)cc7)cc6)cc5)CCCCC4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4(c5ccc(Oc6ccc(S(=O)(=O)c7ccc(Oc8ccc(/C=C9\CCCC/C(=C\c%10ccc(*)cc%10)C9=O)cc8)cc7)cc6)cc5)CCCCCC4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C45CC6CC(CC(c7ccc(*)cc7)(C6)C4)C5)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C4CCCC(C(C)c5ccc(*)cc5)C4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(Cc4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(NC(=O)c4cc(C(=O)Nc5ccc(*)cc5)cc(C(C)(C)C)c4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(NC(=O)c4cc(NC(=O)c5ccc(OC(C)=O)cc5)cc(C(=O)Nc5ccc(*)cc5)c4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(NC(=O)c4cc(NC(=O)c5ccncc5)cc(C(=O)Nc5ccc(*)cc5)c4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(NC(=O)c4ccc(-c5ccc(C(=O)Nc6ccc(*)cc6)c(C)c5)cc4C)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(NC(=O)c4ccc(Oc5ccc(C(C)(C)c6ccc(C(C)(C)c7ccc(Oc8ccc(C(=O)Nc9ccc(*)cc9)cc8)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(NC(=O)c4ccc(Oc5ccc(C(C)(C)c6cccc(C(C)(C)c7ccc(Oc8ccc(C(=O)Nc9ccc(*)cc9)cc8)cc7)c6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(NC(=O)c4cccc(C(=O)Nc5ccc(*)cc5)c4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(P(=O)(c4ccccc4)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(P(C)(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(S(=O)(=O)c4ccc(-c5ccc(-c6ccc(S(=O)(=O)c7ccc(*)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(S(=O)(=O)c4ccc(-c5ccc(S(=O)(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(Sc4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc4c(=O)n5c6cc(-c7ccc8c(c7)nc7c9ccc(*)c%10cccc(c(=O)n87)c%109)ccc6nc5c5cccc3c45)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc4c(=O)n5c6cc(Oc7ccc8c(c7)nc7c9ccc(*)c%10cccc(c(=O)n87)c%109)ccc6nc5c5cccc3c45)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc(Oc3cccc(NC(=O)c4ccc(-c5ccc(C(=O)Nc6cccc(*)c6)c(C)c5)cc4C)c3)cc2)cc1
*Oc1ccc(S(=O)(=O)c2ccc3cc(S(=O)(=O)c4ccc(Oc5c(C)cc(-c6cc(C)c(*)c(C)c6)cc5C)cc4)ccc3c2)cc1
*Oc1ccc(S(=O)(=O)c2ccc3cc(S(=O)(=O)c4ccc(Oc5c(C)cc(-c6cc(C)c(*)c(C)c6C)c(C)c5C)cc4)ccc3c2)cc1
*Oc1ccc(S(=O)(=O)c2ccc3cc(S(=O)(=O)c4ccc(Oc5ccc(-c6ccc(*)cc6)cc5)cc4)ccc3c2)cc1
*Oc1ccc(S(=O)(=O)c2ccc3ccc(S(=O)(=O)c4ccc(*)cc4)cc3c2)cc1
*Oc1ccc(S(=O)(=O)c2cccc3c(S(=O)(=O)c4ccc(Oc5c(C)cc(-c6cc(C)c(*)c(C)c6)cc5C)cc4)cccc23)cc1
*Oc1ccc(S(=O)(=O)c2cccc3c(S(=O)(=O)c4ccc(Oc5ccc(-c6ccc(*)cc6)cc5)cc4)cccc23)cc1
*Oc1ccc(S(=O)c2ccc(Oc3ccc(/C=N/N=C/c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(S(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(SC(=O)Oc2ccc(C(C)(C)c3ccc(OC(=O)Sc4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc(SC(=O)Sc2ccc(*)cc2)cc1
*Oc1ccc(SSc2ccc(*)cc2)cc1
*Oc1ccc(Sc2ccc(OC(*)=O)cc2)cc1
*Oc1ccc(Sc2ccc(OC(=O)OC3C(C)(C)C(OC(*)=O)C3(C)C)cc2)cc1
*Oc1ccc(Sc2ccc(OC3(F)C(*)(F)C(F)(F)C3(F)F)cc2)cc1
*Oc1ccc(Sc2ccc(Oc3ccc(-c4ccc(-c5ccc(*)c(C(F)(F)F)c5)cc4)cc3C(F)(F)F)cc2)cc1
*Oc1ccc(Sc2ccc(Oc3ccc(-c4ccc(-c5ccc(-c6ccc(*)c(C(F)(F)F)c6)cc5)cc4)cc3C(F)(F)F)cc2)cc1
*Oc1ccc(Sc2ccc(Oc3ccc4c(=O)n5c6cc(-c7ccc8c(c7)nc7c9ccc(*)c%10cccc(c(=O)n87)c%109)ccc6nc5c5cccc3c45)cc2)cc1
*Oc1ccc(Sc2ccc(Oc3ccc4c(=O)n5c6cc(Oc7ccc8c(c7)nc7c9ccc(*)c%10cccc(c(=O)n87)c%109)ccc6nc5c5cccc3c45)cc2)cc1
*Oc1ccc(Sc2ccc(Sc3ccc(Sc4ccc(*)cc4)cc3)cc2)cc1
*Oc1ccc2c(c1)C(C)(c1ccc(OC(*)=O)cc1)CC2(C)C
*Oc1ccc2c(c1)C(C)(c1ccc(OC(=O)c3ccc(C(*)=O)cc3)cc1)CC2(C)C
*Oc1ccc2c(c1)C(C)(c1ccc(OC(=O)c3cccc(C(*)=O)c3)cc1)CC2(C)C
*Oc1ccc2c(c1)C1(CC2(C)C)CC(C)(C)c2ccc(OC(*)=O)cc21
*Oc1ccc2c(c1)C1(CC2(C)C)CC(C)(C)c2ccc(OC(=O)c3ccc(C(*)=O)cc3)cc21
*Oc1ccc2c(c1)C1(CC2(C)C)CC(C)(C)c2ccc(OC(=O)c3cccc(C(*)=O)c3)cc21
*Oc1ccc2cc(C(*)=O)ccc2c1
*Oc1ccc2cc(OC(=O)c3ccc(-c4ccc(C(*)=O)cc4)cc3-c3ccccc3)ccc2c1
*Oc1ccc2cc(OC(=O)c3ccc(Oc4ccc(C(*)=O)cc4)cc3)ccc2c1
*Oc1ccc2cc(OC(=O)c3cccc(Oc4ccc(Oc5cccc(C(*)=O)c5)cc4)c3)ccc2c1
*Oc1ccc2cc(Oc3ccc(C(=O)c4ccc(*)cc4)cc3)ccc2c1
*Oc1ccc2cc(Oc3ccc(S(=O)(=O)c4ccc(*)cc4)cc3)ccc2c1
*Oc1ccc2cc(Oc3ccc(S(=O)(=O)c4ccc(Oc5ccc(C(=O)c6ccc(*)cc6)cc5)cc4)cc3)ccc2c1
*Oc1ccc2cc(Oc3ccc4c(=O)n5c6cc(-c7ccc8c(c7)nc7c9ccc(*)c%10cccc(c(=O)n87)c%109)ccc6nc5c5cccc3c45)ccc2c1
*Oc1ccc2ccc(OC(=O)c3ccc(Oc4ccc(C(*)=O)cc4)cc3)cc2c1
*Oc1ccc2ccc(Oc3ccc(/C(=N/c4ccc(/N=C(\c5ccccc5)c5ccc(*)cc5)cc4)c4ccccc4)cc3)cc2c1
*Oc1ccc2ccc(Oc3ccc(/C(=N/c4ccc(Oc5ccc(/N=C(\c6ccccc6)c6ccc(*)cc6)cc5)cc4)c4ccccc4)cc3)cc2c1
*Oc1ccc2ccc(Oc3ccc(C(=O)Nc4ccc(C(C)(C)c5ccc(C(C)(C)c6ccc(NC(=O)c7ccc(*)cc7)cc6)cc5)cc4)cc3)cc2c1
*Oc1ccc2ccc(Oc3ccc(C(=O)Nc4ccc(Oc5ccc(C(C)(C)c6ccc(C(C)(C)c7ccc(Oc8ccc(NC(=O)c9ccc(*)cc9)cc8)cc7)cc6)cc5)cc4)cc3)cc2c1
*Oc1ccc2ccc(Oc3ccc(C(=O)Nc4ccc(Oc5ccc(C(C)(C)c6ccc(Oc7ccc(NC(=O)c8ccc(*)cc8)cc7)cc6)cc5)cc4)cc3)cc2c1
*Oc1ccc2ccc(Oc3ccc(C(=O)Nc4ccc(Oc5ccc(C(C)(c6ccccc6)c6ccc(Oc7ccc(NC(=O)c8ccc(*)cc8)cc7)cc6)cc5)cc4)cc3)cc2c1
*Oc1ccc2ccc(Oc3ccc(C(=O)Nc4ccc(Oc5ccc(C(c6ccc(Oc7ccc(NC(=O)c8ccc(*)cc8)cc7)cc6)(C(F)(F)F)C(F)(F)F)cc5)cc4)cc3)cc2c1
*Oc1ccc2ccc(Oc3ccc(C(=O)Nc4ccc(Oc5ccc(Oc6ccc(NC(=O)c7ccc(*)cc7)cc6)cc5)cc4)cc3)cc2c1
*Oc1ccc2ccc(Oc3ccc(C(=O)Nc4ccc(Oc5ccc(S(=O)(=O)c6ccc(Oc7ccc(NC(=O)c8ccc(*)cc8)cc7)cc6)cc5)cc4)cc3)cc2c1
*Oc1ccc2ccc(Oc3ccc(C(=O)Nc4cccc(NC(=O)c5ccc(*)cc5)c4)cc3)cc2c1
*Oc1ccc2ccc(Oc3ccc(C(=O)Nc4cccc(Oc5ccc(NC(=O)c6ccc(*)cc6)cc5)c4)cc3)cc2c1
*Oc1cccc(*)c1
*Oc1cccc(C(*)=O)c1
*Oc1cccc(C(=O)Nc2ccc(-c3ccc(NC(=O)c4cccc(Oc5nc(*)nc(Sc6ccccc6)n5)c4)cc3)cc2)c1
*Oc1cccc(C(=O)Nc2ccc(S(=O)(=O)c3ccc(NC(=O)c4cccc(Oc5nc(*)nc(Sc6ccccc6)n5)c4)cc3)cc2)c1
*Oc1cccc(C(=O)OCC(F)(F)C(F)(F)C(F)(F)COC(=O)c2cccc(*)c2)c1
*Oc1cccc(C(=O)c2ccc(*)cc2)c1
*Oc1cccc(C(=O)c2ccc(Oc3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)c1
*Oc1cccc(C(F)(F)C(F)(F)C(F)(F)c2cccc(OC(*)=O)c2)c1
*Oc1cccc(C(F)(F)C(F)(F)C(F)(F)c2cccc(OC(=O)c3cccc(C(F)(F)C(F)(F)C(F)(F)c4cccc(C(*)=O)c4)c3)c2)c1
*Oc1cccc(N(c2ccccc2)c2ccc(-c3ccc(N(c4ccc(/N=N/c5ccc([N+](=O)[O-])cc5)cc4)c4cccc(OC(=O)OCCOCCOC(*)=O)c4)cc3)cc2)c1
*Oc1cccc(N(c2ccccc2)c2ccc(-c3ccc(N(c4ccccc4)c4cccc(OC(=O)OCCOCCOC(*)=O)c4)cc3)cc2)c1
*Oc1cccc(NC(=O)CCCCC(=O)Nc2ccc(*)cc2)c1
*Oc1cccc(NC(=O)CCCCCC(=O)Nc2ccc(*)cc2)c1
*Oc1cccc(NC(=O)CCCCCCC(=O)Nc2ccc(*)cc2)c1
*Oc1cccc(NC(=O)CCCCCCCC(=O)Nc2ccc(*)cc2)c1
*Oc1cccc(NC(=O)CCCCCCCCC(=O)Nc2ccc(*)cc2)c1
*Oc1cccc(NC(=O)CCCCCCCCCC(=O)Nc2ccc(*)cc2)c1
*Oc1cccc(NC(=O)CCCCCCCCCCC(=O)Nc2ccc(*)cc2)c1
*Oc1cccc(NC(=O)c2cc(C(=O)Nc3ccc(*)cc3)cc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)c2)c1
*Oc1cccc(NC(=O)c2cc(C(=O)Nc3ccc(*)cc3)cc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)F)c2)c1
*Oc1cccc(NC(=O)c2cc(NC(=O)c3ccc(OC(C)=O)cc3)cc(C(=O)Nc3ccc(*)cc3)c2)c1
*Oc1cccc(NC(=O)c2ccc(C(=O)Nc3ccc(*)cc3)cc2)c1
*Oc1cccc(NC(=O)c2ccc(C(=O)Nc3ccc(Oc4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)c1
*Oc1cccc(NC(=O)c2ccc(C(=O)c3cccc(C(=O)Nc4ccc(*)cc4)c3)cc2)c1
*Oc1cccc(NC(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(C(C)(C)c5ccc(Oc6ccc(C(=O)Nc7ccc(*)cc7)cc6)cc5)cc4)cc3)cc2)c1
*Oc1cccc(NC(=O)c2ccc(Oc3ccc(C(C)(C)c4cccc(C(C)(C)c5ccc(Oc6ccc(C(=O)Nc7ccc(*)cc7)cc6)cc5)c4)cc3)cc2)c1
*Oc1cccc(NC(=O)c2ccc(P(=O)(c3ccccc3)c3ccc(C(=O)Nc4cccc(Oc5ccc(P(=O)(c6ccccc6)c6ccc(*)cc6)cc5)c4)cc3)cc2)c1
*Oc1cccc(NC(=O)c2ccc([Si](C)(C)c3ccc(C(*)=O)cc3)cc2)c1
*Oc1cccc(NC(=O)c2cccc(C(=O)Nc3ccc(*)cc3)c2)c1
*Oc1cccc(NC(=O)c2cccc(C(=O)Nc3ccc(Oc4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)c2)c1
*Oc1cccc(OC(=O)CCC(*)=O)c1
*Oc1cccc(OC(=O)Oc2ccc(C(C)(C)c3ccc(OC(*)=O)cc3)cc2)c1
*Oc1cccc(OC(=O)c2cc(OCCCCC)cc(C(*)=O)c2)c1
*Oc1cccc(OC(=O)c2ccc(C(C)(C)c3ccc(C(*)=O)cc3)cc2)c1
*Oc1cccc(OC(=O)c2ccc(OC(=O)c3cccc(C(=O)Oc4ccc(C(*)=O)cc4)c3)cc2)c1
*Oc1cccc(OC(=O)c2ccc(Oc3ccc(C(*)=O)cc3)cc2)c1
*Oc1cccc(OC(=O)c2ccc([Si](C)(C)c3ccc(C(*)=O)cc3)cc2)c1
*Oc1cccc(OC(=O)c2cccc(C(*)=O)c2)c1
*Oc1cccc(OC2(F)C(*)(F)C(F)(F)C2(F)F)c1
*Oc1cccc(Oc2ccc(-c3ccc(*)cc3)cc2)c1C#N
*Oc1cccc(Oc2ccc(/N=C/c3ccc(N(c4ccccc4)c4ccc(/C=N/c5ccc(*)cc5)cc4)cc3)cc2)c1
*Oc1cccc(Oc2ccc(C(=O)O)c(C(=O)Nc3ccc(Oc4ccc(-c5ccc(Oc6ccc(NC(=O)c7cc(*)ccc7C(=O)O)cc6)cc5)cc4)cc3)c2)c1
*Oc1cccc(Oc2ccc(C(=O)Oc3cccc(OC(=O)c4ccc(*)cc4)c3)cc2)c1
*Oc1cccc(Oc2ccc(C(=O)c3ccc(*)cc3)cc2)c1
*Oc1cccc(Oc2ccc(C(=O)c3ccc(NC(=O)CCCCCCCCC(=O)Nc4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)c1
*Oc1cccc(Oc2ccc(C(=O)c3ccc(NC(=O)c4ccc(C(=O)Nc5ccc(C(=O)c6ccc(*)cc6)cc5)cc4)cc3)cc2)c1
*Oc1cccc(Oc2ccc(C(=O)c3ccc(NC(=O)c4cccc(C(=O)Nc5ccc(C(=O)c6ccc(*)cc6)cc5)c4)cc3)cc2)c1
*Oc1cccc(Oc2ccc(C(=O)c3ccc(P(=O)(c4ccccc4)c4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)c1
*Oc1cccc(Oc2ccc(NC(=O)c3cc(NC(=O)c4ccc(OC(C)=O)cc4)cc(C(=O)Nc4ccc(*)cc4)c3)cc2)c1
*Oc1cccc(Oc2ccc(NC(=O)c3ccc(-c4ccc(C(=O)Nc5ccc(*)cc5)c(C)c4)cc3C)cc2)c1
*Oc1cccc(Oc2ccc(NC(=O)c3ccc(Oc4cccc(Oc5ccc(C(=O)Nc6ccc(*)cc6)cc5)c4)cc3)cc2)c1
*Oc1cccc(Oc2ccc(Oc3cccc(OC(=O)c4ccc(C(*)=O)cc4)c3)cc2)c1
*Oc1cccc(Oc2ccc(S(=O)(=O)c3ccc(*)cc3)cc2)c1
*Oc1cccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(C(=O)c5ccc(*)cc5)cc4)cc3)cc2)c1
*Oc1cccc(Oc2ccc3c(=O)n4c5cc(-c6ccc7c(c6)nc6c8ccc(*)c9cccc(c(=O)n76)c98)ccc5nc4c4cccc2c34)c1
*Oc1cccc(Oc2ccc3c(=O)n4c5cc(Oc6ccc7c(c6)nc6c8ccc(*)c9cccc(c(=O)n76)c98)ccc5nc4c4cccc2c34)c1
*Oc1cccc(Oc2cccc(*)c2)c1C#N
*Oc1cccc(Oc2cccc(*)n2)c1
*Oc1cccc(Oc2cccc(NC(=O)c3cc(Oc4ccc(C(=O)O)c(C(=O)Nc5cccc(*)c5)c4)ccc3C(=O)O)c2)c1
*Oc1cccc(Oc2cccc(NC(=O)c3cc(Oc4ccc(C(=O)O)c(C(=O)Nc5cccc(*)c5)c4)ccc3C(=O)O)c2)c1C#N
*Oc1cccc(Oc2cccc(NC(=O)c3cccc(Oc4cccc(Oc5cccc(C(=O)Nc6cccc(*)c6)c5)c4)c3)c2)c1
*Oc1cccc(Oc2cccc(OC(*)=O)c2)c1
*Oc1cccc(S(=O)(=O)c2ccc(*)cc2)c1
*Oc1cccc2c(OC(=O)c3ccc(Oc4ccc(C(*)=O)cc4)cc3)cccc12
*Oc1cccc2c(OC3(F)C(*)(F)C(F)(F)C3(F)F)cccc12
*Oc1cccc2cc(OC(=O)c3ccc(Oc4ccc(C(*)=O)cc4)cc3)ccc12
*Oc1cccc2ccc(Oc3ccc(NC(=O)c4cc(C(=O)Nc5ccc(*)cc5)cc(C(C)(C)C)c4)cc3)cc12
*Oc1cccc2ccc(Oc3ccc(NC(=O)c4ccc(C(c5ccc(C(=O)Nc6ccc(*)cc6)cc5)(C(F)(F)F)C(F)(F)F)cc4)cc3)cc12
*Oc1cccc2ccc(Oc3ccc(NC(=O)c4ccc(Oc5ccc(C(=O)Nc6ccc(*)cc6)cc5)cc4)cc3)cc12
*Oc1cccc2ccc(Oc3ccc(NC(=O)c4ccc(Oc5ccc(C(C)(C)c6cccc(C(C)(C)c7ccc(Oc8ccc(C(=O)Nc9ccc(*)cc9)cc8)cc7)c6)cc5)cc4)cc3)cc12
*Oc1cccc2ccc(Oc3ccc(NC(=O)c4ccc(Oc5ccc(C(C)(c6ccccc6)c6ccc(Oc7ccc(C(=O)Nc8ccc(*)cc8)cc7)cc6)cc5)cc4)cc3)cc12
*Oc1cccc2ccc(Oc3ccc(NC(=O)c4ccc(Oc5ccc(Oc6ccc(C(=O)Nc7ccc(*)cc7)cc6)cc5)cc4)cc3)cc12
*Oc1cccc2ccc(Oc3ccc(NC(=O)c4ccc(Oc5ccc(S(=O)(=O)c6ccc(Oc7ccc(C(=O)Nc8ccc(*)cc8)cc7)cc6)cc5)cc4)cc3)cc12
*Oc1cccc2ccc(Oc3ccc(NC(=O)c4ccc(Oc5cccc(Oc6ccc(C(=O)Nc7ccc(*)cc7)cc6)c5)cc4)cc3)cc12
*Oc1cccc2ccc(Oc3ccc(NC(=O)c4ccc(Oc5ccccc5Oc5ccc(C(=O)Nc6ccc(*)cc6)cc5)cc4)cc3)cc12
*Oc1cccc2ccc(Oc3ccc(NC(=O)c4ccc(S(=O)(=O)c5ccc(C(=O)Nc6ccc(*)cc6)cc5)cc4)cc3)cc12
*Oc1cccc2ccc(Oc3ccc(NC(=O)c4cccc(C(=O)Nc5ccc(*)cc5)c4)cc3)cc12
*Oc1ccccc1-c1ccccc1Oc1ccc2c(=O)n3c4cc(-c5ccc6c(c5)nc5c7ccc(*)c8cccc(c(=O)n65)c87)ccc4nc3c3cccc1c23
*Oc1ccccc1C(=O)OC(=O)c1ccccc1OC(=O)C(C)C(C)C(*)=O
*Oc1ccccc1C(=O)OC(=O)c1ccccc1OC(=O)C(CC)C(CC)C(*)=O
*Oc1ccccc1C(=O)OC(=O)c1ccccc1OC(=O)CCCC(*)=O
*Oc1ccccc1C(=O)OC(=O)c1ccccc1OC(=O)CCCCC(*)=O
*Oc1ccccc1C(=O)OC(=O)c1ccccc1OC(=O)CCCCCC(*)=O
*Oc1ccccc1C(=O)OC(=O)c1ccccc1OC(=O)CCCCCCC(*)=O
*Oc1ccccc1C(=O)OC(=O)c1ccccc1OC(=O)CCCCCCCC(*)=O
*Oc1ccccc1C(=O)OC(=O)c1ccccc1OC(=O)CCCCCCCCC(*)=O
*Oc1ccccc1C(=O)OC(=O)c1ccccc1OC(=O)c1ccc(C(*)=O)cc1
*Oc1ccccc1C(=O)OC(=O)c1ccccc1OC(=O)c1cccc(C(*)=O)c1
*Oc1ccccc1Oc1ccc(S(=O)(=O)c2ccc(*)cc2)cc1
*Oc1ccccc1S(=O)(=O)c1ccc(*)cc1
*SC(*)(F)F
*SC(C)C(*)=O
*SC1CCCC(*)C1
*SC1CCCCC1*
*SCC(=O)N/N=C/c1ccc(OCCCCCCCCCCOc2ccc(/C=N/NC(=O)CSc3nnc(*)s3)cc2)cc1
*SCC(=O)N/N=C/c1ccc(OCCCCCCCCCCOc2ccc(/C=N/NC(=O)CSc3nnc(*)s3)cc2OC)c(OC)c1
*SSc1ccc(/N=C/Nc2ccc(*)cc2)cc1
*Sc1c(C)cc(*)cc1C
*Sc1cc(C)c(*)cc1C
*Sc1ccc(*)cc1
*Sc1ccc(C(=O)CNCCCCCCCCNCC(=O)c2ccc(*)cc2)cc1
*Sc1ccc(C(=O)c2ccc(C(C)(C)c3ccc(C(=O)c4ccc(*)cc4)cc3)cc2)cc1
*Sc1ccc(C(=O)c2ccc(C(c3ccc(C(=O)c4ccc(*)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*Sc1ccc(Cc2ccc(SC(*)=O)cc2)cc1
*Sc1ccc(Nc2ccc(*)cc2)cc1
*Sc1ccc(Nc2ccc(Nc3ccc(*)cc3)cc2)cc1
*Sc1ccc(P(=O)(c2ccccc2)c2ccc(Sc3ccc(-c4ccc(*)cc4)cc3)cc2)cc1
*Sc1ccc(Sc2ccc(Sc3ccc(P(=O)(c4ccccc4)c4ccc(*)cc4)cc3)cc2)cc1
*Sc1ccc(Sc2ccc(Sc3ccc4c(c3)C(=O)N(N3C(=O)c5ccc(*)cc5C3=O)C4=O)cc2)cc1
*Sc1ccc(Sc2ccc(Sc3ccc4c(c3)C(=O)N(N3C(=O)c5cccc(*)c5C3=O)C4=O)cc2)cc1
*Sc1ccc(Sc2ccc(Sc3ccc4c(c3)Sc3ccc(*)cc3S4)cc2)cc1
*Sc1ccc(Sc2ccc(Sc3cccc4c3C(=O)N(N3C(=O)c5cccc(*)c5C3=O)C4=O)cc2)cc1
*Sc1ccc2c(c1)Sc1ccc(*)cc1S2
*Sc1cccc(Sc2ccc(Sc3ccc(*)cc3)cc2)c1
*Sc1cccc(Sc2ccc(Sc3ccc(*)cc3)cc2)c1
*[Se]c1c(Cl)c(Cl)c(*)c(Cl)c1Cl
*[Si](*)(C)CCC(F)(F)F
*[Si](*)(C)CCCOCC1COC(C)(C)O1
*[Si](*)(C)c1ccc(COCCN(CC)c2ccc(/N=N/c3ccc([N+](=O)[O-])cc3)cc2)cc1
*[Si](*)(C)c1ccccc1
*[Si](*)(CCCC)CCCC
*[Si](*)(CCCC)CCCCC
*[Si](*)(CCCC)CCCCCC
*[Si](*)(CCCCCC)c1ccccc1
*c1[nH]c(*)c(OC)c1OC
*c1[nH]c2ccccc2c1*
*c1c(C)cc(-c2cc(C)c(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c(C)c2)cc1C
*c1c(C)cc(-c2cc(C)c(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C)c2)cc1C
*c1c(C)cc(-c2cc(C)c(N3C(=O)c4ccc(Oc5c(C)cc(Cc6cc(C)c(Oc7ccc8c(c7)C(=O)N(*)C8=O)c(C)c6)cc5C)cc4C3=O)c(C)c2)cc1C
*c1c(C)cc(-c2cc(C)c(N3C(=O)c4ccc(Oc5cc6ccccc6cc5Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C)c2)cc1C
*c1c(C)cc(-c2cc(C)c(N3C(=O)c4ccc(Oc5ccc(C(C)(C)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)c(C)c2)cc1C
*c1c(C)cc(-c2cc(C)c(N3C(=O)c4ccc(S(=O)(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C)c2)cc1C
*c1c(C)cc(-c2cc(C)c(N3C(=O)c4cccc(Oc5c(C)cc(-c6cc(C)c(Oc7cccc8c7C(=O)N(*)C8=O)c(C)c6)cc5C)c4C3=O)c(C)c2)cc1C
*c1c(C)cc(-c2cc(C)c(N3C(=O)c4cccc(Oc5c(C)cc(Cc6cc(C)c(Oc7cccc8c7C(=O)N(*)C8=O)c(C)c6)cc5C)c4C3=O)c(C)c2)cc1C
*c1c(C)cc(-c2cc(C)c(N3C(=O)c4cccc(Oc5ccc(C(C)(C)c6ccc(Oc7cccc8c7C(=O)N(*)C8=O)cc6)cc5)c4C3=O)c(C)c2)cc1C
*c1c(C)cc(C(C)(C)c2cc(C)c(S(*)(=O)=O)c(C)c2)cc1C
*c1c(C)cc(C(c2cc(C(F)(F)F)cc(C(F)(F)F)c2)c2cc(C)c(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C)c2)cc1C
*c1c(C)cc(C(c2cc(C(F)(F)F)cc(C(F)(F)F)c2)c2cc(C)c(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C)c2)cc1C
*c1c(C)cc(C(c2cc(C)c(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C)c2)c2cccc3ccccc23)cc1C
*c1c(C)cc(C(c2cccc(C(F)(F)F)c2)c2cc(C)c(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C)c2)cc1C
*c1c(C)cc(C(c2cccc(C(F)(F)F)c2)c2cc(C)c(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C)c2)cc1C
*c1c(C)cc(C(c2cccnc2)c2cc(C)c(N3C(=O)CC(Nc4ccc(Cc5ccc(NC6CC(=O)N(*)C6=O)cc5)cc4)C3=O)c(C)c2)cc1C
*c1c(C)cc(C(c2cccnc2)c2cc(C)c(N3C(=O)CC(Nc4ccc(NC5CC(=O)N(*)C5=O)cc4)C3=O)c(C)c2)cc1C
*c1c(C)cc(C(c2cccnc2)c2cc(C)c(N3C(=O)CC(Nc4cccc(NC5CC(=O)N(*)C5=O)c4)C3=O)c(C)c2)cc1C
*c1c(C)cc(C)c(-n2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)c1C
*c1c(C)cc(C)c(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1C
*c1c(C)cc(C)c(N2C(=O)c3ccc(Oc4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4)cc3C2=O)c1C
*c1c(C)cc(C)c(N2C(=O)c3ccc(S(=O)(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1C
*c1c(C)cc(C)c(N2C(=O)c3ccc(Sc4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1C
*c1c(C)cc(C)c(N2C(=O)c3cccc(Oc4c(C)cc(-c5cc(C)c(Oc6cccc7c6C(=O)N(*)C7=O)c(C)c5)cc4C)c3C2=O)c1C
*c1c(C)cc(C)c(N2C(=O)c3cccc(Oc4c(C)cc(Cc5cc(C)c(Oc6cccc7c6C(=O)N(*)C7=O)c(C)c5)cc4C)c3C2=O)c1C
*c1c(C)cc(Cc2cc(C)c(N3C(=O)c4ccc(Oc5c(C)cc(-c6cc(C)c(Oc7ccc8c(c7)C(=O)N(*)C8=O)c(C)c6)cc5C)cc4C3=O)c(C)c2)cc1C
*c1c(C)cc(Cc2cc(C)c(N3C(=O)c4ccc(Oc5c(C)cc(Cc6cc(C)c(Oc7ccc8c(c7)C(=O)N(*)C8=O)c(C)c6)cc5C)cc4C3=O)c(C)c2)cc1C
*c1c(C)cc(Cc2cc(C)c(N3C(=O)c4ccc(Oc5cc6ccccc6cc5Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C)c2)cc1C
*c1c(C)cc(Cc2cc(C)c(N3C(=O)c4ccc(Oc5ccc(C(C)(C)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)c(C)c2)cc1C
*c1c(C)cc(Cc2cc(C)c(N3C(=O)c4ccc(Oc5ccc6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6c5)cc4C3=O)c(C)c2)cc1C
*c1c(C)cc(Cc2cc(C)c(N3C(=O)c4ccc(Oc5cccc6c(Oc7ccc8c(c7)C(=O)N(*)C8=O)cccc56)cc4C3=O)c(C)c2)cc1C
*c1c(C)cc(Cc2cc(C)c(N3C(=O)c4cccc(Oc5c(C)cc(-c6cc(C)c(Oc7cccc8c7C(=O)N(*)C8=O)c(C)c6)cc5C)c4C3=O)c(C)c2)cc1C
*c1c(C)cc(Cc2cc(C)c(N3C(=O)c4cccc(Oc5c(C)cc(Cc6cc(C)c(Oc7cccc8c7C(=O)N(*)C8=O)c(C)c6)cc5C)c4C3=O)c(C)c2)cc1C
*c1c(C)cc(Cc2cc(C)c(N3C(=O)c4cccc(Oc5ccc(C(C)(C)c6ccc(Oc7cccc8c7C(=O)N(*)C8=O)cc6)cc5)c4C3=O)c(C)c2)cc1C
*c1c(F)c(F)c(-c2c(F)c(F)c(Oc3ccc(-c4nn(*)c(=O)c5ccccc45)c(F)c3)c(F)c2F)c(F)c1F
*c1cc(*)cc(-c2nc3ccccc3o2)c1
*c1cc(-c2ccc(C(=C3C=C(C(C)(C)C)C(=O)C(C(C)(C)C)=C3)c3cc(C(C)(C)C)c(O)c(C(C)(C)C)c3)cc2)c(*)s1
*c1cc(-c2ccc(C(=C3C=C(C(C)(C)C)C(=O)C(C(C)(C)C)=C3)c3cc(C(C)(C)C)c(OC(C)=O)c(C(C)(C)C)c3)cc2)c(*)s1
*c1cc(-c2nc3ccccc3[nH]2)cc(-n2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)c1
*c1cc(-c2nc3ccccc3oc2=O)cc(-n2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)c1
*c1cc(-c2sc(-c3cc(CCCC)c(*)s3)cc2CCCC)c2cccccc1-2
*c1cc(-c2sc(-c3cc(CCCCCCC)c(*)s3)cc2CCCCCCC)c2cccccc1-2
*c1cc(-c2sc(-c3cc(CCCCCCCCCC)c(*)s3)cc2CCCCCCCCCC)c2cccccc1-2
*c1cc(-c2sc(-c3cc(CCCCCCCCCCCCCC)c(*)s3)cc2CCCCCCCCCCCCCC)c2cccccc1-2
*c1cc(-n2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)c(O)cc1O
*c1cc(/C=N/C(=S)Nc2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2)ccc1Cl
*c1cc(/C=N/c2ccc3c(c2)c2cc(/N=C/c4cc(*)c(O)c(*)c4)ccc2n3CCCC)ccc1O
*c1cc(/C=N/c2ccc3c(c2)c2cc(/N=C/c4cc(*)c(O)c(*)c4)ccc2n3CCCCCC)ccc1O
*c1cc(/C=N/c2ccc3c(c2)c2cc(/N=C/c4cc(*)c(O)c(*)c4)ccc2n3CCCCCCCCCCCC)ccc1O
*c1cc(/N=N/c2ccc(C#N)cc2)cc(*)c1O
*c1cc(/N=N/c2ccc(OC)cc2)cc(*)c1O
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCC)c(C(=O)OCCCC)c1
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCCCC)c(C(=O)OCCCCCC)c1
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCCCCCC)c(C(=O)OCCCCCCCC)c1
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCCCCCCCC)c(C(=O)OCCCCCCCCCC)c1
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCCCCCCCCCC)c(C(=O)OCCCCCCCCCCCC)c1
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCCCCCCCCCCCC)c(C(=O)OCCCCCCCCCCCCCC)c1
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCCCCCCCCCCCCCC)c(C(=O)OCCCCCCCCCCCCCCCC)c1
*c1cc(Br)c(-c2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(=O)OCCCCCCCCCCCCCCCCCC)c(C(=O)OCCCCCCCCCCCCCCCCCC)c1
*c1cc(Br)c(OC(=O)c2ccc(C(=O)Oc3c(Br)cc(C4(*)OC(=O)c5ccccc54)cc3Br)cc2)c(Br)c1
*c1cc(Br)c(Oc2c(Br)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2Br)c(Br)c1
*c1cc(Br)c(Oc2c(Br)cc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2Br)c(Br)c1
*c1cc(Br)c(Oc2c(Br)cc(N3C(=O)c4ccc(S(=O)(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2Br)c(Br)c1
*c1cc(C(=O)NCCCC)cc(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cc(C(=O)NCCCCCC)cc(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cc(C(=O)NCCCCCCCC)cc(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cc(C(=O)NCCCCCCCCCC)cc(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cc(C(=O)NCCCCCCCCCCCC)cc(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cc(C(=O)NCCCCCCCCCCCCCC)cc(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cc(C(=O)Nc2ccc(-c3nnc(*)o3)cc2)cc(N2C(=O)c3ccccc3C2=O)c1
*c1cc(C(=O)O)cc(N2C(=O)CC(C3CC4C(=O)N(*)C(=O)C4c4ccccc43)C2=O)c1
*c1cc(C(=O)OC)cc(N2C(=O)CC(C3Cc4ccccc4C4C(=O)N(*)C(=O)C43)C2=O)c1
*c1cc(C(=O)OCC(F)(F)C(F)(F)C(F)(F)F)c(*)s1
*c1cc(C(=O)OCC)cc(N2C(=O)CC(C3Cc4ccccc4C4C(=O)N(*)C(=O)C43)C2=O)c1
*c1cc(C(=O)OCCC)cc(N2C(=O)CC(C3Cc4ccccc4C4C(=O)N(*)C(=O)C43)C2=O)c1
*c1cc(C(=O)OCCCC)cc(N2C(=O)CC(C3Cc4ccccc4C4C(=O)N(*)C(=O)C43)C2=O)c1
*c1cc(C(=O)OCCCCC)cc(N2C(=O)CC(C3Cc4ccccc4C4C(=O)N(*)C(=O)C43)C2=O)c1
*c1cc(C(=O)OCCCCCC)cc(N2C(=O)CC(C3Cc4ccccc4C4C(=O)N(*)C(=O)C43)C2=O)c1
*c1cc(C(=O)OCCCCCCCCCCOc2ccc(-c3ccc(OC(=O)c4ccc(OCC(F)CCCCCC)cc4)cc3)cc2)c(*)s1
*c1cc(C(=O)Oc2ccc(-c3ccc(O[Si](C)(C)C(C)(C)C)cc3)cc2)cc(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cc(C(=O)Oc2cccc3cccnc23)c(*)s1
*c1cc(C(C(=O)OC)C(=O)OC)c(*)s1
*c1cc(C(C)(C)c2ccc(O)cc2)cc(*)c1O
*c1cc(C(C)C)c(Oc2ccc(S(=O)(=O)c3ccc(Oc4cc(C)c(C5(*)OC(=O)c6ccccc65)cc4C(C)C)cc3)cc2)cc1C
*c1cc(C(c2ccc(O)c(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c2)(C(F)(F)F)C(F)(F)F)ccc1O
*c1cc(C(c2ccc(O)c(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)(C(F)(F)F)C(F)(F)F)ccc1O
*c1cc(C(c2ccc(OCCN(C)c3ccc(/C=C/C4=C/C(=C(/C#N)C(=O)OC)CC(C)(C)C4)cc3)c(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)(C(F)(F)F)C(F)(F)F)ccc1OCCN(c1ccccc1)c1ccc(/C=C/C2=C/C(=C(/C#N)C(=O)OC)CC(C)(C)C2)cc1
*c1cc(C(c2ccc(OCCN(C)c3ccc(/C=C/C4=C/C(=C(/C#N)C(=O)OCCc5ccccc5)CC(C)(C)C4)cc3)c(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)(C(F)(F)F)C(F)(F)F)ccc1OCCN(c1ccccc1)c1ccc(/C=C/C2=C/C(=C(/C#N)C(=O)OCCc3ccccc3)CC(C)(C)C2)cc1
*c1cc(C(c2ccc(OCCN(C)c3ccc(/C=C/C4=C/C(=C(/C#N)C(=O)OCc5ccc(Oc6c(F)c(F)c(F)c(F)c6F)cc5)CC(C)(C)C4)cc3)c(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)(C(F)(F)F)C(F)(F)F)ccc1OCCN(c1ccccc1)c1ccc(/C=C/C2=C/C(=C(/C#N)C(=O)OCc3ccc(Oc4c(F)c(F)c(F)c(F)c4F)cc3)CC(C)(C)C2)cc1
*c1cc(C(c2ccc(OCCN(C)c3ccc(/C=C/C4=C/C(=C(/C#N)S(C)(=O)=O)CC(C)(C)C4)cc3)c(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)(C(F)(F)F)C(F)(F)F)ccc1OCCN(C)c1ccc(/C=C/C2=C/C(=C(/C#N)S(C)(=O)=O)CC(C)(C)C2)cc1
*c1cc(C(c2ccc(OCCN(C)c3ccc(/C=C/C4=CC(=C(C#N)C#N)CC(C)(C)C4)cc3)c(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)(C(F)(F)F)C(F)(F)F)ccc1OCCN(C)c1ccc(/C=C/C2=CC(=C(C#N)C#N)CC(C)(C)C2)cc1
*c1cc(C(c2ccc(OCCN(CC)c3ccc(/C=C/C4=CC(=C(C#N)C#N)CC(C)(C)C4)cc3)c(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)(C(F)(F)F)C(F)(F)F)ccc1OCCN(CC)c1ccc(/C=C/C2=CC(=C(C#N)C#N)CC(C)(C)C2)cc1
*c1cc(C(c2ccc(OCCN(c3ccccc3)c3ccc(/C=C/C4=C/C(=C(/C#N)S(C)(=O)=O)CC(C)(C)C4)cc3)c(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)(C(F)(F)F)C(F)(F)F)ccc1OCCN(c1ccccc1)c1ccc(/C=C/C2=C/C(=C(/C#N)S(C)(=O)=O)CC(C)(C)C2)cc1
*c1cc(C(c2ccc(OCCN(c3ccccc3)c3ccc(/C=C/C4=CC(=C(C#N)C#N)CC(C)(C)C4)cc3)c(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)(C(F)(F)F)C(F)(F)F)ccc1OCCN(c1ccccc1)c1ccc(/C=C/C2=CC(=C(C#N)C#N)CC(C)(C)C2)cc1
*c1cc(C(c2ccc(OCc3cc(OCCN(C)c4ccc(/C=C/C5=CC(=C(C#N)C#N)CC(C)(C)C5)cc4)cc(OCCN(C)c4ccc(/C=C/C5=CC(=C(C#N)C#N)CC(C)(C)C5)cc4)c3)c(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)(C(F)(F)F)C(F)(F)F)ccc1OCc1cc(OCCN(C)c2ccc(/C=C/C3=CC(=C(C#N)C#N)CC(C)(C)C3)cc2)cc(OCCN(C)c2ccc(/C=C/C3=CC(=C(C#N)C#N)CC(C)(C)C3)cc2)c1
*c1cc(C)c(*)s1
*c1cc(C)c(-c2c(C)cc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2C)c(C)c1
*c1cc(C)c(-c2c(C)cc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C)c(C)c1
*c1cc(C)c(/N=C/c2ccc(OCc3ccc(COc4ccc(/C=N/c5cc(C)c(-n6c(=O)c7ccc8c9ccc%10c(=O)n(*)c(=O)c%11ccc(c%12ccc(c6=O)c7c8%12)c9c%10%11)cc5C)cc4)cc3)cc2)cc1C
*c1cc(C)c(/N=C/c2ccc(OCc3ccc(COc4ccc(/C=N/c5cc(C)c(-n6c(=O)c7ccc8c9ccc%10c(=O)n(*)c(=O)c%11ccc(c%12ccc(c6=O)c7c8%12)c9c%10%11)cc5C)cc4OC)cc3)c(OC)c2)cc1C
*c1cc(C)c(/N=C/c2ccc(OCc3ccccc3COc3ccc(/C=N/c4cc(C)c(-n5c(=O)c6ccc7c8ccc9c(=O)n(*)c(=O)c%10ccc(c%11ccc(c5=O)c6c7%11)c8c9%10)cc4C)cc3)cc2)cc1C
*c1cc(C)c(/N=C/c2ccc(OCc3ccccc3COc3ccc(/C=N/c4cc(C)c(-n5c(=O)c6ccc7c8ccc9c(=O)n(*)c(=O)c%10ccc(c%11ccc(c5=O)c6c7%11)c8c9%10)cc4C)cc3OC)c(OC)c2)cc1C
*c1cc(C)c(Cc2cc(C)c(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)cc2C)cc1C
*c1cc(C)c(Oc2ccc(S(=O)(=O)c3ccc(Oc4cc(C)c(C5(*)OC(=O)c6ccccc65)cc4C)cc3)cc2)cc1C
*c1cc(CC(=O)OCC)c(*)s1
*c1cc(CC(=O)OCCCCCCOc2ccc(/N=N/c3ccc(C#N)cc3)cc2)c(*)s1
*c1cc(CC(=O)OCCN(CC)c2ccc(/N=N/c3ccc([N+](=O)[O-])cc3)cc2)c(*)s1
*c1cc(CCCC)c(*)s1
*c1cc(CCCCBr)c(*)s1
*c1cc(CCCCCC)c(*)s1
*c1cc(CCCCCC)c(-c2ccc3c4ccc(-c5sc(-c6sc(*)c7nc(CC)c(CC)nc67)cc5CCCCCC)cc4n(CCCCCCCCCCCC)c3c2)s1
*c1cc(CCCCCCCC)c(*)s1
*c1cc(CCCCCCCCCC)c(*)s1
*c1cc(CCCCCCCCCCCC)c(*)s1
*c1cc(CCCCCCOC(=O)COC)c(*)s1
*c1cc(CCCCCCOC(=O)COc2ccccc2)c(*)s1
*c1cc(CCCCCCOC(=O)Cc2ccccc2)c(*)s1
*c1cc(CCCCCCO[Si](C)(C)C)c(*)s1
*c1cc(CCOC(=O)C(C)Br)c(*)s1
*c1cc(CCOC(=O)NCCCC)c(*)s1
*c1cc(CCOCCOC)c(*)[nH]1
*c1cc(CCOc2ccc(/N=N/c3ccc(C#N)cc3)cc2)c(*)s1
*c1cc(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c(O)cc1O
*c1cc(N2C(=O)c3ccc(Oc4cc5ccccc5cc4Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)cc(C(F)(F)F)c1
*c1cc(N2C(=O)c3ccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c(O)cc1O
*c1cc(N2C(=O)c3ccc(Oc4ccc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5c4)cc3C2=O)cc(C(F)(F)F)c1
*c1cc(N2C(=O)c3ccc(Oc4cccc5c(Oc6ccc7c(c6)C(=O)N(*)C7=O)cccc45)cc3C2=O)cc(C(F)(F)F)c1
*c1cc(N2C(=O)c3ccc(P(=O)(c4ccccc4)c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)cc(C(F)(F)F)c1
*c1cc(OCCCC)c(*)cc1OCCCC
*c1cc(OCCCC)cc(N2C(=O)c3ccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cc(OCCCCCCCC)cc(N2C(=O)c3ccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cc(OCCCCCCCCCCCC)cc(N2C(=O)c3ccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cc(OCCCCCCCCCCCCCCCC)cc(N2C(=O)c3ccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cc(Oc2c(C)cc(-c3cc(C)c(Oc4cc(N5C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc(C(F)(F)F)c4)c(C)c3)cc2C)cc(C(F)(F)F)c1
*c1cc(Oc2c(C)cc(-c3cc(C)c(Oc4cc(N5C(=O)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc(C(F)(F)F)c4)c(C)c3)cc2C)cc(C(F)(F)F)c1
*c1cc(SCCCCCC)c(*)s1
*c1cc(SCCCCCCCCCCCC)c(*)s1
*c1cc2c(cc1C)-c1cc(C)c(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc1S2(=O)=O
*c1ccc(*)[nH]1
*c1ccc(*)c(C(=O)c2ccc(C)cc2)c1
*c1ccc(*)c(C(=O)c2ccc(F)cc2)c1
*c1ccc(*)c(C(=O)c2ccccc2)c1
*c1ccc(*)cc1
*c1ccc(*)s1
*c1ccc(-c2c(-c3ccc(-c4ccccc4)cc3)cc(-c3ccc(OCCCCCCCCCCOc4ccc(-c5cc(-c6ccc(-c7ccccc7)cc6)c(-c6ccc(-n7c(=O)c8cc9c(=O)n(*)c(=O)c9cc8c7=O)cc6)c(-c6ccc(-c7ccccc7)cc6)c5)cc4)cc3)cc2-c2ccc(-c3ccccc3)cc2)cc1
*c1ccc(-c2c(-c3ccc(-c4ccccc4)cc3)cc(-c3ccc(OCCCCCCOc4ccc(-c5cc(-c6ccc(-c7ccccc7)cc6)c(-c6ccc(-n7c(=O)c8cc9c(=O)n(*)c(=O)c9cc8c7=O)cc6)c(-c6ccc(-c7ccccc7)cc6)c5)cc4)cc3)cc2-c2ccc(-c3ccccc3)cc2)cc1
*c1ccc(-c2c(-c3ccccc3)nc3ccc(-c4ccc5nc(-c6ccccc6)c(*)c(-c6ccccc6)c5c4)cc3c2-c2ccccc2)cc1
*c1ccc(-c2c(-c3ccccc3)nc3ccc(Oc4ccc5nc(-c6ccccc6)c(*)c(-c6ccccc6)c5c4)cc3c2-c2ccccc2)cc1
*c1ccc(-c2cc(-c3ccc(-c4ccccc4)cc3)cc(-c3ccc(N4C(=O)c5ccc(NC(=O)Nc6ccc(-c7cc(-c8ccc(-c9ccccc9)cc8)cc(-c8ccc(NC(=O)Nc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)n7)cc6)cc5C4=O)cc3)n2)cc1
*c1ccc(-c2cc(-c3ccc(-c4ccccc4)cc3)cc(-c3ccc(N4C(=O)c5ccc(NC(=O)Nc6ccc(NC(=O)Nc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5C4=O)cc3)n2)cc1
*c1ccc(-c2cc(-c3ccc(-c4ccccc4)cc3)cc(-c3ccc(N4C(=O)c5ccc(NC(=O)Nc6ccc(Oc7ccc(NC(=O)Nc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)n2)cc1
*c1ccc(-c2cc(-c3ccc(-c4ccccc4)cc3)cc(-c3ccc(N4C(=O)c5ccc(NC(=O)Nc6ccc(Oc7ccccc7-c7ccccc7Oc7ccc(NC(=O)Nc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)n2)cc1
*c1ccc(-c2cc(-c3ccc(-c4ccccc4)cc3)cc(-c3ccc(N4C(=O)c5ccc(NC(=O)Nc6cccc(NC(=O)Nc7ccc8c(c7)C(=O)N(*)C8=O)n6)cc5C4=O)cc3)n2)cc1
*c1ccc(-c2cc(-c3ccc(-c4ccccc4)cc3)cc(-c3ccc(N4C(=O)c5ccc(NC(=O)Nc6cccc7c(NC(=O)Nc8ccc9c(c8)C(=O)N(*)C9=O)cccc67)cc5C4=O)cc3)n2)cc1
*c1ccc(-c2cc(-c3ccc(OCC(CC)CCCC)cc3)cc(-c3ccc(-c4ccc5c(c4)C(CCCCCC)(CCCCCC)c4cc(*)ccc4-5)cc3)c2-c2ccc(OCC(CC)CCCC)cc2)cc1
*c1ccc(-c2cc(-c3ccc(OCCCC#N)cc3)cc(-c3ccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)cc3)n2)cc1
*c1ccc(-c2cc(-c3ccc(OCCCC#N)cc3)cc(-c3ccc(N4C(=O)c5ccc(-c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)n2)cc1
*c1ccc(-c2cc(-c3ccc(OCCCC#N)cc3)cc(-c3ccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)n2)cc1
*c1ccc(-c2cc(-c3ccc(OCCCCCC)cc3)cc(-c3ccc(-c4ccc5c(c4)C(CCCCCC)(CCCCCC)c4cc(*)ccc4-5)cc3)c2-c2ccc(OCCCCCC)cc2)cc1
*c1ccc(-c2cc(-c3ccc(OCCCCCCCCCCCC)cc3)cc(-c3ccc(-c4ccc5c(c4)C(CCCCCC)(CCCCCC)c4cc(*)ccc4-5)cc3)c2-c2ccc(OCCCCCCCCCCCC)cc2)cc1
*c1ccc(-c2cc(-c3ccccc3)c3cc(-c4ccc5nc(*)cc(-c6ccccc6)c5c4)ccc3n2)cc1
*c1ccc(-c2cc(C(C)(C)C)c(Oc3ccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)cc3)c(C(C)(C)C)c2)cc1
*c1ccc(-c2cc(C(C)(C)C)c(Oc3ccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)cc3C(F)(F)F)c(C(C)(C)C)c2)cc1
*c1ccc(-c2cc(C(C)(C)C)c(Oc3ccc(-n4c(=O)c5ccc6c(=O)n(*)c(=O)c7ccc(c4=O)c5c67)cc3C(F)(F)F)c(C(C)(C)C)c2)cc1
*c1ccc(-c2cc(C(C)(C)C)c(Oc3ccc(N4C(=O)c5ccc(-c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)c(C(C)(C)C)c2)cc1
*c1ccc(-c2cc(C(C)(C)C)c(Oc3ccc(N4C(=O)c5ccc(-c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3C(F)(F)F)c(C(C)(C)C)c2)cc1
*c1ccc(-c2cc(C(C)(C)C)c(Oc3ccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)c(C(C)(C)C)c2)cc1
*c1ccc(-c2cc(C(C)(C)C)c(Oc3ccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3C(F)(F)F)c(C(C)(C)C)c2)cc1
*c1ccc(-c2cc(C(C)(C)C)c(Oc3ccc(N4C(=O)c5ccc(S(=O)(=O)c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)c(C(C)(C)C)c2)cc1
*c1ccc(-c2cc(C(C)(C)C)c(Oc3ccc(N4C(=O)c5ccc(S(=O)(=O)c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3C(F)(F)F)c(C(C)(C)C)c2)cc1
*c1ccc(-c2cc(CCCCCC)c(-c3ccc(-c4ccc5ccc6ccc(*)nc6c5n4)cc3)cc2CCCCCC)cc1
*c1ccc(-c2cc(CCCCCCBr)c(*)s2)s1
*c1ccc(-c2cc(CCCCCCCC)c(-c3ccc(-c4ccc(-c5sc(-c6ccc([Si](*)(C)C)s6)cc5CCCCCCCC)s4)s3)s2)s1
*c1ccc(-c2cc(CCCCCCCC)c(-c3ccc(-c4ccc(-c5sc(-c6ccc([Si](*)(CCCC)CCCC)s6)cc5CCCCCCCC)s4)s3)s2)s1
*c1ccc(-c2cc(CCCCCCCC)c(-c3ccc(-c4ccc(-c5sc(-c6ccc([Si](C)(C)[Si](*)(C)C)s6)cc5CCCCCCCC)s4)s3)s2)s1
*c1ccc(-c2cc(CCCCCCCC)c(-c3ccc(-c4ccc(-c5sc(-c6ccc([Si](C)(C)[Si](C)(C)[Si](C)(C)[Si](*)(C)C)s6)cc5CCCCCCCC)s4)s3)s2)s1
*c1ccc(-c2cc(CCCCCCCC)c(-c3ccc(-c4ccc(-c5sc(-c6ccc([Si](C)(C)[Si](C)(C)[Si](C)(C)[Si](C)(C)[Si](C)(C)[Si](C)(C)[Si](C)(C)[Si](*)(C)C)s6)cc5CCCCCCCC)s4)s3)s2)s1
*c1ccc(-c2cc(CCCCCCCC)c(-c3ccc(-c4ccc(-c5sc(-c6ccc([Si](CCCC)(CCCC)[Si](*)(CCCC)CCCC)s6)cc5CCCCCCCC)s4)s3)s2)s1
*c1ccc(-c2cc(CCCCCCCC)c(-c3ccc(-c4sc(*)cc4CCCCCCCC)cc3)s2)cn1
*c1ccc(-c2cc(CCCCCCOC(=O)COc3ccccc3)c(*)s2)s1
*c1ccc(-c2cc3c(ccc4ccccc43)cc2-c2ccc(N(*)c3ccc(C)cc3)cc2)cc1
*c1ccc(-c2ccc(-c3c(-c4ccccc4)nc4ccc(-c5ccc6nc(-c7ccccc7)c(*)c(-c7ccccc7)c6c5)cc4c3-c3ccccc3)cc2)cc1
*c1ccc(-c2ccc(-c3c(-c4ccccc4)nc4ccc(Oc5ccc6nc(-c7ccccc7)c(*)c(-c7ccccc7)c6c5)cc4c3-c3ccccc3)cc2)cc1
*c1ccc(-c2ccc(-c3cc(-c4ccc(-c5ccc(Oc6ccccc6)cc5)cc4)c4cc(-c5ccc6nc(*)cc(-c7ccc(-c8ccc(Oc9ccccc9)cc8)cc7)c6c5)ccc4n3)cc2)cc1
*c1ccc(-c2ccc(-c3cc(-c4ccc(Oc5ccc(-c6cc(*)nc7ccccc67)cc5)cc4)c4ccccc4n3)cc2)cc1
*c1ccc(-c2ccc(-c3cc(-c4ccc(Oc5ccc(-c6ccccc6)cc5)cc4)c4cc(-c5ccc6nc(*)cc(-c7ccc(Oc8ccc(-c9ccccc9)cc8)cc7)c6c5)ccc4n3)cc2)cc1
*c1ccc(-c2ccc(-c3cc(-c4ccc(Oc5ccc(Oc6ccccc6)cc5)cc4)c4cc(-c5ccc6nc(*)cc(-c7ccc(Oc8ccc(Oc9ccccc9)cc8)cc7)c6c5)ccc4n3)cc2)cc1
*c1ccc(-c2ccc(-c3cc(-c4ccccc4)c4cc(-c5ccc6nc(*)cc(-c7ccccc7)c6c5)ccc4n3)cc2)cc1
*c1ccc(-c2ccc(-c3cc(-c4ccccc4)c4cc(C5(c6ccc7nc(*)cc(-c8ccccc8)c7c6)c6ccccc6-c6ccccc65)ccc4n3)cc2)cc1
*c1ccc(-c2ccc(-c3cc(-c4ccccc4)c4cc(C5(c6ccc7nc(*)cc(-c8ccccc8)c7c6)c6ccccc6C(=O)c6ccccc65)ccc4n3)cc2)cc1
*c1ccc(-c2ccc(-c3cc(-c4ccccc4)c4cc5c(-c6ccccc6)cc(*)nc5cc4n3)cc2)cc1
*c1ccc(-c2ccc(-c3cc(-c4ccccc4)c4cc5nc(*)cc(-c6ccccc6)c5cc4n3)cc2)cc1
*c1ccc(-c2ccc(-c3cc(CCCCCCCC)c(-c4ccc(-c5sc(*)cc5CCCCCCCC)cc4)s3)cc2)cc1
*c1ccc(-c2ccc(-c3ccc(-c4cc(-c5ccccc5)c5cc(-c6ccc7nc(*)cc(-c8ccccc8)c7c6)ccc5n4)cc3)cc2)cc1
*c1ccc(-c2ccc(-c3ccc(-c4cc(-c5ccccc5)c5cc(Oc6ccc7nc(*)cc(-c8ccccc8)c7c6)ccc5n4)cc3)cc2)cc1
*c1ccc(-c2ccc(-c3ccc(-c4ccc(*)n4CCCCCCCCCCCC)s3)s2)s1
*c1ccc(-c2ccc(-c3ccc(-c4ccc(-c5cc(-c6ccccc6)c6cc(-c7ccc8nc(*)cc(-c9ccccc9)c8c7)ccc6n5)cc4)cc3)cc2)cc1
*c1ccc(-c2ccc(-c3ccc(-c4ccc(-c5cc(-c6ccccc6)c6cc(Oc7ccc8nc(*)cc(-c9ccccc9)c8c7)ccc6n5)cc4)cc3)cc2)cc1
*c1ccc(-c2ccc(-c3ccc(-c4ccc(-c5ccc(*)n5CCCCCCCCCCCC)s4)s3)s2)s1
*c1ccc(-c2ccc(-c3ccc(-c4ccc(-c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(C(C)(C)c8ccc(Oc9ccc(N%10C(=O)c%11ccc(*)cc%11C%10=O)cc9)cc8)cc7)cc5)C6=O)cc4)cc3)cc2)cc1
*c1ccc(-c2ccc(-c3ccc(-c4ccc(-c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(N8C(=O)c9ccc(*)cc9C8=O)cc7)cc5)C6=O)cc4)cc3)cc2)cc1
*c1ccc(-c2ccc(-c3ccc(-c4ccc(-c5ccc6c(c5)C(=O)N(c5cccc(N7C(=O)c8ccc(*)cc8C7=O)c5)C6=O)cc4)cc3)cc2)cc1
*c1ccc(-c2ccc(-c3ccc(-c4ccc(N(*)c5ccc(C)cc5)cc4)c4c(C)ccc(C)c34)c3c(C)ccc(C)c23)cc1
*c1ccc(-c2ccc(-c3ccc(-c4ccc([Si](*)(CCCC)CCCC)s4)s3)s2)s1
*c1ccc(-c2ccc(-c3ccc(-c4ccc([Si](CCCC)(CCCC)[Si](*)(CCCC)CCCC)s4)s3)s2)s1
*c1ccc(-c2ccc(-c3ccc(-c4cnc5ccc(-c6ccc7ncc(*)nc7c6)cc5n4)cc3)cc2)cc1
*c1ccc(-c2ccc(-c3ccc(-n4c(=O)c5c(C(F)(F)F)c6c(=O)n(*)c(=O)c6c(C(F)(F)F)c5c4=O)cc3)cc2)cc1
*c1ccc(-c2ccc(-c3ccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6c(C(F)(F)F)c5c4=O)cc3)cc2)cc1
*c1ccc(-c2ccc(-c3ccc([Si](*)(C)C)s3)s2)s1
*c1ccc(-c2ccc(-c3ccc([Si](*)(CCCC)CCCC)s3)s2)s1
*c1ccc(-c2ccc(-c3ccc([Si](C)(C)[Si](*)(C)C)s3)s2)s1
*c1ccc(-c2ccc(-c3ccc([Si](CCCC)(CCCC)[Si](*)(CCCC)CCCC)s3)s2)s1
*c1ccc(-c2ccc(-c3ccc4c(c3)C(CCCCCCCC)(CCCCCCCC)c3cc(-c5ccc(*)s5)ccc3-4)s2)cn1
*c1ccc(-c2ccc(-c3ccc4nc(Oc5cc(-c6ccccc6)c6cc(*)ccc6n5)cc(-c5ccccc5)c4c3)cc2)cc1
*c1ccc(-c2ccc(-c3cnc4cc(-c5ccc6nc(*)cnc6c5)ccc4n3)cc2)cc1
*c1ccc(-c2ccc(-c3cnc4cc(Oc5ccc6nc(*)cnc6c5)ccc4n3)cc2)cc1
*c1ccc(-c2ccc(-c3nc4cc(Oc5ccc(NC(=O)c6ccc7c(c6)C(=O)N(c6ccc(C(c8ccc(N9C(=O)c%10ccc(C(=O)Nc%11ccc(Oc%12ccc%13nc(-c%14ccccc%14)c(*)nc%13c%12)cc%11)cc%10C9=O)cc8)(C(F)(F)F)C(F)(F)F)cc6)C7=O)cc5)ccc4nc3-c3ccccc3)cc2)cc1
*c1ccc(-c2ccc(-c3nc4cc(Oc5ccc6nc(-c7ccccc7)c(*)nc6c5)ccc4nc3-c3ccccc3)cc2)cc1
*c1ccc(-c2ccc(-c3nc4ccc(-c5ccc6nc(*)c(-c7ccccc7)c(-c7ccccc7)c6c5)cc4c(-c4ccccc4)c3-c3ccccc3)cc2)cc1
*c1ccc(-c2ccc(-c3nc4ccc(-c5ccc6nc(*)c(-c7ccccc7)nc6c5)cc4nc3-c3ccccc3)cc2)cc1
*c1ccc(-c2ccc(-c3nc4ccc(Oc5ccc6nc(*)c(-c7ccccc7)c(-c7ccccc7)c6c5)cc4c(-c4ccccc4)c3-c3ccccc3)cc2)cc1
*c1ccc(-c2ccc(-c3nc4ccccc4c(-c4ccc(Oc5ccc(-c6c(-c7ccccc7)c(*)nc7ccccc67)cc5)cc4)c3-c3ccccc3)cc2)cc1
*c1ccc(-c2ccc(-c3nnc(-c4ccc([Si](c5ccccc5)(c5ccccc5)c5ccc(-c6nnc(*)o6)cc5)cc4)o3)cc2)cc1
*c1ccc(-c2ccc(-n3c(-c4ccc(Oc5ccc(C(=O)c6ccc(C(=O)c7ccc(Oc8ccc(-c9nc%10ccccc%10n9*)cc8)cc7)cc6)cc5)cc4)nc4ccccc43)cc2)cc1
*c1ccc(-c2ccc(-n3c(-c4ccc(Oc5ccc(C(=O)c6ccc(Oc7ccc(-c8nc9ccccc9n8*)cc7)cc6)cc5)cc4)nc4ccccc43)cc2)cc1
*c1ccc(-c2ccc(-n3c(-c4ccc(Oc5ccc(C(=O)c6ccc(Oc7ccc(C(=O)c8ccc(Oc9ccc(-c%10nc%11ccccc%11n%10*)cc9)cc8)cc7)cc6)cc5)cc4)nc4ccccc43)cc2)cc1
*c1ccc(-c2ccc(-n3c(-c4ccc(Oc5ccc(C(=O)c6cccc(C(=O)c7ccc(Oc8ccc(-c9nc%10ccccc%10n9*)cc8)cc7)c6)cc5)cc4)nc4ccccc43)cc2)cc1
*c1ccc(-c2ccc(-n3c(-c4ccc(Oc5ccc(S(=O)(=O)c6ccc(Oc7ccc(-c8nc9ccccc9n8*)cc7)cc6)cc5)cc4)nc4ccccc43)cc2)cc1
*c1ccc(-c2ccc(-n3c(=O)c4c(C(F)(F)F)c5c(=O)n(*)c(=O)c5c(C(F)(F)F)c4c3=O)cc2C(F)(F)F)c(C(F)(F)F)c1
*c1ccc(-c2ccc(-n3c(=O)c4c(C(F)(F)F)c5c(=O)n(*)c(=O)c5c(C(F)(F)F)c4c3=O)cc2C)c(C)c1
*c1ccc(-c2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5c(C(F)(F)F)c4c3=O)cc2C(F)(F)F)c(C(F)(F)F)c1
*c1ccc(-c2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5c(C(F)(F)F)c4c3=O)cc2C)c(C)c1
*c1ccc(-c2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2C(F)(F)F)c(C(F)(F)F)c1
*c1ccc(-c2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2C)c(C)c1
*c1ccc(-c2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2F)c(F)c1
*c1ccc(-c2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2OC(F)(F)F)c(OC(F)(F)F)c1
*c1ccc(-c2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2OC)c(OC)c1
*c1ccc(-c2ccc(C(*)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(-c2ccc(C3(*)CCCCC3)cc2)cc1
*c1ccc(-c2ccc(C3(*)c4ccccc4-c4ccccc43)cc2)cc1
*c1ccc(-c2ccc(N(*)c3cc(C(F)(F)F)cc(C(F)(F)F)c3)cc2)cc1
*c1ccc(-c2ccc(N(*)c3ccc(C(F)(F)F)cc3)cc2)cc1
*c1ccc(-c2ccc(N(*)c3ccc(C)cc3)cc2)cc1
*c1ccc(-c2ccc(N(*)c3ccc(CCCC)cc3)cc2)cc1
*c1ccc(-c2ccc(N(c3ccc(OC)cc3)c3ccc4ccc(N(*)c5ccc(OC)cc5)cc4c3)cc2)cc1
*c1ccc(-c2ccc(N(c3ccccc3)c3ccc(-c4ccc(N(c5ccccc5)c5ccc(-c6ccc(-n7c(=O)c8cc9c(=O)n(*)c(=O)c9cc8c7=O)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(-c2ccc(N3C(=O)c4c(c(-c5ccccc5)c(-c5ccc(Oc6ccc(-c7c(-c8ccccc8)c8c(c(-c9ccccc9)c7-c7ccccc7)C(=O)N(*)C8=O)cc6)cc5)c(-c5ccccc5)c4-c4ccccc4)C3=O)cc2)cc1
*c1ccc(-c2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C)c2)cc1C
*c1ccc(-c2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(F)(F)F)c(C(F)(F)F)c1
*c1ccc(-c2ccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)c(C)c2)cc1C
*c1ccc(-c2ccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)cc2C(F)(F)F)c(C(F)(F)F)c1
*c1ccc(-c2ccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)cc2C)c(C)c1
*c1ccc(-c2ccc(N3C(=O)c4ccc(Oc5c(C)cc(C(C)(C)c6cc(C)c(Oc7ccc8c(c7)C(=O)N(*)C8=O)c(C)c6)cc5C)cc4C3=O)cc2C(F)(F)F)c(C(F)(F)F)c1
*c1ccc(-c2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)c(OC)c2)cc1OC
*c1ccc(-c2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)cc2)cc1
*c1ccc(-c2ccc(N3C(=O)c4ccc(Oc5ccc(Sc6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)cc2)cc1
*c1ccc(-c2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(OCCCCCCOc3ccc(/C=C/c4ccc(F)cc4)cc3)c2)cc1OCCCCCCOc1ccc(/C=C/c2ccc(F)cc2)cc1
*c1ccc(-c2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(-c2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(F)(F)F)c(C(F)(F)F)c1
*c1ccc(-c2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C)c(C)c1
*c1ccc(-c2ccc(N3C(=O)c4ccc(S(=O)(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(F)(F)F)c(C(F)(F)F)c1
*c1ccc(-c2ccc(NC(=O)c3cccc(C(=O)Nc4ccc(-c5ccc(-n6c(=O)c7cc8c(=O)n(*)c(=O)c8cc7c6=O)cc5)cc4)c3)cc2)cc1
*c1ccc(-c2ccc(NC(=O)c3cccc(C(=O)Nc4ccc(-c5ccc(N6C(=O)c7ccc(-c8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)cc5)cc4)c3)cc2)cc1
*c1ccc(-c2ccc(NC(=O)c3cccc(C(=O)Nc4ccc(-c5ccc(N6C(=O)c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)cc5)cc4)c3)cc2)cc1
*c1ccc(-c2ccc(Oc3ccc(C4(C)CC(C)(C)c5ccc(Oc6ccc(-c7ccc(-n8c(=O)c9cc%10c(=O)n(*)c(=O)c%10cc9c8=O)cc7)cc6C(F)(F)F)cc54)cc3)c(C(F)(F)F)c2)cc1
*c1ccc(-c2ccc(Oc3ccc(C4(C)CC(C)(C)c5ccc(Oc6ccc(-c7ccc(N8C(=O)c9ccc(Oc%10ccc%11c(c%10)C(=O)N(*)C%11=O)cc9C8=O)cc7)cc6C(F)(F)F)cc54)cc3)c(C(F)(F)F)c2)cc1
*c1ccc(-c2ccc(Oc3ccc(C4(C)CC(C)(C)c5ccc(Oc6ccc(-c7ccc(N8C(=O)c9ccc(Oc%10ccc(C(C)(C)c%11ccc(Oc%12ccc%13c(c%12)C(=O)N(*)C%13=O)cc%11)cc%10)cc9C8=O)cc7)cc6C(F)(F)F)cc54)cc3)c(C(F)(F)F)c2)cc1
*c1ccc(-c2ccc([Si](*)(C)C)s2)s1
*c1ccc(-c2ccc([Si](*)(CCCC)CCCC)s2)s1
*c1ccc(-c2ccc([Si](C)(C)[Si](*)(C)C)s2)s1
*c1ccc(-c2ccc([Si](CCCC)(CCCC)[Si](*)(CCCC)CCCC)s2)s1
*c1ccc(-c2ccc3c(c2)C(CCCCCC)(CCCCCC)c2cc(*)ccc2-3)cc1
*c1ccc(-c2ccc3c(c2)C(CCCCCCBr)(CCCCCCBr)c2cc(*)ccc2-3)cc1
*c1ccc(-c2ccc3c(c2)C(CCCCCCC#N)(CCCCCCC#N)c2cc(*)ccc2-3)cc1
*c1ccc(-c2ccc3c(c2)C(CCCCCCCC)(CCCCCCCC)c2cc(*)ccc2-3)c(F)c1
*c1ccc(-c2ccc3c(c2)C(CCCCCCCC)(CCCCCCCC)c2cc(*)ccc2-3)cc1
*c1ccc(-c2ccc3c(c2)C(CCCCCCCC)(CCCCCCCC)c2cc(-c4ccc(N(*)c5ccc(N(c6ccc(C(C)(C)c7ccccc7)cc6)c6ccc(C(C)(C)c7ccccc7)cc6)cc5)cc4)ccc2-3)cc1
*c1ccc(-c2ccc3nc(*)cc(-c4ccccc4)c3c2)cc1
*c1ccc(-c2ccc3nc(Oc4cc(-c5ccccc5)c5cc(*)ccc5n4)cc(-c4ccccc4)c3c2)cc1
*c1ccc(-c2cnc3cc(-c4ccc5nc(*)cnc5c4)ccc3n2)cc1
*c1ccc(-c2cnc3cc(Oc4ccc5nc(*)cnc5c4)ccc3n2)cc1
*c1ccc(-c2cnc3cc(S(=O)(=O)c4ccc5nc(*)cnc5c4)ccc3n2)cc1
*c1ccc(-c2cnc3cc4nc(*)cnc4cc3n2)cc1
*c1ccc(-c2cnc3cc4ncc(*)nc4cc3n2)cc1
*c1ccc(-c2cnc3ccc(-c4ccc5nc(*)cnc5c4)cc3n2)cc1
*c1ccc(-c2cnc3ccc(-c4ccc5ncc(*)nc5c4)cc3n2)cc1
*c1ccc(-c2cnc3ccc(Oc4ccc5ncc(*)nc5c4)cc3n2)cc1
*c1ccc(-c2cnc3ccc(S(=O)(=O)c4ccc5ncc(*)nc5c4)cc3n2)cc1
*c1ccc(-c2nc(-c3cccc(-c4nnc(-c5ccccc5)c(*)n4)n3)nnc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(-c4ccc5nc(*)c(-c6ccc7ccccc7c6)nc5c4)ccc3nc2-c2ccc3ccccc3c2)cc1
*c1ccc(-c2nc3cc(-c4ccc5nc(*)c(-c6ccccc6)nc5c4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(-c4ccc5nc(-c6ccccc6)c(*)nc5c4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(-c4ccc5nc(-c6ccccc6)c(-c6ccc(-n7c(=O)c8cc9c(=O)n(*)c(=O)c9cc8c7=O)cc6)nc5c4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(-c4ccc5nc(-c6ccccc6)c(-c6ccc(N7C(=O)C8OC9C(=O)N(*)C(=O)C9C8C7=O)cc6)nc5c4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(-c4ccc5oc(*)nc5c4)ccc3o2)cc1
*c1ccc(-c2nc3cc(C(=O)c4ccc5nc(-c6ccccc6)c(-c6ccc(-n7c(=O)c8cc9c(=O)n(*)c(=O)c9cc8c7=O)cc6)nc5c4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(C(=O)c4ccc5nc(-c6ccccc6)c(-c6ccc(N7C(=O)C8OC9C(=O)N(*)C(=O)C9C8C7=O)cc6)nc5c4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(C)ccc3o2)c(*)c1
*c1ccc(-c2nc3cc(Oc4ccc(NC(=O)CN5C(=O)c6ccc(C(c7ccc8c(c7)C(=O)N(CC(=O)Nc7ccc(Oc9ccc%10nc(-c%11ccccc%11)c(*)nc%10c9)cc7)C8=O)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(Oc4ccc(NC(=O)CN5C(=O)c6ccc(C(c7ccc8c(c7)C(=O)N(CC(=O)Nc7ccc(Oc9ccc%10nc(-c%11ccccc%11)c(-c%11ccc(-n%12c(=O)c%13ccc%14c(=O)n(*)c(=O)c%15ccc(c%12=O)c%13c%14%15)cc%11)nc%10c9)cc7)C8=O)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(Oc4ccc(NC(=O)c5cc(C(=O)Nc6ccc(Oc7ccc8nc(-c9ccccc9)c(*)nc8c7)cc6)cc(N6C(=O)c7ccccc7C6=O)c5)cc4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(Oc4ccc(NC(=O)c5cc(C(=O)Nc6ccc(Oc7ccc8nc(-c9ccccc9)c(-c9ccc(-n%10c(=O)c%11ccc%12c(=O)n(*)c(=O)c%13ccc(c%10=O)c%11c%12%13)cc9)nc8c7)cc6)cc(N6C(=O)c7c(Cl)c(Cl)c(Cl)c(Cl)c7C6=O)c5)cc4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(Oc4ccc(NC(=O)c5cc(C(=O)Nc6ccc(Oc7ccc8nc(-c9ccccc9)c(-c9ccc(-n%10c(=O)c%11ccc%12c(=O)n(*)c(=O)c%13ccc(c%10=O)c%11c%12%13)cc9)nc8c7)cc6)cc(N6C(=O)c7ccccc7C6=O)c5)cc4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(Oc4ccc(NC(=O)c5ccc6c(c5)C(=O)N(c5ccc(C(c7ccc(N8C(=O)c9ccc(C(=O)Nc%10ccc(Oc%11ccc%12nc(-c%13ccccc%13)c(*)nc%12c%11)cc%10)cc9C8=O)cc7)(C(F)(F)F)C(F)(F)F)cc5)C6=O)cc4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(Oc4ccc5nc(*)c(-c6ccccc6)nc5c4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(Oc4ccc5nc(-c6ccccc6)c(*)nc5c4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(Oc4ccc5nc(-c6ccccc6)c(-c6ccc(-n7c(=O)c8cc9c(=O)n(*)c(=O)c9cc8c7=O)cc6)nc5c4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(S(=O)(=O)c4ccc5nc(*)c(-c6ccccc6)nc5c4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(S(=O)(=O)c4ccc5nc(-c6ccccc6)c(*)nc5c4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc(S(=O)(=O)c4ccc5nc(-c6ccccc6)c(-c6ccc(-n7c(=O)c8cc9c(=O)n(*)c(=O)c9cc8c7=O)cc6)nc5c4)ccc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc4nc(*)c(-c5ccccc5)nc4cc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc4nc(-c5ccccc5)c(*)nc4cc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3cc4sc(*)nc4cc3s2)cc1
*c1ccc(-c2nc3ccc(-c4ccc5nc(*)c(-c6ccccc6)c(-c6ccccc6)c5c4)cc3c(-c3ccccc3)c2-c2ccccc2)cc1
*c1ccc(-c2nc3ccc(-c4ccc5nc(*)c(-c6ccccc6)nc5c4)cc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3ccc(Oc4ccc(Oc5ccc(P(=O)(c6ccccc6)c6ccc(Oc7ccc(Oc8ccc9nc(*)c(-c%10ccccc%10)nc9c8)cc7)cc6)cc5)cc4)cc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3ccc(Oc4ccc5nc(*)c(-c6ccccc6)c(-c6ccccc6)c5c4)cc3c(-c3ccccc3)c2-c2ccccc2)cc1
*c1ccc(-c2nc3ccc(Oc4ccc5nc(*)c(-c6ccccc6)nc5c4)cc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3ccc(S(=O)(=O)c4ccc5nc(*)c(-c6ccccc6)nc5c4)cc3nc2-c2ccccc2)cc1
*c1ccc(-c2nc3ccccc3o2)c(*)c1
*c1ccc(-c2nnc(*)o2)c(OCC)c1
*c1ccc(-c2nnc(*)o2)c(OCCCCC)c1
*c1ccc(-c2nnc(*)o2)c(OCCCCCCCC)c1
*c1ccc(-c2nnc(*)o2)c(OCCCCCCCCCC)c1
*c1ccc(-c2nnc(-c3cccc(-c4nnc(*)n4-c4ccccc4)c3)n2-c2ccccc2)cc1
*c1ccc(-c2nnc(-c3cccc(-c4nnc(*)o4)c3)o2)cc1
*c1ccc(-c2sc(-c3cc(CCCC)c(*)s3)cc2CCCC)cc1
*c1ccc(-c2sc(-c3cc(CCCCCCCC)c(*)s3)cc2CCCCCCCC)cc1
*c1ccc(-c2sc(-c3cc(CCCCCCCCCCCC)c(*)s3)cc2CCCCCCCCCCCC)cc1
*c1ccc(-c2sc(-c3cc(SCCCC)c(*)s3)cc2SCCCC)cc1
*c1ccc(-c2sc(-c3cc(SCCCCCCCC)c(*)s3)cc2SCCCCCCCC)cc1
*c1ccc(-c2sc(-c3cc(SCCCCCCCCCCCC)c(*)s3)cc2SCCCCCCCCCCCC)cc1
*c1ccc(-c2sc(-c3ccc(-c4cc(CCCC)c(*)s4)cc3)cc2CCCC)cc1
*c1ccc(-c2sc(-c3ccc(-c4cc(CCCCCCCC)c(*)s4)cc3)cc2CCCCCCCC)cc1
*c1ccc(-c2sc(-c3ccc(-c4cc(CCCCCCCCCCCC)c(*)s4)cc3)cc2CCCCCCCCCCCC)cc1
*c1ccc(-c2sc(-c3ccc(-c4cc(SCCCC)c(*)s4)cc3)cc2SCCCC)cc1
*c1ccc(-c2sc(-c3ccc(-c4cc(SCCCCCCCC)c(*)s4)cc3)cc2SCCCCCCCC)cc1
*c1ccc(-c2sc(-c3ccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)cc3)c(-c3ccccc3)c2-c2ccccc2)cc1
*c1ccc(-n2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)c(S(=O)(=O)O[Na])c1
*c1ccc(-n2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)cc1
*c1ccc(-n2ccc(-c3ccc(-c4ccn(*)n4)cc3)n2)cc1
*c1ccc(-n2ccc(-c3cccc(-c4ccn(*)n4)c3)n2)cc1
*c1ccc(/C=C(\C#N)C(=O)NC2CCCCC2NC(=O)/C(C#N)=C/c2ccc(N(c3ccccc3)c3ccc(N(*)c4ccccc4)cc3)cc2)cc1
*c1ccc(/C=C(\C#N)c2cc(OCCCCCCCC)c(/C(C#N)=C/c3ccc(N(*)c4ccccc4)cc3)cc2OCCCCCCCC)cc1
*c1ccc(/C=C(\c2ccc(OC)cc2)c2ccc(/C(=C/c3ccc(N(*)c4ccccc4)cc3)c3ccc(OC)cc3)cc2)cc1
*c1ccc(/C=C/c2cc(OCC(CC)CCCC)c(/C=C/c3ccc(N(*)c4ccccc4)cc3)cc2OC)cc1
*c1ccc(/C=C/c2cc(OCCCCCCCC)c(/C=C/c3ccc(N(*)c4ccccc4)cc3)cc2OCCCCCCCC)cc1
*c1ccc(/C=C/c2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2S(=O)(=O)O[Na])c(S(=O)(=O)O[Na])c1
*c1ccc(/C=N/c2ccc(C(c3ccccc3)c3ccc(/N=C/c4ccc(-c5ccc(*)s5)cc4)cc3)cc2)cc1
*c1ccc(/C=N/c2cccc(/N=C/c3ccc(-c4ccc(*)s4)cc3)c2)cc1
*c1ccc(C#Cc2cc(OCCCCCCCC)c(C#Cc3ccc(-c4nnc(*)o4)cc3)cc2OCCCCCCCC)cc1
*c1ccc(C(*)C)cc1
*c1ccc(C(=O)NC(CC)COC(=O)c2ccc(N3C(=O)CC(SCCCCCSC4CC(=O)N(*)C4=O)C3=O)cc2)cc1
*c1ccc(C(=O)NCCCCCCCCCCCCNC(=O)c2ccc(-c3nc4ccccc4nc3*)cc2)cc1
*c1ccc(C(=O)NCCCCCCCCCCNC(=O)c2ccc(-c3nc4ccccc4nc3*)cc2)cc1
*c1ccc(C(=O)NCCCCCCCCNC(=O)c2ccc(-c3nc4ccccc4nc3*)cc2)cc1
*c1ccc(C(=O)NCCCCCCNC(=O)c2ccc(-c3nc4ccccc4nc3*)cc2)cc1
*c1ccc(C(=O)NNC(=O)c2ccc(C(=O)NNC(=O)c3ccc(C4(*)OC(=O)c5ccccc54)cc3)cc2)cc1
*c1ccc(C(=O)Nc2ccc(-c3cc(Br)ccc3NC(=O)c3ccc(S(*)(=O)=O)cc3)cc2)cc1
*c1ccc(C(=O)Nc2ccc(-c3ccccc3NC(=O)c3ccc(S(*)(=O)=O)cc3)cc2)cc1
*c1ccc(C(=O)Nc2ccc(-c3nnc(*)o3)cc2)c(Oc2ccccc2)c1
*c1ccc(C(=O)Nc2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2C(=O)OCC)cc1
*c1ccc(C(=O)Nc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(C(=O)Nc2ccc(NC(=O)c3ccc(Oc4ccc(-c5nn(*)c(=O)c6ccccc56)c5ccccc45)cc3)cc2)cc1
*c1ccc(C(=O)Nc2ccc(Oc3ccc(NC(=O)c4ccc(-n5c(=O)c6cc7c(=O)n(*)c(=O)c7cc6c5=O)cc4)cc3)cc2)cc1
*c1ccc(C(=O)Nc2ccc(Oc3ccc(NC(=O)c4ccc(Oc5ccc(-c6nn(*)c(=O)c7ccccc67)c6ccccc56)cc4)cc3)cc2)cc1
*c1ccc(C(=O)Nc2ccc(Oc3ccc(NC(=O)c4ccc(Oc5ccc(-c6nn(*)c(=O)c7ccccc67)cc5)cc4)cc3)cc2)cc1
*c1ccc(C(=O)Nc2ccc(Oc3ccc(Oc4ccc(NC(=O)c5ccc(Oc6ccc(-c7nn(*)c(=O)c8ccccc78)c7ccccc67)cc5)cc4)cc3)cc2)cc1
*c1ccc(C(=O)Nc2ccc(Oc3ccc(Oc4ccc(NC(=O)c5ccc(Oc6ccc(-c7nn(*)c(=O)c8ccccc78)c7ccccc67)cc5)cc4C(F)(F)F)cc3)c(C(F)(F)F)c2)cc1
*c1ccc(C(=O)Nc2ccc(Oc3ccc(S(=O)(=O)c4ccc(Oc5ccc(NC(=O)c6ccc(N7C(=O)c8ccc(-c9ccc%10c(c9)C(=O)N(*)C%10=O)cc8C7=O)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(C(=O)Nc2ccc(Oc3ccc(S(=O)(=O)c4ccc(Oc5ccc(NC(=O)c6ccc(N7C(=O)c8ccc(-c9cccc%10c9C(=O)N(*)C%10=O)cc8C7=O)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(C(=O)Nc2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c2)cc1
*c1ccc(C(=O)Nc2cccc(Oc3ccc(S(=O)(=O)c4ccc(Oc5cccc(NC(=O)c6ccc(N7C(=O)c8ccc(-c9ccc%10c(c9)C(=O)N(*)C%10=O)cc8C7=O)cc6)c5)cc4)cc3)c2)cc1
*c1ccc(C(=O)Nc2cccc(Oc3ccc(S(=O)(=O)c4ccc(Oc5cccc(NC(=O)c6ccc(N7C(=O)c8ccc(-c9cccc%10c9C(=O)N(*)C%10=O)cc8C7=O)cc6)c5)cc4)cc3)c2)cc1
*c1ccc(C(=O)Nc2cccc(S(=O)(=O)c3cccc(NC(=O)c4ccc(N5C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)c3)c2)cc1
*c1ccc(C(=O)OCCCCCCCCCCCCOC(=O)c2ccc(-c3nnc(*)s3)cc2)cc1
*c1ccc(C(=O)OCCCCCCCCCCOC(=O)c2ccc(-c3nnc(*)s3)cc2)cc1
*c1ccc(C(=O)OCCCCCCCCCCOc2ccc(/C=C/C(=O)/C=C/c3ccc(OCCCCCCCCCCOC(=O)c4ccc(-c5nnc(*)o5)cc4)cc3)cc2)cc1
*c1ccc(C(=O)OCCCCCCCCCCOc2ccc(/C=C3\CC/C(=C\c4ccc(OCCCCCCCCCCOC(=O)c5ccc(-c6nnc(*)o6)cc5)cc4)C3=O)cc2)cc1
*c1ccc(C(=O)OCCCCCCCCCCOc2ccc(/C=C3\CCCC/C(=C\c4ccc(OCCCCCCCCCCOC(=O)c5ccc(-c6nnc(*)o6)cc5)cc4)C3=O)cc2)cc1
*c1ccc(C(=O)OCCCCCCCCCOC(=O)c2ccc(-c3nnc(*)s3)cc2)cc1
*c1ccc(C(=O)OCCCCCCCCOC(=O)c2ccc(-c3nnc(*)s3)cc2)cc1
*c1ccc(C(=O)OCCCCCCCCOc2ccc(/C=C/C(=O)/C=C/c3ccc(OCCCCCCCCOC(=O)c4ccc(-c5nnc(*)o5)cc4)cc3)cc2)cc1
*c1ccc(C(=O)OCCCCCCCCOc2ccc(/C=C3\CC/C(=C\c4ccc(OCCCCCCCCOC(=O)c5ccc(-c6nnc(*)o6)cc5)cc4)C3=O)cc2)cc1
*c1ccc(C(=O)OCCCCCCCCOc2ccc(/C=C3\CCC/C(=C\c4ccc(OCCCCCCCCOC(=O)c5ccc(-c6nnc(*)o6)cc5)cc4)C3=O)cc2)cc1
*c1ccc(C(=O)OCCCCCCCCOc2ccc(/C=C3\CCCC/C(=C\c4ccc(OCCCCCCCCOC(=O)c5ccc(-c6nnc(*)o6)cc5)cc4)C3=O)cc2)cc1
*c1ccc(C(=O)OCCCCCCOC(=O)c2ccc(-c3nnc(*)s3)cc2)cc1
*c1ccc(C(=O)OCCCCCCOc2ccc(/C=C/C(=O)/C=C/c3ccc(OCCCCCCOC(=O)c4ccc(-c5nnc(*)o5)cc4)cc3)cc2)cc1
*c1ccc(C(=O)OCCCCCCOc2ccc(/C=C3\CCC/C(=C\c4ccc(OCCCCCCOC(=O)c5ccc(-c6nnc(*)o6)cc5)cc4)C3=O)cc2)cc1
*c1ccc(C(=O)OCCCCCCOc2ccc(/C=C3\CCCC/C(=C\c4ccc(OCCCCCCOC(=O)c5ccc(-c6nnc(*)o6)cc5)cc4)C3=O)cc2)cc1
*c1ccc(C(=O)Oc2c(C)cc(C(c3cccnc3)c3cc(C)c(OC(=O)c4ccc(N5C(=O)CC(Nc6ccc(Cc7ccc(NC8CC(=O)N(*)C8=O)cc7)cc6)C5=O)cc4)c(C)c3)cc2C)cc1
*c1ccc(C(=O)Oc2c(C)cc(C(c3cccnc3)c3cc(C)c(OC(=O)c4ccc(N5C(=O)CC(Nc6ccc(NC7CC(=O)N(*)C7=O)cc6)C5=O)cc4)c(C)c3)cc2C)cc1
*c1ccc(C(=O)Oc2c(C)cc(C(c3cccnc3)c3cc(C)c(OC(=O)c4ccc(N5C(=O)CC(Nc6cccc(NC7CC(=O)N(*)C7=O)c6)C5=O)cc4)c(C)c3)cc2C)cc1
*c1ccc(C(=O)Oc2ccc(C(C)(C)c3ccc(OC(=O)c4ccc(N5C(=O)CC(SCCOCCSC6CC(=O)N(*)C6=O)C5=O)cc4)cc3)cc2)cc1
*c1ccc(C(=O)Oc2ccc(OC(=O)c3ccc(N4C(=O)CC(SCCOCCSC5CC(=O)N(*)C5=O)C4=O)cc3)cc2)cc1
*c1ccc(C(=O)Oc2ccc3ccc(OC(=O)c4ccc(N5C(=O)CC(SCCOCCSC6CC(=O)N(*)C6=O)C5=O)cc4)cc3c2)cc1
*c1ccc(C(=O)c2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2)cc1
*c1ccc(C(=O)c2ccc(-n3nc(-c4ccc(Oc5ccc(-c6nn(*)c(=O)c7ccccc67)cc5)cc4)c4ccccc4c3=O)cc2)cc1
*c1ccc(C(=O)c2ccc(-n3nc(-c4ccc(Sc5ccc(-c6nn(*)c(=O)c7ccccc67)cc5)cc4)c4ccccc4c3=O)cc2)cc1
*c1ccc(C(=O)c2ccc(-n3nc(-c4ccccc4)c4cc(Oc5ccc(C(C)(C)c6ccc(Oc7ccc8c(-c9ccccc9)nn(*)c(=O)c8c7)cc6)cc5)ccc4c3=O)cc2)cc1
*c1ccc(C(=O)c2ccc(-n3nc(-c4ccccc4)c4cc(Oc5ccc6c(-c7ccccc7)nn(*)c(=O)c6c5)ccc4c3=O)cc2)cc1
*c1ccc(C(=O)c2ccc(C(=O)c3ccc(Oc4ccc(-c5nn(*)c(=O)c6ccccc56)c(F)c4)cc3)cc2)cc1
*c1ccc(C(=O)c2ccc(C(=O)c3ccc(S(*)(=O)=O)cc3)cc2)cc1
*c1ccc(C(=O)c2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)cc2)cc1
*c1ccc(C(=O)c2ccc(Oc3ccc(-c4nn(*)c(=O)c5ccccc45)c(F)c3)cc2)cc1
*c1ccc(C(=O)c2ccc(Oc3ccc(-c4nn(*)c(=O)c5ccccc45)cc3)cc2)cc1
*c1ccc(C(=O)c2ccc(Oc3ccc(-c4nn(*)c(=O)c5ccccc45)cc3Br)cc2)cc1
*c1ccc(C(=O)c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c2)cc1
*c1ccc(C(=O)c2cccc(C(=O)c3ccc(S(*)(=O)=O)cc3)c2)cc1
*c1ccc(C(C#N)(C(=O)O)C(*)(C#N)C(=O)O)cc1
*c1ccc(C(C#N)(C(=O)OC)C(*)(C#N)C(=O)OC)cc1
*c1ccc(C(C#N)(C(=O)OCC(C)C)C(*)(C#N)C(=O)OCC(C)C)cc1
*c1ccc(C(C#N)(C(=O)OCC)C(*)(C#N)C(=O)OCC)cc1
*c1ccc(C(C#N)(C(=O)OCCCC)C(*)(C#N)C(=O)OCCCC)cc1
*c1ccc(C(C)(C)c2ccc(C(C)(C)c3ccc(N4C(=O)c5ccc(Oc6ccc7cc(Oc8ccc9c(c8)C(=O)N(*)C9=O)ccc7c6)cc5C4=O)cc3)cc2)cc1
*c1ccc(C(C)(C)c2ccc(C(C)(C)c3ccc(N4C(=O)c5ccc(Oc6ccc7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7c6)cc5C4=O)cc3)cc2)cc1
*c1ccc(C(C)(C)c2ccc(C(C)(C)c3ccc(N4C(=O)c5ccc(Oc6cccc7c(Oc8ccc9c(c8)C(=O)N(*)C9=O)cccc67)cc5C4=O)cc3)cc2)cc1
*c1ccc(C(C)(C)c2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)cc2)cc1
*c1ccc(C(C)(C)c2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(C(C)(C)c2ccc(N3C(=O)c4ccc(Oc5ccc6cc(Oc7ccc8c(c7)C(=O)N(*)C8=O)ccc6c5)cc4C3=O)cc2)cc1
*c1ccc(C(C)(C)c2ccc(N3C(=O)c4ccc(Oc5ccc6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6c5)cc4C3=O)cc2)cc1
*c1ccc(C(C)(C)c2ccc(N3C(=O)c4ccc(Oc5cccc6c(Oc7ccc8c(c7)C(=O)N(*)C8=O)cccc56)cc4C3=O)cc2)cc1
*c1ccc(C(Cl)(Cl)C(*)Cl)cc1
*c1ccc(C(F)(F)C(*)(F)F)cc1
*c1ccc(C(c2ccc(-c3cc(-c4ccccc4)c4cc(Oc5ccc6nc(*)cc(-c7ccccc7)c6c5)ccc4n3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*c1ccc(C(c2ccc(-c3nc4ccc(-c5ccc6nc(*)[nH]c6c5)cc4[nH]3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*c1ccc(C(c2ccc(-c3nc4ccc(-c5ccc6nc(*)oc6c5)cc4o3)cc2)(C(F)(F)F)C(F)(F)F)cc1
*c1ccc(C(c2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*c1ccc(C(c2ccc(C#N)cc2)c2ccc(N(*)c3ccc(C)cc3)cc2)cc1
*c1ccc(C(c2ccc(C(=O)OC)cc2)c2ccc(N(*)c3ccc(C)cc3)cc2)cc1
*c1ccc(C(c2ccc(N(*)c3ccc(C)cc3)cc2)c2ccc([N+](=O)[O-])cc2)cc1
*c1ccc(C(c2ccc(N3C(=O)c4cc(-c5ccc(C(C)(C)C)cc5)c(-c5cc6c(cc5-c5ccc(C(C)(C)C)cc5)C(=O)N(*)C6=O)cc4C3=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*c1ccc(C(c2ccc(N3C(=O)c4cc(-c5ccc([Si](C)(C)C)cc5)c(-c5cc6c(cc5-c5ccc([Si](C)(C)C)cc5)C(=O)N(*)C6=O)cc4C3=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*c1ccc(C(c2ccc(N3C(=O)c4cc5c(cc4C3=O)C(C(F)(F)F)(C(F)(F)F)c3cc4c(cc3O5)C(=O)N(*)C4=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*c1ccc(C(c2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*c1ccc(C(c2ccc(N3C(=O)c4ccc(OCCN(CCOc5ccc6c(c5)C(=O)N(*)C6=O)c5ccc(/C=C/c6ccc([N+](=O)[O-])cc6)cc5)cc4C3=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*c1ccc(C(c2ccc(N3C(=O)c4ccc(Oc5cc6ccccc6cc5Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*c1ccc(C(c2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*c1ccc(C(c2ccc(N3C(=O)c4ccc(Oc5ccc6cc(Oc7ccc8c(c7)C(=O)N(*)C8=O)ccc6c5)cc4C3=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*c1ccc(C(c2ccc(N3C(=O)c4ccc(Oc5ccc6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6c5)cc4C3=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*c1ccc(C(c2ccc(N3C(=O)c4ccc(Oc5cccc6c(Oc7ccc8c(c7)C(=O)N(*)C8=O)cccc56)cc4C3=O)cc2)(C(F)(F)F)C(F)(F)F)cc1
*c1ccc(C(c2ccc(OCCCC)cc2)c2ccc(N(*)c3ccc(C)cc3)cc2)cc1
*c1ccc(C(c2ccccc2)(c2ccc(-c3nc4ccc(-c5ccc6nc(*)oc6c5)cc4o3)cc2)C(F)(F)F)cc1
*c1ccc(C(c2ccccc2)(c2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2)C(F)(F)F)cc1
*c1ccc(C(c2ccccc2)(c2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)C(F)(F)F)cc1
*c1ccc(C(c2ccccc2)c2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2)cc1
*c1ccc(C(c2ccccc2)c2ccc(N(*)c3ccc(C)cc3)cc2)cc1
*c1ccc(C(c2ccccc2)c2ccc(N(c3ccc(C)cc3)c3ccc(-c4ccc(N(*)c5ccc(C)cc5)cc4)cc3)cc2)cc1
*c1ccc(C(c2ccccc2)c2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(C(c2ccccc2)c2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(C(c2ccccc2)c2ccc(N3C(=O)c4ccc(S(=O)(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(C)c(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1ccc(C)c(N2C(=O)c3ccc(S(=O)(=O)c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1ccc(C2(c3ccc(-c4cc(-c5ccccc5)c5cc(-c6ccc7nc(*)cc(-c8ccccc8)c7c6)ccc5n4)cc3)c3ccccc3-c3ccccc32)cc1
*c1ccc(C2(c3ccc(-c4cc(-c5ccccc5)c5cc(-c6ccc7nc(*)cc(-c8ccccc8)c7c6)ccc5n4)cc3)c3ccccc3C(=O)c3ccccc32)cc1
*c1ccc(C2(c3ccc(-c4cc(-c5ccccc5)c5cc(C6(c7ccc8nc(*)cc(-c9ccccc9)c8c7)c7ccccc7-c7ccccc76)ccc5n4)cc3)c3ccccc3-c3ccccc32)cc1
*c1ccc(C2(c3ccc(-c4cc(-c5ccccc5)c5cc(C6(c7ccc8nc(*)cc(-c9ccccc9)c8c7)c7ccccc7-c7ccccc76)ccc5n4)cc3)c3ccccc3C(=O)c3ccccc32)cc1
*c1ccc(C2(c3ccc(-c4cc(-c5ccccc5)c5cc(C6(c7ccc8nc(*)cc(-c9ccccc9)c8c7)c7ccccc7C(=O)c7ccccc76)ccc5n4)cc3)c3ccccc3-c3ccccc32)cc1
*c1ccc(C2(c3ccc(-c4cc(-c5ccccc5)c5cc(C6(c7ccc8nc(*)cc(-c9ccccc9)c8c7)c7ccccc7C(=O)c7ccccc76)ccc5n4)cc3)c3ccccc3C(=O)c3ccccc32)cc1
*c1ccc(C2(c3ccc(-c4cc(-c5ccccc5)c5cc(Oc6ccc7nc(*)cc(-c8ccccc8)c7c6)ccc5n4)cc3)c3ccccc3-c3ccccc32)cc1
*c1ccc(C2(c3ccc(-c4cc(-c5ccccc5)c5cc(Oc6ccc7nc(*)cc(-c8ccccc8)c7c6)ccc5n4)cc3)c3ccccc3C(=O)c3ccccc32)cc1
*c1ccc(C2(c3ccc(-c4nnc(*)o4)cc3)OC(=O)c3ccccc32)cc1
*c1ccc(C2(c3ccc(N4C(=O)C(c5ccccc5)=C(c5ccc(C6=C(c7ccccc7)C(=O)N(*)C6=O)cc5)C4=O)cc3)c3ccccc3-c3ccccc32)cc1
*c1ccc(C2(c3ccc(N4C(=O)c5ccc(-c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)c3ccccc3-c3ccccc32)cc1
*c1ccc(C2(c3ccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)c3ccccc3-c3ccccc32)cc1
*c1ccc(C2(c3ccc(N4C(=O)c5ccc(S(=O)(=O)c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)c3ccccc3-c3ccccc32)cc1
*c1ccc(C2C3C(=O)N(c4ccc(N5C(=O)C6ON(C)C(*)C6C5=O)cc4)C(=O)C3ON2C)cc1
*c1ccc(CC(=O)Nc2ccc(-c3ccc(NC(=O)Cc4ccc(-c5sc(*)c(-c6ccccc6)c5-c5ccccc5)cc4)cc3)cc2)cc1
*c1ccc(CC(=O)Nc2ccc(Cc3ccc(NC(=O)Cc4ccc(-c5sc(*)c(-c6ccccc6)c5-c5ccccc5)cc4)cc3)cc2)cc1
*c1ccc(CC(=O)Nc2ccc(NC(=O)Cc3ccc(-c4sc(*)c(-c5ccccc5)c4-c4ccccc4)cc3)cc2)cc1
*c1ccc(CC(=O)Nc2ccc(Oc3ccc(NC(=O)Cc4ccc(-c5sc(*)c(-c6ccccc6)c5-c5ccccc5)cc4)cc3)cc2)cc1
*c1ccc(CC(=O)Nc2ccc(S(=O)(=O)c3ccc(NC(=O)Cc4ccc(-c5sc(*)c(-c6ccccc6)c5-c5ccccc5)cc4)cc3)cc2)cc1
*c1ccc(CC(=O)Nc2cccc(NC(=O)Cc3ccc(-c4sc(*)c(-c5ccccc5)c4-c4ccccc4)cc3)c2)cc1
*c1ccc(CC(=O)Nc2cccc3c(NC(=O)Cc4ccc(-c5sc(*)c(-c6ccccc6)c5-c5ccccc5)cc4)cccc23)cc1
*c1ccc(CCc2ccc(N3C(=O)C(=O)N(*)C3=O)cc2)cc1
*c1ccc(CCc2ccc(N3C(=O)C(=O)N(c4ccc(CCc5ccccc5N5C(=O)C(=O)N(*)C5=O)cc4)C3=O)cc2)cc1
*c1ccc(CCc2ccccc2N2C(=O)C(=O)N(c3ccc(Cc4ccc(N5C(=O)C(=O)N(*)C5=O)cc4)cc3)C2=O)cc1
*c1ccc(CCc2ccccc2N2C(=O)C(=O)N(c3ccc(Oc4ccc(N5C(=O)C(=O)N(*)C5=O)cc4)cc3)C2=O)cc1
*c1ccc(Cc2ccc(-c3nc(-c4cccc(-c5nnc(-c6ccccc6)c(*)n5)n4)nnc3-c3ccccc3)cc2)cc1
*c1ccc(Cc2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c(C(C)(C)C)c2)cc1C(C)(C)C
*c1ccc(Cc2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2)cc1
*c1ccc(Cc2ccc(-n3nc(-c4ccccc4)cc3-c3ccc(-c4cc(-c5ccccc5)nn4*)cc3)cc2)cc1
*c1ccc(Cc2ccc(-n3nc(-c4ccccc4)cc3-c3cccc(-c4cc(-c5ccccc5)nn4*)c3)cc2)cc1
*c1ccc(Cc2ccc(-n3nccc3-c3ccc(-c4ccnn4*)cc3)cc2)cc1
*c1ccc(Cc2ccc(-n3nccc3-c3cccc(-c4ccnn4*)c3)cc2)cc1
*c1ccc(Cc2ccc(N(c3ccc(C)cc3)c3ccc(-c4ccc(N(*)c5ccc(C)cc5)cc4)cc3)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)C(C)C(SCCOCCSC4C(=O)N(c5ccc(Cc6ccc(N7C(=O)CC(C)(SCCOCCSC8(C)CC(=O)N(*)C8=O)C7=O)cc6)cc5)C(=O)C4C)C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)C(Cl)=C(Oc4ccc(C(C)(C)c5ccc(OC6=C(Cl)C(=O)N(*)C6=O)cc5)cc4)C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)C(Cl)=C(Oc4ccc(C(c5ccc(OC6=C(Cl)C(=O)N(*)C6=O)cc5)(C(F)(F)F)C(F)(F)F)cc4)C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)C4CCC(C5CCC6C(=O)N(*)C(=O)C6C5)CC4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)C4CCC5C(=O)N(*)C(=O)C5C4C3=O)c(C)c2)cc1C
*c1ccc(Cc2ccc(N3C(=O)C4CCC5C(=O)N(*)C(=O)C5C4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)CC(Nc4ccc(Cc5ccc(NC6CC(=O)N(*)C6=O)cc5)cc4)C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)CC(Nc4ccc(N(c5ccc(NC6CC(=O)N(*)C6=O)cc5)c5ccc([N+](=O)[O-])cc5)cc4)C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)CC(Nc4ccc(N(c5ccc(Nc6ccc([N+](=O)[O-])cc6)cc5)c5ccc(NC6CC(=O)N(*)C6=O)cc5)cc4)C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)N(c4ccc(Cc5ccc(N6C(=O)N(*)C(C)(C)C6=O)cc5)cc4)C(=O)C3(C)C)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4c(c(-c5ccccc5)c(-c5ccc(Oc6ccc(-c7c(-c8ccccc8)c8c(c(-c9ccccc9)c7-c7ccccc7)C(=O)N(*)C8=O)cc6)cc5)c(-c5ccccc5)c4-c4ccccc4)C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4cc(-c5ccc(C(C)(C)C)cc5)c(-c5cc6c(cc5-c5ccc(C(C)(C)C)cc5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4cc(-c5ccc([Si](C)(C)C)cc5)c(-c5cc6c(cc5-c5ccc([Si](C)(C)C)cc5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4ccc(-c5ccc(-c6ccc(-c7ccc(-c8ccc(-c9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)cc7)cc6)cc5)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C(C)(C)C)c2)cc1C(C)(C)C
*c1ccc(Cc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4ccc(OCCN(CCOc5ccc6c(c5)C(=O)N(*)C6=O)c5ccc(/C=C/c6ccc([N+](=O)[O-])cc6)cc5)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Oc5ccc(C(C)(C)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Oc5ccc(C6(c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)CCC(c7ccccc7)CC6)cc5)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Oc5ccc(C6(c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)CCCCCCCCCCC6)cc5)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)c(C)c2C)c(C)c1C
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)c(OC)c2)cc1OC
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C(C)(C)C)c2)cc1C(C)(C)C
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4ccc(S(=O)(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C(C)(C)C)c2)cc1C(C)(C)C
*c1ccc(Cc2ccc(N3C(=O)c4ccc(S(=O)(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Sc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c(C(C)(C)C)c2)cc1C(C)(C)C
*c1ccc(Cc2ccc(N3C(=O)c4ccc(Sc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4ccc([Si](C)(C)O[Si](C)(C)O[Si](C)(C)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4ccc([Si](C)(C)O[Si](C)(C)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4ccc([Si](C)(C)c5ccc([Si](C)(C)c6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4ccc([Si](C)(C)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Cc2ccc(N3C(=O)c4cccc5c(N6CCC(CCCC7CCN(c8ccc9c%10c(cccc8%10)C(=O)N(*)C9=O)CC7)CC6)ccc(c45)C3=O)c(C(C)(C)C)c2)cc1C(C)(C)C
*c1ccc(Cc2ccc(NC(=O)c3ccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2)cc1
*c1ccc(Cc2ccc(NC(=O)c3ccc(Oc4cccc5c4C(=O)N(*)C5=O)cc3)cc2)cc1
*c1ccc(Cc2ccc(NC(=O)c3cccc(-c4nc5cc(-c6ccc7[nH]c(-c8cccc(C(=O)Nc9ccc(Cc%10ccc(-n%11c(=O)c%12cc%13c(=O)n(*)c(=O)c%13cc%12c%11=O)cc%10)cc9)c8)nc7c6)ccc5[nH]4)c3)cc2)cc1
*c1ccc(Cc2ccc(NC(=O)c3cccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)c3)cc2)cc1
*c1ccc(Cc2ccc(S(*)(=O)=O)cc2)cc1
*c1ccc(Cc2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c2)cc1
*c1ccc(Cc2ccccc2-n2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)cc1
*c1ccc(N(c2ccc(C#N)cc2)c2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2)cc1
*c1ccc(N(c2ccc(C#N)cc2)c2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(N(c2ccc(C#N)cc2)c2ccc(N3C(=O)c4ccc(Oc5ccc(C(C)(C)c6ccc(C(C)(C)c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5)cc4C3=O)cc2)cc1
*c1ccc(N(c2ccc(C#N)cc2)c2ccc(N3C(=O)c4ccc(Oc5ccc(C(C)(C)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)cc2)cc1
*c1ccc(N(c2ccc(C#N)cc2)c2ccc(N3C(=O)c4ccc(Oc5ccc(C(c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)(C(F)(F)F)C(F)(F)F)cc5)cc4C3=O)cc2)cc1
*c1ccc(N(c2ccc(C#N)cc2)c2ccc(N3C(=O)c4ccc(Oc5ccc(C6(c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)CCCCC6)cc5)cc4C3=O)cc2)cc1
*c1ccc(N(c2ccc(C#N)cc2)c2ccc(N3C(=O)c4ccc(Oc5ccc(C6(c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)c7ccccc7-c7ccccc76)cc5)cc4C3=O)cc2)cc1
*c1ccc(N(c2ccc(C#N)cc2)c2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(N/C=N/c2ccc(S(*)(=O)=O)cc2)cc1
*c1ccc(N2C(=O)CC(C3C=C(C)C4C(=O)N(*)C(=O)C4C3)C2=O)cc1
*c1ccc(N2C(=O)c3c(c(-c4ccccc4)c(-c4ccc(-c5c(-c6ccccc6)c6c(c(-c7ccccc7)c5-c5ccccc5)C(=O)N(*)C6=O)cc4)c(-c4ccccc4)c3-c3ccccc3)C2=O)cc1
*c1ccc(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)cc1
*c1ccc(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(c4ccc6[nH]c(*)nc6c4)C5=O)cc3C2=O)cc1
*c1ccc(N2C(=O)c3ccc(-c4cccc5c4C(=O)N(*)C5=O)cc3C2=O)cc1
*c1ccc(N2C(=O)c3ccc(C(=O)c4ccc5c(c4)C(=O)N(c4ccc(-c6nc7cc(-c8ccc9nc(-c%10ccccc%10)c(*)nc9c8)ccc7nc6-c6ccccc6)cc4)C5=O)cc3C2=O)cc1
*c1ccc(N2C(=O)c3ccc(C(=O)c4ccc5c(c4)C(=O)N(c4ccc(-c6nc7cc(Oc8ccc9nc(-c%10ccccc%10)c(*)nc9c8)ccc7nc6-c6ccccc6)cc4)C5=O)cc3C2=O)cc1
*c1ccc(N2C(=O)c3ccc(C(=O)c4ccc5c(c4)C(=O)N(c4ccc(-c6nc7cc(S(=O)(=O)c8ccc9nc(-c%10ccccc%10)c(*)nc9c8)ccc7nc6-c6ccccc6)cc4)C5=O)cc3C2=O)cc1
*c1ccc(N2C(=O)c3ccc(C(=O)c4ccc5c(c4)C(=O)N(c4ccc6[nH]c(*)nc6c4)C5=O)cc3C2=O)cc1
*c1ccc(N2C(=O)c3ccc(C(c4ccc5c(c4)C(=O)N(c4ccc(N6C(=O)c7ccc(-c8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)cc4)C5=O)(C(F)(F)F)C(F)(F)F)cc3C2=O)cc1
*c1ccc(N2C(=O)c3ccc(Oc4ccc(C(C)(C)c5ccc(Oc6ccc7c(c6)C(=O)N(c6ccc8[nH]c(*)nc8c6)C7=O)cc5)cc4)cc3C2=O)cc1
*c1ccc(N2C(=O)c3ccc(Oc4ccc(C(C)(C)c5cccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)c5)cc4)cc3C2=O)cc1
*c1ccc(N2C(=O)c3ccc(Oc4ccc(Oc5ccc(P(=O)(c6ccccc6)c6ccc(Oc7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5)cc4)cc3C2=O)cc1
*c1ccc(N2C(=O)c3ccc(Oc4ccc(Sc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4)cc3C2=O)cc1
*c1ccc(N2C(=O)c3ccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)cc1
*c1ccc(N2C(=O)c3ccc(P(=O)(c4ccccc4)c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)cc1
*c1ccc(NC(=O)CCCCC(=O)Nc2ccc(S(*)(=O)=O)cc2)cc1
*c1ccc(NC(=O)CCCCCC(=O)Nc2ccc(S(*)(=O)=O)cc2)cc1
*c1ccc(NC(=O)CCCCCCCC(=O)Nc2ccc(S(*)(=O)=O)cc2)cc1
*c1ccc(NC(=O)CCCCCCCCC(=O)Nc2ccc(S(*)(=O)=O)cc2)cc1
*c1ccc(NC(=O)CCCCCCCCCCC(=O)Nc2ccc(S(*)(=O)=O)cc2)cc1
*c1ccc(NC(=O)CN2C(=O)c3ccc(C(c4ccc5c(c4)C(=O)N(CC(=O)Nc4ccc(-c6nnc(*)o6)cc4)C5=O)(C(F)(F)F)C(F)(F)F)cc3C2=O)cc1
*c1ccc(NC(=O)Cc2ccc(-c3sc(-c4ccc(CC(=O)Nc5ccc(-c6sc(*)c(-c7ccccc7)c6-c6ccccc6)cc5)cc4)c(-c4ccccc4)c3-c3ccccc3)cc2)cc1
*c1ccc(NC(=O)Nc2ccc(NC(=O)Nc3ccc(-c4nc(-c5ccc(-c6nc(-c7ccc([N+](=O)[O-])cc7)c(-c7ccc([N+](=O)[O-])cc7)[nH]6)cc5)[nH]c4*)cc3)cc2)cc1
*c1ccc(NC(=O)Nc2ccc(NC(=O)Nc3ccc(-c4nc(-c5ccc([N+](=O)[O-])cc5)[nH]c4*)cc3)cc2)cc1
*c1ccc(NC(=O)Nc2ccc(NC(=O)Nc3ccc(-c4nc(C#N)c(C#N)nc4*)cc3)cc2)cc1
*c1ccc(NC(=O)c2cc(C(=O)Nc3ccc(-c4nnc(*)o4)cc3)cc(N3C(=O)c4ccccc4C3=O)c2)cc1
*c1ccc(NC(=O)c2cc(C(=O)Nc3ccc(S(*)(=O)=O)cc3)cc(S(=O)(=O)c3ccccc3)c2)cc1
*c1ccc(NC(=O)c2cc(NC(=O)c3ccc(NC(=O)C(C)N4C(=O)c5ccccc5C4=O)cc3)cc(C(=O)Nc3ccc(S(*)(=O)=O)cc3)c2)cc1
*c1ccc(NC(=O)c2cc(NC(=O)c3ccc(OC(C)=O)cc3)cc(C(=O)Nc3ccc(-c4nnc(*)o4)cc3)c2)cc1
*c1ccc(NC(=O)c2ccc(C(=O)Nc3ccc(-c4nnc(*)o4)cc3)c(Oc3ccccc3)c2)cc1
*c1ccc(NC(=O)c2ccc(NC(=O)c3ccc([Si](C)(C)c4ccc(C(=O)Nc5ccc(C(=O)Nc6ccc(-c7sc(*)c(-c8ccccc8)c7-c7ccccc7)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(NC(=O)c2ccc(NC(=O)c3ccc([Si](C)(C)c4ccc(C(=O)Nc5ccc(C(=O)Nc6ccc(S(*)(=O)=O)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(NC(=O)c2ccc(OCCOCCOCCOCCOCCOc3ccc(C(=O)Nc4ccc5[nH]c(*)nc5c4)cc3)cc2)cc1
*c1ccc(NC(=O)c2ccc(OCCOCCOCCOCCOc3ccc(C(=O)Nc4ccc5[nH]c(*)nc5c4)cc3)cc2)cc1
*c1ccc(NC(=O)c2ccc(OCCOCCOCCOc3ccc(C(=O)Nc4ccc5[nH]c(*)nc5c4)cc3)cc2)cc1
*c1ccc(NC(=O)c2ccc(OCCOCCOc3ccc(C(=O)Nc4ccc5[nH]c(*)nc5c4)cc3)cc2)cc1
*c1ccc(NC(=O)c2ccc(OCCOc3ccc(C(=O)Nc4ccc5[nH]c(*)nc5c4)cc3)cc2)cc1
*c1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)cc3)c2)cc1
*c1ccc(NC(=O)c2cccc(C(=O)Nc3ccc(S(*)(=O)=O)cc3)c2)cc1
*c1ccc(NC2CC(=O)N(c3ccc(-c4sc(-c5ccc(N6C(=O)CC(Nc7ccc(-c8nc(-c9ccc(-c%10nc(-c%11ccc([N+](=O)[O-])cc%11)c(-c%11ccc([N+](=O)[O-])cc%11)[nH]%10)cc9)[nH]c8*)cc7)C6=O)cc5)c(-c5ccccc5)c4-c4ccccc4)cc3)C2=O)cc1
*c1ccc(NC2CC(=O)N(c3ccc(-c4sc(-c5ccc(N6C(=O)CC(Nc7ccc(-c8nc(-c9ccc(/C=C/c%10ccc([N+](=O)[O-])cc%10)s9)[nH]c8*)cc7)C6=O)cc5)c(-c5ccccc5)c4-c4ccccc4)cc3)C2=O)cc1
*c1ccc(NC2CC(=O)N(c3ccc(-c4sc(-c5ccc(N6C(=O)CC(Nc7ccc(-c8nc(C#N)c(C#N)nc8*)cc7)C6=O)cc5)c(-c5ccccc5)c4-c4ccccc4)cc3)C2=O)cc1
*c1ccc(NC2CC(=O)N(c3ccc(Cc4ccc(N5C(=O)CC(Nc6ccc(-c7nc(-c8ccc(/C=C/c9ccc([N+](=O)[O-])cc9)cc8)[nH]c7*)cc6)C5=O)cc4)cc3)C2=O)cc1
*c1ccc(NC2CC(=O)N(c3ccc(Cc4ccc(N5C(=O)CC(Nc6ccc(-c7nc(-c8ccc(/N=N/c9ccc([N+](=O)[O-])cc9)cc8)[nH]c7*)cc6)C5=O)cc4)cc3)C2=O)cc1
*c1ccc(N[Se]Nc2ccc(S(*)(=O)=O)cc2)cc1
*c1ccc(Nc2c(F)c(F)c(-c3c(F)c(F)c(Nc4ccc(S(*)(=O)=O)cc4)c(F)c3F)c(F)c2F)cc1
*c1ccc(Nc2ccc(C(c3ccc(Nc4ccc(S(*)(=O)=O)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Nc2ccc(C3(c4ccc(Nc5ccc(S(*)(=O)=O)cc5)c(C)c4)c4ccccc4-c4ccccc43)cc2C)cc1
*c1ccc(Nc2ccc(C3(c4ccc(Nc5ccc(S(*)(=O)=O)cc5)c(F)c4)c4ccccc4-c4ccccc43)cc2F)cc1
*c1ccc(Nc2ccc(C3(c4ccc(Nc5ccc(S(*)(=O)=O)cc5)cc4)c4ccccc4-c4ccccc43)cc2)cc1
*c1ccc(Nc2ccc(Nc3ccc(S(*)(=O)=O)cc3)cc2)cc1
*c1ccc(Nc2cccc(Nc3ccc(S(*)(=O)=O)cc3)c2)cc1
*c1ccc(Nc2nc(Nc3ccc(-c4nc5cc(-c6ccc7[nH]c(*)nc7c6)ccc5[nH]4)cc3)nc(N(c3ccccc3)c3ccccc3)n2)cc1
*c1ccc(O)c(N2C(=O)c3ccc(Oc4ccc(C(C)(C)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4)cc3C2=O)c1
*c1ccc(OC(=O)CCCCCCCCCCCCCCCCCCCCC(=O)Oc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(OC(=O)Nc2ccc(-c3ccc(NC(=O)Oc4ccc(-c5nc(-c6ccc(-c7nc(-c8ccc([N+](=O)[O-])cc8)c(-c8ccc([N+](=O)[O-])cc8)[nH]7)cc6)[nH]c5*)cc4)cc3OC)c(OC)c2)cc1
*c1ccc(OC(=O)Nc2ccc(-c3ccc(NC(=O)Oc4ccc(-c5nc(-c6ccc([N+](=O)[O-])cc6)[nH]c5*)cc4)cc3OC)c(OC)c2)cc1
*c1ccc(OC(=O)Oc2ccc(C3(*)OC(=O)c4ccccc43)cc2)cc1
*c1ccc(OC(=O)c2cc(C(=O)Oc3ccc(C4(*)OC(=O)c5ccccc54)cc3)cc(C(C)(C)C)c2)cc1
*c1ccc(OC(=O)c2cc(Cl)cc(C(=O)Oc3ccc(C4(*)OC(=O)c5ccccc54)cc3)c2)cc1
*c1ccc(OC(=O)c2ccc(C(=O)Oc3ccc(-c4nc5ccccc5nc4*)cc3)cc2)cc1
*c1ccc(OC(=O)c2ccc(C(=O)Oc3ccc(C4(*)NC(=O)c5ccccc54)cc3)cc2)cc1
*c1ccc(OC(=O)c2ccc(C(=O)Oc3ccc(C4(*)OC(=O)c5ccccc54)cc3)cc2)cc1
*c1ccc(OC(=O)c2ccc(C(=O)Oc3ccc(C4(*)OCc5ccccc54)cc3)cc2)cc1
*c1ccc(OC(=O)c2ccc(C(=O)Oc3ccc(C4(*)c5ccccc5C(=O)N4C)cc3)cc2)cc1
*c1ccc(OC(=O)c2ccc(C(=O)Oc3ccc(C4(*)c5ccccc5Oc5ccccc54)cc3)cc2)cc1
*c1ccc(OC(=O)c2ccc([Si](C)(C)c3ccc(C(=O)Oc4ccc(C5(*)OC(=O)c6ccccc65)cc4)cc3)cc2)cc1
*c1ccc(OC(=O)c2ccc([Si](C)(C)c3ccc(C(=O)Oc4ccc(C5(*)c6ccccc6C(=O)N5c5ccccc5)cc4)cc3)cc2)cc1
*c1ccc(OC(=O)c2ccc([Si](c3ccccc3)(c3ccccc3)c3ccc(C(=O)Oc4ccc(C5(*)OC(=O)c6ccccc65)cc4)cc3)cc2)cc1
*c1ccc(OC(=O)c2cccc(C(=O)Oc3ccc(-c4nc5ccccc5nc4*)cc3)c2)cc1
*c1ccc(OC(=O)c2cccc(C(=O)Oc3ccc(C4(*)C(=O)Nc5c(Cl)cc(Cl)cc54)cc3)c2)cc1
*c1ccc(OC(=O)c2cccc(C(=O)Oc3ccc(C4(*)C(=O)Nc5ccccc54)cc3)c2)cc1
*c1ccc(OC(=O)c2cccc(C(=O)Oc3ccc(C4(*)NC(=O)c5ccccc54)cc3)c2)cc1
*c1ccc(OC(=O)c2cccc(C(=O)Oc3ccc(C4(*)OC(=O)c5ccccc54)cc3)c2)cc1
*c1ccc(OC(=O)c2cccc(C(=O)Oc3ccc(C4(*)OC(=O)c5ccccc54)cc3C)c2)c(C)c1
*c1ccc(OC(=O)c2cccc(C(=O)Oc3ccc(C4(*)c5ccccc5C(=O)N4C)cc3)c2)cc1
*c1ccc(OCC2CCC(COc3ccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)cc3)CC2)cc1
*c1ccc(OCC2CCC(COc3ccc(N4C(=O)c5ccc(-c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)CC2)cc1
*c1ccc(OCCCCCCCCCCCCCCCC)c(N2C(=O)CC(C3C=C(C)C4C(=O)N(*)C(=O)C4C3)C2=O)c1
*c1ccc(OCCCCCCCCCCCOC(=O)CCCCC(=O)OCCCCCCCCCCCOc2ccc(-c3nnc(*)s3)cc2)cc1
*c1ccc(OCCCCCCCCCCCOC(=O)CCCCCCCCC(=O)OCCCCCCCCCCCOc2ccc(-c3nnc(*)s3)cc2)cc1
*c1ccc(OCCCCCCCCCCCOC(=O)c2ccc(C(=O)OCCCCCCCCCCCOc3ccc(-c4nnc(*)s4)cc3)cc2)cc1
*c1ccc(OCCCCCCCCCCCOC(=O)c2ccc(C(=O)OCCCCCCCCCCCOc3cccc(-c4nnc(*)s4)c3)cc2)cc1
*c1ccc(OCCCCCCCCCCCOC(=O)c2cccc(C(=O)OCCCCCCCCCCCOc3ccc(-c4nnc(*)s4)cc3)c2)cc1
*c1ccc(OCCCCCCCCCCCOC(=O)c2cccc(C(=O)OCCCCCCCCCCCOc3cccc(-c4nnc(*)s4)c3)c2)cc1
*c1ccc(OCCCCCCCCCCCOc2ccc(-c3ccc4nc(-c5cccc(-c6cc(-c7ccccc7)c7cc(*)ccc7n6)c5)cc(-c5ccccc5)c4c3)cc2)cc1
*c1ccc(OCCCCCCCCCCOc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(OCCCCCCN(CC)c2ccc(C(C#N)=C(C#N)C#N)cc2)c(-c2cc(-c3ccccc3)c3cc(Oc4ccc5nc(*)cc(-c6ccccc6)c5c4)ccc3n2)c1
*c1ccc(OCCCCCCOc2ccc(C3C4C(=O)N(c5ccc(N6C(=O)C7ON(C)C(*)C7C6=O)cc5)C(=O)C4ON3C)cc2)cc1
*c1ccc(OCCCCCOc2ccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(OCCCCOc2ccc(-c3nc4ccc(-c5ccc6nc(*)c(-c7ccccc7)nc6c5)cc4nc3-c3ccccc3)cc2)cc1
*c1ccc(OCCCOc2ccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(OCCN(C)c2ccc(/C=C/C3=C/C(=C4/C(=O)c5ccccc5C4=C(C#N)C#N)C=C(/C=C/c4ccc(N(C)CCOc5ccc(-c6nc7ccc(Oc8ccc9nc(*)c(-c%10ccccc%10)nc9c8)cc7nc6-c6ccccc6)cc5)cc4)O3)cc2)cc1
*c1ccc(OCCN(CC)c2ccc(-c3ccc(C(=C(C#N)C#N)c4ccc(-c5ccc(N(CC)CC)cc5)s4)s3)cc2)c(-c2cc(-c3ccccc3)c3cc(Oc4ccc5nc(*)cc(-c6ccccc6)c5c4)ccc3n2)c1
*c1ccc(OCCN(CC)c2ccc(-c3ccc(C(=C(C#N)C#N)c4ccc(N(CC)CC)cc4)s3)cc2)c(-c2cc(-c3ccccc3)c3cc(Oc4ccc5nc(*)cc(-c6ccccc6)c5c4)ccc3n2)c1
*c1ccc(OCCN(CC)c2ccc(C(C#N)=C(C#N)C#N)cc2)c(-c2cc(-c3ccccc3)c3cc(Oc4ccc5nc(*)cc(-c6ccccc6)c5c4)ccc3n2)c1
*c1ccc(OCCN(CCOc2ccc(-c3cc(-c4ccccc4)c4cc(Oc5ccc6nc(*)cc(-c7ccccc7)c6c5)ccc4n3)cc2)c2ccc(C(C#N)=C(C#N)C#N)cc2)cc1
*c1ccc(OCCN(CCOc2ccc(-c3nc4ccc(Oc5ccc6nc(*)c(-c7ccccc7)nc6c5)cc4nc3-c3ccccc3)cc2)c2ccc(/C=C/c3ccc([N+](=O)[O-])cc3)cc2)cc1
*c1ccc(OCCN(CCOc2ccc(-c3nc4ccc(Oc5ccc6nc(*)c(-c7ccccc7)nc6c5)cc4nc3-c3ccccc3)cc2)c2ccc(/N=N/c3ccc([N+](=O)[O-])cc3)cc2)cc1
*c1ccc(OCCN(CCOc2ccc(-c3nc4ccc(Oc5ccc6nc(*)c(-c7ccccc7)nc6c5)cc4nc3-c3ccccc3)cc2)c2ccc(/N=N/c3ccc([N+](=O)[O-])cc3C)cc2)cc1
*c1ccc(OCCOCCOCCOc2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)cc1
*c1ccc(OCCOCCOc2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)cc1
*c1ccc(OCCOc2ccc(/C=C/c3ccc([N+](=O)[O-])cc3)c(OCCOc3ccc(-c4nc5ccc(Oc6ccc7nc(*)c(-c8ccccc8)nc7c6)cc5nc4-c4ccccc4)cc3)c2)cc1
*c1ccc(OCCOc2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)cc1
*c1ccc(OCOc2ccc(-c3nc(-c4ccc([N+](=O)[O-])cc4)oc3*)cc2)cc1
*c1ccc(Oc2c(C)cc(-c3cc(C)c(Oc4ccc(N5C(=O)c6ccc(Oc7ccccc7Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4C(F)(F)F)c(C)c3)cc2C)c(C(F)(F)F)c1
*c1ccc(Oc2c(C)cc(C(C)(C)c3cc(C)c(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(C8(c9ccc(Oc%10ccc%11c(c%10)C(=O)N(*)C%11=O)cc9)CCC(c9ccccc9)CC8)cc7)cc6C5=O)cc4)c(C)c3)cc2C)cc1
*c1ccc(Oc2c(C)cc(C(c3cc(C)c(Oc4ccc(-n5c(=O)c6cc7c(=O)n(*)c(=O)c7cc6c5=O)cc4)c(C)c3)c3cccc4ccccc34)cc2C)cc1
*c1ccc(Oc2c(C)cc(C(c3cc(C)c(Oc4ccc(N5C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)c(C)c3)c3cccc4ccccc34)cc2C)cc1
*c1ccc(Oc2c(C)cc(C(c3cccnc3)c3cc(C)c(Oc4ccc(N5C(=O)CC(Nc6ccc(Cc7ccc(NC8CC(=O)N(*)C8=O)cc7)cc6)C5=O)cc4)c(C)c3)cc2C)cc1
*c1ccc(Oc2c(C)cc(C(c3cccnc3)c3cc(C)c(Oc4ccc(N5C(=O)CC(Nc6ccc(NC7CC(=O)N(*)C7=O)cc6)C5=O)cc4)c(C)c3)cc2C)cc1
*c1ccc(Oc2c(C)cc(C(c3cccnc3)c3cc(C)c(Oc4ccc(N5C(=O)CC(Nc6cccc(NC7CC(=O)N(*)C7=O)c6)C5=O)cc4)c(C)c3)cc2C)cc1
*c1ccc(Oc2c(C)cc(Cc3cc(C)c(Oc4ccc(-n5c(=O)c6cc7c(=O)n(*)c(=O)c7cc6c5=O)cc4)c(C)c3)cc2C)cc1
*c1ccc(Oc2c(C)cc(Cc3cc(C)c(Oc4ccc(N5C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)c(C)c3)cc2C)cc1
*c1ccc(Oc2c(F)c(F)c(C(=O)c3c(F)c(F)c(Oc4ccc(C5(*)OC(=O)c6ccccc65)cc4)c(F)c3F)c(F)c2F)cc1
*c1ccc(Oc2cc(C(C)(C)C)c(Oc3ccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)cc3)cc2C(C)(C)C)cc1
*c1ccc(Oc2cc(C(C)(C)C)c(Oc3ccc(N4C(=O)c5ccc(-c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)cc2C(C)(C)C)cc1
*c1ccc(Oc2cc(C(C)(C)C)c(Oc3ccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)cc2C(C)(C)C)cc1
*c1ccc(Oc2cc(C(C)(C)C)c(Oc3ccc(N4C(=O)c5ccc(S(=O)(=O)c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)cc2C(C)(C)C)cc1
*c1ccc(Oc2cc(C(C)(C)C)ccc2Oc2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2)cc1
*c1ccc(Oc2cc(C(C)(C)C)ccc2Oc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2cc3ccccc3cc2Oc2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2)cc1
*c1ccc(Oc2cc3ccccc3cc2Oc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2cc3ccccc3cc2Oc2ccc(N3C(=O)c4ccc(Oc5cc6ccccc6cc5Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2cc3ccccc3cc2Oc2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2cc3ccccc3cc2Oc2ccc(N3C(=O)c4ccc(Oc5ccccc5Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2cc3ccccc3cc2Oc2ccc(N3C(=O)c4ccc(S(=O)(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(-c3c(-c4ccccc4)c(-c4ccc(-c5nc6ccccc6c(*)c5-c5ccccc5)cc4)nc4ccccc34)cc2)cc1
*c1ccc(Oc2ccc(-c3c(-c4ccccc4)c4c(c(-c5ccccc5)c3-c3ccccc3)C(=O)N(c3ccc(N5C(=O)c6c(c(-c7ccccc7)c(-c7ccccc7)c(*)c6-c6ccccc6)C5=O)cc3)C4=O)cc2)cc1
*c1ccc(Oc2ccc(-c3c(-c4ccccc4)c4c(c(-c5ccccc5)c3-c3ccccc3)C(=O)N(c3cccc(N5C(=O)c6c(c(-c7ccccc7)c(-c7ccccc7)c(*)c6-c6ccccc6)C5=O)c3)C4=O)cc2)cc1
*c1ccc(Oc2ccc(-c3c(-c4ccccc4)nc4ccc(-c5ccc6nc(-c7ccccc7)c(*)c(-c7ccccc7)c6c5)cc4c3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3c(-c4ccccc4)nc4ccc(Oc5ccc6nc(-c7ccccc7)c(*)c(-c7ccccc7)c6c5)cc4c3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3c4ccccc4c(-c4ccc(Oc5ccc(-c6c7c(c(*)c8ccccc68)C(=O)N(c6ccccc6)C7=O)cc5)c(C(F)(F)F)c4)c4ccccc34)cc2C(F)(F)F)cc1
*c1ccc(Oc2ccc(-c3cc(-c4ccc(Oc5ccc(-c6cc(*)nc7ccccc67)cc5)cc4)c4ccccc4n3)cc2)cc1
*c1ccc(Oc2ccc(-c3cc(-c4cccc(-c5cc(*)c6ccccc6n5)c4)nc4ccccc34)cc2)cc1
*c1ccc(Oc2ccc(-c3cc(-c4ccccc4)c4cc(-c5ccc6nc(*)cc(-c7ccccc7)c6c5)ccc4n3)cc2)cc1
*c1ccc(Oc2ccc(-c3cc(-c4ccccc4)c4cc(C5(c6ccc7nc(*)cc(-c8ccccc8)c7c6)c6ccccc6-c6ccccc65)ccc4n3)cc2)cc1
*c1ccc(Oc2ccc(-c3cc(-c4ccccc4)c4cc(C5(c6ccc7nc(*)cc(-c8ccccc8)c7c6)c6ccccc6C(=O)c6ccccc65)ccc4n3)cc2)cc1
*c1ccc(Oc2ccc(-c3cc(-c4ccccc4)c4cc(Oc5ccc6nc(*)c(-c7ccccc7)c(-c7ccccc7)c6c5)ccc4n3)cc2)cc1
*c1ccc(Oc2ccc(-c3cc(-c4ccccc4)c4cc(Oc5ccc6nc(*)cc(-c7ccccc7)c6c5)ccc4n3)cc2)cc1
*c1ccc(Oc2ccc(-c3cc(-c4ccccc4)c4cc5c(-c6ccccc6)cc(*)nc5cc4n3)cc2)cc1
*c1ccc(Oc2ccc(-c3cc(-c4ccccc4)c4cc5nc(*)cc(-c6ccccc6)c5cc4n3)cc2)cc1
*c1ccc(Oc2ccc(-c3cc(OCCCCCC)c(-c4ccc(Oc5ccc(-c6nnc(*)o6)cc5)cc4)cc3OCCCCCC)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(-c4ccc(-c5ccc(Oc6ccc(-c7c8c(c(*)c9ccccc79)C(=O)N(c7ccccc7)C8=O)cc6)c(C(F)(F)F)c5)cc4)cc3)cc2C(F)(F)F)cc1
*c1ccc(Oc2ccc(-c3ccc(-c4ccc(Oc5ccc(-c6c7c(c(*)c8ccccc68)C(=O)N(c6ccccc6)C7=O)cc5)c(C(F)(F)F)c4)cc3)cc2C(F)(F)F)cc1
*c1ccc(Oc2ccc(-c3ccc(-c4ccc(Oc5ccc(-c6c7c(c(*)c8ccccc68)C(=O)N(c6ccccc6)C7=O)cc5)c(C(F)(F)F)c4)s3)cc2C(F)(F)F)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(-c5cnc6cc(-c7ccc8nc(*)cnc8c7)ccc6n5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(-c5nc(*)nc(-c6ccccc6)n5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(-c5nc(-c6ccccn6)nnc5*)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(-c5ncc(*)o5)cc4C(F)(F)F)cc3)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(-c5nnc(*)c6c(-c7ccc(F)cc7)c(-c7ccc(F)cc7)c(-c7ccc(F)cc7)c(-c7ccc(F)cc7)c56)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(-c5nnc(*)c6c(-c7ccccc7)c(-c7ccc8ccccc8c7)c(-c7ccc8ccccc8c7)c(-c7ccccc7)c56)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(-n5c(=O)c6cc7c(=O)n(*)c(=O)c7cc6c5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)C(c6ccccc6)=C(c6ccc(C7=C(c8ccccc8)C(=O)N(*)C7=O)cc6)C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)CC(=O)N(*)C5=S)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7c(C)cc(C(C)(C)c8cc(C)c(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)c(C)c8)cc7C)cc6C5=O)cc4)cc3C)c(C)c2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7c(C)cc(Cc8cc(C)c(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)c(C)c8)cc7C)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7cc(C(C)(C)C)c(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7C(C)(C)C)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7cc8ccccc8cc7Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(-c8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(C(C)(C)c8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(C(c8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)(C(F)(F)F)C(F)(F)F)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(C8(c9ccc(Oc%10ccc%11c(c%10)C(=O)N(*)C%11=O)cc9)CCC(C(C)(C)C)CC8)cc7)cc6C5=O)cc4)cc3C)c(C)c2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(C8(c9ccc(Oc%10ccc%11c(c%10)C(=O)N(*)C%11=O)cc9)CCC(c9ccccc9)CC8)cc7)cc6C5=O)cc4)cc3C)c(C)c2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(C8(c9ccc(Oc%10ccc%11c(c%10)C(=O)N(*)C%11=O)cc9)CCCCCCCCCCC8)cc7)cc6C5=O)cc4)cc3C)c(C)c2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(Oc8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(Sc8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc8cc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)ccc8c7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8c7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7cccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)c7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccccc7Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccccc7Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4C(F)(F)F)cc3)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6cccc(Oc7c(C)cc(-c8cc(C)c(Oc9cccc%10c9C(=O)N(*)C%10=O)c(C)c8)cc7C)c6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6cccc(Oc7c(C)cc(Cc8cc(C)c(Oc9cccc%10c9C(=O)N(*)C%10=O)c(C)c8)cc7C)c6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6cccc(Oc7c(Oc8cccc9c8C(=O)N(*)C9=O)cc(C(C)(C)C)cc7C(C)(C)C)c6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6cccc(Oc7cc(C(C)(C)C)c(Oc8cccc9c8C(=O)N(*)C9=O)cc7C(C)(C)C)c6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6cccc(Oc7ccc(C(C)(C)c8ccc(Oc9cccc%10c9C(=O)N(*)C%10=O)cc8)cc7)c6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc(Oc4ccc(N5C(=O)c6cccc(Oc7ccc(C(c8ccc(Oc9cccc%10c9C(=O)N(*)C%10=O)cc8)(C(F)(F)F)C(F)(F)F)cc7)c6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3ccc4nc(Oc5cc(-c6ccccc6)c6cc(*)ccc6n5)cc(-c5ccccc5)c4c3)cc2)cc1
*c1ccc(Oc2ccc(-c3cccc(-c4ccc(Oc5ccc(-c6c7c(c(*)c8ccccc68)C(=O)N(c6ccccc6)C7=O)cc5)c(C(F)(F)F)c4)c3)cc2C(F)(F)F)cc1
*c1ccc(Oc2ccc(-c3cccc(-c4ccc(Oc5ccc(-c6c7c(c(*)c8ccccc68)C(=O)N(c6ccccc6)C7=O)cc5)c(C(F)(F)F)c4)n3)cc2C(F)(F)F)cc1
*c1ccc(Oc2ccc(-c3cccc4c3C(=O)N(Oc3ccc(C(=O)c5ccc(Oc6cccc7c6C(=O)N(*)C7=O)cc5)cc3)C4=O)cc2)cc1
*c1ccc(Oc2ccc(-c3cnc4cc(-c5ccc6nc(*)cnc6c5)ccc4n3)cc2)cc1
*c1ccc(Oc2ccc(-c3cnc4cc(Oc5ccc6nc(*)cnc6c5)ccc4n3)cc2)cc1
*c1ccc(Oc2ccc(-c3cnc4cc5nc(*)cnc5cc4n3)cc2)cc1
*c1ccc(Oc2ccc(-c3cnc4cc5ncc(*)nc5cc4n3)cc2)cc1
*c1ccc(Oc2ccc(-c3cnc4ccc(-c5ccc6nc(*)cnc6c5)cc4n3)cc2)cc1
*c1ccc(Oc2ccc(-c3cnc4ccc(-c5ccc6ncc(*)nc6c5)cc4n3)cc2)cc1
*c1ccc(Oc2ccc(-c3cnc4ccc(Oc5ccc6nc(*)cnc6c5)cc4n3)cc2)cc1
*c1ccc(Oc2ccc(-c3cnnc(-c4cccc(-c5ncc(*)nn5)n4)n3)cc2)cc1
*c1ccc(Oc2ccc(-c3csc(/N=C/c4ccc(OCCCCCCCCOc5ccc(/C=N/c6nc(*)cs6)cc5)cc4)n3)cc2)cc1
*c1ccc(Oc2ccc(-c3csc(/N=C/c4ccc(OCCCCCCOc5ccc(/C=N/c6nc(*)cs6)cc5)cc4)n3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc(-c4cccc(-c5nnc(-c6ccccc6)c(*)n5)n4)nnc3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4cc(-c5ccc6nc(*)c(-c7ccccc7)nc6c5)ccc4nc3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4cc(-c5ccc6nc(-c7ccccc7)c(*)nc6c5)ccc4nc3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4cc(Oc5ccc(NC(=O)CN6C(=O)c7ccc(C(c8ccc9c(c8)C(=O)N(CC(=O)Nc8ccc(Oc%10ccc%11nc(-c%12ccccc%12)c(*)nc%11c%10)cc8)C9=O)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)ccc4nc3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4cc(Oc5ccc(NC(=O)c6cc(C(=O)Nc7ccc(Oc8ccc9nc(-c%10ccc([N+](=O)[O-])cc%10)c(*)nc9c8)cc7)cc(N7C(=O)c8ccccc8C7=O)c6)cc5)ccc4nc3-c3ccc([N+](=O)[O-])cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4cc(Oc5ccc(NC(=O)c6cc(C(=O)Nc7ccc(Oc8ccc9nc(-c%10ccccc%10)c(*)nc9c8)cc7)cc(N7C(=O)c8c(Cl)c(Cl)c(Cl)c(Cl)c8C7=O)c6)cc5)ccc4nc3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4cc(Oc5ccc(NC(=O)c6cc(C(=O)Nc7ccc(Oc8ccc9nc(-c%10ccccc%10)c(*)nc9c8)cc7)cc(N7C(=O)c8ccccc8C7=O)c6)cc5)ccc4nc3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4cc(Oc5ccc(NC(=O)c6ccc7c(c6)C(=O)N(c6ccc(C(c8ccc(N9C(=O)c%10ccc(C(=O)Nc%11ccc(Oc%12ccc%13nc(-c%14ccc([N+](=O)[O-])cc%14)c(*)nc%13c%12)cc%11)cc%10C9=O)cc8)(C(F)(F)F)C(F)(F)F)cc6)C7=O)cc5)ccc4nc3-c3ccc([N+](=O)[O-])cc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4cc(Oc5ccc(NC(=O)c6ccc7c(c6)C(=O)N(c6ccc(C(c8ccc(N9C(=O)c%10ccc(C(=O)Nc%11ccc(Oc%12ccc%13nc(-c%14ccccc%14)c(*)nc%13c%12)cc%11)cc%10C9=O)cc8)(C(F)(F)F)C(F)(F)F)cc6)C7=O)cc5)ccc4nc3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4cc(Oc5ccc6nc(*)c(-c7ccccc7)nc6c5)ccc4nc3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4cc(Oc5ccc6nc(-c7ccccc7)c(*)nc6c5)ccc4nc3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4cc(S(=O)(=O)c5ccc6nc(-c7ccccc7)c(*)nc6c5)ccc4nc3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4cc5sc(*)nc5cc4s3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4ccc(-c5ccc6nc(*)c(-c7ccccc7)c(-c7ccccc7)c6c5)cc4c(-c4ccccc4)c3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4ccc(-c5ccc6nc(*)c(-c7ccccc7)nc6c5)cc4nc3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4ccc(Oc5ccc(N6C(=O)c7ccc(C(c8ccc9c(c8)C(=O)N(c8ccc(Oc%10ccc%11nc(*)c(-c%12ccccc%12)nc%11c%10)cc8)C9=O)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)cc4nc3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4ccc(Oc5ccc6nc(*)c(-c7ccccc7)c(-c7ccccc7)c6c5)cc4c(-c4ccccc4)c3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nc4ccc(Oc5ccc6nc(*)c(-c7ccccc7)nc6c5)cc4nc3-c3ccccc3)cc2)cc1
*c1ccc(Oc2ccc(-c3nnc(*)o3)cc2)cc1
*c1ccc(Oc2ccc(-n3c(=O)c4c(Oc5ccc(OC)cc5)c5c(=O)n(*)c(=O)c5c(Oc5ccc(OC)cc5)c4c3=O)cc2)cc1
*c1ccc(Oc2ccc(-n3c(=O)c4c(Oc5ccc(OCCCC)cc5)c5c(=O)n(*)c(=O)c5c(Oc5ccc(OCCCC)cc5)c4c3=O)cc2)cc1
*c1ccc(Oc2ccc(-n3c(=O)c4c(Oc5ccc(OCCCCCCCC)cc5)c5c(=O)n(*)c(=O)c5c(Oc5ccc(OCCCCCCCC)cc5)c4c3=O)cc2)cc1
*c1ccc(Oc2ccc(-n3c(=O)c4c(Oc5ccc(OCCCCCCCCCCCC)cc5)c5c(=O)n(*)c(=O)c5c(Oc5ccc(OCCCCCCCCCCCC)cc5)c4c3=O)cc2)cc1
*c1ccc(Oc2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2)cc1
*c1ccc(Oc2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2-c2ccccc2)cc1
*c1ccc(Oc2ccc(-n3c(=O)c4cc5c(=O)n(-c6ccc(Oc7ccc(-c8nc9cc%10sc(*)nc%10cc9s8)cc7)cc6)c(=O)c5cc4c3=O)cc2)cc1
*c1ccc(Oc2ccc(-n3c(=O)c4cc5c(=O)n(-c6ncc(*)s6)c(=O)c5cc4c3=O)cc2)cc1
*c1ccc(Oc2ccc(-n3nc(-c4ccccc4)cc3-c3ccc(-c4cc(-c5ccccc5)nn4*)cc3)cc2)cc1
*c1ccc(Oc2ccc(-n3nc(-c4ccccc4)cc3-c3cccc(-c4cc(-c5ccccc5)nn4*)c3)cc2)cc1
*c1ccc(Oc2ccc(-n3nccc3-c3ccc(-c4ccnn4*)cc3)cc2)cc1
*c1ccc(Oc2ccc(-n3nccc3-c3cccc(-c4ccnn4*)c3)cc2)cc1
*c1ccc(Oc2ccc(/C=C/c3ccc(Oc4ccc(-c5nnc(*)o5)cc4)c4ccccc34)cc2)cc1
*c1ccc(Oc2ccc(/C=C/c3ccc(Oc4ccc(-c5nnc(*)o5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(-c4ccc(C(=O)c5ccc(Oc6ccc(-c7nc(-c8ccc(-c9nc(*)c(-c%10ccccc%10)[nH]9)cc8)[nH]c7-c7ccccc7)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(-c4ccc(C(=O)c5ccc(Oc6ccc(-c7nc(-c8cccc(-c9nc(*)c(-c%10ccccc%10)[nH]9)c8)[nH]c7-c7ccccc7)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(-c4ccc(C(=O)c5ccc(Oc6ccc(-c7nc(-c8ccccc8)[nH]c7*)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(C(=O)c4ccc(C(=O)c5ccc(Oc6ccc(-c7nc(-c8ccccc8)[nH]c7*)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(C(=O)c4ccc(Oc5ccc(-c6cc(*)[nH]n6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(C(=O)c4ccc(Oc5ccc(-c6cc(*)n(-c7ccccc7)n6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(C(=O)c4ccc(Oc5ccc(-c6nc(-c7ccc(-c8nc(*)c(-c9ccccc9)[nH]8)cc7)[nH]c6-c6ccccc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(C(=O)c4ccc(Oc5ccc(-c6nc(-c7cccc(-c8nc(*)c(-c9ccccc9)[nH]8)c7)[nH]c6-c6ccccc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(C(=O)c4ccc(Oc5ccc(-c6nc(-c7ccccc7)[nH]c6*)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(C(=O)c4ccc(Oc5ccc(-c6nc7ccccc7n6-c6ccc(-n7c(*)nc8ccccc87)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(C(=O)c4ccc(Oc5ccc(N6C(=O)c7ccc(-c8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Cc4ccc(C(=O)c5ccc(Oc6ccc(-c7nc(-c8ccccc8)[nH]c7*)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(C)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(CCCCCCCCCCCC)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(c5ccc(C(F)(F)F)cc5)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(c5ccc(Cl)cc5)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(c5ccc(F)cc5)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(c5cccc(C(F)(F)F)c5)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(c5ccccc5)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(c5ccccc5-c5ccccc5)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(c5ccccc5C(F)(F)F)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(c5ccccc5F)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5cc(*)[nH]n5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5cc(*)n(-c6ccccc6)n5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5nc(-c6ccc(-c7nc(*)c(-c8ccccc8)[nH]7)cc6)[nH]c5-c5ccccc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5nc(-c6cccc(-c7nc(*)c(-c8ccccc8)[nH]7)c6)[nH]c5-c5ccccc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5nc(-c6ccccc6)[nH]c5*)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5nc(-c6ccccn6)nnc5*)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5nc6ccccc6n5-c5ccc(-n6c(*)nc7ccccc76)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5nnc(*)c6c(-c7ccc(F)cc7)c(-c7ccc(F)cc7)c(-c7ccc(F)cc7)c(-c7ccc(F)cc7)c56)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(-c5nnc(*)c6c(-c7ccccc7)c(-c7ccc8ccccc8c7)c(-c7ccc8ccccc8c7)c(-c7ccccc7)c56)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(C(=O)c5ccc(Oc6ccc(-c7cc(*)n(-c8ccccc8)n7)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(C(=O)c5ccc(Oc6ccc(-c7nc(-c8ccc(-c9nc(*)c(-c%10ccccc%10)[nH]9)cc8)[nH]c7-c7ccccc7)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(C(=O)c5ccc(Oc6ccc(-c7nc(-c8ccccc8)[nH]c7*)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(C(=O)c5ccc(Oc6ccc(-c7nc8ccccc8n7-c7ccc(-n8c(*)nc9ccccc98)cc7)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(C5(*)OC(=O)c6ccccc65)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(C5(*)c6ccccc6C(=O)N5C)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(N5C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7cccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)c7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc(Oc4ccc(N5C(=O)c6ccc(S(=O)(=O)c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc4cc(C(=O)c5ccc(Oc6ccc(-c7nc(-c8ccc(-c9nc(*)c(-c%10ccccc%10)[nH]9)cc8)[nH]c7-c7ccccc7)cc6)cc5)ccc4c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3ccc4cc(C(=O)c5ccc(Oc6ccc(-c7nc(-c8ccccc8)[nH]c7*)cc6)cc5)ccc4c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(-c4cccc(C(=O)c5ccc(Oc6ccc(-c7nc(-c8cccc(-c9nc(*)c(-c%10ccccc%10)[nH]9)c8)[nH]c7-c7ccccc7)cc6)cc5)c4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(-c6c7c(c(*)c8ccccc68)C(=O)N(C)C7=O)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(-c6c7c(c(*)c8ccccc68)C(=O)N(CCCCCCCCCCCC)C7=O)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(-c6c7c(c(*)c8ccccc68)C(=O)N(c6ccc(F)cc6)C7=O)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(-c6cc(*)[nH]n6)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(-c6cc(*)n(-c7ccccc7)n6)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(-c6nc(-c7ccc(-c8nc(*)c(-c9ccccc9)[nH]8)cc7)[nH]c6-c6ccccc6)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(-c6nc(-c7cccc(-c8nc(*)c(-c9ccccc9)[nH]8)c7)[nH]c6-c6ccccc6)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(-c6nc(-c7ccccc7)[nH]c6*)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(-c6nc(-c7ccccn7)nnc6*)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(-c6nc7ccccc7n6-c6ccc(-n7c(*)nc8ccccc87)cc6)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(-n6c(=O)c7cc8c(=O)n(*)c(=O)c8cc7c6=O)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(-n6c(=O)c7cc8c(=O)n(*)c(=O)c8cc7c6=O)cc5)cc4)n3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(-n6c(=O)c7cc8c(=O)n(*)c(=O)c8cc7c6=O)cc5C(F)(F)F)cc4)n3)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(N6C(=O)c7ccc(-c8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccc(Oc9ccc(Oc%10ccc%11c(c%10)C(=O)N(*)C%11=O)cc9)cc8)cc7C6=O)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccc(Sc9ccc(Oc%10ccc%11c(c%10)C(=O)N(*)C%11=O)cc9)cc8)cc7C6=O)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)cc5)cc4)n3)cc2)cc1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)cc5C(F)(F)F)cc4)n3)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(C(=O)c3cccc(C(=O)c4ccc(Oc5ccc(N6C(=O)c7ccc([Si](C)(C)c8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(-c5ccc(*)s5)cc4C(F)(F)F)cc3)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(-c5cccc(*)n5)cc4C(F)(F)F)cc3)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(-c5nc(*)nc(-c6ccccc6)n5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(-c5nc(-c6ccccn6)nnc5*)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(-c5ncc(*)o5)cc4C(F)(F)F)cc3)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(-c5nnc(*)c6c(-c7ccc(F)cc7)c(-c7ccc(F)cc7)c(-c7ccc(F)cc7)c(-c7ccc(F)cc7)c56)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(-c5nnc(*)c6c(-c7ccccc7)c(-c7ccc8ccccc8c7)c(-c7ccc8ccccc8c7)c(-c7ccccc7)c56)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(-c5nnc(*)n5-c5ccc(C)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(-c5nnc(*)n5-c5ccc(S(=O)(=O)c6ccccc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(-c5nnc(*)n5-c5ccc(S(C)(=O)=O)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(-c5nnc(*)n5-c5cccc(C(F)(F)F)c5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(-c5nnc(*)n5-c5ccccc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(-c5nnc(*)o5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(N5C(=O)C6CCC(C7CCC8C(=O)N(*)C(=O)C8C7)CC6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(N5C(=O)C6CCC7C(=O)N(*)C(=O)C7C6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(N5C(=O)CC(=O)N(*)C5=S)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(N5C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(C(C)(C)c8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccccc7Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4C(F)(F)F)cc3)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(C(C)(C)c3ccc(Oc4ccc(S(*)(=O)=O)cc4)cc3)cc2)s1
*c1ccc(Oc2ccc(C(C)(C)c3cccc(C(C)(C)c4ccc(Oc5ccc(-n6c(=O)c7cc8c(=O)n(*)c(=O)c8cc7c6=O)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(C(C)(C)c3ccccc3)cc2)c(-n2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)c1
*c1ccc(Oc2ccc(C(C)(C)c3ccccc3)cc2)c(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1ccc(Oc2ccc(C(C)(c3ccccc3)c3ccc(Oc4ccc(-c5nc(-c6ccccn6)nnc5*)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-c5ccc(*)s5)cc4C(F)(F)F)cc3)(C(F)(F)F)C(F)(F)F)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-c5cccc(*)n5)cc4C(F)(F)F)cc3)(C(F)(F)F)C(F)(F)F)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-c5nc(*)nc(-c6ccccc6)n5)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-c5nc(-c6ccccn6)nnc5*)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-c5ncc(*)o5)cc4C(F)(F)F)cc3)(C(F)(F)F)C(F)(F)F)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-c5nnc(*)c6c(-c7ccc(F)cc7)c(-c7ccc(F)cc7)c(-c7ccc(F)cc7)c(-c7ccc(F)cc7)c56)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-c5nnc(*)c6c(-c7ccccc7)c(-c7ccc8ccccc8c7)c(-c7ccc8ccccc8c7)c(-c7ccccc7)c56)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-c5nnc(*)n5-c5ccc(C)cc5)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-c5nnc(*)n5-c5ccc(S(=O)(=O)c6ccccc6)cc5)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-c5nnc(*)n5-c5ccc(S(C)(=O)=O)cc5)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-c5nnc(*)n5-c5cccc(C(F)(F)F)c5)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-c5nnc(*)n5-c5ccccc5)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-n5c(=O)c6cc7c(=O)n(*)c(=O)c7cc6c5=O)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-n5c(=O)c6cc7c(=O)n(*)c(=O)c7cc6c5=O)cc4C(F)(F)F)cc3)(C(F)(F)F)C(F)(F)F)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-n5c(=O)c6ccc7c(=O)n(*)c(=O)c8ccc(c5=O)c6c78)cc4)c(S(=O)(=O)O)c3)(C(F)(F)F)C(F)(F)F)cc2S(=O)(=O)O)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-n5c(=O)c6ccc7c(=O)n(*)c(=O)c8ccc(c5=O)c6c78)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(N5C(=O)CC(=O)N(*)C5=S)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(N5C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(N5C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4C(F)(F)F)cc3)(C(F)(F)F)C(F)(F)F)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7c(C)cc(C(C)(C)c8cc(C)c(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)c(C)c8)cc7C)cc6C5=O)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(C8(c9ccc(Oc%10ccc%11c(c%10)C(=O)N(*)C%11=O)cc9)CCC(C(C)(C)C)CC8)cc7)cc6C5=O)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(Sc8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)cc7)cc6C5=O)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)(C(F)(F)F)C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccccc7Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4C(F)(F)F)cc3)(C(F)(F)F)C(F)(F)F)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(C(c3ccccc3)(c3ccccc3)c3ccc(Oc4ccc(-c5nc(*)nc(-c6ccccc6)n5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(-c6ccc(*)s6)cc5C(F)(F)F)cc4)c4ccccc4-c4ccccc43)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(-c6cccc(*)n6)cc5C(F)(F)F)cc4)c4ccccc4-c4ccccc43)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(-c6nc(-c7ccccn7)nnc6*)cc5)cc4)c4ccccc4-c4ccccc43)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(-c6nnc(*)c7c(-c8ccc(F)cc8)c(-c8ccc(F)cc8)c(-c8ccc(F)cc8)c(-c8ccc(F)cc8)c67)cc5)cc4)c4ccccc4-c4ccccc43)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(-c6nnc(*)c7c(-c8ccccc8)c(-c8ccc9ccccc9c8)c(-c8ccc9ccccc9c8)c(-c8ccccc8)c67)cc5)cc4)c4ccccc4-c4ccccc43)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(N6C(=O)CC(=O)N(*)C6=S)cc5)cc4)c4ccccc4-c4ccccc43)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8c(C)cc(C(C)(C)c9cc(C)c(Oc%10ccc%11c(c%10)C(=O)N(*)C%11=O)c(C)c9)cc8C)cc7C6=O)cc5)cc4)C4CC5CC(C4)CC3C5)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8c(C)cc(C(C)(C)c9cc(C)c(Oc%10ccc%11c(c%10)C(=O)N(*)C%11=O)c(C)c9)cc8C)cc7C6=O)cc5)cc4)CC4CC3C3CCCC43)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8c(C)cc(C(C)(C)c9cc(C)c(Oc%10ccc%11c(c%10)C(=O)N(*)C%11=O)c(C)c9)cc8C)cc7C6=O)cc5)cc4)CC4CCC3C4)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccc(C9(c%10ccc(Oc%11ccc%12c(c%11)C(=O)N(*)C%12=O)cc%10)CCC(C(C)(C)C)CC9)cc8)cc7C6=O)cc5)cc4)C4CC5CC(C4)CC3C5)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccc(C9(c%10ccc(Oc%11ccc%12c(c%11)C(=O)N(*)C%12=O)cc%10)CCC(C(C)(C)C)CC9)cc8)cc7C6=O)cc5)cc4)CC4CC3C3CCCC43)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccc(C9(c%10ccc(Oc%11ccc%12c(c%11)C(=O)N(*)C%12=O)cc%10)CCC(c%10ccccc%10)CC9)cc8)cc7C6=O)cc5)cc4)CC4CC3C3CCCC43)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccc(C9(c%10ccc(Oc%11ccc%12c(c%11)C(=O)N(*)C%12=O)cc%10)CCC(c%10ccccc%10)CC9)cc8)cc7C6=O)cc5)cc4)CC4CCC3C4)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccc(C9(c%10ccc(Oc%11ccc%12c(c%11)C(=O)N(*)C%12=O)cc%10)CCC(c%10ccccc%10)CC9)cc8)cc7C6=O)cc5)cc4)CCC(C(C)(C)C)CC3)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccc(C9(c%10ccc(Oc%11ccc%12c(c%11)C(=O)N(*)C%12=O)cc%10)CCCCCCCCCCC9)cc8)cc7C6=O)cc5)cc4)C4CC5CC(C4)CC3C5)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccc(C9(c%10ccc(Oc%11ccc%12c(c%11)C(=O)N(*)C%12=O)cc%10)CCCCCCCCCCC9)cc8)cc7C6=O)cc5)cc4)CC4CCC3C4)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccc(C9(c%10ccc(Oc%11ccc%12c(c%11)C(=O)N(*)C%12=O)cc%10)CCCCCCCCCCC9)cc8)cc7C6=O)cc5)cc4)CCC(C(C)(C)C)CC3)cc2)cc1
*c1ccc(Oc2ccc(C3(c4ccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccccc8Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)cc5C(F)(F)F)cc4)c4ccccc4-c4ccccc43)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(N3C(=O)C(C)C(SCCOCCSC4C(=O)N(c5ccc(Oc6ccc(N7C(=O)CC(C)(SCCOCCSC8(C)CC(=O)N(*)C8=O)C7=O)cc6)cc5)C(=O)C4C)C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)C(Cl)=C(Oc4ccc(C(C)(C)c5ccc(OC6=C(Cl)C(=O)N(*)C6=O)cc5)cc4)C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)C(Cl)=C(Oc4ccc(C(c5ccc(OC6=C(Cl)C(=O)N(*)C6=O)cc5)(C(F)(F)F)C(F)(F)F)cc4)C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)C(c4ccccc4)=C(c4ccc(C5=C(c6ccccc6)C(=O)N(*)C5=O)cc4)C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)C4CCC(C5CCC6C(=O)N(*)C(=O)C6C5)CC4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)C4CCC5C(=O)N(*)C(=O)C5C4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)CC(C4C=C(C)C5C(=O)N(*)C(=O)C5C4)C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)CC4C(CC5C(=O)N(*)C(=O)C54)C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)N4C(c5ccc(OC)cc5)N(*)C(=O)N4C3c3ccc(OC)cc3)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4c(c(-c5ccccc5)c(-c5ccc(-c6c(-c7ccccc7)c7c(c(-c8ccccc8)c6-c6ccccc6)C(=O)N(*)C7=O)cc5)c(-c5ccccc5)c4-c4ccccc4)C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4c(c(-c5ccccc5)c(-c5ccc(Oc6ccc(-c7c(-c8ccccc8)c8c(c(-c9ccccc9)c7-c7ccccc7)C(=O)N(*)C8=O)cc6)cc5)c(-c5ccccc5)c4-c4ccccc4)C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4cc(-c5ccc(C(C)(C)C)cc5)c(-c5cc6c(cc5-c5ccc(C(C)(C)C)cc5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4cc(-c5ccc([Si](C)(C)C)cc5)c(-c5cc6c(cc5-c5ccc([Si](C)(C)C)cc5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2Br)c(Br)c1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(C(=O)c5ccc6c(c5)C(=O)N(c5ncc(*)s5)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(C(c5ccc6c(c5)C(=O)N(c5ncc(*)s5)C6=O)(C(F)(F)F)C(F)(F)F)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5c(C)cc(-c6cc(C)c(Oc7ccc8c(c7)C(=O)N(*)C8=O)c(C)c6)cc5C)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5c(C)cc(Cc6cc(C)c(Oc7ccc8c(c7)C(=O)N(*)C8=O)c(C)c6)cc5C)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5c(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc(C(C)(C)C)cc5C(C)(C)C)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5cc6ccccc6cc5Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccc(-c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccc(C(=O)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccc(C(C)(C)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccc(C(c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)(C(F)(F)F)C(F)(F)F)cc5)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccc(C6(c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)CCC(c7ccccc7)CC6)cc5)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccc(C6(c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)CCCCCCCCCCC6)cc5)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccc(S(=O)(=O)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccc(Sc6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2Br)c(Br)c1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(c5ncc(*)s5)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccc6cc(Oc7ccc8c(c7)C(=O)N(*)C8=O)ccc6c5)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccc6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6c5)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5cccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)c5)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5cccc6c(Oc7ccc8c(c7)C(=O)N(*)C8=O)cccc56)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5cccc6c5C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Oc5ccccc5Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(P(=O)(c5ccccc5)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(S(=O)(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(S(=O)(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2Br)c(Br)c1
*c1ccc(Oc2ccc(N3C(=O)c4ccc(Sc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc([Si](C)(C)O[Si](C)(C)c5ccc6c(c5)C(=O)N(c5ccc(Oc7ccc(-c8nnc(*)o8)cc7)cc5)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4ccc([Si](C)(C)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5c(C)cc(-c6cc(C)c(Oc7cccc8c7C(=O)N(*)C8=O)c(C)c6)cc5C)c4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5c(C)cc(Cc6cc(C)c(Oc7cccc8c7C(=O)N(*)C8=O)c(C)c6)cc5C)c4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5c(Oc6cccc7c6C(=O)N(*)C7=O)cc(C(C)(C)C)cc5C(C)(C)C)c4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5ccc(-c6ccc(Oc7cccc8c7C(=O)N(*)C8=O)cc6)cc5)c4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5ccc(C(C)(C)c6ccc(Oc7cccc8c7C(=O)N(*)C8=O)cc6)cc5)c4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5ccc(Oc6ccc(Oc7cccc8c7C(=O)N(*)C8=O)cc6)cc5)c4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5ccc(Oc6cccc7c6C(=O)N(*)C7=O)cc5)c4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5ccc(S(=O)(=O)c6ccc(Oc7cccc8c7C(=O)N(*)C8=O)cc6)cc5)c4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5ccc(Sc6ccc(Oc7cccc8c7C(=O)N(*)C8=O)cc6)cc5)c4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5cccc(Oc6cccc7c6C(=O)N(*)C7=O)c5)c4C3=O)cc2)cc1
*c1ccc(Oc2ccc(N3C(=O)c4cccc(Oc5cccc6c5C(=O)N(*)C6=O)c4C3=O)cc2)cc1
*c1ccc(Oc2ccc(NC(=O)CN3C(=O)c4ccc(C(c5ccc6c(c5)C(=O)N(CC(=O)Nc5ccc(Oc7ccc(-c8nnc(*)o8)cc7)cc5)C6=O)(C(F)(F)F)C(F)(F)F)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccc(NC(=O)c3cc(C(=O)Nc4ccc(Oc5ccc(-c6nnc(*)o6)cc5)cc4)cc(N4C(=O)c5ccccc5C4=O)c3)cc2)cc1
*c1ccc(Oc2ccc(NC(=O)c3cc(NC(=O)c4ccc(OC(C)=O)cc4)cc(C(=O)Nc4ccc(Oc5ccc(-c6nnc(*)o6)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(NC(=O)c3ccc(C(=O)Nc4ccc(Oc5ccc(-c6nnc(*)o6)cc5)cc4)c(Oc4ccccc4)c3)cc2)cc1
*c1ccc(Oc2ccc(NC(=O)c3ccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(NC(=O)c3ccc(Oc4cccc5c4C(=O)N(*)C5=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Cc5ccc(N6C(=O)c7ccc(C(=O)Nc8ccc(Oc9ccc(C%10(*)NC(=O)c%11ccccc%11%10)cc9)cc8)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*c1ccc(Oc2ccc(NC(=O)c3ccc4c(c3)C(=O)N(c3ccc(Sc5ccc(N6C(=O)c7ccc(C(=O)Nc8ccc(Oc9ccc(C%10(*)NC(=O)c%11ccccc%11%10)cc9)cc8)cc7C6=O)cc5)cc3)C4=O)cc2)cc1
*c1ccc(Oc2ccc(NC(=O)c3cccc(C(=O)Nc4ccc(Oc5ccc(-n6c(=O)c7cc8c(=O)n(*)c(=O)c8cc7c6=O)cc5)cc4)c3)cc2)cc1
*c1ccc(Oc2ccc(NC(=O)c3cccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)c3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(-c4ccc(Oc5ccc(Oc6ccc(C7(*)OC(=O)c8ccccc87)cc6)cc5)c5ccccc45)c4ccccc34)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(-c4cnc5cc(-c6ccc7nc(*)cnc7c6)ccc5n4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(-c4cnc5ccc(-c6ccc7nc(*)cnc7c6)cc5n4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(-c4cnc5ccc(-c6ccc7ncc(*)nc7c6)cc5n4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(-c4nc(*)nc(-c5ccccc5)n4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(-c4nnc(*)c5c(-c6ccc(F)cc6)c(-c6ccc(F)cc6)c(-c6ccc(F)cc6)c(-c6ccc(F)cc6)c45)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)cc3)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(Oc3ccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)cc3)cc2-c2ccccc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)cc3)cc2C(C)(C)C)cc1
*c1ccc(Oc2ccc(Oc3ccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)cc3)cc2C)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)C(c5ccccc5)=C(c5ccc(C6=C(c7ccccc7)C(=O)N(*)C6=O)cc5)C4=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)C5CCC(C6CCC7C(=O)N(*)C(=O)C7C6)CC5C4=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)C5CCC6C(=O)N(*)C(=O)C6C5C4=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)CC(=O)N(*)C4=S)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(-c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)c(-c3ccccc3)c2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(-c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)c(C(C)(C)C)c2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(-c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)c(C)c2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(-c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(-c6cccc7c6C(=O)N(*)C7=O)cc5C4=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6c(C)cc(C(C)(C)c7cc(C)c(Oc8ccc9c(c8)C(=O)N(*)C9=O)c(C)c7)cc6C)cc5C4=O)cc3)c(C(C)(C)C)c2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6cc7ccccc7cc6Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(-c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(C(c7ccccc7)(c7ccccc7)c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)c(C(C)(C)C)c2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(C7(c8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)CCC(C(C)(C)C)CC7)cc6)cc5C4=O)cc3)c(C(C)(C)C)c2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(C7(c8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)CCC(c8ccccc8)CC7)cc6)cc5C4=O)cc3)c(C(C)(C)C)c2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(C7(c8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)CCCCCCCCCCC7)cc6)cc5C4=O)cc3)c(C(C)(C)C)c2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(Oc7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(Sc7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3C(F)(F)F)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7c6)cc5C4=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6cccc7c6C(=O)N(*)C7=O)cc5C4=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccccc6Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccccc6Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3C(F)(F)F)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5cccc(Oc6c(C)cc(Cc7cc(C)c(Oc8cccc9c8C(=O)N(*)C9=O)c(C)c7)cc6C)c5C4=O)c(C)c3)cc2)cc1C
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5cccc(Oc6ccc(C(C)(C)c7ccc(Oc8cccc9c8C(=O)N(*)C9=O)cc7)cc6)c5C4=O)c(C)c3)cc2)cc1C
*c1ccc(Oc2ccc(Oc3ccc(N4C(=O)c5cccc(Oc6cccc7c6C(=O)N(*)C7=O)c5C4=O)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(NC(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(NC(=O)c4ccc(Oc5cccc6c5C(=O)N(*)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(NC(=O)c4cccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)c4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(Oc4ccc(-c5cnc6cc(-c7ccc8nc(*)cnc8c7)ccc6n5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(Oc4ccc(-c5cnc6ccc(-c7ccc8nc(*)cnc8c7)cc6n5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(Oc4ccc(-c5cnc6ccc(-c7ccc8ncc(*)nc8c7)cc6n5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(Oc4ccc(-c5nc(*)nc(-c6ccccc6)n5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(Oc4ccc(-c5nc6ccc(-c7ccc8nc(*)c(-c9ccccc9)nc8c7)cc6nc5-c5ccccc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(Oc4ccc(-n5c(=O)c6cc7c(=O)n(*)c(=O)c7cc6c5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(Oc4ccc(N5C(=O)C6CCC(C7CCC8C(=O)N(*)C(=O)C8C7)CC6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(Oc4ccc(N5C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(-c8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(P(=O)(c4ccccc4)c4ccc(Oc5ccc(Oc6ccc(-c7nc8cc9sc(*)nc9cc8s7)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(P(=O)(c4ccccc4)c4ccc(Oc5ccc(Oc6ccc(-c7nc8ccc(-c9ccc%10nc(*)oc%10c9)cc8o7)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Oc3ccc(P(=O)(c4ccccc4)c4ccc(Oc5ccc(Oc6ccc(-c7nc8ccccc8nc7*)cc6)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(P(=O)(c3ccccc3)c3ccc(Oc4ccc(-c5cc(*)[nH]n5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(P(=O)(c3ccccc3)c3ccc(Oc4ccc(-c5cc(*)n(-c6ccccc6)n5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(P(=O)(c3ccccc3)c3ccc(Oc4ccc(-c5ccc(*)nc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(P(=O)(c3ccccc3)c3ccc(Oc4ccc(-c5nc(-c6ccccc6)[nH]c5*)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(P(C)(=O)c3ccc(Oc4ccc(C5(*)OC(=O)c6ccccc65)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(C)cc3)cc2)c(-n2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)c1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(C)cc3)cc2)c(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(C)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(CCCCCCCCCCCC)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(c5ccc(C(F)(F)F)cc5)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(c5ccc(Cl)cc5)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(c5ccc(F)cc5)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(c5cccc(C(F)(F)F)c5)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(c5ccccc5)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(c5ccccc5-c5ccccc5)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5c6c(c(*)c7ccccc57)C(=O)N(c5ccccc5C(F)(F)F)C6=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5cc(*)[nH]n5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5cc(*)n(-c6ccccc6)n5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5nc(-c6ccc(-c7nc(*)c(-c8ccccc8)[nH]7)cc6)[nH]c5-c5ccccc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5nc(-c6cccc(-c7nc(*)c(-c8ccccc8)[nH]7)c6)[nH]c5-c5ccccc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5nc(-c6ccccc6)[nH]c5*)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5nc(-c6ccccn6)nnc5*)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5nc6ccccc6n5-c5ccc(-n6c(*)nc7ccccc76)cc5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(-c5ncc(*)o5)cc4C(F)(F)F)cc3)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(C5(*)OC(=O)c6ccccc65)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(C5(*)OC(=O)c6ccccc65)cc4C)cc3)cc2)c(C)c1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(N5C(=O)C6CCC(C7CCC8C(=O)N(*)C(=O)C8C7)CC6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(C(C)(C)c8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7cccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)c7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7cccc8c(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cccc78)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(N5C(=O)c6ccc(S(=O)(=O)c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4ccc(N5C(=O)c6ccc([Si](C)(C)c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Sc3ccc(Oc4ccc(-c5ccc(*)s5)cc4C(F)(F)F)cc3)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(Sc3ccc(Oc4ccc(-c5cccc(*)n5)cc4C(F)(F)F)cc3)cc2)c(C(F)(F)F)c1
*c1ccc(Oc2ccc(Sc3ccc(Oc4ccc(-c5nc(*)nc(-c6ccccc6)n5)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Sc3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(-c8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Sc3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(Oc8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Sc3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc(Sc3ccc(Oc4ccc(N5C(=O)c6ccc(Oc7ccc(Sc8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Oc2ccc3c(c2)C(=O)N(*)C3=O)cc1
*c1ccc(Oc2ccc3ccc(Oc4ccc(-c5nc(*)nc(-c6ccccc6)n5)cc4)cc3c2)cc1
*c1ccc(Oc2ccc3ccc(Oc4ccc(-n5c(=O)c6cc7c(=O)n(*)c(=O)c7cc6c5=O)cc4)cc3c2)cc1
*c1ccc(Oc2ccc3ccc(Oc4ccc(N5C(=O)CC(=O)N(*)C5=S)cc4)cc3c2)cc1
*c1ccc(Oc2ccc3ccccc3c2-c2c(Oc3ccc(N4C(=O)c5ccc(NC(=O)Nc6ccc(-c7cc(-c8ccc(-c9ccccc9)cc8)cc(-c8ccc(NC(=O)Nc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)n7)cc6)cc5C4=O)cc3)ccc3ccccc23)cc1
*c1ccc(Oc2ccc3ccccc3c2-c2c(Oc3ccc(N4C(=O)c5ccc(NC(=O)Nc6ccc(Oc7ccc(NC(=O)Nc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)ccc3ccccc23)cc1
*c1ccc(Oc2ccc3ccccc3c2-c2c(Oc3ccc(N4C(=O)c5ccc(NC(=O)Nc6ccc(Oc7ccccc7-c7ccccc7Oc7ccc(NC(=O)Nc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)ccc3ccccc23)cc1
*c1ccc(Oc2ccc3ccccc3c2-c2c(Oc3ccc(N4C(=O)c5ccc(NC(=O)Nc6cccc(NC(=O)Nc7ccc8c(c7)C(=O)N(*)C8=O)n6)cc5C4=O)cc3)ccc3ccccc23)cc1
*c1ccc(Oc2ccc3ccccc3c2-c2c(Oc3ccc(N4C(=O)c5ccc(NC(=O)Nc6cccc7c(NC(=O)Nc8ccc9c(c8)C(=O)N(*)C9=O)cccc67)cc5C4=O)cc3)ccc3ccccc23)cc1
*c1ccc(Oc2cccc(-c3cc(-c4ccccc4)cc(-c4cccc(Oc5ccc(-n6c(=O)c7cc8c(=O)n(*)c(=O)c8cc7c6=O)cc5)c4)n3)c2)cc1
*c1ccc(Oc2cccc(-c3cc(-c4ccccc4)cc(-c4cccc(Oc5ccc(N6C(=O)c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)cc5)c4)n3)c2)cc1
*c1ccc(Oc2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c2)cc1
*c1ccc(Oc2cccc(-n3c(=O)c4cc5c(=O)n(-c6cccc(Oc7ccc(-c8nc9cc%10sc(*)nc%10cc9s8)cc7)c6)c(=O)c5cc4c3=O)c2)cc1
*c1ccc(Oc2cccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)cc1
*c1ccc(Oc2cccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)c2)cc1
*c1ccc(Oc2cccc(N3C(=O)c4ccc(Oc5cc6ccccc6cc5Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)cc1
*c1ccc(Oc2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)cc1
*c1ccc(Oc2cccc(N3C(=O)c4ccc(Oc5ccc6cc(Oc7ccc8c(c7)C(=O)N(*)C8=O)ccc6c5)cc4C3=O)c2)cc1
*c1ccc(Oc2cccc(N3C(=O)c4ccc(Oc5ccc6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6c5)cc4C3=O)c2)cc1
*c1ccc(Oc2cccc(N3C(=O)c4ccc(Oc5cccc6c(Oc7ccc8c(c7)C(=O)N(*)C8=O)cccc56)cc4C3=O)c2)cc1
*c1ccc(Oc2cccc(N3C(=O)c4ccc([Si](C)(C)O[Si](C)(C)c5ccc6c(c5)C(=O)N(c5cccc(Oc7ccc(-c8nnc(*)o8)cc7)c5)C6=O)cc4C3=O)c2)cc1
*c1ccc(Oc2cccc(NC(=O)c3cc(C(=O)Nc4cccc(Oc5ccc(-c6nnc(*)o6)cc5)c4)cc(N4C(=O)c5c(Cl)c(Cl)c(Cl)c(Cl)c5C4=O)c3)c2)cc1
*c1ccc(Oc2cccc(NC(=O)c3cc(C(=O)Nc4cccc(Oc5ccc(-c6nnc(*)o6)cc5)c4)cc(N4C(=O)c5ccccc5C4=O)c3)c2)cc1
*c1ccc(Oc2cccc(NC(=O)c3cc(NC(=O)c4ccc(OC(C)=O)cc4)cc(C(=O)Nc4cccc(Oc5ccc(-c6nnc(*)o6)cc5)c4)c3)c2)cc1
*c1ccc(Oc2cccc(NC(=O)c3ccc(C(=O)Nc4cccc(Oc5ccc(-c6nnc(*)o6)cc5)c4)c(Oc4ccccc4)c3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)cc3)c2C#N)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)C5CCC(C6CCC7C(=O)N(*)C(=O)C7C6)CC5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)CC(=O)N(*)C4=S)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5ccc(-c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5ccc(-c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)c2C#N)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5ccc(-c6cccc7c6C(=O)N(*)C7=O)cc5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5ccc(Oc6cc7ccccc7cc6Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(-c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(C(=O)c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(C(C)(C)c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(C(C)(C)c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)c2C#N)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(Oc7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(S(=O)(=O)c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc(Sc7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5ccc(Oc6cccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)c6)cc5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5cccc(Oc6ccc(-c7ccc(Oc8cccc9c8C(=O)N(*)C9=O)cc7)cc6)c5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5cccc(Oc6ccc(C(=O)c7ccc(Oc8cccc9c8C(=O)N(*)C9=O)cc7)cc6)c5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5cccc(Oc6ccc(Oc7cccc8c7C(=O)N(*)C8=O)cc6)c5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5cccc(Oc6ccc(S(=O)(=O)c7ccc(Oc8cccc9c8C(=O)N(*)C9=O)cc7)cc6)c5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5cccc(Oc6ccc(Sc7ccc(Oc8cccc9c8C(=O)N(*)C9=O)cc7)cc6)c5C4=O)cc3)c2)cc1
*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5cccc(Oc6cccc(Oc7cccc8c7C(=O)N(*)C8=O)c6)c5C4=O)cc3)c2)cc1
*c1ccc(Oc2ccccc2-n2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)cc1
*c1ccc(Oc2ccccc2N2C(=O)c3ccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)cc1
*c1ccc(Oc2ccccc2Oc2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2)cc1
*c1ccc(Oc2ccccc2Oc2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2C(F)(F)F)c(C(F)(F)F)c1
*c1ccc(Oc2ccccc2Oc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccccc2Oc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(F)(F)F)c(C(F)(F)F)c1
*c1ccc(Oc2ccccc2Oc2ccc(N3C(=O)c4ccc(Oc5cc6ccccc6cc5Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccccc2Oc2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(F)(F)F)c(C(F)(F)F)c1
*c1ccc(Oc2ccccc2Oc2ccc(N3C(=O)c4ccc(Oc5ccccc5Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(Oc2ccccc2Oc2ccc(N3C(=O)c4ccc(Oc5ccccc5Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(F)(F)F)c(C(F)(F)F)c1
*c1ccc(Oc2ccccc2Oc2ccc(N3C(=O)c4ccc(S(=O)(=O)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2C(F)(F)F)c(C(F)(F)F)c1
*c1ccc(S(*)(=O)=O)cc1
*c1ccc(S(=O)(=O)c2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2)cc1
*c1ccc(S(=O)(=O)c2ccc(-n3nc(-c4ccc(Oc5ccc(-c6nn(*)c(=O)c7ccccc67)cc5)cc4)c4ccccc4c3=O)cc2)cc1
*c1ccc(S(=O)(=O)c2ccc(-n3nc(-c4ccccc4)c4cc(Oc5ccc(C(C)(C)c6ccc(Oc7ccc8c(-c9ccccc9)nn(*)c(=O)c8c7)cc6)cc5)ccc4c3=O)cc2)cc1
*c1ccc(S(=O)(=O)c2ccc(-n3nc(-c4ccccc4)c4ccc(Oc5ccc6c(-c7ccccc7)nn(*)c(=O)c6c5)cc4c3=O)cc2)cc1
*c1ccc(S(=O)(=O)c2ccc(-n3nc(-c4ccccc4)c4ccc(S(=O)(=O)c5ccc6c(-c7ccccc7)nn(*)c(=O)c6c5)cc4c3=O)cc2)cc1
*c1ccc(S(=O)(=O)c2ccc(-n3nccc3-c3ccc(-c4ccnn4*)cc3)cc2)cc1
*c1ccc(S(=O)(=O)c2ccc(-n3nccc3-c3cccc(-c4ccnn4*)c3)cc2)cc1
*c1ccc(S(=O)(=O)c2ccc(N3C(=O)C(C)C(SCCOCCSC4C(=O)N(c5ccc(S(=O)(=O)c6ccc(N7C(=O)CC(C)(SCCOCCSC8(C)CC(=O)N(*)C8=O)C7=O)cc6)cc5)C(=O)C4C)C3=O)cc2)cc1
*c1ccc(S(=O)(=O)c2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(S(=O)(=O)c2ccc(N3C(=O)c4ccc(Oc5ccc(C(C)(C)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)cc2)cc1
*c1ccc(S(=O)(=O)c2ccc(N3C(=O)c4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)cc2)cc1
*c1ccc(S(=O)(=O)c2ccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
*c1ccc(S(=O)(=O)c2ccc(NC(=O)c3cccc(C(=O)Nc4ccc(S(=O)(=O)c5ccc(-n6c(=O)c7cc8c(=O)n(*)c(=O)c8cc7c6=O)cc5)cc4)c3)cc2)cc1
*c1ccc(S(=O)(=O)c2ccc(Oc3ccc(-c4nn(*)c(=O)c5ccccc45)c(F)c3)cc2)cc1
*c1ccc(Sc2cc3c(cc2Sc2ccc(N4C(=O)C5C6C=CC(C7C(=O)N(*)C(=O)C67)C5C4=O)cc2)C(=O)N(CCN(C)C)C3=O)cc1
*c1ccc(Sc2cc3c(cc2Sc2ccc(N4C(=O)C5C6C=CC(C7C(=O)N(*)C(=O)C67)C5C4=O)cc2)C(=O)N(CCc2ccccc2)C3=O)cc1
*c1ccc(Sc2cc3c(cc2Sc2ccc(N4C(=O)C5C6C=CC(C7C(=O)N(*)C(=O)C67)C5C4=O)cc2)C(=O)N(c2ccc(CC)cc2)C3=O)cc1
*c1ccc(Sc2cc3c(cc2Sc2ccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc2)C(=O)N(CCCCCCCCCCCCCCCC)C3=O)cc1
*c1ccc(Sc2cc3c(cc2Sc2ccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc2)C(=O)N(CCc2ccccc2)C3=O)cc1
*c1ccc(Sc2cc3c(cc2Sc2ccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc2)C(=O)N(c2ccc(CC)cc2)C3=O)cc1
*c1ccc(Sc2ccc(-c3nc4cc(-c5ccc6nc(-c7ccccc7)c(*)nc6c5)ccc4nc3-c3ccccc3)cc2)cc1
*c1ccc(Sc2ccc(-c3nc4ccc(-c5ccc6nc(*)c(-c7ccccc7)nc6c5)cc4nc3-c3ccccc3)cc2)cc1
*c1ccc(Sc2ccc(-c3nn(-c4ccc(S(=O)(=O)c5ccc(-n6nc(*)c7ccccc7c6=O)cc5)cc4)c(=O)c4ccccc34)cc2)cc1
*c1ccc(Sc2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cc2)cc1
*c1ccc(Sc2ccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)cn2)nc1
*c1ccc(Sc2ccc(Oc3ccc(Sc4ccc(N5C(=O)c6ccc(Oc7ccc(C(C)(C)c8ccc(Oc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Sc2ccc(S(*)(=O)=O)cc2)cc1
*c1ccc(Sc2ccc(Sc3ccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)s2)cc1
*c1ccc(Sc2ccc(Sc3ccc(N4C(=O)c5ccc(Sc6ccc(Sc7ccc(Sc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)cc2)cc1
*c1ccc(Sc2ccc(Sc3ccc(N4C(=O)c5ccc(Sc6ccc(Sc7ccc(Sc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)cc3)s2)cc1
*c1ccc(Sc2ccc(Sc3ccc(Sc4ccc(-n5c(=O)c6cc7c(=O)n(*)c(=O)c7cc6c5=O)cc4)cc3)cc2)cc1
*c1ccc(Sc2ccc(Sc3ccc(Sc4ccc(N5C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Sc2ccc(Sc3ccc(Sc4ccc(N5C(=O)c6ccc(-c7cccc8c7C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Sc2ccc(Sc3ccc(Sc4ccc(N5C(=O)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Sc2ccc(Sc3ccc(Sc4ccc(N5C(=O)c6ccc(Sc7ccc(Sc8ccc(Sc9ccc%10c(c9)C(=O)N(*)C%10=O)cc8)cc7)cc6C5=O)cc4)cc3)cc2)cc1
*c1ccc(Sc2ccc(Sc3ccc(Sc4ccc(S(*)(=O)=O)cc4S(=O)(=O)O)cc3)cc2)c(S(=O)(=O)O)c1
*c1ccc(Sc2ccc(Sc3ccc(Sc4ccc(S(*)=O)cc4)cc3)cc2)cc1
*c1ccc(Sc2ccc(Sc3ccc(Sc4ccc(Sc5ccc(N6C(=O)c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)cc5)s4)s3)s2)cc1
*c1ccc(Sc2ccc(Sc3ccc(Sc4ccc(Sc5ccc(N6C(=O)c7ccc(Sc8ccc(Sc9ccc(Sc%10ccc%11c(c%10)C(=O)N(*)C%11=O)cc9)cc8)cc7C6=O)cc5)s4)s3)s2)cc1
*c1ccc(Sc2ccc3c(c2)Sc2ccc(Sc4ccc(-n5c(=O)c6cc7c(=O)n(*)c(=O)c7cc6c5=O)cc4)cc2S3)cc1
*c1ccc(Sc2cccc(Sc3ccc(S(*)=O)cc3)c2)cc1
*c1ccc([Si](*)(C)C)s1
*c1ccc([Si](C)(C)[Si](*)(C)C)s1
*c1ccc([Si](CCCC)(CCCC)[Si](*)(CCCC)CCCC)s1
*c1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(-c3ccc4c(c3)c3cc(*)ccc3n4CCCCCC#N)cc2)cc1
*c1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(-c3nnc(*)o3)cc2)cc1
*c1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(-c3nnc(-c4ccc(-c5nnc(*)o5)c5ccccc45)o3)cc2)cc1
*c1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(-c3nnc(-c4ccc(-c5nnc(*)o5)cc4)o3)cc2)cc1
*c1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(-c3nnc(-c4ccc(-c5nnc(*)o5)nc4)o3)cc2)cc1
*c1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(-c3nnc(-c4ccc(-c5nnc(*)o5)o4)o3)cc2)cc1
*c1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(-c3nnc(-c4cccc(-c5nnc(*)o5)n4)o3)cc2)cc1
*c1ccc([Si](c2ccccc2)(c2ccccc2)c2ccc(-c3nnc(-c4cncc(-c5nnc(*)o5)c4)o3)cc2)cc1
*c1ccc2[nH]c(*)nc2c1
*c1ccc2c(c1)C(*)(C)CC2(C)C
*c1ccc2c(c1)C(=O)N(C1CC(C)(C)CC(C)(CN3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)C1)C2=O
*c1ccc2c(c1)C(=O)N(C1CCC(CC3CCC(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)C(C)C3)CC1C)C2=O
*c1ccc2c(c1)C(=O)N(C1CCC(CC3CCC(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)CC3)CC1)C2=O
*c1ccc2c(c1)C(=O)N(C1CCC(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)CC1)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C(C)C)cc(C(c3ccccc3)(c3cc(C(C)C)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(C(C)C)c3)C(F)(F)F)cc1C(C)C)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C(C)C)cc(Cc3cc(C(C)C)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(C(C)C)c3)cc1C(C)C)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C)c(C)c(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)c(C)c1C)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C)cc(-c3cc(C)c(-c4cc(C)c(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)c(C)c4)cc3C)cc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C)cc(-c3cc(C)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(C)c3)cc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C)cc(-c3cc(C)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c4ccccc34)c3ccccc13)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C)cc(C(c3cc(C(F)(F)F)cc(C(F)(F)F)c3)c3cc(C)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(C)c3)cc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C)cc(C(c3cc(C)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(C)c3)c3cccc4ccccc34)cc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C)cc(C(c3cccc(C(F)(F)F)c3)c3cc(C)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(C)c3)cc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C)cc(C(c3ccccc3)(c3cc(C)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(C(C)C)c3)C(F)(F)F)cc1C(C)C)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C)cc(C)c(C(=O)c3cccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)c1C)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C)cc(C)c(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)c1C)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C)cc(C3(c4cc(C)c(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)c(C(C)C)c4)c4ccccc4-c4ccccc43)cc1C(C)C)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C)cc(C3(c4cc(C)c(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)c(C)c4)c4ccccc4-c4ccccc43)cc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C)cc(Cc3cc(C)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(C(C)C)c3)cc1C(C)C)C2=O
*c1ccc2c(c1)C(=O)N(c1c(C)cc(Cc3cc(C)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(C)c3)cc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1c(CC)cc(Cc3cc(CC)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(CC)c3)cc1CC)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(-c3ccc(O)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)ccc1O)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(Br)c(Oc3c(Br)cc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3Br)c(Br)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(C(=O)Nc3c(C)cc(-c4cc(C)c(NC(=O)c5ccc(C)c(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)c(C)c4)cc3C)ccc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(C(=O)Nc3ccc(C(c4ccc(NC(=O)c5ccc(C)c(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)cc4)(C(F)(F)F)C(F)(F)F)cc3)ccc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(C(=O)Nc3cccc4c(NC(=O)c5ccc(C)c(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)cccc34)ccc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(C(=O)O)cc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(C(=O)Oc3ccc(C(c4ccc(OC(=O)c5ccc(C)c(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)cc4)(C(F)(F)F)C(F)(F)F)cc3)ccc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(C(c3ccc(O)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)(C(F)(F)F)C(F)(F)F)ccc1O)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(C(c3ccc(OC(=O)C45CC6CC(CC(C6)C4)C5)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)(C(F)(F)F)C(F)(F)F)ccc1OC(=O)C13CC4CC(CC(C4)C1)C3)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(C(c3ccc(OC(C)=O)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)(C(F)(F)F)C(F)(F)F)ccc1OC(C)=O)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(C(c3ccc(OCCN(CC)c4ccc(/N=N/c5ccc([N+](=O)[O-])cc5)cc4)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)(C(F)(F)F)C(F)(F)F)ccc1OCCN(CC)c1ccc(/N=N/c3ccc([N+](=O)[O-])cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(C)c(-c3c(C)cc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3C)c(C)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(C)c(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)cc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)c(O)cc1O)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)cc(C(F)(F)F)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)ccc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)ccc1OCCCCCCOc1ccc(-c3ccc(OCC)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)ccc1OCCCCCCOc1ccc(/N=N/c3ccc(OCC)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(NC(=O)OCCN(CCOC(=O)Nc3cc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)ccc3C)c3ccc(/N=N/c4ccc([N+](=O)[O-])cc4)cc3)ccc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(OCCCC)cc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(OCCCCCCCC)cc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(OCCCCCCCCCCCC)cc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(OCCCCCCCCCCCCCCCC)cc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(OCCN(CC)c3ccc(/N=N/c4ccc([N+](=O)[O-])cc4)cc3)cc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cc(Oc3c(C)cc(-c4cc(C)c(Oc5cc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc(C(F)(F)F)c5)c(C)c4)cc3C)cc(C(F)(F)F)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(-c3cc(-c4ccc(OCCCC#N)cc4)cc(-c4ccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)n3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(C)c3)cc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(O)c3)cc1O)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(OC(=O)/C=C/c4ccccc4)c3)cc1OC(=O)/C=C/c1ccccc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(OCCCCCCOc4ccc(/C=C/c5ccc(F)cc5)cc4)c3)cc1OCCCCCCOc1ccc(/C=C/c3ccc(F)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(OCCN(CC)c4ccc(C(C#N)=C(C#N)C#N)cc4)c3)cc1OCCN(CC)c1ccc(C(C#N)=C(C#N)C#N)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(OCCOc4ccc5c(C)cc(=O)oc5c4)c3)cc1OCCOc1ccc3c(C)cc(=O)oc3c1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3C(F)(F)F)c(C(F)(F)F)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(N4C(=O)c5ccc(C(*)(c6ccccc6)C(F)(F)F)cc5C4=O)cc3C(F)(F)F)c(C(F)(F)F)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(-c3ccc(Oc4ccc5c(c4)C(C)(c4ccc(Oc6ccc(-c7ccc(N8C(=O)c9ccc(C(*)(C(F)(F)F)C(F)(F)F)cc9C8=O)cc7)cc6C(F)(F)F)cc4)CC5(C)C)c(C(F)(F)F)c3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C(=O)Nc3ccc(C(c4ccc(NC(=O)c5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c(C)c5)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C(=O)Nc3ccc(C(c4ccc(NC(=O)c5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C(=O)Nc3ccc(Oc4ccc(-c5ccc(Oc6ccc(NC(=O)c7ccc(N8C(=O)c9ccc(C(*)(C(F)(F)F)C(F)(F)F)cc9C8=O)cc7)cc6)cc5)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C(=O)Nc3ccc(Oc4ccc(C(C)(C)c5ccc(Oc6ccc(NC(=O)c7ccc(N8C(=O)c9ccc(C(*)(C(F)(F)F)C(F)(F)F)cc9C8=O)cc7)cc6)cc5)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C(=O)Nc3ccc(Oc4ccc(C(c5ccc(Oc6ccc(NC(=O)c7ccc(N8C(=O)c9ccc(C(*)(C(F)(F)F)C(F)(F)F)cc9C8=O)cc7)cc6)cc5)(C(F)(F)F)C(F)(F)F)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C(=O)Nc3ccc(Oc4ccc(NC(=O)c5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C(=O)Nc3ccc(Oc4ccc(Oc5ccc(NC(=O)c6ccc(N7C(=O)c8ccc(C(*)(C(F)(F)F)C(F)(F)F)cc8C7=O)cc6)cc5)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C(=O)Nc3ccc(Oc4cccc(Oc5ccc(NC(=O)c6ccc(N7C(=O)c8ccc(C(*)(C(F)(F)F)C(F)(F)F)cc8C7=O)cc6)cc5)c4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C(=O)Nc3ccc(Oc4ccccc4Oc4ccc(NC(=O)c5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C(C)(C)c3ccc(C(C)(C)c4ccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C(C)(C)c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C(c3ccc(C)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)(C(F)(F)F)C(F)(F)F)cc1C)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C(c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)(C(F)(F)F)C(F)(F)F)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C(c3ccccc3)(c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)C(F)(F)F)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C3(c4ccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)c(C(C)C)c4)c4ccccc4-c4ccccc43)cc1C(C)C)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C3(c4ccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)c(F)c4)c4ccccc4-c4ccccc43)cc1F)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(C3(c4ccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)c4ccccc4-c4ccccc43)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Cc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c(C(C)(C)C)c3)cc1C(C)(C)C)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Cc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Cc3ccc(N4C(=O)c5ccc(C(C)(C)CCCCC(*)(C)C)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Cc3ccc(N4C(=O)c5ccc(C(F)(F)C(F)(F)C(*)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(N(C)CCN(C)c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(N(c3ccc(/C=C/c4ccc([N+](=O)[O-])cc4)cc3)c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(N(c3ccc(C#N)cc3)c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(N3C(=O)c4ccc(-c5c(-c6ccccc6)c(-c6ccccc6)c(-c6ccc(Oc7ccc(-c8c(-c9ccccc9)c(-c9ccccc9)c(*)c(-c9ccccc9)c8-c8ccccc8)cc7)cc6)c(-c6ccccc6)c5-c5ccccc5)cc4C3=O)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(N3C(=O)c4ccc(-c5c(-c6ccccc6)c(-c6ccccc6)c(-c6ccc(Sc7ccc(-c8c(-c9ccccc9)c(-c9ccccc9)c(*)c(-c9ccccc9)c8-c8ccccc8)cc7)cc6)c(-c6ccccc6)c5-c5ccccc5)cc4C3=O)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(NC(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(=O)Nc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(NC(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(=O)Nc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(NC(=S)/N=C/c3ccc(Cl)c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(OC(C)COc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(OC(CCC)COc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(OC(CCCCCC)COc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(OCCCCCCCCCCOc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(OCCCCCCOc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(OCCCCOc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(OCCN(CCOc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)c3ccc(C(C#N)=C(C#N)C#N)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(OCCN(CCOc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)c3ccccc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(OCCOCCOCCOCCOc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(OCCOCCOCCOc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(OCCOCCOc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(OCCOc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3c(C(C)(C)C)cc(-c4ccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)cc3C(C)(C)C)c(C(F)(F)F)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3c(C(C)(C)C)cc(-c4ccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)cc3C(C)(C)C)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3c(C)cc(C(c4cc(C)c(Oc5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)c(C)c4)c4cccc5ccccc45)cc3C)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3c(C)cc(Cc4cc(C)c(Oc5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)c(C)c4)cc3C)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3cc(C(C)(C)C)c(Oc4ccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)cc3C(C)(C)C)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(-c4ccc(Oc5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4ccc(Oc5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4cccc(C(=O)c5ccc(Oc6ccc(N7C(=O)c8ccc(C(*)(C(F)(F)F)C(F)(F)F)cc8C7=O)cc6)cc5)c4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4cccc(C(=O)c5ccc(Oc6ccc(N7C(=O)c8ccc(C(*)(C(F)(F)F)C(F)(F)F)cc8C7=O)cc6)cc5)n4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4cccc(C(=O)c5ccc(Oc6ccc(N7C(=O)c8ccc(C(*)(C(F)(F)F)C(F)(F)F)cc8C7=O)cc6C(F)(F)F)cc5)n4)cc3)c(C(F)(F)F)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4cccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)c4)cc3)c(C(F)(F)F)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4cccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)c4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(C)(C)c4ccc(Oc5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(c4ccc(Oc5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)cc4)(C(F)(F)F)C(F)(F)F)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(c4ccc(Oc5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5C(F)(F)F)cc4)(C(F)(F)F)C(F)(F)F)cc3)c(C(F)(F)F)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(-c6c(-c7ccccc7)c(-c7ccccc7)c(-c7ccc(Oc8ccc(-c9c(-c%10ccccc%10)c(-c%10ccccc%10)c(*)c(-c%10ccccc%10)c9-c9ccccc9)cc8)cc7)c(-c7ccccc7)c6-c6ccccc6)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(-c6c(-c7ccccc7)c(-c7ccccc7)c(-c7ccc(Sc8ccc(-c9c(-c%10ccccc%10)c(-c%10ccccc%10)c(*)c(-c%10ccccc%10)c9-c9ccccc9)cc8)cc7)c(-c7ccccc7)c6-c6ccccc6)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)c(O)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)c(OCCN(CC)c3ccc(/N=N/c4ccc([N+](=O)[O-])cc4)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3Br)c(Br)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(C(*)O)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(C(*)O[Si](C)(C)C)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(C(*)O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)C)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(C(C)(C)CCCCCCCCC(*)(C)C)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(C(F)(F)C(F)(F)C(*)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(C(F)(F)C(F)(F)C(F)(F)C(*)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(*)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(*)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(*)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(Oc4ccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)cc3)c(C(F)(F)F)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(Oc4ccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(S(=O)(=O)c4ccc(Oc5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc4ccc(Oc5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)cc4c3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3cccc(-c4cc(-c5ccccc5)cc(-c5cccc(Oc6ccc(N7C(=O)c8ccc(C(*)(C(F)(F)F)C(F)(F)F)cc8C7=O)cc6)c5)n4)c3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3cccc(Oc4ccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)c3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3cccc(Oc4ccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)c3C#N)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccccc3-c3ccccc3Oc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccccc3Oc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3C(F)(F)F)c(C(F)(F)F)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(S(=O)(=O)c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(S(=O)(=O)c3ccc(N4C(=O)c5ccc(C(F)(F)C(F)(F)C(*)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(S(=O)(=O)c3ccc(S(=O)(=O)c4ccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(S(=O)(=O)c3ccc(S(=O)(=O)c4ccc(S(=O)(=O)c5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Sc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Sc3ccc(Oc4ccc(Sc5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Sc3ccc(Sc4ccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Sc3ccc(Sc4ccc(Sc5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc(Sc3ccc(Sc4ccc(Sc5ccc(Sc6ccc(N7C(=O)c8ccc(C(*)(C(F)(F)F)C(F)(F)F)cc8C7=O)cc6)cc5)cc4)cc3)cc1)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc3c(c1)-c1cc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)ccc1C3)C2=O
*c1ccc2c(c1)C(=O)N(c1ccc3c(c1)Cc1cc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)ccc1-3)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(-c4ccc(NC(=O)c5cccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)cc4)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(C(c4ccc(NC(=O)c5cccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)cc4)(C(F)(F)F)C(F)(F)F)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(C(c4ccc(NC(=O)c5cccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5C)cc4)(C(F)(F)F)C(F)(F)F)cc3)c1C)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(NC(=O)c4ccc(NC(=O)c5cccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)cc4)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(NC(=O)c4cccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)c4)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(Oc4ccc(-c5ccc(Oc6ccc(NC(=O)c7cccc(N8C(=O)c9ccc(C(*)(C(F)(F)F)C(F)(F)F)cc9C8=O)c7)cc6)cc5)cc4)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(Oc4ccc(C(C)(C)c5ccc(Oc6ccc(NC(=O)c7cccc(N8C(=O)c9ccc(C(*)(C(F)(F)F)C(F)(F)F)cc9C8=O)c7)cc6)cc5)cc4)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(Oc4ccc(C(c5ccc(Oc6ccc(NC(=O)c7cccc(N8C(=O)c9ccc(C(*)(C(F)(F)F)C(F)(F)F)cc9C8=O)c7)cc6)cc5)(C(F)(F)F)C(F)(F)F)cc4)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(Oc4ccc(NC(=O)c5cccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)cc4)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(Oc4ccc(Oc5ccc(NC(=O)c6cccc(N7C(=O)c8ccc(C(*)(C(F)(F)F)C(F)(F)F)cc8C7=O)c6)cc5)cc4)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(Oc4cccc(Oc5ccc(NC(=O)c6cccc(N7C(=O)c8ccc(C(*)(C(F)(F)F)C(F)(F)F)cc8C7=O)c6)cc5)c4)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(=O)Nc3ccc(Oc4ccccc4Oc4ccc(NC(=O)c5cccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)cc4)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(C)(C)c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)c3cccc(N4C(=O)c5ccc(C(F)(F)C(F)(F)C(*)(F)F)cc5C4=O)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)c3cccc(N4C(=O)c5ccc(C(F)(F)C(F)(F)C(F)(F)C(*)(F)F)cc5C4=O)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)c3cccc(N4C(=O)c5ccc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(*)(F)F)cc5C4=O)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(F)(F)C(F)(F)C(F)(F)c3cccc(N4C(=O)c5ccc(C(F)(F)C(F)(F)C(*)(F)F)cc5C4=O)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(F)(F)C(F)(F)C(F)(F)c3cccc(N4C(=O)c5ccc(C(F)(F)C(F)(F)C(F)(F)C(*)(F)F)cc5C4=O)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(F)(F)C(F)(F)C(F)(F)c3cccc(N4C(=O)c5ccc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(*)(F)F)cc5C4=O)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(O)c3cccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(c3cccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)(C(F)(F)F)C(F)(F)F)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(C(c3ccccc3)(c3cccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)C(F)(F)F)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)c1C)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(NC(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(=O)Nc3cccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(NC(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(=O)Nc3cccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(Oc3cc(Oc4cccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)c4)ccc3P(=O)(c3ccccc3)c3ccccc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(Oc3ccc(C(c4ccc(Oc5cccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)cc4)(C(F)(F)F)C(F)(F)F)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(Oc3ccc(N4C(=O)c5ccc(-c6c(-c7ccccc7)c(-c7ccccc7)c(-c7ccc(Oc8ccc(-c9c(-c%10ccccc%10)c(-c%10ccccc%10)c(*)c(-c%10ccccc%10)c9-c9ccccc9)cc8)cc7)c(-c7ccccc7)c6-c6ccccc6)cc5C4=O)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(Oc3ccc(N4C(=O)c5ccc(-c6c(-c7ccccc7)c(-c7ccccc7)c(-c7ccc(Sc8ccc(-c9c(-c%10ccccc%10)c(-c%10ccccc%10)c(*)c(-c%10ccccc%10)c9-c9ccccc9)cc8)cc7)c(-c7ccccc7)c6-c6ccccc6)cc5C4=O)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(Oc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(Oc3ccc(Oc4cccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)c4)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(Oc3ccc(P(=O)(c4ccccc4)c4ccc(Oc5cccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)cc4)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(Oc3ccc(S(=O)(=O)c4ccc(Oc5cccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)c5)cc4)cc3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(Oc3cccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(Oc3cccc(N4C(=O)c5ccc(C(F)(F)C(F)(F)C(*)(F)F)cc5C4=O)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(Oc3cccc(Oc4cccc(N5C(=O)c6ccc(C(*)(C(F)(F)F)C(F)(F)F)cc6C5=O)c4)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(P(=O)(c3ccc(C(F)(F)F)cc3)c3cccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(P(=O)(c3ccc(Oc4ccc(C56CC7CC(CC(C7)C5)C6)cc4)cc3)c3cccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(P(=O)(c3cccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)c3cc(C(F)(F)F)cc(C(F)(F)F)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(P(=O)(c3ccccc3)c3cccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc(S(=O)(=O)c3cccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)c1)C2=O
*c1ccc2c(c1)C(=O)N(c1cccc3c(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cccc13)C2=O
*c1ccc2c(c1)C(=O)N(c1ccccc1Oc1ccc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)cc1)C2=O
*c1ccc2c(c1)C(CC(CC)CCCC)(CC(CC)CCCC)c1cc(*)ccc1-2
*c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(*)ccc1-2
*c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(-c3cc(CCCCCC)c(*)cc3CCCCCC)ccc1-2
*c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(-c3cc(CCCCCCCC)c(*)cc3CCCCCCCC)ccc1-2
*c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(-c3cc(OCCCCCCCC)c(*)cc3OCCCCCCCC)ccc1-2
*c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(-c3cc(OCc4ccccc4)c(*)cc3OCc3ccccc3)ccc1-2
*c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(-c3ccc4c(c3)C(CC3=NC(Cc5ccccc5)CO3)(CC3=NC(Cc5ccccc5)CO3)c3cc(*)ccc3-4)ccc1-2
*c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(-c3ccc4c(c3)c3cc(*)ccc3n4-c3ccc(C)cc3)ccc1-2
*c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(-c3ccc4c(c3)c3cc(*)ccc3n4-c3ccc(OC)cc3)ccc1-2
*c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(-c3ccc4c(c3)c3cc(*)ccc3n4-c3ccccc3)ccc1-2
*c1ccc2c(c1)C(CCCCCCCC)(CCCCCCCC)c1cc(*)ccc1-2
*c1ccc2c(c1)C(CCCCCCCC)(CCCCCCCC)c1cc(-c3c(F)c(F)c(*)c(F)c3F)ccc1-2
*c1ccc2c(c1)C(CCCCCCCC)(CCCCCCCC)c1cc(-c3cc(F)c(*)cc3F)ccc1-2
*c1ccc2c(c1)C(CCCCCCCC)(CCCCCCCC)c1cc(-c3ccc(*)c4nsnc34)ccc1-2
*c1ccc2c(c1)C(CCCCCCCC)(CCCCCCCC)c1cc(-c3ccc4c(c3)C(c3ccc(-c5nnc(-c6ccc(C(C)(C)C)cc6)o5)cc3)(c3ccc(-c5nnc(-c6ccc(C(C)(C)C)cc6)o5)cc3)c3cc(*)ccc3-4)ccc1-2
*c1ccc2c(c1)Cc1cc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)ccc1-2
*c1ccc2c(c1)Sc1cc(-c3ccc(-c4ccc(*)s4)s3)ccc1N2CCCCCC
*c1ccc2c(c1)Sc1cc(-c3ccc(-c4ccc(*)s4)s3)ccc1N2CCCCCCCCCC
*c1ccc2c(c1)Sc1cc(-c3ccc(-c4ccc(*)s4)s3)ccc1N2CCCCCCCCCCCC
*c1ccc2c(c1)Sc1cc(-c3sc(/C=C/C4=CC(=C(C#N)C#N)C=C(/C=C/c5sc(*)c(CCCCCC)c5CCCCCC)O4)c(CCCCCC)c3CCCCCC)ccc1N2CCCCCC
*c1ccc2c(c1)Sc1cc(-c3sc4cc(*)sc4c3CCCCC)ccc1N2CCCCCC
*c1ccc2c(c1)Sc1cc(-c3sc4cc(*)sc4c3CCCCC)ccc1N2CCCCCCCCCC
*c1ccc2c(c1)Sc1cc(-c3sc4cc(*)sc4c3CCCCC)ccc1N2CCCCCCCCCCCC
*c1ccc2c3ccc(-c4ccc(-c5ccc(-c6ccc(*)s6)c6nccnc56)s4)cc3n(C(CCCCCCCC)CCCCCCCC)c2c1
*c1ccc2c3ccc(-c4ccc(-c5ccc(-c6ccc(*)s6)c6nsnc56)s4)cc3n(C(CCCCCCCC)CCCCCCCC)c2c1
*c1ccc2cc(-c3nc4ccc(-c5ccc6nc(*)[nH]c6c5)cc4[nH]3)ccc2c1
*c1ccc2cc(OC(=O)c3ccc(C(=O)Oc4ccc5cc(-c6nc7ccccc7nc6*)ccc5c4)cc3)ccc2c1
*c1ccc2cc(OC(=O)c3cccc(C(=O)Oc4ccc5cc(-c6nc7ccccc7nc6*)ccc5c4)c3)ccc2c1
*c1ccc2cc3cc(-c4sc(-c5cc(CCCCCCCCCCCC)c(*)s5)cc4CCCCCCCCCCCC)ccc3cc2c1
*c1ccc2nc(*)cc(-c3ccccc3)c2c1
*c1ccc2nc(-c3ccc4c(c3)C(=O)N(c3cc(C(*)(C(F)(F)F)C(F)(F)F)ccc3O)C4=O)oc2c1
*c1ccc2oc(-c3cc(OCCCCCCCCCC)c(-c4nc5cc(C(*)(C(F)(F)F)C(F)(F)F)ccc5o4)cc3OCCCCCCCCCC)nc2c1
*c1ccc2oc(-c3ccc(-c4nc5cc(C(*)(C(F)(F)F)C(F)(F)F)ccc5o4)cc3)nc2c1
*c1ccc2oc(-c3ccc(-c4nc5cc(C(*)(C(F)(F)F)C(F)(F)F)ccc5o4)cn3)nc2c1
*c1ccc2oc(-c3ccc(-c4nc5cc(C(*)(c6ccccc6)C(F)(F)F)ccc5o4)cc3)nc2c1
*c1ccc2oc(-c3ccc(C(c4ccc(-c5nc6cc(C(*)(C(F)(F)F)C(F)(F)F)ccc6o5)cc4)(C(F)(F)F)C(F)(F)F)cc3)nc2c1
*c1ccc2oc(-c3ccc(C(c4ccc(-c5nc6cc(C(*)(c7ccccc7)C(F)(F)F)ccc6o5)cc4)(C(F)(F)F)C(F)(F)F)cc3)nc2c1
*c1ccc2oc(-c3ccc(C(c4ccccc4)(c4ccc(-c5nc6cc(C(*)(C(F)(F)F)C(F)(F)F)ccc6o5)cc4)C(F)(F)F)cc3)nc2c1
*c1ccc2oc(-c3ccc(C(c4ccccc4)(c4ccc(-c5nc6cc(C(*)(c7ccccc7)C(F)(F)F)ccc6o5)cc4)C(F)(F)F)cc3)nc2c1
*c1ccc2oc(-c3ccc(Oc4ccc(-c5nc6cc(C(*)(C(F)(F)F)C(F)(F)F)ccc6o5)cc4)cc3)nc2c1
*c1ccc2oc(-c3ccc(Oc4ccc(-c5nc6cc(C(*)(c7ccccc7)C(F)(F)F)ccc6o5)cc4)cc3)nc2c1
*c1ccc2oc(-c3ccc(Oc4ccc(C(c5ccc(Oc6ccc(-c7nc8cc(C(*)(C(F)(F)F)C(F)(F)F)ccc8o7)cc6)cc5)(C(F)(F)F)C(F)(F)F)cc4)cc3)nc2c1
*c1ccc2oc(-c3ccc4oc5ccc(-c6nc7cc(C(*)(C(F)(F)F)C(F)(F)F)ccc7o6)cc5c4c3)nc2c1
*c1ccc2oc(-c3cccc(-c4nc5cc(C(*)(C(F)(F)F)C(F)(F)F)ccc5o4)c3)nc2c1
*c1ccc2oc(-c3cccc(-c4nc5cc(C(*)(C(F)(F)F)C(F)(F)F)ccc5o4)n3)nc2c1
*c1ccc2oc(-c3cccc(-c4nc5cc(C(*)(c6ccccc6)C(F)(F)F)ccc5o4)c3)nc2c1
*c1ccc2oc(C3CCC(c4nc5cc(C(*)(C(F)(F)F)C(F)(F)F)ccc5o4)CC3)nc2c1
*c1ccc2oc3ccc(S(*)(=O)=O)cc3c2c1
*c1ccc2oc3ccc(S(=O)(=O)c4ccc(Oc5ccc(S(*)(=O)=O)cc5)cc4)cc3c2c1
*c1cccc(*)c1
*c1cccc(-c2cc(C(=O)Nc3ccc(OC(=O)c4cc(OCCCCCCCCCCCC)c(OCCCCCCCCCCCC)c(OCCCCCCCCCCCC)c4)cc3)cc(-c3cccc(N4C(=O)CC(C5C=C(C)C6C(=O)N(*)C(=O)C6C5)C4=O)c3)c2)c1
*c1cccc(-c2cc(C(=O)Nc3ccc(OC(=O)c4cc(OCCCCCCCCCCCC)c(OCCCCCCCCCCCC)c(OCCCCCCCCCCCC)c4)cc3)cc(-c3cccc(N4C(=O)c5ccc(S(=O)(=O)c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)c3)c2)c1
*c1cccc(-c2ccc3[nH]c(S(=O)(=O)c4nc5ccc(*)cc5[nH]4)nc3c2)c1
*c1cccc(-c2cccc(C(F)(F)C(F)(F)C(*)(F)F)c2)c1
*c1cccc(-c2nc3cc(-c4ccc5[nH]c(*)nc5c4)ccc3[nH]2)c1
*c1cccc(-c2nc3cc(-c4ccc5c(c4)nc(*)n5C)ccc3[nH]2)c1
*c1cccc(-c2nc3cc(-c4ccc5nc(*)c(-c6ccc7ccccc7c6)nc5c4)ccc3nc2-c2ccc3ccccc3c2)c1
*c1cccc(-c2nc3cc(-c4ccc5nc(*)c(-c6ccccc6)nc5c4)ccc3nc2-c2ccccc2)c1
*c1cccc(-c2nc3cc(-c4ccc5nc(-c6ccccc6)c(*)nc5c4)ccc3nc2-c2ccccc2)c1
*c1cccc(-c2nc3cc(Oc4ccc5nc(-c6ccccc6)c(*)nc5c4)ccc3nc2-c2ccccc2)c1
*c1cccc(-c2nc3cc4nc(*)c(-c5ccccc5)nc4cc3nc2-c2ccccc2)c1
*c1cccc(-c2nc3cc4nc(-c5ccccc5)c(*)nc4cc3nc2-c2ccccc2)c1
*c1cccc(-c2nc3ccc(-c4ccc5nc(*)[nH]c5c4)cc3[nH]2)c1
*c1cccc(-c2nc3ccc(-c4ccc5nc(*)sc5c4)cc3s2)c1
*c1cccc(-c2nc3ccc(Oc4ccc5nc(*)c(-c6ccccc6)nc5c4)cc3nc2-c2ccccc2)c1
*c1cccc(-n2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)c1
*c1cccc(-n2c(=O)c3cc4c(=O)n(*)c(=O)c4cc3c2=O)n1
*c1cccc(-n2nc(-c3ccccc3)c3cc(Oc4ccc(C(C)(C)c5ccc(Oc6ccc7c(-c8ccccc8)nn(*)c(=O)c7c6)cc5)cc4)ccc3c2=O)c1C#N
*c1cccc(-n2nc(-c3ccccc3)c3cc(Oc4ccc5c(-c6ccccc6)nn(*)c(=O)c5c4)ccc3c2=O)c1C#N
*c1cccc(C(=O)Nc2ccc(Cc3ccc(N4C(=O)c5ccc(S(=O)(=O)c6ccc7c(c6)C(=O)N(c6ccc(Cc8ccc(NC(=O)c9cccc(-c%10nc%11cc(-c%12ccc%13[nH]c(*)nc%13c%12)ccc%11[nH]%10)c9)cc8)cc6)C7=O)cc5C4=O)cc3)cc2)c1
*c1cccc(C(=O)Nc2ccc(NC(=O)c3cccc(N4C(=O)c5ccc(-c6cccc7c6C(=O)N(*)C7=O)cc5C4=O)c3)cc2)c1
*c1cccc(C(=O)Nc2ccc(Oc3ccc(-c4ccc(Oc5ccc(NC(=O)c6cccc(N7C(=O)c8ccc(-c9ccc%10c(c9)C(=O)N(*)C%10=O)cc8C7=O)c6)cc5)cc4)cc3)cc2)c1
*c1cccc(C(=O)Nc2ccc(Oc3ccc(-c4ccc(Oc5ccc(NC(=O)c6cccc(N7C(=O)c8ccc(-c9cccc%10c9C(=O)N(*)C%10=O)cc8C7=O)c6)cc5)cc4)cc3)cc2)c1
*c1cccc(C(=O)Nc2ccc(Oc3ccc(C(C)(C)c4ccc(Oc5ccc(NC(=O)c6cccc(N7C(=O)c8ccc(-c9ccc%10c(c9)C(=O)N(*)C%10=O)cc8C7=O)c6)cc5)cc4)cc3)cc2)c1
*c1cccc(C(=O)Nc2ccc(Oc3ccc(C(C)(C)c4ccc(Oc5ccc(NC(=O)c6cccc(N7C(=O)c8ccc(-c9cccc%10c9C(=O)N(*)C%10=O)cc8C7=O)c6)cc5)cc4)cc3)cc2)c1
*c1cccc(C(=O)Nc2ccc(Oc3ccc(NC(=O)c4cccc(N5C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)c4)cc3)cc2)c1
*c1cccc(C(=O)Nc2ccc(Oc3ccc(NC(=O)c4cccc(N5C(=O)c6ccc(-c7cccc8c7C(=O)N(*)C8=O)cc6C5=O)c4)cc3)cc2)c1
*c1cccc(C(=O)Nc2ccc(Oc3ccc(Oc4ccc(NC(=O)c5cccc(N6C(=O)c7ccc(-c8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)c5)cc4)cc3)cc2)c1
*c1cccc(C(=O)Nc2ccc(Oc3ccc(Oc4ccc(NC(=O)c5cccc(N6C(=O)c7ccc(-c8cccc9c8C(=O)N(*)C9=O)cc7C6=O)c5)cc4)cc3)cc2)c1
*c1cccc(C(=O)Nc2ccc(Oc3ccc(Oc4ccc(Oc5ccc(NC(=O)c6cccc(N7C(=O)c8ccc(-c9ccc%10c(c9)C(=O)N(*)C%10=O)cc8C7=O)c6)cc5)cc4)cc3)cc2)c1
*c1cccc(C(=O)Nc2ccc(Oc3ccc(Oc4ccc(Oc5ccc(NC(=O)c6cccc(N7C(=O)c8ccc(-c9cccc%10c9C(=O)N(*)C%10=O)cc8C7=O)c6)cc5)cc4)cc3)cc2)c1
*c1cccc(C(=O)Nc2ccc(Oc3ccc(S(=O)(=O)c4ccc(Oc5ccc(NC(=O)c6cccc(N7C(=O)c8ccc(-c9ccc%10c(c9)C(=O)N(*)C%10=O)cc8C7=O)c6)cc5)cc4)cc3)cc2)c1
*c1cccc(C(=O)Nc2ccc(Oc3ccc(S(=O)(=O)c4ccc(Oc5ccc(NC(=O)c6cccc(N7C(=O)c8ccc(-c9cccc%10c9C(=O)N(*)C%10=O)cc8C7=O)c6)cc5)cc4)cc3)cc2)c1
*c1cccc(C(=O)Nc2ccc(Oc3cccc(Oc4ccc(NC(=O)c5cccc(N6C(=O)c7ccc(-c8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)c5)cc4)c3)cc2)c1
*c1cccc(C(=O)Nc2ccc(Oc3cccc(Oc4ccc(NC(=O)c5cccc(N6C(=O)c7ccc(-c8cccc9c8C(=O)N(*)C9=O)cc7C6=O)c5)cc4)c3)cc2)c1
*c1cccc(C(=O)Nc2cccc(S(=O)(=O)c3cccc(NC(=O)c4cccc(N5C(=O)c6ccc(-c7ccc8c(c7)C(=O)N(*)C8=O)cc6C5=O)c4)c3)c2)c1
*c1cccc(C(=O)Nc2cccc(S(=O)(=O)c3cccc(NC(=O)c4cccc(N5C(=O)c6ccc(-c7cccc8c7C(=O)N(*)C8=O)cc6C5=O)c4)c3)c2)c1
*c1cccc(C(=O)c2c(C)cc(C)c(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2C)c1
*c1cccc(C(=O)c2c(C)cc(C)c(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2C)c1
*c1cccc(C(=O)c2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3)cc2)c1
*c1cccc(C(=O)c2ccc(Oc3ccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)cc3C(F)(F)F)cc2)c1
*c1cccc(C(=O)c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c2)c1
*c1cccc(C(F)(F)C(F)(F)C(*)(F)F)c1
*c1cccc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)c2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(C(F)(F)C(F)(F)C(F)(F)c2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(C(O)c2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(C(O[Si](C)(C)O[Si](C)(C)O[Si](C)(C)C)c2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(C(c2cccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)(C(F)(F)F)C(F)(F)F)c1
*c1cccc(C(c2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)(C(F)(F)F)C(F)(F)F)c1
*c1cccc(C(c2ccccc2)(c2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)C(F)(F)F)c1
*c1cccc(CCCOc2cccc(N3C(=O)c4cccc(-c5ccc6c(c5)C(=O)N(*)C6=O)c4C3=O)c2)c1
*c1cccc(Cc2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c2)c1
*c1cccc(Cc2cccc(N3C(=O)c4ccc([Si](C)(C)O[Si](C)(C)O[Si](C)(C)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(Cc2cccc(N3C(=O)c4ccc([Si](C)(C)O[Si](C)(C)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(Cc2cccc(N3C(=O)c4ccc([Si](C)(C)c5ccc(-c6ccc([Si](C)(C)c7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)c2)c1
*c1cccc(Cc2cccc(N3C(=O)c4ccc([Si](C)(C)c5ccc([Si](C)(C)c6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4C3=O)c2)c1
*c1cccc(Cc2cccc(N3C(=O)c4ccc([Si](C)(C)c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(N/C=C/C(=O)c2cccc(C(=O)/C=C/Nc3cccc(S(*)(=O)=O)c3)c2)c1
*c1cccc(N2C(=O)C(=O)N(CCCCCCN3C(=O)C(=O)N(*)C3=O)C2=O)n1
*c1cccc(N2C(=O)C(=O)N(c3ccc(Cc4ccc(N5C(=O)C(=O)N(*)C5=O)cc4)cc3)C2=O)n1
*c1cccc(N2C(=O)C3CCC(C4CCC5C(=O)N(*)C(=O)C5C4)CC3C2=O)c1
*c1cccc(N2C(=O)c3c(c(-c4ccccc4)c(-c4ccc(-c5c(-c6ccccc6)c6c(c(-c7ccccc7)c5-c5ccccc5)C(=O)N(*)C6=O)cc4)c(-c4ccccc4)c3-c3ccccc3)C2=O)c1
*c1cccc(N2C(=O)c3ccc(-c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(-c4cccc5c4C(=O)N(*)C5=O)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(C(c4ccc5c(c4)C(=O)N(c4cccc(-n6c(=O)c7cc8c(=O)n(*)c(=O)c8cc7c6=O)c4)C5=O)(C(F)(F)F)C(F)(F)F)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(C(c4ccc5c(c4)C(=O)N(c4cccc(N6C(=O)c7ccc(-c8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)c4)C5=O)(C(F)(F)F)C(F)(F)F)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4c(C)cc(-c5cc(C)c(Oc6ccc7c(c6)C(=O)N(*)C7=O)c(C)c5)cc4C)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4c(C)cc(Cc5cc(C)c(Oc6ccc7c(c6)C(=O)N(*)C7=O)c(C)c5)cc4C)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4cc5ccccc5cc4Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4ccc(-c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4ccc(C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4ccc(C(C)(C)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4ccc(Oc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4ccc(S(=O)(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4ccc(Sc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5)cc4)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4ccc5c(c4)C4(CC(C)(C)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc64)CC5(C)C)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4ccc5cc(Oc6ccc7c(c6)C(=O)N(*)C7=O)ccc5c4)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4ccc5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5c4)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4cccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)c4)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4cccc5c(Oc6ccc7c(c6)C(=O)N(*)C7=O)cccc45)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(Oc4ccccc4Oc4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cccc(N2C(=O)c3ccc(P(=O)(c4ccccc4)c4ccc5c(c4)C(=O)N(*)C5=O)cc3C2=O)c1
*c1cccc(N2C(=O)c3cccc(Oc4c(C)cc(-c5cc(C)c(Oc6cccc7c6C(=O)N(*)C7=O)c(C)c5)cc4C)c3C2=O)c1
*c1cccc(N2C(=O)c3cccc(Oc4c(C)cc(Cc5cc(C)c(Oc6cccc7c6C(=O)N(*)C7=O)c(C)c5)cc4C)c3C2=O)c1
*c1cccc(N2C(=O)c3cccc(Oc4ccc(-c5ccc(Oc6cccc7c6C(=O)N(*)C7=O)cc5)cc4)c3C2=O)c1
*c1cccc(N2C(=O)c3cccc(Oc4ccc(C(=O)c5ccc(Oc6cccc7c6C(=O)N(*)C7=O)cc5)cc4)c3C2=O)c1
*c1cccc(N2C(=O)c3cccc(Oc4ccc(C(C)(C)c5ccc(Oc6cccc7c6C(=O)N(*)C7=O)cc5)cc4)c3C2=O)c1
*c1cccc(N2C(=O)c3cccc(Oc4ccc(Oc5ccc(Oc6cccc7c6C(=O)N(*)C7=O)cc5)cc4)c3C2=O)c1
*c1cccc(N2C(=O)c3cccc(Oc4ccc(Oc5cccc6c5C(=O)N(*)C6=O)cc4)c3C2=O)c1
*c1cccc(N2C(=O)c3cccc(Oc4ccc(S(=O)(=O)c5ccc(Oc6cccc7c6C(=O)N(*)C7=O)cc5)cc4)c3C2=O)c1
*c1cccc(N2C(=O)c3cccc(Oc4ccc(Sc5ccc(Oc6cccc7c6C(=O)N(*)C7=O)cc5)cc4)c3C2=O)c1
*c1cccc(N2C(=O)c3cccc(Oc4cccc(Oc5cccc6c5C(=O)N(*)C6=O)c4)c3C2=O)c1
*c1cccc(NC(=O)CCCCC(=O)Nc2cccc(S(*)(=O)=O)c2)c1
*c1cccc(NC(=O)CCCCCCC(=O)Nc2cccc(S(*)(=O)=O)c2)c1
*c1cccc(NC(=O)CCCCCCCC(=O)Nc2cccc(S(*)(=O)=O)c2)c1
*c1cccc(NC(=O)CCCCCCCCC(=O)Nc2cccc(S(*)(=O)=O)c2)c1
*c1cccc(NC(=O)CCCCCCCCCCC(=O)Nc2cccc(S(*)(=O)=O)c2)c1
*c1cccc(NC(=O)c2ccc(-c3ccc(C(=O)Nc4cccc(S(*)(=O)=O)c4)c(C)c3)cc2C)c1
*c1cccc(NC(=O)c2ccc(OCCOCCOCCOCCOCCOc3ccc(C(=O)Nc4ccc5[nH]c(*)nc5c4)cc3)cc2)c1
*c1cccc(NC(=O)c2ccc(OCCOCCOCCOCCOc3ccc(C(=O)Nc4ccc5[nH]c(*)nc5c4)cc3)cc2)c1
*c1cccc(NC(=O)c2ccc(OCCOCCOCCOc3ccc(C(=O)Nc4ccc5[nH]c(*)nc5c4)cc3)cc2)c1
*c1cccc(NC(=O)c2ccc(OCCOCCOc3ccc(C(=O)Nc4ccc5[nH]c(*)nc5c4)cc3)cc2)c1
*c1cccc(NC(=O)c2ccc(OCCOc3ccc(C(=O)Nc4ccc5[nH]c(*)nc5c4)cc3)cc2)c1
*c1cccc(NC(=O)c2cccc(C(=O)Nc3cccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)c3)c2)c1
*c1cccc(OCCCCCCCCCCCCOc2cccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(OCCCCCCCCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(OCCCCCCCCCCCOC(=O)c2ccc(C(=O)OCCCCCCCCCCCOc3cccc(-c4nnc(*)s4)c3)cc2)c1
*c1cccc(OCCCCCCCCCCCOC(=O)c2cccc(C(=O)OCCCCCCCCCCCOc3cccc(-c4nnc(*)s4)c3)c2)c1
*c1cccc(OCCCCCCCCCCOc2cccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(OCCCCCCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(OCCCCCCCCCOc2cccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(OCCCCCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(OCCCCCCCCOc2cccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(OCCCCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(OCCCCCCOc2cccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(OCCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(OCCCCCOc2cccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(OCCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(OCCCCOc2cccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(OCCCCOc2cccc(N3C(=O)c4ccc(-c5cccc6c5C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(OCCCOc2cccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(OP(=O)(Oc2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c2)c2ccccc2)c1
*c1cccc(Oc2ccc(-c3ccc(Oc4cccc(-n5c(=O)c6cc7c(=O)n(*)c(=O)c7cc6c5=O)c4)cc3)cc2)c1
*c1cccc(Oc2ccc(C#N)c(Oc3cccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)c3)c2)c1
*c1cccc(Oc2ccc(C(c3ccc(Oc4cccc(-n5c(=O)c6cc7c(=O)n(*)c(=O)c7cc6c5=O)c4)cc3)(C(F)(F)F)C(F)(F)F)cc2)c1
*c1cccc(Oc2ccc(Oc3cccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)c3)cc2)c1
*c1cccc(Oc2ccc(P(=O)(c3ccccc3)c3ccccc3)c(Oc3cccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)c3)c2)c1
*c1cccc(Oc2ccc(P(=O)(c3ccccc3)c3ccccc3)c(Oc3cccc(N4C(=O)c5ccc(-c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)c3)c2)c1
*c1cccc(Oc2ccc(P(=O)(c3ccccc3)c3ccccc3)c(Oc3cccc(N4C(=O)c5ccc(Oc6ccc(-c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)c3)c2)c1
*c1cccc(Oc2ccc(P(=O)(c3ccccc3)c3ccccc3)c(Oc3cccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)c3)c2)c1
*c1cccc(Oc2ccc(S(=O)(=O)c3ccc(Oc4cccc(-n5c(=O)c6cc7c(=O)n(*)c(=O)c7cc6c5=O)c4)cc3)cc2)c1
*c1cccc(Oc2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c2)c1
*c1cccc(Oc2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(Oc2cccc(Oc3cccc(-n4c(=O)c5cc6c(=O)n(*)c(=O)c6cc5c4=O)c3)c2)c1
*c1cccc(Oc2cccc(Oc3cccc(N4C(=O)C5C6C=CC(C7C(=O)N(*)C(=O)C67)C5C4=O)c3)c2)c1
*c1cccc(Oc2cccc(Oc3cccc(N4C(=O)c5c(c(-c6ccccc6)c(-c6ccc(-c7c(-c8ccccc8)c8c(c(-c9ccccc9)c7-c7ccccc7)C(=O)N(*)C8=O)cc6)c(-c6ccccc6)c5-c5ccccc5)C4=O)c3)c2)c1
*c1cccc(Oc2cccc(Oc3cccc(N4C(=O)c5c(c(-c6ccccc6)c(-c6ccc(Oc7ccc(-c8c(-c9ccccc9)c9c(c(-c%10ccccc%10)c8-c8ccccc8)C(=O)N(*)C9=O)cc7)cc6)c(-c6ccccc6)c5-c5ccccc5)C4=O)c3)c2)c1
*c1cccc(Oc2cccc(Oc3cccc(N4C(=O)c5c(c(-c6ccccc6)c(-c6ccc(Oc7ccc(Oc8ccc(-c9c(-c%10ccccc%10)c%10c(c(-c%11ccccc%11)c9-c9ccccc9)C(=O)N(*)C%10=O)cc8)cc7)cc6)c(-c6ccccc6)c5-c5ccccc5)C4=O)c3)c2)c1
*c1cccc(Oc2cccc(Oc3cccc(N4C(=O)c5ccc(-c6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)c3)c2)c1
*c1cccc(Oc2cccc(Oc3cccc(N4C(=O)c5ccc(-c6cccc7c6C(=O)N(*)C7=O)cc5C4=O)c3)c2)c1
*c1cccc(Oc2cccc(Oc3cccc(N4C(=O)c5ccc(Oc6ccc(Sc7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7)cc6)cc5C4=O)c3)c2)c1
*c1cccc(Oc2cccc(Oc3cccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)c3)c2)c1
*c1cccc(Oc2cccc(Oc3cccc(N4C(=O)c5ccc(Oc6ccc7c(c6)C(=O)N(*)C7=O)cc5C4=O)c3)c2C#N)c1
*c1cccc(Oc2cccc(Oc3cccc(Oc4cccc(Oc5cccc(N6C(=O)c7ccc(Oc8ccc9c(c8)C(=O)N(*)C9=O)cc7C6=O)c5)c4C#N)c3)c2C#N)c1
*c1cccc(P(=O)(c2ccc(C(F)(F)F)cc2)c2cccc(-n3c(=O)c4c(-c5cc(C(F)(F)F)cc(C(F)(F)F)c5)c5c(=O)n(*)c(=O)c5c(-c5cc(C(F)(F)F)cc(C(F)(F)F)c5)c4c3=O)c2)c1
*c1cccc(P(=O)(c2ccc(C(F)(F)F)cc2)c2cccc(-n3c(=O)c4c(-c5ccc(C(F)(F)F)cc5)c5c(=O)n(*)c(=O)c5c(-c5ccc(C(F)(F)F)cc5)c4c3=O)c2)c1
*c1cccc(P(=O)(c2ccc(C(F)(F)F)cc2)c2cccc(-n3c(=O)c4c(-c5ccccc5)c5c(=O)n(*)c(=O)c5c(-c5ccccc5)c4c3=O)c2)c1
*c1cccc(P(=O)(c2ccc(C(F)(F)F)cc2)c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5c(-c5cc(C(F)(F)F)cc(C(F)(F)F)c5)c4c3=O)c2)c1
*c1cccc(P(=O)(c2ccc(C(F)(F)F)cc2)c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5c(-c5ccc(C(F)(F)F)cc5)c4c3=O)c2)c1
*c1cccc(P(=O)(c2ccc(C(F)(F)F)cc2)c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5c(-c5ccccc5)c4c3=O)c2)c1
*c1cccc(P(=O)(c2ccc(C(F)(F)F)cc2)c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c2)c1
*c1cccc(P(=O)(c2ccc(C(F)(F)F)cc2)c2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(P(=O)(c2ccc(Oc3ccc(C45CC6CC(CC(C6)C4)C5)cc3)cc2)c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c2)c1
*c1cccc(P(=O)(c2ccc(Oc3ccc(C45CC6CC(CC(C6)C4)C5)cc3)cc2)c2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(P(=O)(c2cccc(-n3c(=O)c4c(-c5cc(C(F)(F)F)cc(C(F)(F)F)c5)c5c(=O)n(*)c(=O)c5c(-c5cc(C(F)(F)F)cc(C(F)(F)F)c5)c4c3=O)c2)c2cc(C(F)(F)F)cc(C(F)(F)F)c2)c1
*c1cccc(P(=O)(c2cccc(-n3c(=O)c4c(-c5ccc(C(F)(F)F)cc5)c5c(=O)n(*)c(=O)c5c(-c5ccc(C(F)(F)F)cc5)c4c3=O)c2)c2cc(C(F)(F)F)cc(C(F)(F)F)c2)c1
*c1cccc(P(=O)(c2cccc(-n3c(=O)c4c(-c5ccccc5)c5c(=O)n(*)c(=O)c5c(-c5ccccc5)c4c3=O)c2)c2cc(C(F)(F)F)cc(C(F)(F)F)c2)c1
*c1cccc(P(=O)(c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5c(-c5cc(C(F)(F)F)cc(C(F)(F)F)c5)c4c3=O)c2)c2cc(C(F)(F)F)cc(C(F)(F)F)c2)c1
*c1cccc(P(=O)(c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5c(-c5ccc(C(F)(F)F)cc5)c4c3=O)c2)c2cc(C(F)(F)F)cc(C(F)(F)F)c2)c1
*c1cccc(P(=O)(c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5c(-c5ccccc5)c4c3=O)c2)c2cc(C(F)(F)F)cc(C(F)(F)F)c2)c1
*c1cccc(P(=O)(c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c2)c2cc(C(F)(F)F)cc(C(F)(F)F)c2)c1
*c1cccc(P(=O)(c2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c2cc(C(F)(F)F)cc(C(F)(F)F)c2)c1
*c1cccc(P(=O)(c2ccccc2)c2cccc(-n3c(=O)c4c(-c5cc(C(F)(F)F)cc(C(F)(F)F)c5)c5c(=O)n(*)c(=O)c5c(-c5cc(C(F)(F)F)cc(C(F)(F)F)c5)c4c3=O)c2)c1
*c1cccc(P(=O)(c2ccccc2)c2cccc(-n3c(=O)c4c(-c5ccc(C(F)(F)F)cc5)c5c(=O)n(*)c(=O)c5c(-c5ccc(C(F)(F)F)cc5)c4c3=O)c2)c1
*c1cccc(P(=O)(c2ccccc2)c2cccc(-n3c(=O)c4c(-c5ccccc5)c5c(=O)n(*)c(=O)c5c(-c5ccccc5)c4c3=O)c2)c1
*c1cccc(P(=O)(c2ccccc2)c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5c(-c5cc(C(F)(F)F)cc(C(F)(F)F)c5)c4c3=O)c2)c1
*c1cccc(P(=O)(c2ccccc2)c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5c(-c5ccc(C(F)(F)F)cc5)c4c3=O)c2)c1
*c1cccc(P(=O)(c2ccccc2)c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5c(-c5ccccc5)c4c3=O)c2)c1
*c1cccc(P(=O)(c2ccccc2)c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c2)c1
*c1cccc(P(=O)(c2ccccc2)c2cccc(N3C(=O)c4ccc(NC(=O)OCCOc5c(Br)cc(C(C)(C)c6cc(Br)c(OCCOC(=O)Nc7ccc8c(c7)C(=O)N(*)C8=O)c(Br)c6)cc5Br)cc4C3=O)c2)c1
*c1cccc(P(=O)(c2ccccc2)c2cccc(N3C(=O)c4ccc(NC(=O)OCCOc5ccc(C(C)(C)c6ccc(OCCOC(=O)Nc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)c2)c1
*c1cccc(P(=O)(c2ccccc2)c2cccc(N3C(=O)c4ccc(NC(=O)OCCOc5ccc(C(c6ccc(OCCOC(=O)Nc7ccc8c(c7)C(=O)N(*)C8=O)cc6)(C(F)(F)F)C(F)(F)F)cc5)cc4C3=O)c2)c1
*c1cccc(P(=O)(c2ccccc2)c2cccc(N3C(=O)c4ccc(NC(=O)OCCOc5ccc(P(=O)(c6ccccc6)c6ccc(OCCOC(=O)Nc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)c2)c1
*c1cccc(P(=O)(c2ccccc2)c2cccc(N3C(=O)c4ccc(Oc5ccc(C(C)(C)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)c2)c1
*c1cccc(P(=O)(c2ccccc2)c2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1cccc(P(C)(=O)c2cccc(N3C(=O)c4ccc(NC(=O)OCCOc5c(Br)cc(C(C)(C)c6cc(Br)c(OCCOC(=O)Nc7ccc8c(c7)C(=O)N(*)C8=O)c(Br)c6)cc5Br)cc4C3=O)c2)c1
*c1cccc(P(C)(=O)c2cccc(N3C(=O)c4ccc(NC(=O)OCCOc5ccc(C(C)(C)c6ccc(OCCOC(=O)Nc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)c2)c1
*c1cccc(P(C)(=O)c2cccc(N3C(=O)c4ccc(NC(=O)OCCOc5ccc(C(c6ccc(OCCOC(=O)Nc7ccc8c(c7)C(=O)N(*)C8=O)cc6)(C(F)(F)F)C(F)(F)F)cc5)cc4C3=O)c2)c1
*c1cccc(P(C)(=O)c2cccc(N3C(=O)c4ccc(NC(=O)OCCOc5ccc(P(=O)(c6ccccc6)c6ccc(OCCOC(=O)Nc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)c2)c1
*c1cccc(P(C)(=O)c2cccc(N3C(=O)c4ccc(Oc5ccc(C(C)(C)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)c2)c1
*c1cccc(S(=O)(=O)NCCCCCCNS(*)(=O)=O)c1
*c1cccc(S(=O)(=O)NCCCCCNS(*)(=O)=O)c1
*c1cccc(S(=O)(=O)NCCCCNS(*)(=O)=O)c1
*c1cccc(S(=O)(=O)NCCCNS(*)(=O)=O)c1
*c1cccc(S(=O)(=O)NCCNS(*)(=O)=O)c1
*c1cccc(S(=O)(=O)c2cccc(-n3c(=O)c4cc5c(=O)n(*)c(=O)c5cc4c3=O)c2)c1
*c1cccc(S(=O)(=O)c2cccc(N3C(=O)c4ccc(Oc5ccc(S(=O)(=O)c6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)c2)c1
*c1cccc(S(=O)(=O)c2cccc(N3C(=O)c4ccc(Oc5ccc(Sc6ccc(Oc7ccc8c(c7)C(=O)N(*)C8=O)cc6)cc5)cc4C3=O)c2)c1
*c1cccc(S(=O)(=O)c2cccc(N3C(=O)c4ccc(Oc5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)c2)c1
*c1ccccc1C(=O)Oc1ccc2c(*)c3ccc(=O)cc-3oc2c1
*c1ccccc1CCc1ccc(N2C(=O)C(=O)N(c3ccc(C)c(N4C(=O)C(=O)N(*)C4=O)c3)C2=O)cc1
*c1cn(*)c2ccccc12
*c1cnc(C2C(c3ccccc3)C(*)C2c2ccccc2)cn1
*c1nc(-c2ccccc2)nc(N(C)CCCCCCN(*)C)n1
*c1nc(-c2ccccc2)nc(N(CCCCCCCCCCN(*)c2ccccc2)c2ccccc2)n1
*c1nc(-c2ccccc2)nc(N(CCCCCCN(*)c2ccccc2)c2ccccc2)n1
*c1nc(-c2ccccc2)nc(N(CCCCN(*)c2ccccc2)c2ccccc2)n1
*c1nc(-c2ccccc2)nc(N(c2ccccc2)c2ncnc(N(*)c3ccccc3)n2)n1
*c1nc(C(F)(F)C(F)(F)C(F)(F)F)nc(C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(*)(F)F)n1
*c1nc(C)nc(N(CCCCCCCCCCN(*)c2ccccc2)c2ccccc2)n1
*c1nc(C)nc(N(CCCCCCN(*)c2ccccc2)c2ccccc2)n1
*c1nc(C)nc(N(CCCCN(*)c2ccccc2)c2ccccc2)n1
*c1nc2cc(S(=O)(=O)c3ccc4oc(C5CCC(*)CC5)nc4c3)ccc2o1
*c1ncnc(N(CCCCCCCCCCN(*)c2ccccc2)c2ccccc2)n1
*c1ncnc(N(CCCCCCN(*)c2ccccc2)c2ccccc2)n1
*c1ncnc(N(CCCCN(*)c2ccccc2)c2ccccc2)n1
*c1nnc(-c2cc(CCCCCCCC)c(-c3ccc(-c4sc(-c5nnc(-c6cc(OCCCCCCCC)c(*)cc6OCCCCCCCC)o5)cc4CCCCCCCC)s3)s2)o1
*c1nnc(-c2cc(CCCCCCCCCC)c(-c3sc(-c4nnc(-c5cc(OCCCCCCCC)c(*)cc5OCCCCCCCC)o4)cc3CCCCCCCCCC)s2)o1
*c1nnc(-c2ccc(Oc3ccc(/C=C/c4ccc(Oc5ccc(*)c6ccccc56)cc4)c4ccccc34)c3ccccc23)o1
*c1nnc(-c2ccc(Oc3ccc(/C=C/c4ccc(Oc5ccc(*)c6ccccc56)cc4)cc3)c3ccccc23)o1
*c1nnc(-c2ccc(Oc3ccc(C(c4ccc(Oc5ccc(*)c6ccccc56)cc4)(C(F)(F)F)C(F)(F)F)cc3)c3ccccc23)o1
*c1nnc(-c2sc(-c3nnc(-c4cc(OCCCCCCCC)c(*)cc4OCCCCCCCC)o3)cc2CCCCCCCC)o1
*c1sc(*)c(CCCCCCN(CC)c2ccc(/N=N/c3ccc([N+](=O)[O-])cc3)cc2)c1CCCCCC

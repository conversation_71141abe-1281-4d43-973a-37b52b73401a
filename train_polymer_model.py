"""
Training script for NEURIPs Polymer Competition
"""

import os
import sys
from pathlib import Path

# Set working directory
WORK_DIR = Path("/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/Kaggle/NEURIPs_polymer")
os.chdir(WORK_DIR)
sys.path.insert(0, str(WORK_DIR))

from polymer_competition_solution import PolymerPropertyPredictor
import pandas as pd

def main():
    """Main training pipeline"""
    print("=== NEURIPs Polymer Property Prediction - Training ===")
    print(f"Working directory: {WORK_DIR}")
    
    # Initialize predictor
    predictor = PolymerPropertyPredictor(work_dir=WORK_DIR)
    
    # Load and integrate data
    train_df, datasets = predictor.load_and_integrate_data()
    if train_df.empty:
        print("No training data found. Please ensure data files are in the correct location.")
        return
    
    integrated_data = predictor.integrate_supplementary_data(train_df, datasets)
    
    # Prepare multi-task data
    X, y = predictor.prepare_multitask_data(integrated_data)
    
    # Create and train models with optimization
    predictor.create_multitask_models()
    # Use fewer trials for faster training, increase for better performance
    predictor.train_models(X, y, cv_folds=5, n_trials=50)
    
    # Save trained model
    predictor.save_model('polymer_model.pkl')
    
    print("Training completed successfully!")
    print(f"Model saved to: {WORK_DIR / 'polymer_model.pkl'}")

if __name__ == "__main__":
    main()
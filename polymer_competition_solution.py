"""
NEURIPs Open Polymer Prediction 2025 - Complete Solution
Multi-task learning approach for polymer property prediction from SMILES
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Core ML libraries
from sklearn.model_selection import KFold, StratifiedKFold
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_absolute_error
import joblib

# XGBoost and optimization
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    print("XGBoost not available - falling back to sklearn models")
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    XGBOOST_AVAILABLE = False

try:
    import optuna
    OPTUNA_AVAILABLE = True
except ImportError:
    print("Optuna not available - using default hyperparameters")
    OPTUNA_AVAILABLE = False

# Molecular processing
try:
    from rdkit import Chem
    from rdkit.Chem import Descriptors, rdMolDescriptors, <PERSON>rippen
    RDKIT_AVAILABLE = True
except ImportError:
    print("RDKit not available - using fallback features")
    RDKIT_AVAILABLE = False

import os
import json
from pathlib import Path
import time

# Set working directory
WORK_DIR = Path("/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/Kaggle/NEURIPs_polymer")
os.chdir(WORK_DIR)

# GPU detection
def detect_gpu():
    """Detect if GPU is available for XGBoost"""
    try:
        import GPUtil
        gpus = GPUtil.getGPUs()
        if gpus:
            print(f"GPU detected: {gpus[0].name}")
            return True
    except:
        pass
    
    # Fallback: try CUDA detection
    try:
        import torch
        if torch.cuda.is_available():
            print(f"CUDA GPU detected: {torch.cuda.get_device_name(0)}")
            return True
    except:
        pass
    
    print("No GPU detected, using CPU")
    return False

GPU_AVAILABLE = detect_gpu()

class PolymerPropertyPredictor:
    """Complete solution for polymer property prediction competition"""
    
    def __init__(self, work_dir=None):
        self.work_dir = Path(work_dir) if work_dir else WORK_DIR
        self.target_properties = ['Tg', 'FFV', 'Tc', 'Density', 'Rg']
        self.models = {}
        self.scalers = {}
        self.feature_columns = None
        self.property_weights = None
    
    def load_and_integrate_data(self):
        """Load and integrate all available datasets"""
        print("Loading and integrating datasets...")
        print(f"Working directory: {self.work_dir}")
        
        # Load main training data
        train_path = self.work_dir / 'train.csv'
        if train_path.exists():
            train_df = pd.read_csv(train_path)
            print(f"Main training data: {train_df.shape}")
        else:
            print(f"Warning: {train_path} not found")
            return pd.DataFrame(), {}
        
        # Load supplementary datasets
        datasets = {}
        supplement_dir = self.work_dir / 'train_supplement'
        if supplement_dir.exists():
            for i in range(1, 5):
                file_path = supplement_dir / f'dataset{i}.csv'
                if file_path.exists():
                    datasets[f'dataset{i}'] = pd.read_csv(file_path)
                    print(f"Dataset {i}: {datasets[f'dataset{i}'].shape}")
                else:
                    print(f"Dataset {i} not found at {file_path}")
        else:
            print(f"Supplement directory not found: {supplement_dir}")
        
        return train_df, datasets
    
    def integrate_supplementary_data(self, train_df, datasets):
        """Integrate supplementary datasets with main training data"""
        print("Integrating supplementary datasets...")
        
        integrated_data = train_df.copy()
        # Track data sources for validation
        integrated_data['data_source'] = 'main'
        
        # Process each supplementary dataset
        for dataset_name, dataset in datasets.items():
            print(f"Processing {dataset_name}...")
            if 'SMILES' in dataset.columns:
                # Add source tracking
                dataset = dataset.copy()
                dataset['data_source'] = dataset_name
                
                # Identify property columns (non-SMILES, non-ID columns)
                property_cols = [col for col in dataset.columns 
                               if col not in ['SMILES', 'id', 'data_source']]
                
                if property_cols:
                    print(f"  Found properties: {property_cols}")
                    
                    # Merge with main data, keeping additional examples
                    for prop in property_cols:
                        if prop in self.target_properties or prop.replace('_mean', '') in self.target_properties:
                            # Standardize column names
                            if '_mean' in prop:
                                prop_standard = prop.replace('_mean', '')
                                dataset = dataset.rename(columns={prop: prop_standard})
                                prop = prop_standard
                            
                            # Add rows with this property to integrated dataset
                            supplement_rows = dataset[['SMILES', prop, 'data_source']].dropna()
                            
                            # Avoid duplicates by checking SMILES
                            existing_smiles = set(integrated_data['SMILES'])
                            new_rows = supplement_rows[~supplement_rows['SMILES'].isin(existing_smiles)]
                            
                            if len(new_rows) > 0:
                                # Create full rows with NaN for other properties
                                full_rows = pd.DataFrame({
                                    'SMILES': new_rows['SMILES'],
                                    'data_source': new_rows['data_source']
                                })
                                
                                # Add the specific property
                                full_rows[prop] = new_rows[prop].values
                                
                                # Add NaN for other properties
                                for other_prop in self.target_properties:
                                    if other_prop not in full_rows.columns:
                                        full_rows[other_prop] = np.nan
                                
                                # Add synthetic IDs
                                max_id = integrated_data['id'].max() if 'id' in integrated_data.columns else 0
                                full_rows['id'] = range(max_id + 1, max_id + 1 + len(full_rows))
                                
                                integrated_data = pd.concat([integrated_data, full_rows], ignore_index=True)
                                print(f"  Added {len(new_rows)} new examples for {prop}")
        
        print(f"Final integrated dataset: {integrated_data.shape}")
        return integrated_data 
   
    def extract_molecular_features(self, smiles_list):
        """Extract comprehensive molecular features from SMILES"""
        print("Extracting molecular features...")
        
        features_list = []
        for smiles in smiles_list:
            features = {}
            
            if RDKIT_AVAILABLE and smiles and pd.notna(smiles):
                try:
                    mol = Chem.MolFromSmiles(smiles)
                    if mol is not None:
                        # Basic molecular descriptors with individual error handling
                        try:
                            features['mol_weight'] = Descriptors.MolWt(mol)
                        except:
                            features['mol_weight'] = 0
                        
                        try:
                            features['logp'] = Crippen.MolLogP(mol)
                        except:
                            features['logp'] = 0
                        
                        try:
                            features['num_atoms'] = mol.GetNumAtoms()
                        except:
                            features['num_atoms'] = 0
                        
                        try:
                            features['num_bonds'] = mol.GetNumBonds()
                        except:
                            features['num_bonds'] = 0
                        
                        try:
                            features['num_rings'] = rdMolDescriptors.CalcNumRings(mol)
                        except:
                            features['num_rings'] = 0
                        
                        try:
                            features['aromatic_rings'] = rdMolDescriptors.CalcNumAromaticRings(mol)
                        except:
                            features['aromatic_rings'] = 0
                        
                        try:
                            features['tpsa'] = rdMolDescriptors.CalcTPSA(mol)
                        except:
                            features['tpsa'] = 0
                        
                        # Topological descriptors with correct imports
                        try:
                            features['bertz_ct'] = Descriptors.BertzCT(mol)
                        except:
                            features['bertz_ct'] = 0
                        
                        try:
                            features['balaban_j'] = rdMolDescriptors.BalabanJ(mol)
                        except:
                            features['balaban_j'] = 0
                        
                        try:
                            features['kappa1'] = rdMolDescriptors.Kappa1(mol)
                        except:
                            features['kappa1'] = 0
                        
                        try:
                            features['kappa2'] = rdMolDescriptors.Kappa2(mol)
                        except:
                            features['kappa2'] = 0
                        
                        try:
                            features['kappa3'] = rdMolDescriptors.Kappa3(mol)
                        except:
                            features['kappa3'] = 0
                        
                        # Chi descriptors
                        try:
                            features['chi0v'] = rdMolDescriptors.Chi0v(mol)
                        except:
                            features['chi0v'] = 0
                        
                        try:
                            features['chi1v'] = rdMolDescriptors.Chi1v(mol)
                        except:
                            features['chi1v'] = 0
                        
                        try:
                            features['chi2v'] = rdMolDescriptors.Chi2v(mol)
                        except:
                            features['chi2v'] = 0
                        
                        try:
                            features['chi3v'] = rdMolDescriptors.Chi3v(mol)
                        except:
                            features['chi3v'] = 0
                        
                        try:
                            features['chi4v'] = rdMolDescriptors.Chi4v(mol)
                        except:
                            features['chi4v'] = 0
                        
                        # Additional descriptors
                        try:
                            features['fraction_csp3'] = rdMolDescriptors.CalcFractionCSP3(mol)
                        except:
                            features['fraction_csp3'] = 0
                        
                        try:
                            features['num_heteroatoms'] = rdMolDescriptors.CalcNumHeteroatoms(mol)
                        except:
                            features['num_heteroatoms'] = 0
                        
                        try:
                            features['num_rotatable_bonds'] = rdMolDescriptors.CalcNumRotatableBonds(mol)
                        except:
                            features['num_rotatable_bonds'] = 0
                        
                        try:
                            features['num_hbd'] = rdMolDescriptors.CalcNumHBD(mol)
                        except:
                            features['num_hbd'] = 0
                        
                        try:
                            features['num_hba'] = rdMolDescriptors.CalcNumHBA(mol)
                        except:
                            features['num_hba'] = 0
                    else:
                        # Invalid SMILES - use default values
                        for key in ['mol_weight', 'logp', 'num_atoms', 'num_bonds', 'num_rings', 
                                   'aromatic_rings', 'tpsa', 'bertz_ct', 'balaban_j', 'kappa1', 
                                   'kappa2', 'kappa3', 'chi0v', 'chi1v', 'chi2v', 'chi3v', 'chi4v', 
                                   'fraction_csp3', 'num_heteroatoms', 'num_rotatable_bonds',
                                   'num_hbd', 'num_hba']:
                            features[key] = 0
                except Exception as e:
                    print(f"Error processing SMILES {smiles}: {e}")
                    # Use default values for failed cases
                    for key in ['mol_weight', 'logp', 'num_atoms', 'num_bonds', 'num_rings', 
                               'aromatic_rings', 'tpsa', 'bertz_ct', 'balaban_j', 'kappa1', 
                               'kappa2', 'kappa3', 'chi0v', 'chi1v', 'chi2v', 'chi3v', 'chi4v', 
                               'fraction_csp3', 'num_heteroatoms', 'num_rotatable_bonds',
                               'num_hbd', 'num_hba']:
                        features[key] = 0
            else:
                # Fallback features when RDKit not available
                features = self.extract_fallback_features(smiles)
            
            features_list.append(features)
        
        features_df = pd.DataFrame(features_list)
        print(f"Extracted {features_df.shape[1]} molecular features")
        return features_df
    
    def extract_fallback_features(self, smiles):
        """Extract basic features when RDKit is not available"""
        features = {}
        
        if smiles and pd.notna(smiles):
            # Basic string-based features
            features['smiles_length'] = len(smiles)
            features['num_carbons'] = smiles.count('C')
            features['num_nitrogens'] = smiles.count('N')
            features['num_oxygens'] = smiles.count('O')
            features['num_sulfurs'] = smiles.count('S')
            features['num_rings_approx'] = smiles.count('1') + smiles.count('2')
            features['num_branches'] = smiles.count('(')
            features['num_double_bonds'] = smiles.count('=')
            features['num_triple_bonds'] = smiles.count('#')
            features['aromatic_carbons'] = smiles.count('c')
            features['aromatic_nitrogens'] = smiles.count('n')
        else:
            # Default values for missing SMILES
            for key in ['smiles_length', 'num_carbons', 'num_nitrogens', 'num_oxygens', 
                       'num_sulfurs', 'num_rings_approx', 'num_branches', 'num_double_bonds',
                       'num_triple_bonds', 'aromatic_carbons', 'aromatic_nitrogens']:
                features[key] = 0
        
        return features
    
    def prepare_multitask_data(self, integrated_data):
        """Prepare data for multi-task learning"""
        print("Preparing multi-task training data...")
        
        # Extract molecular features
        features_df = self.extract_molecular_features(integrated_data['SMILES'])
        
        # Combine with any additional features
        X = features_df.copy()
        self.feature_columns = list(X.columns)
        
        # Prepare target matrix
        y = integrated_data[self.target_properties].copy()
        
        # Calculate property statistics for weighting
        self.calculate_property_weights(y)
        
        print(f"Feature matrix shape: {X.shape}")
        print(f"Target matrix shape: {y.shape}")
        
        # Print missing value statistics
        for prop in self.target_properties:
            missing_pct = (y[prop].isna().sum() / len(y)) * 100
            available_count = y[prop].notna().sum()
            print(f"{prop}: {available_count} samples ({100-missing_pct:.1f}% available)")
        
        return X, y    

    def calculate_property_weights(self, y):
        """Calculate weights for weighted MAE metric"""
        print("Calculating property weights for wMAE...")
        
        weights = {}
        total_tasks = len(self.target_properties)
        
        for prop in self.target_properties:
            # Count available samples
            available_data = y[prop].dropna()
            n_samples = len(available_data)
            
            if n_samples > 0:
                # Estimate range (using IQR to be robust to outliers)
                q75, q25 = np.percentile(available_data, [75, 25])
                prop_range = q75 - q25
                if prop_range == 0:
                    prop_range = available_data.std()
                if prop_range == 0:
                    prop_range = 1.0
                
                # Calculate weight: inverse sqrt scaling / range
                weight = (1.0 / np.sqrt(n_samples)) / prop_range
                weights[prop] = weight
                print(f"{prop}: {n_samples} samples, range={prop_range:.4f}, weight={weight:.6f}")
            else:
                weights[prop] = 0.0
                print(f"{prop}: No samples available")
        
        # Normalize weights to sum to total_tasks
        total_weight = sum(weights.values())
        if total_weight > 0:
            for prop in weights:
                weights[prop] = (weights[prop] / total_weight) * total_tasks
        
        self.property_weights = weights
        print(f"Normalized weights: {weights}")
    
    def create_multitask_models(self):
        """Create models for each property with shared preprocessing"""
        print("Creating multi-task model framework...")
        
        # Initialize models dictionary
        for prop in self.target_properties:
            print(f"Setting up model for {prop}...")
            self.models[prop] = None  # Will be created during optimization
            self.scalers[prop] = RobustScaler()
    
    def optimize_hyperparameters(self, X, y, prop, n_trials=100):
        """Optimize hyperparameters using Optuna"""
        if not OPTUNA_AVAILABLE:
            print(f"Optuna not available, using default hyperparameters for {prop}")
            return self.get_default_params(prop)
        
        print(f"Optimizing hyperparameters for {prop} with {n_trials} trials...")
        
        def objective(trial):
            # Get samples with this property available
            mask = y[prop].notna()
            if mask.sum() < 20:
                return float('inf')
            
            X_prop = X[mask]
            y_prop = y.loc[mask, prop]
            
            # Suggest hyperparameters
            if XGBOOST_AVAILABLE:
                params = {
                    'objective': 'reg:squarederror',
                    'eval_metric': 'mae',
                    'tree_method': 'gpu_hist' if GPU_AVAILABLE else 'hist',
                    'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
                    'max_depth': trial.suggest_int('max_depth', 3, 10),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                    'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                    'reg_alpha': trial.suggest_float('reg_alpha', 0, 10),
                    'reg_lambda': trial.suggest_float('reg_lambda', 0, 10),
                    'random_state': 42
                }
                if GPU_AVAILABLE:
                    params['gpu_id'] = 0
            else:
                # Fallback to sklearn models
                params = {
                    'n_estimators': trial.suggest_int('n_estimators', 50, 300),
                    'max_depth': trial.suggest_int('max_depth', 3, 15),
                    'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                    'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                    'random_state': 42
                }
            
            # Cross-validation
            kf = KFold(n_splits=3, shuffle=True, random_state=42)
            cv_scores = []
            early_stopping_count = []
            
            for train_idx, val_idx in kf.split(X_prop):
                X_train, X_val = X_prop.iloc[train_idx], X_prop.iloc[val_idx]
                y_train, y_val = y_prop.iloc[train_idx], y_prop.iloc[val_idx]
                
                # Scale features
                scaler = RobustScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_val_scaled = scaler.transform(X_val)
                
                # Train model
                if XGBOOST_AVAILABLE:
                    model = xgb.XGBRegressor(**params)
                    # Use callbacks for early stopping in newer XGBoost versions
                    try:
                        model.fit(
                            X_train_scaled, y_train,
                            eval_set=[(X_val_scaled, y_val)],
                            callbacks=[xgb.callback.EarlyStopping(rounds=50, save_best=True)],
                            verbose=False
                        )
                    except TypeError:
                        # Fallback for older XGBoost versions
                        try:
                            model.fit(
                                X_train_scaled, y_train,
                                eval_set=[(X_val_scaled, y_val)],
                                early_stopping_rounds=50,
                                verbose=False
                            )
                        except:
                            # If early stopping fails, train without it
                            model.fit(X_train_scaled, y_train)
                    
                    # Track early stopping iterations
                    if hasattr(model, 'best_iteration') and model.best_iteration is not None:
                        early_stopping_count.append(model.best_iteration)
                    elif hasattr(model, 'best_ntree_limit') and model.best_ntree_limit > 0:
                        early_stopping_count.append(model.best_ntree_limit)
                else:
                    from sklearn.ensemble import RandomForestRegressor
                    model = RandomForestRegressor(**params)
                    model.fit(X_train_scaled, y_train)
                
                # Validate
                val_pred = model.predict(X_val_scaled)
                mae = mean_absolute_error(y_val, val_pred)
                cv_scores.append(mae)
            
            # Store mean early stopping count as trial attribute
            if early_stopping_count:
                trial.set_user_attr('early_stopping_mean', np.mean(early_stopping_count))
            
            return np.mean(cv_scores)
        
        # Run optimization
        study = optuna.create_study(direction='minimize')
        study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
        
        print(f"Best parameters for {prop}: {study.best_params}")
        print(f"Best CV MAE for {prop}: {study.best_value:.4f}")
        
        # Get best trial and its early stopping mean if available
        best_params = study.best_params.copy()
        best_trial = study.best_trial
        if 'early_stopping_mean' in best_trial.user_attrs:
            best_params['early_stopping_mean'] = best_trial.user_attrs['early_stopping_mean']
            print(f"Mean early stopping iterations: {best_params['early_stopping_mean']:.1f}")
        
        return best_params
    
    def get_default_params(self, prop):
        """Get default parameters when Optuna is not available"""
        if XGBOOST_AVAILABLE:
            params = {
                'objective': 'reg:squarederror',
                'eval_metric': 'mae',
                'tree_method': 'gpu_hist' if GPU_AVAILABLE else 'hist',
                'n_estimators': 500,
                'max_depth': 6,
                'learning_rate': 0.1,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'reg_alpha': 1,
                'reg_lambda': 1,
                'random_state': 42
            }
            if GPU_AVAILABLE:
                params['gpu_id'] = 0
            return params
        else:
            return {
                'n_estimators': 200,
                'max_depth': 8,
                'min_samples_split': 5,
                'min_samples_leaf': 2,
                'random_state': 42
            }
    
    def weighted_mae_score(self, y_true, y_pred):
        """Calculate weighted MAE score matching competition metric"""
        total_error = 0.0
        
        for prop in self.target_properties:
            if prop in y_true.columns and prop in y_pred.columns:
                # Get valid predictions (non-NaN in both true and pred)
                mask = y_true[prop].notna() & y_pred[prop].notna()
                if mask.sum() > 0:
                    prop_mae = mean_absolute_error(y_true.loc[mask, prop], y_pred.loc[mask, prop])
                    weight = self.property_weights.get(prop, 0.0)
                    weighted_error = weight * prop_mae
                    total_error += weighted_error
                    print(f"{prop}: MAE={prop_mae:.4f}, weight={weight:.4f}, weighted={weighted_error:.4f}")
        
        return total_error   
 
    def train_models(self, X, y, cv_folds=5, n_trials=100):
        """Train models with hyperparameter optimization and full retraining"""
        print("Training multi-task models with XGBoost + Optuna optimization...")
        
        # Step 1: Optimize hyperparameters for each property
        optimized_params = {}
        for prop in self.target_properties:
            mask = y[prop].notna()
            if mask.sum() > 20:  # Need minimum samples for optimization
                print(f"\n=== Optimizing {prop} ===")
                optimized_params[prop] = self.optimize_hyperparameters(X, y, prop, n_trials)
            else:
                print(f"\n=== {prop}: Insufficient data ({mask.sum()} samples), using defaults ===")
                optimized_params[prop] = self.get_default_params(prop)
        
        # Step 2: Cross-validation with optimized parameters
        print("\n=== Cross-validation with optimized parameters ===")
        kf = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
        cv_scores = {prop: [] for prop in self.target_properties}
        
        for fold, (train_idx, val_idx) in enumerate(kf.split(X)):
            print(f"\nFold {fold + 1}/{cv_folds}")
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            fold_predictions = pd.DataFrame(index=val_idx)
            
            # Train each property model with optimized parameters
            for prop in self.target_properties:
                train_mask = y_train[prop].notna()
                val_mask = y_val[prop].notna()
                
                if train_mask.sum() > 10:
                    # Scale features
                    scaler = RobustScaler()
                    X_train_scaled = scaler.fit_transform(X_train[train_mask])
                    
                    # Create model with optimized parameters
                    if XGBOOST_AVAILABLE:
                        model = xgb.XGBRegressor(**optimized_params[prop])
                        
                        # Train with early stopping if validation data available
                        if val_mask.sum() > 0:
                            X_val_scaled = scaler.transform(X_val[val_mask])
                            try:
                                # Try new XGBoost API first
                                model.fit(
                                    X_train_scaled, y_train.loc[train_mask, prop],
                                    eval_set=[(X_val_scaled, y_val.loc[val_mask, prop])],
                                    callbacks=[xgb.callback.EarlyStopping(rounds=50, save_best=True)],
                                    verbose=False
                                )
                            except (TypeError, AttributeError):
                                # Fallback for older XGBoost versions
                                try:
                                    model.fit(
                                        X_train_scaled, y_train.loc[train_mask, prop],
                                        eval_set=[(X_val_scaled, y_val.loc[val_mask, prop])],
                                        early_stopping_rounds=50,
                                        verbose=False
                                    )
                                except:
                                    # If early stopping fails, train without it
                                    model.fit(X_train_scaled, y_train.loc[train_mask, prop])
                            
                            # Validate
                            val_pred = model.predict(X_val_scaled)
                            mae = mean_absolute_error(y_val.loc[val_mask, prop], val_pred)
                            cv_scores[prop].append(mae)
                            fold_predictions.loc[val_idx[val_mask], prop] = val_pred
                            print(f"  {prop}: {train_mask.sum()} train, {val_mask.sum()} val, MAE={mae:.4f}")
                        else:
                            # No validation data, train without early stopping
                            model.fit(X_train_scaled, y_train.loc[train_mask, prop])
                            print(f"  {prop}: {train_mask.sum()} train, 0 val samples")
                    else:
                        # Fallback to sklearn
                        from sklearn.ensemble import RandomForestRegressor
                        model = RandomForestRegressor(**optimized_params[prop])
                        model.fit(X_train_scaled, y_train.loc[train_mask, prop])
                        
                        if val_mask.sum() > 0:
                            X_val_scaled = scaler.transform(X_val[val_mask])
                            val_pred = model.predict(X_val_scaled)
                            mae = mean_absolute_error(y_val.loc[val_mask, prop], val_pred)
                            cv_scores[prop].append(mae)
                            fold_predictions.loc[val_idx[val_mask], prop] = val_pred
                            print(f"  {prop}: {train_mask.sum()} train, {val_mask.sum()} val, MAE={mae:.4f}")
                else:
                    print(f"  {prop}: Insufficient training data ({train_mask.sum()} samples)")
            
            # Calculate weighted MAE for this fold
            if len(fold_predictions.columns) > 0:
                fold_wmae = self.weighted_mae_score(y_val, fold_predictions)
                print(f"  Fold weighted MAE: {fold_wmae:.4f}")
        
        # Print CV summary
        print("\n=== Cross-validation summary ===")
        total_wmae_components = []
        for prop in self.target_properties:
            if cv_scores[prop]:
                mean_mae = np.mean(cv_scores[prop])
                std_mae = np.std(cv_scores[prop])
                weight = self.property_weights.get(prop, 0.0)
                weighted_contribution = weight * mean_mae
                total_wmae_components.append(weighted_contribution)
                print(f"{prop}: {mean_mae:.4f} ± {std_mae:.4f} (weight: {weight:.4f}, contribution: {weighted_contribution:.4f})")
        
        if total_wmae_components:
            estimated_wmae = sum(total_wmae_components)
            print(f"Estimated wMAE: {estimated_wmae:.4f}")
        
        # Step 3: Train final models on full dataset with optimized parameters
        print("\n=== Training final models on full dataset ===")
        for prop in self.target_properties:
            mask = y[prop].notna()
            if mask.sum() > 10:
                print(f"Training final {prop} model on {mask.sum()} samples...")
                
                # Scale features
                self.scalers[prop].fit(X[mask])
                X_scaled = self.scalers[prop].transform(X[mask])
                
                # Create and train final model
                if XGBOOST_AVAILABLE:
                    # For final training, use mean early stopping iterations if available
                    final_params = optimized_params[prop].copy()
                    
                    if 'early_stopping_mean' in final_params:
                        # Use mean early stopping iterations as n_estimators
                        early_stopping_mean = final_params.pop('early_stopping_mean')
                        # Add a small buffer (10%) to the mean early stopping count
                        final_params['n_estimators'] = int(early_stopping_mean * 1.1)
                        print(f"  Using {final_params['n_estimators']} estimators based on early stopping mean")
                    else:
                        # Fallback to previous approach
                        final_params['n_estimators'] = min(final_params['n_estimators'] * 2, 2000)
                        print(f"  Using {final_params['n_estimators']} estimators (2x optimization value)")
                    
                    self.models[prop] = xgb.XGBRegressor(**final_params)
                    self.models[prop].fit(X_scaled, y.loc[mask, prop])
                else:
                    # Fallback to sklearn
                    from sklearn.ensemble import RandomForestRegressor
                    self.models[prop] = RandomForestRegressor(**optimized_params[prop])
                    self.models[prop].fit(X_scaled, y.loc[mask, prop])
                
                print(f"✓ Final {prop} model trained successfully")
            else:
                print(f"✗ {prop}: Insufficient data for final training ({mask.sum()} samples)")
        
        print("\n=== Model training completed ===")
        print(f"Trained models for: {[prop for prop in self.target_properties if self.models[prop] is not None]}")
    
    def predict(self, test_smiles):
        """Make predictions for test data"""
        print("Making predictions...")
        
        # Extract features for test data
        test_features = self.extract_molecular_features(test_smiles)
        
        # Ensure feature consistency
        for col in self.feature_columns:
            if col not in test_features.columns:
                test_features[col] = 0
        test_features = test_features[self.feature_columns]
        
        # Make predictions for each property
        predictions = {}
        for prop in self.target_properties:
            if prop in self.models and prop in self.scalers:
                try:
                    # Scale features
                    X_scaled = self.scalers[prop].transform(test_features)
                    
                    # Predict
                    pred = self.models[prop].predict(X_scaled)
                    predictions[prop] = pred
                    print(f"{prop}: predictions range [{pred.min():.4f}, {pred.max():.4f}]")
                except Exception as e:
                    print(f"Error predicting {prop}: {e}")
                    predictions[prop] = np.zeros(len(test_features))
            else:
                print(f"No trained model for {prop}")
                predictions[prop] = np.zeros(len(test_features))
        
        return pd.DataFrame(predictions)
    
    def create_submission(self, test_df, predictions_df):
        """Create submission file in required format"""
        submission = pd.DataFrame()
        submission['id'] = test_df['id']
        
        for prop in self.target_properties:
            submission[prop] = predictions_df[prop] if prop in predictions_df.columns else 0.0
        
        return submission
    
    def save_model(self, filepath):
        """Save trained models and scalers"""
        # Ensure filepath is relative to work directory
        if not Path(filepath).is_absolute():
            filepath = self.work_dir / filepath
        
        model_data = {
            'models': self.models,
            'scalers': self.scalers,
            'feature_columns': self.feature_columns,
            'property_weights': self.property_weights,
            'target_properties': self.target_properties
        }
        
        joblib.dump(model_data, filepath)
        print(f"Model saved to {filepath}")
    
    def load_model(self, filepath):
        """Load trained models and scalers"""
        # Ensure filepath is relative to work directory
        if not Path(filepath).is_absolute():
            filepath = self.work_dir / filepath
        
        model_data = joblib.load(filepath)
        self.models = model_data['models']
        self.scalers = model_data['scalers']
        self.feature_columns = model_data['feature_columns']
        self.property_weights = model_data['property_weights']
        self.target_properties = model_data['target_properties']
        print(f"Model loaded from {filepath}")

#!/usr/bin/env python3
"""
NEURIPs Polymer Dataset Analyzer
Complete analysis tool for polymer competition dataset

Usage:
    python polymer_dataset_analyzer.py

This script will analyze all data files in the specified directory and generate
a comprehensive report on dataset structure, quality, and characteristics.
"""

import os
import pandas as pd
import numpy as np
from pathlib import Path
import json
import warnings
warnings.filterwarnings('ignore')

class PolymerDatasetAnalyzer:
    """Complete analyzer for NEURIPs polymer dataset"""
    
    def __init__(self, directory_path):
        self.directory_path = Path(directory_path)
        self.file_inventory = []
        self.file_analyses = {}
        self.data_files = None
        
    def explore_directory(self):
        """Explore directory structure and catalog files"""
        print("=== NEURIPs Polymer Dataset Analysis ===")
        print(f"Directory: {self.directory_path}")
        print(f"Directory exists: {self.directory_path.exists()}")
        
        if not self.directory_path.exists():
            print("❌ Directory does not exist")
            return False
        
        print("\n=== Directory Contents ===")
        
        def scan_directory(path, level=0):
            indent = "  " * level
            for item in sorted(path.iterdir()):
                if item.is_file():
                    size_mb = item.stat().st_size / (1024 * 1024)
                    print(f"{indent}📄 {item.name} ({size_mb:.2f} MB)")
                    self.file_inventory.append({
                        'path': str(item),
                        'name': item.name,
                        'size_mb': size_mb,
                        'extension': item.suffix,
                        'type': 'file'
                    })
                elif item.is_dir():
                    print(f"{indent}📁 {item.name}/")
                    self.file_inventory.append({
                        'path': str(item),
                        'name': item.name,
                        'size_mb': 0,
                        'extension': '',
                        'type': 'directory'
                    })
                    if level < 2:  # Limit recursion depth
                        scan_directory(item, level + 1)
        
        scan_directory(self.directory_path)
        return True

    def analyze_file_types(self):
        """Analyze file types and extensions in the dataset"""
        files_df = pd.DataFrame([f for f in self.file_inventory if f['type'] == 'file'])
        
        if files_df.empty:
            print("No files found in directory")
            return None, None
        
        print("\n=== File Type Analysis ===")
        
        # Group by extension
        ext_summary = files_df.groupby('extension').agg({
            'name': 'count',
            'size_mb': ['sum', 'mean']
        }).round(2)
        ext_summary.columns = ['count', 'total_size_mb', 'avg_size_mb']
        
        print("File extensions summary:")
        print(ext_summary)
        
        # Identify likely data files
        data_extensions = ['.csv', '.json', '.parquet', '.pkl', '.h5', '.xlsx', '.txt']
        data_files = files_df[files_df['extension'].isin(data_extensions)]
        
        print(f"\n=== Potential Data Files ({len(data_files)} found) ===")
        for _, file_info in data_files.iterrows():
            print(f"📊 {file_info['name']} ({file_info['size_mb']:.2f} MB)")
        
        self.data_files = data_files
        return files_df, data_files

    def find_documentation(self):
        """Find documentation and README files"""
        doc_keywords = ['readme', 'doc', 'info', 'description', 'meta', 'schema']
        doc_extensions = ['.txt', '.md', '.rst', '.pdf', '.json']
        
        print("\n=== Documentation Files ===")
        
        doc_files = []
        for file_info in self.file_inventory:
            if file_info['type'] == 'file':
                name_lower = file_info['name'].lower()
                has_doc_keyword = any(keyword in name_lower for keyword in doc_keywords)
                has_doc_extension = file_info['extension'].lower() in doc_extensions
                
                if has_doc_keyword or (has_doc_extension and file_info['size_mb'] < 1):
                    doc_files.append(file_info)
                    print(f"📋 {file_info['name']} ({file_info['size_mb']:.2f} MB)")
        
        if not doc_files:
            print("No documentation files found")
        
        return doc_files

    def analyze_data_files(self):
        """Analyze structure and content of data files"""
        if self.data_files is None or self.data_files.empty:
            print("No data files to analyze")
            return
        
        print("\n=== Data File Structure Analysis ===")
        
        for _, file_info in self.data_files.iterrows():
            file_path = Path(file_info['path'])
            print(f"\n📊 Analyzing: {file_info['name']}")
            
            try:
                # Determine how to load the file based on extension
                if file_info['extension'] == '.csv':
                    # Try to read CSV with different encodings
                    try:
                        df = pd.read_csv(file_path, nrows=5)  # Read first 5 rows for structure
                        full_df = pd.read_csv(file_path)
                    except UnicodeDecodeError:
                        df = pd.read_csv(file_path, encoding='latin-1', nrows=5)
                        full_df = pd.read_csv(file_path, encoding='latin-1')
                    
                    analysis = {
                        'shape': full_df.shape,
                        'columns': list(df.columns),
                        'dtypes': df.dtypes.to_dict(),
                        'sample_data': df.head(3).to_dict(),
                        'missing_values': full_df.isnull().sum().to_dict(),
                        'file_type': 'CSV'
                    }
                    
                elif file_info['extension'] == '.json':
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                    
                    analysis = {
                        'type': type(data).__name__,
                        'keys': list(data.keys()) if isinstance(data, dict) else 'Not a dictionary',
                        'sample_data': str(data)[:500] + '...' if len(str(data)) > 500 else str(data),
                        'file_type': 'JSON'
                    }
                    
                elif file_info['extension'] == '.txt':
                    with open(file_path, 'r') as f:
                        content = f.read(1000)  # Read first 1000 characters
                    
                    analysis = {
                        'content_preview': content,
                        'file_type': 'Text'
                    }
                    
                else:
                    analysis = {
                        'file_type': f'Unknown ({file_info["extension"]})',
                        'note': 'File type not supported for automatic analysis'
                    }
                
                self.file_analyses[file_info['name']] = analysis
                
                # Print summary
                print(f"  Type: {analysis.get('file_type', 'Unknown')}")
                if 'shape' in analysis:
                    print(f"  Shape: {analysis['shape']} (rows × columns)")
                    print(f"  Columns: {len(analysis['columns'])}")
                    print(f"  Column names: {analysis['columns'][:5]}{'...' if len(analysis['columns']) > 5 else ''}")
                
            except Exception as e:
                print(f"  ❌ Error analyzing file: {str(e)}")
                self.file_analyses[file_info['name']] = {'error': str(e)}

    def detailed_column_analysis(self):
        """Provide detailed analysis of columns in CSV files"""
        print("\n=== Detailed Column Analysis ===")
        
        for filename, analysis in self.file_analyses.items():
            if analysis.get('file_type') == 'CSV' and 'columns' in analysis:
                print(f"\n📊 {filename}")
                print(f"  Dataset shape: {analysis['shape']}")
                
                # Column types summary
                dtype_counts = {}
                for col, dtype in analysis['dtypes'].items():
                    dtype_str = str(dtype)
                    dtype_counts[dtype_str] = dtype_counts.get(dtype_str, 0) + 1
                
                print(f"  Data types distribution:")
                for dtype, count in dtype_counts.items():
                    print(f"    {dtype}: {count} columns")
                
                # Missing values summary
                missing_summary = analysis['missing_values']
                total_missing = sum(missing_summary.values())
                total_cells = analysis['shape'][0] * analysis['shape'][1]
                missing_pct = (total_missing / total_cells) * 100 if total_cells > 0 else 0
                
                print(f"  Missing values: {total_missing:,} ({missing_pct:.2f}% of all cells)")
                
                # Columns with missing values
                cols_with_missing = {k: v for k, v in missing_summary.items() if v > 0}
                if cols_with_missing:
                    print(f"  Columns with missing values ({len(cols_with_missing)}):")
                    for col, missing_count in sorted(cols_with_missing.items(), key=lambda x: x[1], reverse=True)[:10]:
                        missing_pct_col = (missing_count / analysis['shape'][0]) * 100
                        print(f"    {col}: {missing_count:,} ({missing_pct_col:.1f}%)")
                    if len(cols_with_missing) > 10:
                        print(f"    ... and {len(cols_with_missing) - 10} more columns")

    def numerical_statistics_analysis(self):
        """Generate summary statistics for numerical columns"""
        print("\n=== Numerical Statistics Analysis ===")
        
        if self.data_files is None or self.data_files.empty:
            return
        
        for _, file_info in self.data_files.iterrows():
            if file_info['extension'] == '.csv':
                file_path = Path(file_info['path'])
                print(f"\n📊 {file_info['name']}")
                
                try:
                    # Load the full dataset
                    try:
                        df = pd.read_csv(file_path)
                    except UnicodeDecodeError:
                        df = pd.read_csv(file_path, encoding='latin-1')
                    
                    # Select numerical columns
                    numerical_cols = df.select_dtypes(include=[np.number]).columns
                    
                    if len(numerical_cols) > 0:
                        print(f"  Numerical columns: {len(numerical_cols)}")
                        
                        # Generate summary statistics
                        stats = df[numerical_cols].describe()
                        
                        print(f"  Summary statistics (showing first 5 columns):")
                        print(stats.iloc[:, :5].round(3))
                        
                        # Check for potential outliers (values beyond 3 standard deviations)
                        outlier_info = {}
                        for col in numerical_cols[:10]:  # Check first 10 numerical columns
                            mean_val = df[col].mean()
                            std_val = df[col].std()
                            if pd.notna(mean_val) and pd.notna(std_val) and std_val > 0:
                                outliers = df[(df[col] < mean_val - 3*std_val) | (df[col] > mean_val + 3*std_val)]
                                if len(outliers) > 0:
                                    outlier_info[col] = len(outliers)
                        
                        if outlier_info:
                            print(f"  Potential outliers (>3σ from mean):")
                            for col, count in sorted(outlier_info.items(), key=lambda x: x[1], reverse=True)[:5]:
                                pct = (count / len(df)) * 100
                                print(f"    {col}: {count} ({pct:.2f}%)")
                    
                    else:
                        print(f"  No numerical columns found")
                    
                    # Check for categorical columns
                    categorical_cols = df.select_dtypes(include=['object']).columns
                    if len(categorical_cols) > 0:
                        print(f"  Categorical columns: {len(categorical_cols)}")
                        
                        # Show unique value counts for first few categorical columns
                        for col in categorical_cols[:3]:
                            unique_count = df[col].nunique()
                            total_count = len(df)
                            print(f"    {col}: {unique_count} unique values ({unique_count/total_count*100:.1f}% unique)")
                            
                            # Show most common values
                            if unique_count <= 20:
                                top_values = df[col].value_counts().head(5)
                                print(f"      Top values: {dict(top_values)}")
                    
                except Exception as e:
                    print(f"  ❌ Error analyzing numerical data: {str(e)}")

    def missing_value_pattern_analysis(self):
        """Analyze missing value patterns in detail"""
        print("\n=== Missing Value Pattern Analysis ===")
        
        if self.data_files is None or self.data_files.empty:
            return
        
        for _, file_info in self.data_files.iterrows():
            if file_info['extension'] == '.csv':
                file_path = Path(file_info['path'])
                print(f"\n📊 {file_info['name']}")
                
                try:
                    # Load dataset
                    try:
                        df = pd.read_csv(file_path)
                    except UnicodeDecodeError:
                        df = pd.read_csv(file_path, encoding='latin-1')
                    
                    # Calculate missing value statistics
                    missing_stats = df.isnull().sum()
                    missing_pct = (missing_stats / len(df)) * 100
                    
                    # Overall missing value summary
                    total_missing = missing_stats.sum()
                    total_cells = df.shape[0] * df.shape[1]
                    overall_missing_pct = (total_missing / total_cells) * 100
                    
                    print(f"  Overall missing data: {total_missing:,} / {total_cells:,} ({overall_missing_pct:.2f}%)")
                    
                    # Columns with missing values
                    cols_with_missing = missing_stats[missing_stats > 0]
                    if len(cols_with_missing) > 0:
                        print(f"  Columns with missing values: {len(cols_with_missing)} / {len(df.columns)}")
                        
                        # Show worst missing value columns
                        worst_missing = cols_with_missing.sort_values(ascending=False).head(10)
                        print(f"  Top missing value columns:")
                        for col, count in worst_missing.items():
                            pct = (count / len(df)) * 100
                            print(f"    {col}: {count:,} ({pct:.1f}%)")
                        
                        # Check for rows with many missing values
                        row_missing_counts = df.isnull().sum(axis=1)
                        rows_with_many_missing = (row_missing_counts > len(df.columns) * 0.5).sum()
                        if rows_with_many_missing > 0:
                            print(f"  Rows with >50% missing values: {rows_with_many_missing:,}")
                    
                    else:
                        print(f"  ✅ No missing values found!")
                    
                except Exception as e:
                    print(f"  ❌ Error analyzing missing values: {str(e)}")

    def outlier_and_anomaly_detection(self):
        """Detect outliers and anomalies in the data"""
        print("\n=== Outlier and Anomaly Detection ===")
        
        if self.data_files is None or self.data_files.empty:
            return
        
        for _, file_info in self.data_files.iterrows():
            if file_info['extension'] == '.csv':
                file_path = Path(file_info['path'])
                print(f"\n📊 {file_info['name']}")
                
                try:
                    # Load dataset
                    try:
                        df = pd.read_csv(file_path)
                    except UnicodeDecodeError:
                        df = pd.read_csv(file_path, encoding='latin-1')
                    
                    numerical_cols = df.select_dtypes(include=[np.number]).columns
                    
                    if len(numerical_cols) > 0:
                        print(f"  Analyzing {len(numerical_cols)} numerical columns for outliers...")
                        
                        outlier_summary = {}
                        
                        for col in numerical_cols[:15]:  # Analyze first 15 numerical columns
                            col_data = df[col].dropna()
                            
                            if len(col_data) > 0:
                                # IQR method
                                Q1 = col_data.quantile(0.25)
                                Q3 = col_data.quantile(0.75)
                                IQR = Q3 - Q1
                                lower_bound = Q1 - 1.5 * IQR
                                upper_bound = Q3 + 1.5 * IQR
                                
                                iqr_outliers = col_data[(col_data < lower_bound) | (col_data > upper_bound)]
                                
                                # Z-score method (>3 standard deviations)
                                mean_val = col_data.mean()
                                std_val = col_data.std()
                                if std_val > 0:
                                    z_scores = np.abs((col_data - mean_val) / std_val)
                                    z_outliers = col_data[z_scores > 3]
                                else:
                                    z_outliers = pd.Series([], dtype=float)
                                
                                # Check for extreme values
                                min_val = col_data.min()
                                max_val = col_data.max()
                                
                                outlier_summary[col] = {
                                    'iqr_outliers': len(iqr_outliers),
                                    'z_outliers': len(z_outliers),
                                    'min': min_val,
                                    'max': max_val,
                                    'range': max_val - min_val
                                }
                        
                        # Report outliers
                        cols_with_outliers = {k: v for k, v in outlier_summary.items() 
                                            if v['iqr_outliers'] > 0 or v['z_outliers'] > 0}
                        
                        if cols_with_outliers:
                            print(f"  Columns with outliers: {len(cols_with_outliers)}")
                            for col, stats in list(cols_with_outliers.items())[:5]:
                                iqr_pct = (stats['iqr_outliers'] / len(df)) * 100
                                z_pct = (stats['z_outliers'] / len(df)) * 100
                                print(f"    {col}:")
                                print(f"      IQR outliers: {stats['iqr_outliers']} ({iqr_pct:.2f}%)")
                                print(f"      Z-score outliers: {stats['z_outliers']} ({z_pct:.2f}%)")
                                print(f"      Range: [{stats['min']:.3f}, {stats['max']:.3f}]")
                        else:
                            print(f"  ✅ No significant outliers detected")
                    
                    # Check for duplicate rows
                    duplicate_count = df.duplicated().sum()
                    if duplicate_count > 0:
                        duplicate_pct = (duplicate_count / len(df)) * 100
                        print(f"  ⚠️  Duplicate rows: {duplicate_count:,} ({duplicate_pct:.2f}%)")
                    else:
                        print(f"  ✅ No duplicate rows found")
                    
                except Exception as e:
                    print(f"  ❌ Error detecting outliers: {str(e)}")

    def data_consistency_validation(self):
        """Validate data consistency and format issues"""
        print("\n=== Data Consistency Validation ===")
        
        if self.data_files is None or self.data_files.empty:
            return
        
        for _, file_info in self.data_files.iterrows():
            if file_info['extension'] == '.csv':
                file_path = Path(file_info['path'])
                print(f"\n📊 {file_info['name']}")
                
                try:
                    # Load dataset
                    try:
                        df = pd.read_csv(file_path)
                    except UnicodeDecodeError:
                        df = pd.read_csv(file_path, encoding='latin-1')
                    
                    print(f"  Dataset shape: {df.shape}")
                    
                    # Check for consistent data types
                    print(f"  Data type consistency:")
                    for col in df.columns[:10]:  # Check first 10 columns
                        dtype = df[col].dtype
                        if dtype == 'object':
                            # Check if numeric data is stored as strings
                            sample_values = df[col].dropna().head(100)
                            numeric_count = 0
                            for val in sample_values:
                                try:
                                    float(str(val))
                                    numeric_count += 1
                                except (ValueError, TypeError):
                                    pass
                            
                            if numeric_count > len(sample_values) * 0.8:
                                print(f"    ⚠️  {col}: Object type but {numeric_count}/{len(sample_values)} values are numeric")
                            else:
                                unique_count = df[col].nunique()
                                print(f"    ✅ {col}: Text data ({unique_count} unique values)")
                        else:
                            print(f"    ✅ {col}: {dtype}")
                    
                    # Check for ID columns or primary keys
                    potential_id_cols = []
                    for col in df.columns:
                        if any(keyword in col.lower() for keyword in ['id', 'key', 'index']):
                            unique_ratio = df[col].nunique() / len(df)
                            if unique_ratio > 0.95:  # High uniqueness suggests ID column
                                potential_id_cols.append((col, unique_ratio))
                    
                    if potential_id_cols:
                        print(f"  Potential ID columns:")
                        for col, ratio in potential_id_cols:
                            print(f"    {col}: {ratio:.3f} uniqueness ratio")
                    
                    # Check for date/time columns
                    potential_date_cols = []
                    for col in df.columns:
                        if any(keyword in col.lower() for keyword in ['date', 'time', 'timestamp', 'created', 'updated']):
                            potential_date_cols.append(col)
                    
                    if potential_date_cols:
                        print(f"  Potential date/time columns: {potential_date_cols}")
                        for col in potential_date_cols[:3]:
                            sample_vals = df[col].dropna().head(5).tolist()
                            print(f"    {col} samples: {sample_vals}")
                    
                    # Check for categorical columns with high cardinality
                    categorical_cols = df.select_dtypes(include=['object']).columns
                    high_cardinality_cols = []
                    for col in categorical_cols:
                        unique_count = df[col].nunique()
                        total_count = len(df)
                        if unique_count > total_count * 0.5 and unique_count > 100:
                            high_cardinality_cols.append((col, unique_count))
                    
                    if high_cardinality_cols:
                        print(f"  High cardinality categorical columns:")
                        for col, unique_count in high_cardinality_cols:
                            print(f"    {col}: {unique_count} unique values")
                    
                except Exception as e:
                    print(f"  ❌ Error validating consistency: {str(e)}")

    def analyze_file_relationships(self):
        """Analyze relationships and connections between different data files"""
        print("\n=== File Relationship Analysis ===")
        
        csv_files = {name: analysis for name, analysis in self.file_analyses.items() 
                    if analysis.get('file_type') == 'CSV' and 'columns' in analysis}
        
        if len(csv_files) < 2:
            print("Need at least 2 CSV files to analyze relationships")
            return
        
        print(f"Analyzing relationships between {len(csv_files)} CSV files...")
        
        # Look for common columns between files
        all_columns = {}
        for filename, analysis in csv_files.items():
            all_columns[filename] = set(analysis['columns'])
        
        # Find common columns
        file_pairs = []
        for i, (file1, cols1) in enumerate(all_columns.items()):
            for j, (file2, cols2) in enumerate(all_columns.items()):
                if i < j:  # Avoid duplicate pairs
                    common_cols = cols1.intersection(cols2)
                    if common_cols:
                        file_pairs.append({
                            'file1': file1,
                            'file2': file2,
                            'common_columns': common_cols,
                            'common_count': len(common_cols)
                        })
        
        if file_pairs:
            print(f"\nCommon columns between files:")
            for pair in sorted(file_pairs, key=lambda x: x['common_count'], reverse=True):
                print(f"  {pair['file1']} ↔ {pair['file2']}: {pair['common_count']} common columns")
                if pair['common_count'] <= 10:
                    print(f"    Columns: {sorted(pair['common_columns'])}")
                else:
                    sample_cols = sorted(list(pair['common_columns']))[:5]
                    print(f"    Sample columns: {sample_cols}... (+{pair['common_count']-5} more)")
        
        # Look for potential foreign key relationships
        print(f"\nPotential key relationships:")
        id_columns = {}
        for filename, analysis in csv_files.items():
            potential_ids = [col for col in analysis['columns'] 
                            if any(keyword in col.lower() for keyword in ['id', 'key', 'index'])]
            if potential_ids:
                id_columns[filename] = potential_ids
        
        if id_columns:
            for filename, ids in id_columns.items():
                print(f"  {filename}: {ids}")
        else:
            print("  No obvious ID/key columns found")

    def identify_key_patterns(self):
        """Identify key patterns and characteristics in the dataset"""
        print("\n=== Key Pattern Identification ===")
        
        # Analyze dataset characteristics
        total_files = len([f for f in self.file_analyses.values() if f.get('file_type') == 'CSV'])
        total_rows = 0
        total_columns = 0
        
        print(f"Dataset Overview:")
        print(f"  Total CSV files: {total_files}")
        
        for filename, analysis in self.file_analyses.items():
            if analysis.get('file_type') == 'CSV' and 'shape' in analysis:
                rows, cols = analysis['shape']
                total_rows += rows
                total_columns += cols
                print(f"  {filename}: {rows:,} rows × {cols} columns")
        
        if total_files > 0:
            print(f"  Total data points: {total_rows:,} rows")
            print(f"  Average columns per file: {total_columns/total_files:.1f}")
        
        # Identify common column patterns
        all_column_names = []
        for analysis in self.file_analyses.values():
            if analysis.get('file_type') == 'CSV' and 'columns' in analysis:
                all_column_names.extend(analysis['columns'])
        
        # Find common column name patterns
        column_patterns = {}
        for col in all_column_names:
            col_lower = col.lower()
            # Look for common prefixes/suffixes
            if '_' in col_lower:
                parts = col_lower.split('_')
                for part in parts:
                    if len(part) > 2:  # Ignore very short parts
                        column_patterns[part] = column_patterns.get(part, 0) + 1
        
        common_patterns = {k: v for k, v in column_patterns.items() if v > 2}
        if common_patterns:
            print(f"\nCommon column name patterns:")
            for pattern, count in sorted(common_patterns.items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f"  '{pattern}': appears in {count} column names")
        
        # Identify data scale and complexity
        print(f"\nData Complexity Assessment:")
        
        # Check for high-dimensional data
        high_dim_files = []
        for filename, analysis in self.file_analyses.items():
            if analysis.get('file_type') == 'CSV' and 'shape' in analysis:
                rows, cols = analysis['shape']
                if cols > 50:
                    high_dim_files.append((filename, cols))
        
        if high_dim_files:
            print(f"  High-dimensional files (>50 columns):")
            for filename, cols in high_dim_files:
                print(f"    {filename}: {cols} columns")
        
        # Check for large datasets
        large_files = []
        for filename, analysis in self.file_analyses.items():
            if analysis.get('file_type') == 'CSV' and 'shape' in analysis:
                rows, cols = analysis['shape']
                if rows > 100000:
                    large_files.append((filename, rows))
        
        if large_files:
            print(f"  Large datasets (>100K rows):")
            for filename, rows in large_files:
                print(f"    {filename}: {rows:,} rows")

    def generate_summary_and_recommendations(self):
        """Generate comprehensive summary and recommendations"""
        print("\n" + "="*60)
        print("COMPREHENSIVE DATASET ANALYSIS SUMMARY")
        print("="*60)
        
        # Dataset overview
        csv_files = [f for f in self.file_analyses.values() if f.get('file_type') == 'CSV']
        json_files = [f for f in self.file_analyses.values() if f.get('file_type') == 'JSON']
        other_files = [f for f in self.file_analyses.values() if f.get('file_type') not in ['CSV', 'JSON']]
        
        print(f"\n📊 DATASET OVERVIEW:")
        print(f"  • CSV files: {len(csv_files)}")
        print(f"  • JSON files: {len(json_files)}")
        print(f"  • Other files: {len(other_files)}")
        
        # Data volume summary
        total_rows = sum(analysis.get('shape', [0, 0])[0] for analysis in csv_files)
        total_cols = sum(analysis.get('shape', [0, 0])[1] for analysis in csv_files)
        
        print(f"\n📈 DATA VOLUME:")
        print(f"  • Total rows across all CSV files: {total_rows:,}")
        print(f"  • Total columns across all CSV files: {total_cols}")
        
        # Data quality summary
        files_with_missing = 0
        total_missing_cells = 0
        
        for filename, analysis in self.file_analyses.items():
            if analysis.get('file_type') == 'CSV' and 'missing_values' in analysis:
                missing_sum = sum(analysis['missing_values'].values())
                if missing_sum > 0:
                    files_with_missing += 1
                    total_missing_cells += missing_sum
        
        print(f"\n🔍 DATA QUALITY:")
        print(f"  • Files with missing values: {files_with_missing}/{len(csv_files)}")
        print(f"  • Total missing cells: {total_missing_cells:,}")
        
        # Key characteristics
        print(f"\n🎯 KEY CHARACTERISTICS:")
        
        # Find the largest file
        largest_file = None
        largest_size = 0
        for filename, analysis in self.file_analyses.items():
            if analysis.get('file_type') == 'CSV' and 'shape' in analysis:
                size = analysis['shape'][0] * analysis['shape'][1]
                if size > largest_size:
                    largest_size = size
                    largest_file = (filename, analysis['shape'])
        
        if largest_file:
            print(f"  • Largest dataset: {largest_file[0]} ({largest_file[1][0]:,} × {largest_file[1][1]})")
        
        # Find most complex file (most columns)
        most_complex = None
        max_cols = 0
        for filename, analysis in self.file_analyses.items():
            if analysis.get('file_type') == 'CSV' and 'shape' in analysis:
                cols = analysis['shape'][1]
                if cols > max_cols:
                    max_cols = cols
                    most_complex = filename
        
        if most_complex:
            print(f"  • Most complex file: {most_complex} ({max_cols} columns)")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        
        print(f"  📋 Data Preparation:")
        if files_with_missing > 0:
            print(f"    • Address missing values in {files_with_missing} files")
        print(f"    • Validate data types and formats")
        print(f"    • Check for and handle outliers")
        
        print(f"  🔧 Analysis Approach:")
        if len(csv_files) > 1:
            print(f"    • Investigate relationships between multiple data files")
            print(f"    • Consider data integration strategies")
        
        if max_cols > 50:
            print(f"    • Consider dimensionality reduction for high-dimensional data")
        
        if total_rows > 1000000:
            print(f"    • Implement efficient data processing for large datasets")
            print(f"    • Consider sampling strategies for initial exploration")
        
        print(f"  🎯 Modeling Considerations:")
        print(f"    • Polymer-specific feature engineering may be needed")
        print(f"    • Consider domain expertise for feature selection")
        print(f"    • Evaluate appropriate ML algorithms for the data characteristics")
        
        print(f"\n✅ ANALYSIS COMPLETE")
        print("="*60)

    def run_complete_analysis(self):
        """Run the complete analysis pipeline"""
        print("🚀 Starting NEURIPs Polymer Dataset Analysis...")
        
        # Step 1: Explore directory
        if not self.explore_directory():
            return
        
        # Step 2: Analyze file types
        self.analyze_file_types()
        
        # Step 3: Find documentation
        self.find_documentation()
        
        # Step 4: Analyze data files
        self.analyze_data_files()
        
        # Step 5: Detailed column analysis
        self.detailed_column_analysis()
        
        # Step 6: Numerical statistics
        self.numerical_statistics_analysis()
        
        # Step 7: Missing value analysis
        self.missing_value_pattern_analysis()
        
        # Step 8: Outlier detection
        self.outlier_and_anomaly_detection()
        
        # Step 9: Data consistency validation
        self.data_consistency_validation()
        
        # Step 10: File relationships
        self.analyze_file_relationships()
        
        # Step 11: Pattern identification
        self.identify_key_patterns()
        
        # Step 12: Summary and recommendations
        self.generate_summary_and_recommendations()
        
        print("\n🎉 Polymer dataset analysis completed!")


def main():
    """Main execution function"""
    # Set the directory path
    directory_path = "/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/Kaggle/NEURIPs_polymer"
    
    # Create analyzer instance
    analyzer = PolymerDatasetAnalyzer(directory_path)
    
    # Run complete analysis
    analyzer.run_complete_analysis()


if __name__ == "__main__":
    main()
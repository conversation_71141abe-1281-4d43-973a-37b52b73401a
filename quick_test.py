"""
Quick test script to verify the solution works
"""

import os
import sys
from pathlib import Path

# Set working directory
WORK_DIR = Path("/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/Kaggle/NEURIPs_polymer")
os.chdir(WORK_DIR)
sys.path.insert(0, str(WORK_DIR))

def test_imports():
    """Test that all imports work correctly"""
    print("Testing imports...")
    try:
        from polymer_competition_solution import PolymerPropertyPredictor
        print("✓ PolymerPropertyPredictor imported successfully")
        
        # Test initialization
        predictor = PolymerPropertyPredictor(work_dir=WORK_DIR)
        print("✓ PolymerPropertyPredictor initialized successfully")
        return True
    except Exception as e:
        print(f"✗ Import error: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality with sample data"""
    print("\nTesting basic functionality...")
    try:
        from polymer_competition_solution import PolymerPropertyPredictor
        import pandas as pd
        
        predictor = PolymerPropertyPredictor(work_dir=WORK_DIR)
        
        # Test feature extraction with sample SMILES
        sample_smiles = ['CCO', 'CC(C)O', 'CCCC', 'c1ccccc1']
        features = predictor.extract_molecular_features(sample_smiles)
        print(f"✓ Feature extraction: {features.shape}")
        
        # Test fallback features
        fallback = predictor.extract_fallback_features('CCO')
        print(f"✓ Fallback features: {len(fallback)} features")
        
        return True
    except Exception as e:
        print(f"✗ Functionality test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("=== Quick Test Suite ===")
    print(f"Working directory: {WORK_DIR}")
    
    # Test imports
    if not test_imports():
        return
    
    # Test basic functionality
    if not test_basic_functionality():
        return
    
    print("\n✓ All tests passed!")

if __name__ == "__main__":
    main()